# Newzora 后台管理系统 UI 详细设计文档

## 📋 文档信息

- **项目名称**: Newzora 后台管理系统
- **文档版本**: v1.0
- **创建日期**: 2024年12月
- **设计师**: UI设计团队
- **技术栈**: Next.js 14 + TypeScript + Tailwind CSS

## 🎯 设计目标

### 核心目标
- 提供高效、直观的管理界面
- 保持与主站品牌一致性
- 支持响应式设计
- 优化管理员操作体验

### 用户群体
- **主要用户**: 平台管理员、内容审核员
- **使用场景**: 日常运营管理、数据分析、内容审核
- **设备环境**: 桌面端为主，兼容平板设备

## 🎨 视觉设计系统

### 色彩规范

#### 主色调
```css
:root {
  /* 品牌主色 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 状态色 */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;
}
```

#### 色彩应用场景
- **主色 (#3b82f6)**: 主要按钮、链接、重要信息
- **成功色 (#10b981)**: 成功状态、正向数据
- **警告色 (#f59e0b)**: 警告信息、待处理状态
- **错误色 (#ef4444)**: 错误状态、危险操作
- **中性色**: 文本、边框、背景

### 字体系统

#### 字体规格
```css
/* 字体大小 */
--text-xs: 0.75rem;    /* 12px - 辅助文字 */
--text-sm: 0.875rem;   /* 14px - 正文 */
--text-base: 1rem;     /* 16px - 基础文字 */
--text-lg: 1.125rem;   /* 18px - 小标题 */
--text-xl: 1.25rem;    /* 20px - 标题 */
--text-2xl: 1.5rem;    /* 24px - 大标题 */
--text-3xl: 1.875rem;  /* 30px - 页面标题 */

/* 字重 */
--font-normal: 400;    /* 正文 */
--font-medium: 500;    /* 强调 */
--font-semibold: 600;  /* 小标题 */
--font-bold: 700;      /* 标题 */
```

#### 字体应用规则
- **页面标题**: text-3xl + font-bold
- **区块标题**: text-xl + font-semibold
- **表格标题**: text-sm + font-medium + uppercase
- **正文内容**: text-sm + font-normal
- **辅助信息**: text-xs + font-normal

### 间距系统

#### 间距规格
```css
/* 间距单位 */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
```

#### 间距应用规则
- **组件内边距**: space-4 (16px)
- **组件间距**: space-6 (24px)
- **区块间距**: space-8 (32px)
- **页面边距**: space-6 (24px)

### 圆角与阴影

#### 圆角规格
```css
--radius-sm: 0.125rem;  /* 2px - 小元素 */
--radius: 0.25rem;      /* 4px - 按钮、输入框 */
--radius-md: 0.375rem;  /* 6px - 卡片 */
--radius-lg: 0.5rem;    /* 8px - 大卡片 */
--radius-xl: 0.75rem;   /* 12px - 模态框 */
```

#### 阴影规格
```css
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
```

## 📐 布局设计

### 整体布局结构

#### 桌面端布局 (≥1024px)
```
┌─────────────────────────────────────────────────────────┐
│  Header (高度: 60px)                                    │
├─────────────────────────────────────────────────────────┤
│ Side │                                                  │
│ bar  │              Main Content                        │
│(240px│              (最小宽度: 784px)                    │
│      │                                                  │
│      │                                                  │
└─────────────────────────────────────────────────────────┘
```

#### 平板端布局 (768px - 1023px)
```
┌─────────────────────────────────────────────────────────┐
│  Header + 汉堡菜单                                       │
├─────────────────────────────────────────────────────────┤
│                                                         │
│              Main Content (全宽)                        │
│                                                         │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 响应式断点
```css
/* 响应式断点定义 */
--breakpoint-sm: 640px;   /* 大手机 */
--breakpoint-md: 768px;   /* 平板竖屏 */
--breakpoint-lg: 1024px;  /* 平板横屏/小桌面 */
--breakpoint-xl: 1280px;  /* 桌面端 */
--breakpoint-2xl: 1536px; /* 大屏桌面 */
```

## 🧩 核心组件设计

### 1. 顶部导航栏 (AdminHeader)

#### 设计规格
- **高度**: 60px
- **背景**: 白色 (#ffffff)
- **边框**: 底部 1px 灰色 (#e5e7eb)
- **z-index**: 50

#### 布局结构
```
┌─────────────────────────────────────────────────────────┐
│ [Logo] Newzora Admin  [搜索框]    [通知][头像][菜单]     │
└─────────────────────────────────────────────────────────┘
```

#### 组件元素详情

**Logo区域**
- 位置: 左侧，距离左边 24px
- 字体: text-xl + font-bold
- 颜色: text-gray-900
- 副标题: text-sm + font-normal + text-gray-500

**搜索框**
- 位置: 中间，最大宽度 400px
- 高度: 40px
- 圆角: radius
- 边框: border-gray-300
- 图标: 左侧搜索图标，颜色 text-gray-400
- 占位符: "搜索用户、文章、评论..."

**功能区域**
- 位置: 右侧，距离右边 24px
- 间距: space-4
- 包含: 通知铃铛、用户头像、下拉菜单

#### 样式实现
```css
.admin-header {
  @apply h-15 bg-white border-b border-gray-200 px-6 flex items-center justify-between fixed top-0 left-0 right-0 z-50;
}

.admin-header .logo {
  @apply text-xl font-bold text-gray-900;
}

.admin-header .search-box {
  @apply flex-1 max-w-md mx-8;
}

.admin-header .search-input {
  @apply w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg;
  @apply focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}
```

### 2. 侧边导航栏 (AdminSidebar)

#### 设计规格
- **宽度**: 240px
- **背景**: 深灰色 (#1f2937)
- **高度**: 100vh - 60px (减去header高度)
- **位置**: 固定定位

#### 导航结构
```
📊 仪表板
👥 用户管理
  ├─ 用户列表
  ├─ 用户统计
  └─ 角色管理
📝 内容管理
  ├─ 文章管理
  ├─ 评论管理
  ├─ 分类管理
  └─ 审核队列
📈 数据分析
  ├─ 用户分析
  ├─ 内容分析
  └─ 系统分析
⚙️ 系统设置
  ├─ 基础设置
  ├─ 安全设置
  └─ 邮件设置
```

#### 菜单项设计
- **一级菜单**: 高度 48px，左边距 16px
- **二级菜单**: 高度 40px，左边距 40px
- **图标**: 16x16px，距离文字 12px
- **激活状态**: 蓝色背景 (#1e40af)，白色文字
- **悬停状态**: 浅灰背景 (#374151)

#### 样式实现
```css
.admin-sidebar {
  @apply w-60 bg-gray-800 h-full fixed left-0 top-15 overflow-y-auto;
}

.sidebar-menu-item {
  @apply flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white;
}

.sidebar-menu-item.active {
  @apply bg-blue-700 text-white;
}

.sidebar-submenu-item {
  @apply flex items-center pl-10 pr-4 py-2 text-sm text-gray-400 hover:bg-gray-700 hover:text-white;
}
```

### 3. 数据统计卡片 (StatsCard)

#### 设计规格
- **最小高度**: 120px
- **背景**: 白色卡片
- **圆角**: radius-lg (8px)
- **阴影**: shadow
- **内边距**: 24px

#### 卡片布局
```
┌─────────────────────────────────┐
│ [图标]  标题                     │
│        数值 (+变化率)            │
│        ────────────────         │
│        [迷你趋势图]              │
└─────────────────────────────────┘
```

#### 卡片类型与配色
1. **用户统计** - 蓝色主题
   - 图标背景: bg-blue-100
   - 图标颜色: text-blue-600
   - 数值颜色: text-gray-900

2. **内容统计** - 绿色主题
   - 图标背景: bg-green-100
   - 图标颜色: text-green-600

3. **活跃度统计** - 黄色主题
   - 图标背景: bg-yellow-100
   - 图标颜色: text-yellow-600

4. **系统统计** - 红色主题
   - 图标背景: bg-red-100
   - 图标颜色: text-red-600

#### 样式实现
```css
.stats-card {
  @apply bg-white rounded-lg shadow p-6 min-h-30;
}

.stats-card-header {
  @apply flex items-center justify-between mb-4;
}

.stats-card-title {
  @apply text-sm font-medium text-gray-600;
}

.stats-card-value {
  @apply text-2xl font-semibold text-gray-900;
}

.stats-card-change {
  @apply ml-2 text-sm;
}

.stats-card-change.positive {
  @apply text-green-600;
}

.stats-card-change.negative {
  @apply text-red-600;
}
```

### 4. 数据表格 (DataTable)

#### 设计规格
- **背景**: 白色卡片
- **圆角**: radius-lg
- **阴影**: shadow
- **表头高度**: 48px
- **数据行高度**: 64px

#### 表格功能
- 排序功能 (可排序列显示排序图标)
- 筛选功能 (表头下方筛选栏)
- 分页功能 (底部分页组件)
- 批量选择 (复选框列)
- 行操作菜单 (最右侧操作列)

#### 表格样式
```css
.data-table {
  @apply bg-white rounded-lg shadow overflow-hidden;
}

.data-table-header {
  @apply bg-gray-50 border-b border-gray-200;
}

.data-table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.data-table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.data-table tr:hover {
  @apply bg-gray-50;
}

.data-table .sortable {
  @apply cursor-pointer hover:bg-gray-100;
}
```

#### 状态标签设计
```css
/* 用户状态 */
.status-active {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800;
}

.status-inactive {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800;
}

/* 内容状态 */
.status-published {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800;
}

.status-draft {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800;
}

.status-pending {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800;
}

.status-rejected {
  @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800;
}
```

### 5. 表单组件设计

#### 输入框 (Input)
```css
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply placeholder-gray-400;
}

.form-input.error {
  @apply border-red-300 focus:ring-red-500;
}

.form-input.disabled {
  @apply bg-gray-100 cursor-not-allowed;
}
```

#### 选择器 (Select)
```css
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply bg-white cursor-pointer;
}
```

#### 按钮 (Button)
```css
/* 主要按钮 */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
}

/* 次要按钮 */
.btn-secondary {
  @apply px-4 py-2 bg-gray-200 text-gray-900 rounded-md hover:bg-gray-300;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

/* 危险按钮 */
.btn-danger {
  @apply px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700;
  @apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

/* 按钮尺寸 */
.btn-sm {
  @apply px-3 py-1.5 text-sm;
}

.btn-lg {
  @apply px-6 py-3 text-lg;
}
```

## 📱 页面设计详情

### 1. 仪表板页面 (/admin/dashboard)

#### 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  仪表板 [时间范围选择器]                                 │
├─────────────────────────────────────────────────────────┤
│ [用户统计] [内容统计] [活跃度] [系统状态]                │
├─────────────────────────────────────────────────────────┤
│ [用户增长趋势图]              [内容发布趋势图]            │
├─────────────────────────────────────────────────────────┤
│ [热门分类统计]                [最新活动列表]              │
├─────────────────────────────────────────────────────────┤
│ [快速操作面板]                [系统通知]                 │
└─────────────────────────────────────────────────────────┘
```

#### 统计卡片内容
1. **用户总数**
   - 主数值: 总用户数
   - 副数值: 今日新增用户
   - 变化率: 相比昨天的增长率
   - 迷你图: 7天用户增长趋势

2. **内容统计**
   - 主数值: 总文章数
   - 副数值: 今日发布文章
   - 变化率: 相比昨天的增长率
   - 迷你图: 7天内容发布趋势

3. **活跃度统计**
   - 主数值: 活跃用户数
   - 副数值: 今日评论数
   - 变化率: 相比昨天的变化
   - 迷你图: 7天活跃度趋势

4. **系统状态**
   - 主数值: 待审核内容数
   - 副数值: 系统健康度
   - 状态: 正常/警告/错误
   - 迷你图: 系统负载趋势

#### 图表设计规范
- **图表高度**: 300px
- **背景**: 白色卡片
- **配色**: 使用品牌色系
- **交互**: 支持悬停显示详细数据

### 2. 用户管理页面 (/admin/users)

#### 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  用户管理 [+ 添加用户] [导出数据]                        │
├─────────────────────────────────────────────────────────┤
│ [搜索框] [角色筛选] [状态筛选] [注册时间] [批量操作]      │
├─────────────────────────────────────────────────────────┤
│                                                         │
│              用户列表表格                                │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                    分页组件                              │
└─────────────────────────────────────────────────────────┘
```

#### 表格列设计
| 列名 | 宽度 | 内容 | 排序 |
|------|------|------|------|
| 选择 | 50px | 复选框 | - |
| 头像 | 60px | 用户头像 | - |
| 用户信息 | 200px | 用户名/邮箱/显示名 | ✓ |
| 角色 | 100px | 角色标签 | ✓ |
| 状态 | 80px | 激活/禁用状态 | ✓ |
| 统计 | 150px | 文章数/评论数/粉丝数 | ✓ |
| 注册时间 | 120px | 注册日期 | ✓ |
| 最后登录 | 120px | 最后登录时间 | ✓ |
| 操作 | 120px | 编辑/禁用/删除 | - |

#### 筛选器设计
- **搜索框**: 支持用户名、邮箱、显示名搜索
- **角色筛选**: 下拉选择 (全部/用户/管理员/审核员)
- **状态筛选**: 下拉选择 (全部/激活/禁用)
- **注册时间**: 日期范围选择器
- **批量操作**: 激活/禁用/删除/更改角色

### 3. 内容管理页面 (/admin/content)

#### 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  内容管理 [发布状态] [分类筛选] [批量操作]               │
├─────────────────────────────────────────────────────────┤
│                                                         │
│              文章列表表格                                │
│                                                         │
├─────────────────────────────────────────────────────────┤
│                    分页组件                              │
└─────────────────────────────────────────────────────────┘
```

#### 表格列设计
| 列名 | 宽度 | 内容 | 排序 |
|------|------|------|------|
| 选择 | 50px | 复选框 | - |
| 标题 | 300px | 文章标题/摘要 | ✓ |
| 作者 | 120px | 作者信息 | ✓ |
| 分类 | 100px | 分类标签 | ✓ |
| 状态 | 80px | 发布状态 | ✓ |
| 统计 | 120px | 浏览/点赞/评论 | ✓ |
| 发布时间 | 120px | 发布日期 | ✓ |
| 操作 | 120px | 编辑/发布/删除 | - |

### 4. 数据分析页面 (/admin/analytics)

#### 页面布局
```
┌─────────────────────────────────────────────────────────┐
│  数据分析 [时间范围] [导出报告]                          │
├─────────────────────────────────────────────────────────┤
│ [概览统计卡片组]                                        │
├─────────────────────────────────────────────────────────┤
│ [用户增长图表]                [内容发布图表]             │
├─────────────────────────────────────────────────────────┤
│ [用户活跃度热力图]            [热门内容排行]             │
├─────────────────────────────────────────────────────────┤
│ [地域分布图]                  [设备统计图]               │
└─────────────────────────────────────────────────────────┘
```

#### 图表类型与设计
1. **折线图** - 趋势数据
   - 用户增长趋势
   - 内容发布趋势
   - 活跃度变化趋势

2. **柱状图** - 对比数据
   - 分类内容统计
   - 月度数据对比
   - 设备类型统计

3. **饼图** - 占比数据
   - 用户角色分布
   - 内容分类占比
   - 流量来源分布

4. **热力图** - 活跃度数据
   - 用户活跃时间分布
   - 内容发布时间分布

## 🎭 交互设计规范

### 状态反馈

#### 加载状态
```css
/* 骨架屏 */
.skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.skeleton-text {
  @apply h-4 bg-gray-200 rounded;
}

.skeleton-avatar {
  @apply h-10 w-10 bg-gray-200 rounded-full;
}

/* 加载动画 */
.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}
```

#### 消息提示
```css
/* 成功提示 */
.toast-success {
  @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded;
}

/* 错误提示 */
.toast-error {
  @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded;
}

/* 警告提示 */
.toast-warning {
  @apply bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded;
}

/* 信息提示 */
.toast-info {
  @apply bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded;
}
```

### 模态框设计

#### 确认对话框
- **背景遮罩**: 半透明黑色 (bg-black bg-opacity-50)
- **对话框**: 白色卡片，最大宽度 400px
- **圆角**: radius-xl
- **阴影**: shadow-lg
- **动画**: 淡入淡出效果

#### 表单模态框
- **最大宽度**: 600px
- **内边距**: 24px
- **表单间距**: 16px
- **按钮区域**: 右对齐，间距 12px

### 批量操作

#### 选择状态
- **全选**: 表头复选框，半选状态支持
- **部分选择**: 显示选中数量
- **操作栏**: 选中后显示批量操作按钮

#### 操作确认
- **危险操作**: 需要二次确认
- **批量删除**: 需要输入确认文字
- **进度显示**: 批量操作进度条

## 📊 图表设计规范

### 图表配色方案
```css
/* 主要配色 */
--chart-primary: #3b82f6;
--chart-secondary: #10b981;
--chart-tertiary: #f59e0b;
--chart-quaternary: #ef4444;
--chart-quinary: #8b5cf6;

/* 渐变配色 */
--chart-gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--chart-gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
--chart-gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
```

### 图表样式规范
- **网格线**: 浅灰色 (#f3f4f6)
- **坐标轴**: 中灰色 (#6b7280)
- **标签文字**: 深灰色 (#374151)
- **工具提示**: 白色背景，阴影效果

### 响应式图表
- **桌面端**: 高度 400px
- **平板端**: 高度 300px
- **手机端**: 高度 250px

## 📱 响应式设计

### 断点策略
```css
/* 移动端优先 */
.responsive-grid {
  @apply grid grid-cols-1 gap-6;
}

/* 平板端 */
@media (min-width: 768px) {
  .responsive-grid {
    @apply grid-cols-2;
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .responsive-grid {
    @apply grid-cols-4;
  }
}
```

### 移动端适配

#### 导航适配
- **侧边栏**: 抽屉式导航
- **顶部栏**: 汉堡菜单
- **搜索框**: 可收缩设计

#### 表格适配
- **水平滚动**: 表格容器可滚动
- **卡片模式**: 小屏幕下转为卡片布局
- **关键信息**: 优先显示重要列

#### 图表适配
- **简化显示**: 减少数据点
- **触摸优化**: 增大触摸区域
- **横屏优化**: 支持横屏查看

## 🎨 主题定制

### 深色主题
```css
[data-theme="dark"] {
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --border-color: #4b5563;
}
```

### 品牌定制
```css
/* 自定义品牌色 */
:root {
  --brand-primary: #6366f1;
  --brand-secondary: #8b5cf6;
  --brand-accent: #06b6d4;
  --brand-success: #10b981;
  --brand-warning: #f59e0b;
  --brand-error: #ef4444;
}
```

## 🔧 组件库规范

### 基础组件接口
```typescript
// 按钮组件
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'danger' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  children: React.ReactNode;
  onClick?: () => void;
}

// 输入框组件
interface InputProps {
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  type?: 'text' | 'email' | 'password' | 'number';
  value?: string;
  onChange?: (value: string) => void;
}

// 选择器组件
interface SelectProps {
  label?: string;
  placeholder?: string;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  multiple?: boolean;
  options: Array<{
    label: string;
    value: string;
    disabled?: boolean;
  }>;
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
}
```

### 复合组件接口
```typescript
// 数据表格组件
interface DataTableProps<T> {
  data: T[];
  columns: Array<{
    key: keyof T;
    title: string;
    width?: string;
    sortable?: boolean;
    render?: (value: any, record: T) => React.ReactNode;
  }>;
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  rowSelection?: {
    selectedRowKeys: string[];
    onChange: (keys: string[]) => void;
  };
  onRow?: (record: T) => {
    onClick?: () => void;
  };
}

// 搜索筛选组件
interface SearchFilterProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  filters?: Array<{
    key: string;
    label: string;
    type: 'select' | 'date' | 'dateRange';
    options?: Array<{label: string; value: string}>;
  }>;
  onFilterChange?: (filters: Record<string, any>) => void;
}
```

## 🎯 可访问性设计

### 键盘导航
- **Tab顺序**: 逻辑清晰的Tab导航顺序
- **焦点指示**: 清晰的焦点状态样式
- **快捷键**: 常用功能的键盘快捷键

### 屏幕阅读器
- **语义化标签**: 使用正确的HTML语义
- **ARIA标签**: 添加必要的ARIA属性
- **替代文本**: 图片和图标的替代文本

### 色彩对比度
- **文字对比度**: 至少4.5:1的对比度
- **状态指示**: 不仅依赖颜色区分状态
- **色盲友好**: 考虑色盲用户的使用体验

## ✅ 设计检查清单

### 视觉设计
- [x] 色彩系统完整且一致
- [x] 字体规范统一应用
- [x] 间距系统规范化
- [x] 组件样式标准化
- [x] 品牌元素正确应用

### 交互设计
- [x] 状态反馈及时清晰
- [x] 操作流程简洁顺畅
- [x] 错误处理用户友好
- [x] 加载状态优雅展示
- [x] 批量操作高效便捷

### 响应式设计
- [x] 桌面端体验优化
- [x] 平板端适配完善
- [x] 移动端友好设计
- [x] 跨设备一致性保证

### 性能优化
- [x] 图片资源优化
- [x] 字体加载优化
- [x] 动画性能优化
- [x] 组件懒加载支持

### 可访问性
- [x] 键盘导航完整
- [x] 屏幕阅读器支持
- [x] 色彩对比度达标
- [x] 语义化标签正确

### 浏览器兼容性
- [x] Chrome 90+
- [x] Firefox 88+
- [x] Safari 14+
- [x] Edge 90+

---

## 📝 附录

### A. 设计资源
- **图标库**: Heroicons, Lucide React
- **图表库**: Chart.js, Recharts
- **动画库**: Framer Motion
- **工具库**: Headless UI, Radix UI

### B. 设计工具
- **设计软件**: Figma
- **原型工具**: Figma, Framer
- **标注工具**: Figma Dev Mode
- **切图工具**: Figma Export

### C. 开发规范
- **CSS框架**: Tailwind CSS
- **组件库**: 自定义组件库
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**负责人**: UI设计团队  
**审核状态**: 待开发团队审核