# 🔧 Supabase真实配置指南

**目标**: 将Newzora项目从模拟认证切换到真实的Supabase认证服务

## 📋 当前问题

### 1. 使用占位符配置
```env
# 当前配置 (占位符)
NEXT_PUBLIC_SUPABASE_URL=https://placeholder.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=placeholder-key-for-development
```

### 2. 用户数据无法持久化
- 注册用户只存在于模拟服务中
- 无法在数据库中查看用户数据
- 刷新页面后用户状态可能丢失

## 🚀 解决方案

### 步骤1: 创建Supabase项目

1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 点击 "New Project"
3. 选择组织并填写项目信息：
   - Name: `newzora`
   - Database Password: 设置强密码
   - Region: 选择最近的区域
4. 等待项目创建完成

### 步骤2: 获取项目配置

1. 在项目Dashboard中，点击 "Settings" → "API"
2. 复制以下信息：
   - Project URL
   - anon public key

### 步骤3: 更新环境配置

更新 `Frontend/.env.local`:
```env
# Supabase Configuration (Real)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
```

### 步骤4: 创建用户表结构

在Supabase SQL Editor中执行：

```sql
-- 创建profiles表
CREATE TABLE public.profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  username TEXT UNIQUE,
  email TEXT,
  display_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  PRIMARY KEY (id)
);

-- 启用RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 创建RLS策略
CREATE POLICY "Public profiles are viewable by everyone." ON public.profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile." ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile." ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

-- 创建触发器自动创建profile
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, username, display_name)
  VALUES (
    new.id,
    new.email,
    COALESCE(new.raw_user_meta_data->>'username', split_part(new.email, '@', 1)),
    COALESCE(new.raw_user_meta_data->>'name', split_part(new.email, '@', 1))
  );
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
```

### 步骤5: 配置邮件模板

1. 在Supabase Dashboard中，转到 "Authentication" → "Templates"
2. 自定义邮件验证模板
3. 设置重定向URL为你的域名

### 步骤6: 测试配置

1. 重启开发服务器
2. 尝试注册新用户
3. 检查Supabase Dashboard中的用户数据
4. 验证邮件功能

## 🔍 验证清单

- [ ] Supabase项目已创建
- [ ] 环境变量已更新
- [ ] profiles表已创建
- [ ] RLS策略已设置
- [ ] 触发器已创建
- [ ] 邮件模板已配置
- [ ] 用户注册功能正常
- [ ] 用户数据在数据库中可见
- [ ] 邮件验证功能正常

## 🚨 注意事项

1. **安全性**: 确保只使用anon key，不要暴露service_role key
2. **RLS**: 必须启用行级安全策略
3. **邮件**: 配置SMTP或使用Supabase内置邮件服务
4. **域名**: 在生产环境中设置正确的重定向URL

## 📊 配置前后对比

| 功能 | 配置前 | 配置后 |
|------|--------|--------|
| 用户注册 | 模拟数据 | 真实数据库 |
| 用户登录 | 本地存储 | Supabase认证 |
| 数据持久化 | 临时 | 永久 |
| 邮件验证 | 模拟 | 真实邮件 |
| 用户管理 | 无 | Supabase Dashboard |

配置完成后，Newzora将使用真实的Supabase认证服务，用户数据将持久化存储在数据库中。