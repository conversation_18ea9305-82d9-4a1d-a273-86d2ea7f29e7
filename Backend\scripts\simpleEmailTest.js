require('dotenv').config();
const nodemailer = require('nodemailer');

async function testGmailConnection() {
  console.log('🔧 测试Gmail SMTP连接...\n');

  const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('验证SMTP连接...');
    await transporter.verify();
    console.log('✅ SMTP连接验证成功');

    console.log('发送测试邮件...');
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: process.env.EMAIL_USER,
      subject: 'Newzora SMTP测试',
      text: '这是一封测试邮件，用于验证Newzora的SMTP配置。',
      html: '<h1>SMTP测试成功</h1><p>这是一封测试邮件，用于验证Newzora的SMTP配置。</p>'
    });

    console.log('✅ 测试邮件发送成功');
    console.log('邮件ID:', info.messageId);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    
    if (error.message.includes('BadCredentials')) {
      console.log('\n💡 可能的解决方案:');
      console.log('1. 确认Gmail账户已启用"两步验证"');
      console.log('2. 确认使用的是"应用专用密码"而不是普通密码');
      console.log('3. 检查应用专用密码是否正确（16位字符，无空格）');
      console.log('4. 尝试重新生成应用专用密码');
    }
  }
}

testGmailConnection();