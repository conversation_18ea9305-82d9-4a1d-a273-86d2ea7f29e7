#!/bin/bash

# OneNews 数据库恢复脚本
# 支持从完整备份、增量备份恢复数据库

set -e  # 遇到错误立即退出

# 配置变量
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-onenews}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD}"

# 备份目录配置
BACKUP_DIR="${BACKUP_DIR:-./backups}"
LOG_FILE="${BACKUP_DIR}/restore.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 检查依赖
check_dependencies() {
    log "检查依赖工具..."
    
    if ! command -v pg_restore &> /dev/null; then
        error_exit "pg_restore 未安装，请安装 PostgreSQL 客户端工具"
    fi
    
    if ! command -v psql &> /dev/null; then
        error_exit "psql 未安装，请安装 PostgreSQL 客户端工具"
    fi
    
    if [ -z "$DB_PASSWORD" ]; then
        error_exit "数据库密码未设置，请设置 DB_PASSWORD 环境变量"
    fi
    
    log "依赖检查完成"
}

# 测试数据库连接
test_connection() {
    log "测试数据库连接..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" &> /dev/null; then
        error_exit "无法连接到数据库服务器 $DB_HOST:$DB_PORT"
    fi
    
    log "数据库连接正常"
}

# 列出可用的备份文件
list_backups() {
    log "可用的备份文件:"
    
    echo ""
    echo "完整备份文件:"
    if ls "$BACKUP_DIR/full/"*.sql.gz 2>/dev/null; then
        ls -lht "$BACKUP_DIR/full/"*.sql.gz | head -10
    else
        echo "  无完整备份文件"
    fi
    
    echo ""
    echo "增量备份目录:"
    if ls -d "$BACKUP_DIR/incremental/"*/ 2>/dev/null; then
        ls -lht "$BACKUP_DIR/incremental/" | head -10
    else
        echo "  无增量备份目录"
    fi
    
    echo ""
    echo "结构备份文件:"
    if ls "$BACKUP_DIR/"onenews_schema_*.sql.gz 2>/dev/null; then
        ls -lht "$BACKUP_DIR/"onenews_schema_*.sql.gz | head -5
    else
        echo "  无结构备份文件"
    fi
    
    echo ""
    echo "数据备份文件:"
    if ls "$BACKUP_DIR/"onenews_data_*.sql.gz 2>/dev/null; then
        ls -lht "$BACKUP_DIR/"onenews_data_*.sql.gz | head -5
    else
        echo "  无数据备份文件"
    fi
}

# 创建数据库 (如果不存在)
create_database() {
    log "检查数据库是否存在..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -lqt | cut -d \| -f 1 | grep -qw "$DB_NAME"; then
        log "数据库 $DB_NAME 已存在"
    else
        log "创建数据库 $DB_NAME..."
        
        if createdb -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" "$DB_NAME"; then
            log "数据库 $DB_NAME 创建成功"
        else
            error_exit "数据库 $DB_NAME 创建失败"
        fi
    fi
}

# 备份当前数据库 (恢复前安全备份)
backup_current_database() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/pre_restore_backup_$timestamp.sql"
    
    log "恢复前备份当前数据库..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --format=custom \
        --file="$backup_file" \
        2>> "$LOG_FILE"; then
        
        gzip "$backup_file"
        log "当前数据库备份完成: $backup_file.gz"
    else
        log "WARNING: 当前数据库备份失败，继续恢复过程"
    fi
}

# 从完整备份恢复
restore_from_full_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        error_exit "备份文件不存在: $backup_file"
    fi
    
    log "从完整备份恢复: $backup_file"
    
    # 解压备份文件 (如果是压缩的)
    local restore_file="$backup_file"
    if [[ "$backup_file" == *.gz ]]; then
        restore_file="${backup_file%.gz}"
        log "解压备份文件..."
        gunzip -c "$backup_file" > "$restore_file"
    fi
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 清空现有数据库
    log "清空现有数据库..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;" \
        2>> "$LOG_FILE" || error_exit "清空数据库失败"
    
    # 恢复数据库
    log "开始恢复数据库..."
    if pg_restore \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --clean \
        --if-exists \
        "$restore_file" \
        2>> "$LOG_FILE"; then
        
        log "数据库恢复成功"
    else
        error_exit "数据库恢复失败"
    fi
    
    # 清理临时文件
    if [[ "$backup_file" == *.gz ]] && [ -f "$restore_file" ]; then
        rm -f "$restore_file"
    fi
}

# 从 SQL 文件恢复
restore_from_sql() {
    local sql_file="$1"
    
    if [ ! -f "$sql_file" ]; then
        error_exit "SQL 文件不存在: $sql_file"
    fi
    
    log "从 SQL 文件恢复: $sql_file"
    
    # 解压 SQL 文件 (如果是压缩的)
    local restore_file="$sql_file"
    if [[ "$sql_file" == *.gz ]]; then
        restore_file="${sql_file%.gz}"
        log "解压 SQL 文件..."
        gunzip -c "$sql_file" > "$restore_file"
    fi
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行 SQL 文件
    log "执行 SQL 文件..."
    if psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -f "$restore_file" \
        2>> "$LOG_FILE"; then
        
        log "SQL 文件执行成功"
    else
        error_exit "SQL 文件执行失败"
    fi
    
    # 清理临时文件
    if [[ "$sql_file" == *.gz ]] && [ -f "$restore_file" ]; then
        rm -f "$restore_file"
    fi
}

# 验证恢复结果
verify_restore() {
    log "验证恢复结果..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 检查表是否存在
    local table_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" \
        2>/dev/null | tr -d ' ')
    
    if [ "$table_count" -gt 0 ]; then
        log "数据库表验证成功，共 $table_count 个表"
    else
        error_exit "数据库表验证失败，未找到任何表"
    fi
    
    # 检查数据完整性
    log "检查主要表的数据..."
    
    local users_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM users;" 2>/dev/null | tr -d ' ' || echo "0")
    
    local articles_count=$(psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -t -c "SELECT COUNT(*) FROM articles;" 2>/dev/null | tr -d ' ' || echo "0")
    
    log "用户数量: $users_count"
    log "文章数量: $articles_count"
    
    # 运行数据库优化
    log "运行数据库优化..."
    psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" \
        -c "ANALYZE;" 2>> "$LOG_FILE" || log "WARNING: 数据库分析失败"
    
    log "恢复验证完成"
}

# 交互式恢复
interactive_restore() {
    echo ""
    echo "=== OneNews 数据库交互式恢复 ==="
    echo ""
    
    list_backups
    
    echo ""
    read -p "请输入要恢复的备份文件完整路径: " backup_file
    
    if [ ! -f "$backup_file" ]; then
        error_exit "文件不存在: $backup_file"
    fi
    
    echo ""
    echo "警告: 此操作将完全替换当前数据库内容！"
    read -p "是否继续? (yes/no): " confirm
    
    if [ "$confirm" != "yes" ]; then
        log "恢复操作已取消"
        exit 0
    fi
    
    # 备份当前数据库
    backup_current_database
    
    # 执行恢复
    if [[ "$backup_file" == *.sql.gz ]] || [[ "$backup_file" == *.sql ]]; then
        restore_from_sql "$backup_file"
    else
        restore_from_full_backup "$backup_file"
    fi
    
    # 验证恢复
    verify_restore
}

# 主函数
main() {
    local restore_type="${1:-interactive}"
    local backup_file="$2"
    
    log "开始 OneNews 数据库恢复 (类型: $restore_type)"
    
    # 检查依赖和连接
    check_dependencies
    test_connection
    
    # 创建数据库 (如果不存在)
    create_database
    
    case "$restore_type" in
        "interactive")
            interactive_restore
            ;;
        "full")
            if [ -z "$backup_file" ]; then
                error_exit "请指定备份文件路径"
            fi
            backup_current_database
            restore_from_full_backup "$backup_file"
            verify_restore
            ;;
        "sql")
            if [ -z "$backup_file" ]; then
                error_exit "请指定 SQL 文件路径"
            fi
            backup_current_database
            restore_from_sql "$backup_file"
            verify_restore
            ;;
        "list")
            list_backups
            ;;
        *)
            error_exit "未知的恢复类型: $restore_type. 支持的类型: interactive, full, sql, list"
            ;;
    esac
    
    log "数据库恢复任务完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
OneNews 数据库恢复脚本

用法: $0 [restore_type] [backup_file]

恢复类型:
  interactive  交互式恢复 (默认)
  full         从完整备份恢复
  sql          从 SQL 文件恢复
  list         列出可用备份

环境变量:
  DB_HOST      数据库主机 (默认: localhost)
  DB_PORT      数据库端口 (默认: 5432)
  DB_NAME      数据库名称 (默认: onenews)
  DB_USER      数据库用户 (默认: postgres)
  DB_PASSWORD  数据库密码 (必需)
  BACKUP_DIR   备份目录 (默认: ./backups)

示例:
  $0                                    # 交互式恢复
  $0 list                              # 列出可用备份
  $0 full ./backups/full/backup.sql.gz # 从指定备份恢复
  $0 sql ./backups/schema.sql.gz       # 从 SQL 文件恢复

警告: 恢复操作将完全替换现有数据库内容！

EOF
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

main "$@"
