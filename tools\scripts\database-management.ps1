# OneNews 数据库管理脚本 (PowerShell)
# 提供数据库备份、恢复、优化和监控功能

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("backup", "restore", "optimize", "monitor", "health", "stats", "help")]
    [string]$Action,
    
    [string]$BackupFile = "",
    [string]$BackupType = "full",
    [switch]$Interactive = $false,
    [switch]$Verbose = $false
)

# 配置变量
$DB_HOST = if ($env:DB_HOST) { $env:DB_HOST } else { "localhost" }
$DB_PORT = if ($env:DB_PORT) { $env:DB_PORT } else { "5432" }
$DB_NAME = if ($env:DB_NAME) { $env:DB_NAME } else { "onenews" }
$DB_USER = if ($env:DB_USER) { $env:DB_USER } else { "postgres" }
$DB_PASSWORD = $env:DB_PASSWORD

$BACKUP_DIR = if ($env:BACKUP_DIR) { $env:BACKUP_DIR } else { "./backups" }
$LOG_FILE = "$BACKUP_DIR/management.log"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Blue" = "Cyan"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Write-Success { param([string]$Message) Write-ColorOutput "✅ $Message" "Green" }
function Write-Error { param([string]$Message) Write-ColorOutput "❌ $Message" "Red" }
function Write-Warning { param([string]$Message) Write-ColorOutput "⚠️  $Message" "Yellow" }
function Write-Info { param([string]$Message) Write-ColorOutput "ℹ️  $Message" "Blue" }

# 日志函数
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # 创建日志目录
    $logDir = Split-Path $LOG_FILE -Parent
    if (!(Test-Path $logDir)) {
        New-Item -ItemType Directory -Path $logDir -Force | Out-Null
    }
    
    # 写入日志文件
    Add-Content -Path $LOG_FILE -Value $logEntry
    
    # 根据详细模式输出
    if ($Verbose) {
        Write-Host $logEntry -ForegroundColor Gray
    }
}

# 检查依赖
function Test-Dependencies {
    Write-Info "检查依赖工具..."
    Write-Log "检查依赖工具"
    
    $dependencies = @("psql", "pg_dump", "pg_restore")
    $missing = @()
    
    foreach ($dep in $dependencies) {
        if (!(Get-Command $dep -ErrorAction SilentlyContinue)) {
            $missing += $dep
        }
    }
    
    if ($missing.Count -gt 0) {
        Write-Error "缺少依赖工具: $($missing -join ', ')"
        Write-Error "请安装 PostgreSQL 客户端工具"
        return $false
    }
    
    if ([string]::IsNullOrEmpty($DB_PASSWORD)) {
        Write-Error "数据库密码未设置，请设置 DB_PASSWORD 环境变量"
        return $false
    }
    
    Write-Success "依赖检查完成"
    return $true
}

# 测试数据库连接
function Test-DatabaseConnection {
    Write-Info "测试数据库连接..."
    Write-Log "测试数据库连接到 ${DB_HOST}:${DB_PORT}/${DB_NAME}"

    $env:PGPASSWORD = $DB_PASSWORD

    try {
        $result = & pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库连接正常"
            Write-Log "数据库连接成功"
            return $true
        } else {
            Write-Error "数据库连接失败: $result"
            Write-Log "数据库连接失败: $result" "ERROR"
            return $false
        }
    } catch {
        Write-Error "数据库连接测试失败: $($_.Exception.Message)"
        Write-Log "数据库连接测试失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 执行备份
function Invoke-DatabaseBackup {
    param([string]$Type = "full")
    
    Write-Info "开始数据库备份 (类型: $Type)..."
    Write-Log "开始数据库备份，类型: $Type"
    
    if (!(Test-Path $BACKUP_DIR)) {
        New-Item -ItemType Directory -Path $BACKUP_DIR -Force | Out-Null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "$BACKUP_DIR/onenews_${Type}_$timestamp.sql"
    
    $env:PGPASSWORD = $DB_PASSWORD
    
    try {
        switch ($Type) {
            "full" {
                & pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --verbose --no-password --format=custom --compress=9 --file=$backupFile
            }
            "schema" {
                & pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema-only --verbose --no-password --file=$backupFile
            }
            "data" {
                & pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --data-only --verbose --no-password --file=$backupFile
            }
        }
        
        if ($LASTEXITCODE -eq 0) {
            # 压缩备份文件
            if (Get-Command gzip -ErrorAction SilentlyContinue) {
                & gzip $backupFile
                $backupFile += ".gz"
            }
            
            $fileSize = (Get-Item $backupFile).Length
            $fileSizeFormatted = if ($fileSize -gt 1MB) { "{0:N2} MB" -f ($fileSize / 1MB) } else { "{0:N2} KB" -f ($fileSize / 1KB) }
            
            Write-Success "备份完成: $backupFile"
            Write-Success "备份大小: $fileSizeFormatted"
            Write-Log "备份成功完成: $backupFile, 大小: $fileSizeFormatted"
            
            return $backupFile
        } else {
            Write-Error "备份失败"
            Write-Log "备份失败，退出代码: $LASTEXITCODE" "ERROR"
            return $null
        }
    } catch {
        Write-Error "备份过程中发生错误: $($_.Exception.Message)"
        Write-Log "备份过程中发生错误: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 执行恢复
function Invoke-DatabaseRestore {
    param([string]$BackupFilePath)
    
    if (!(Test-Path $BackupFilePath)) {
        Write-Error "备份文件不存在: $BackupFilePath"
        return $false
    }
    
    Write-Warning "警告: 此操作将完全替换当前数据库内容！"
    
    if (!$Interactive) {
        $confirm = Read-Host "是否继续? (yes/no)"
        if ($confirm -ne "yes") {
            Write-Info "恢复操作已取消"
            return $false
        }
    }
    
    Write-Info "开始数据库恢复: $BackupFilePath"
    Write-Log "开始数据库恢复: $BackupFilePath"
    
    # 备份当前数据库
    Write-Info "备份当前数据库..."
    $preRestoreBackup = Invoke-DatabaseBackup -Type "full"
    if ($preRestoreBackup) {
        Write-Success "当前数据库已备份: $preRestoreBackup"
    }
    
    $env:PGPASSWORD = $DB_PASSWORD
    
    try {
        # 解压文件 (如果需要)
        $restoreFile = $BackupFilePath
        if ($BackupFilePath.EndsWith(".gz")) {
            $restoreFile = $BackupFilePath.Substring(0, $BackupFilePath.Length - 3)
            if (Get-Command gunzip -ErrorAction SilentlyContinue) {
                & gunzip -c $BackupFilePath > $restoreFile
            } else {
                Write-Error "需要 gunzip 工具来解压备份文件"
                return $false
            }
        }
        
        # 执行恢复
        if ($restoreFile.EndsWith(".sql")) {
            # SQL 文件恢复
            & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f $restoreFile
        } else {
            # 自定义格式恢复
            & pg_restore -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --verbose --no-password --clean --if-exists $restoreFile
        }
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库恢复成功"
            Write-Log "数据库恢复成功"
            
            # 清理临时文件
            if ($restoreFile -ne $BackupFilePath -and (Test-Path $restoreFile)) {
                Remove-Item $restoreFile -Force
            }
            
            return $true
        } else {
            Write-Error "数据库恢复失败"
            Write-Log "数据库恢复失败，退出代码: $LASTEXITCODE" "ERROR"
            return $false
        }
    } catch {
        Write-Error "恢复过程中发生错误: $($_.Exception.Message)"
        Write-Log "恢复过程中发生错误: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 执行数据库优化
function Invoke-DatabaseOptimization {
    Write-Info "开始数据库优化..."
    Write-Log "开始数据库优化"
    
    $optimizationScript = "Backend/scripts/optimize-database.sql"
    
    if (!(Test-Path $optimizationScript)) {
        Write-Error "优化脚本不存在: $optimizationScript"
        return $false
    }
    
    $env:PGPASSWORD = $DB_PASSWORD
    
    try {
        Write-Info "执行数据库优化脚本..."
        & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f $optimizationScript
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库优化完成"
            Write-Log "数据库优化成功完成"
            return $true
        } else {
            Write-Error "数据库优化失败"
            Write-Log "数据库优化失败，退出代码: $LASTEXITCODE" "ERROR"
            return $false
        }
    } catch {
        Write-Error "优化过程中发生错误: $($_.Exception.Message)"
        Write-Log "优化过程中发生错误: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 启动数据库监控
function Start-DatabaseMonitoring {
    Write-Info "启动数据库监控..."
    Write-Log "启动数据库监控"
    
    $monitorScript = "Backend/scripts/database-monitor.js"
    
    if (!(Test-Path $monitorScript)) {
        Write-Error "监控脚本不存在: $monitorScript"
        return $false
    }
    
    try {
        Write-Info "启动监控进程..."
        $env:DB_HOST = $DB_HOST
        $env:DB_PORT = $DB_PORT
        $env:DB_NAME = $DB_NAME
        $env:DB_USER = $DB_USER
        $env:DB_PASSWORD = $DB_PASSWORD
        
        Start-Process -FilePath "node" -ArgumentList $monitorScript -NoNewWindow
        Write-Success "数据库监控已启动"
        Write-Log "数据库监控进程已启动"
        return $true
    } catch {
        Write-Error "启动监控失败: $($_.Exception.Message)"
        Write-Log "启动监控失败: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 获取数据库健康状态
function Get-DatabaseHealth {
    Write-Info "检查数据库健康状态..."
    Write-Log "检查数据库健康状态"
    
    $env:PGPASSWORD = $DB_PASSWORD
    
    try {
        # 基本连接测试
        $connectionTest = Test-DatabaseConnection
        if (!$connectionTest) {
            return @{ Status = "Unhealthy"; Reason = "Connection failed" }
        }
        
        # 查询响应时间测试
        $startTime = Get-Date
        $result = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT NOW();" 2>&1
        $responseTime = (Get-Date) - $startTime
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库健康状态: 正常"
            Write-Success "响应时间: $($responseTime.TotalMilliseconds) ms"
            Write-Log "数据库健康检查通过，响应时间: $($responseTime.TotalMilliseconds) ms"
            
            return @{
                Status = "Healthy"
                ResponseTime = $responseTime.TotalMilliseconds
                Timestamp = Get-Date
            }
        } else {
            Write-Error "数据库查询失败: $result"
            Write-Log "数据库查询失败: $result" "ERROR"
            return @{ Status = "Unhealthy"; Reason = "Query failed"; Error = $result }
        }
    } catch {
        Write-Error "健康检查失败: $($_.Exception.Message)"
        Write-Log "健康检查失败: $($_.Exception.Message)" "ERROR"
        return @{ Status = "Unhealthy"; Reason = "Exception"; Error = $_.Exception.Message }
    }
}

# 获取数据库统计信息
function Get-DatabaseStats {
    Write-Info "获取数据库统计信息..."
    Write-Log "获取数据库统计信息"
    
    $env:PGPASSWORD = $DB_PASSWORD
    
    try {
        # 数据库大小
        $dbSize = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" 2>&1
        
        # 连接数统计
        $connections = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT count(*) FROM pg_stat_activity WHERE datname = '$DB_NAME';" 2>&1
        
        # 表数量
        $tableCount = & psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "数据库大小: $($dbSize.Trim())"
            Write-Success "当前连接数: $($connections.Trim())"
            Write-Success "表数量: $($tableCount.Trim())"
            
            Write-Log "数据库统计 - 大小: $($dbSize.Trim()), 连接数: $($connections.Trim()), 表数量: $($tableCount.Trim())"
            
            return @{
                DatabaseSize = $dbSize.Trim()
                Connections = $connections.Trim()
                TableCount = $tableCount.Trim()
                Timestamp = Get-Date
            }
        } else {
            Write-Error "获取统计信息失败"
            Write-Log "获取统计信息失败" "ERROR"
            return $null
        }
    } catch {
        Write-Error "统计信息查询失败: $($_.Exception.Message)"
        Write-Log "统计信息查询失败: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

# 显示帮助信息
function Show-Help {
    Write-ColorOutput "OneNews 数据库管理脚本" "Blue"
    Write-ColorOutput "========================" "Blue"
    Write-Host ""
    
    Write-ColorOutput "用法:" "Yellow"
    Write-Host "  .\database-management.ps1 -Action <action> [参数]"
    Write-Host ""
    
    Write-ColorOutput "操作:" "Yellow"
    Write-Host "  backup    - 数据库备份"
    Write-Host "  restore   - 数据库恢复"
    Write-Host "  optimize  - 数据库优化"
    Write-Host "  monitor   - 启动监控"
    Write-Host "  health    - 健康检查"
    Write-Host "  stats     - 统计信息"
    Write-Host "  help      - 显示帮助"
    Write-Host ""
    
    Write-ColorOutput "参数:" "Yellow"
    Write-Host "  -BackupFile <path>   备份文件路径 (用于恢复)"
    Write-Host "  -BackupType <type>   备份类型 (full/schema/data)"
    Write-Host "  -Interactive         交互模式"
    Write-Host "  -Verbose             详细输出"
    Write-Host ""
    
    Write-ColorOutput "环境变量:" "Yellow"
    Write-Host "  DB_HOST      数据库主机 (默认: localhost)"
    Write-Host "  DB_PORT      数据库端口 (默认: 5432)"
    Write-Host "  DB_NAME      数据库名称 (默认: onenews)"
    Write-Host "  DB_USER      数据库用户 (默认: postgres)"
    Write-Host "  DB_PASSWORD  数据库密码 (必需)"
    Write-Host "  BACKUP_DIR   备份目录 (默认: ./backups)"
    Write-Host ""
    
    Write-ColorOutput "示例:" "Yellow"
    Write-Host "  .\database-management.ps1 -Action backup -BackupType full"
    Write-Host "  .\database-management.ps1 -Action restore -BackupFile ./backups/backup.sql.gz"
    Write-Host "  .\database-management.ps1 -Action health -Verbose"
}

# 主函数
function Main {
    Write-ColorOutput "OneNews 数据库管理工具" "Blue"
    Write-ColorOutput "=====================" "Blue"
    Write-Host ""
    
    # 检查依赖
    if (!(Test-Dependencies)) {
        exit 1
    }
    
    # 执行操作
    switch ($Action.ToLower()) {
        "backup" {
            if (Test-DatabaseConnection) {
                $result = Invoke-DatabaseBackup -Type $BackupType
                if ($result) {
                    Write-Success "备份操作完成"
                    exit 0
                } else {
                    Write-Error "备份操作失败"
                    exit 1
                }
            }
        }
        
        "restore" {
            if ([string]::IsNullOrEmpty($BackupFile)) {
                Write-Error "请指定备份文件路径 (-BackupFile)"
                exit 1
            }
            
            if (Test-DatabaseConnection) {
                $result = Invoke-DatabaseRestore -BackupFilePath $BackupFile
                if ($result) {
                    Write-Success "恢复操作完成"
                    exit 0
                } else {
                    Write-Error "恢复操作失败"
                    exit 1
                }
            }
        }
        
        "optimize" {
            if (Test-DatabaseConnection) {
                $result = Invoke-DatabaseOptimization
                if ($result) {
                    Write-Success "优化操作完成"
                    exit 0
                } else {
                    Write-Error "优化操作失败"
                    exit 1
                }
            }
        }
        
        "monitor" {
            if (Test-DatabaseConnection) {
                $result = Start-DatabaseMonitoring
                if ($result) {
                    Write-Success "监控启动完成"
                    exit 0
                } else {
                    Write-Error "监控启动失败"
                    exit 1
                }
            }
        }
        
        "health" {
            $health = Get-DatabaseHealth
            if ($health.Status -eq "Healthy") {
                Write-Success "数据库健康检查通过"
                exit 0
            } else {
                Write-Error "数据库健康检查失败"
                exit 1
            }
        }
        
        "stats" {
            if (Test-DatabaseConnection) {
                $stats = Get-DatabaseStats
                if ($stats) {
                    Write-Success "统计信息获取完成"
                    exit 0
                } else {
                    Write-Error "统计信息获取失败"
                    exit 1
                }
            }
        }
        
        "help" {
            Show-Help
            exit 0
        }
        
        default {
            Write-Error "未知操作: $Action"
            Show-Help
            exit 1
        }
    }
}

# 运行主函数
Main
