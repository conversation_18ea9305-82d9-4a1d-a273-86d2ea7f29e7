'use client';

import { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useRouter } from 'next/navigation';
import { Shield, Upload, CheckCircle, AlertCircle, Phone, Mail, MapPin, FileText, User, Video, Camera, Brain, Eye } from 'lucide-react';
import { aiVerificationService } from '@/services/aiVerificationService';
import AIAnalysisDetails from '@/components/AIAnalysisDetails';

interface VerificationItem {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'submitted' | 'ai_reviewing' | 'human_reviewing' | 'verified' | 'rejected';
  icon: React.ReactNode;
  required: boolean;
  aiScore?: number;
  humanReview?: boolean;
  reviewNotes?: string;
  aiAnalysis?: {
    confidence: number;
    checks: any;
    extractedData: any;
    flags: string[];
  };
  crossValidation?: {
    consistent: boolean;
    issues: string[];
    confidence: number;
  };
}

export default function VerificationPage() {
  const { user, isLoading } = useSimpleAuth();
  const router = useRouter();
  const [isRecording, setIsRecording] = useState(false);
  const [recordedVideo, setRecordedVideo] = useState<string | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [recordingTime, setRecordingTime] = useState(0);
  const [verifications, setVerifications] = useState<VerificationItem[]>([
    {
      id: 'identity',
      title: 'Identity Proof',
      description: 'Upload a government-issued ID (passport, driver\'s license, or national ID)',
      status: 'pending',
      icon: <User className="h-6 w-6" />,
      required: true
    },
    {
      id: 'address',
      title: 'Address Proof',
      description: 'Upload a utility bill, bank statement, or official document showing your address',
      status: 'pending',
      icon: <MapPin className="h-6 w-6" />,
      required: true
    },
    {
      id: 'document',
      title: 'Document Proof',
      description: 'Upload additional supporting documents (tax forms, business registration, etc.)',
      status: 'pending',
      icon: <FileText className="h-6 w-6" />,
      required: true
    },
    {
      id: 'phone',
      title: 'Phone Verification',
      description: 'Verify your phone number via SMS code',
      status: 'pending',
      icon: <Phone className="h-6 w-6" />,
      required: true
    },
    {
      id: 'video',
      title: 'Video Verification',
      description: 'Record a 3-5 second video holding your ID document next to your face',
      status: 'pending',
      icon: <Video className="h-6 w-6" />,
      required: true
    },
    {
      id: 'email',
      title: 'Email Verification',
      description: 'Verify your email address',
      status: 'verified',
      icon: <Mail className="h-6 w-6" />,
      required: true
    }
  ]);

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
      return;
    }
  }, [user, isLoading, router]);

  const handleFileUpload = async (verificationId: string, file: File) => {
    setVerifications(prev => prev.map(item => 
      item.id === verificationId 
        ? { ...item, status: 'submitted' as const }
        : item
    ));
    
    // AI审核阶段
    setTimeout(() => {
      setVerifications(prev => prev.map(item => 
        item.id === verificationId 
          ? { ...item, status: 'ai_reviewing' as const }
          : item
      ));
    }, 500);
    
    try {
      // 使用AI服务进行分析
      const aiResult = await aiVerificationService.processVerification({
        type: verificationId as any,
        file
      });
      
      setVerifications(prev => {
        const updated = prev.map(item => 
          item.id === verificationId 
            ? { 
                ...item, 
                status: aiResult.needsHumanReview ? 'human_reviewing' as const : 'verified' as const,
                aiScore: aiResult.score,
                humanReview: aiResult.needsHumanReview,
                aiAnalysis: {
                  confidence: aiResult.confidence,
                  checks: aiResult.checks,
                  extractedData: aiResult.extractedData,
                  flags: aiResult.flags
                },
                reviewNotes: aiResult.needsHumanReview 
                  ? `AI Analysis: ${aiResult.reviewReason}` 
                  : `AI Verified: ${aiResult.confidence.toFixed(1)}% confidence`
              }
            : item
        );
        
        // 执行交叉验证
        performCrossValidation(updated);
        
        if (!aiResult.needsHumanReview) {
          updateVerificationStatus(updated);
        }
        
        return updated;
      });
      
      // 如果需要人工审核
      if (aiResult.needsHumanReview) {
        setTimeout(() => {
          setVerifications(prev => {
            const final = prev.map(item => 
              item.id === verificationId 
                ? { 
                    ...item, 
                    status: 'verified' as const,
                    reviewNotes: `Human Verified: ${aiResult.reviewReason} - Approved after manual review`
                  }
                : item
            );
            updateVerificationStatus(final);
            return final;
          });
        }, 3000 + Math.random() * 4000);
      }
    } catch (error) {
      console.error('AI verification failed:', error);
      setVerifications(prev => prev.map(item => 
        item.id === verificationId 
          ? { ...item, status: 'rejected' as const, reviewNotes: 'AI analysis failed' }
          : item
      ));
    }
  };

  const handlePhoneVerification = () => {
    const phoneNumber = prompt('Enter your phone number:');
    if (phoneNumber) {
      setVerifications(prev => prev.map(item => 
        item.id === 'phone' 
          ? { ...item, status: 'submitted' as const }
          : item
      ));
      
      setTimeout(() => {
        const code = prompt('Enter the SMS verification code (demo: 123456):');
        if (code === '123456') {
          // 电话验证也需要AI+人工审核
          setVerifications(prev => prev.map(item => 
            item.id === 'phone' 
              ? { ...item, status: 'ai_reviewing' as const }
              : item
          ));
          
          setTimeout(() => {
            const aiScore = Math.random() * 100;
            const needsHumanReview = Math.random() < 0.2; // 20%随机抽检
            
            setVerifications(prev => {
              const updated = prev.map(item => 
                item.id === 'phone' 
                  ? { 
                      ...item, 
                      status: needsHumanReview ? 'human_reviewing' as const : 'verified' as const,
                      aiScore,
                      humanReview: needsHumanReview,
                      reviewNotes: needsHumanReview ? 'Under human review' : 'AI verified'
                    }
                  : item
              );
              
              if (!needsHumanReview) {
                updateVerificationStatus(updated);
              }
              
              return updated;
            });
            
            if (needsHumanReview) {
              setTimeout(() => {
                setVerifications(prev => {
                  const final = prev.map(item => 
                    item.id === 'phone' 
                      ? { 
                          ...item, 
                          status: 'verified' as const,
                          reviewNotes: 'Human verified after AI analysis'
                        }
                      : item
                  );
                  updateVerificationStatus(final);
                  return final;
                });
              }, 2000);
            }
          }, 1500);
        } else {
          alert('Invalid verification code');
          setVerifications(prev => prev.map(item => 
            item.id === 'phone' 
              ? { ...item, status: 'pending' as const }
              : item
          ));
        }
      }, 1000);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'submitted':
        return <Upload className="h-5 w-5 text-blue-600" />;
      case 'ai_reviewing':
        return (
          <div className="relative">
            <AlertCircle className="h-5 w-5 text-purple-600 animate-pulse" />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-purple-600 rounded-full animate-ping"></div>
          </div>
        );
      case 'human_reviewing':
        return (
          <div className="relative">
            <User className="h-5 w-5 text-orange-600" />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-orange-600 rounded-full animate-pulse"></div>
          </div>
        );
      case 'rejected':
        return <AlertCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'verified':
        return 'Verified';
      case 'submitted':
        return 'Submitted';
      case 'ai_reviewing':
        return 'AI Reviewing';
      case 'human_reviewing':
        return 'Human Review';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Pending';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'text-green-600 bg-green-100';
      case 'submitted':
        return 'text-blue-600 bg-blue-100';
      case 'ai_reviewing':
        return 'text-purple-600 bg-purple-100';
      case 'human_reviewing':
        return 'text-orange-600 bg-orange-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const startVideoRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'user' }, 
        audio: false 
      });
      
      const recorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunks.push(event.data);
        }
      };
      
      recorder.onstop = async () => {
        const blob = new Blob(chunks, { type: 'video/webm' });
        const videoUrl = URL.createObjectURL(blob);
        setRecordedVideo(videoUrl);
        stream.getTracks().forEach(track => track.stop());
        
        // 视频验证的AI分析
        setVerifications(prev => prev.map(item => 
          item.id === 'video' 
            ? { ...item, status: 'submitted' as const }
            : item
        ));
        
        setTimeout(() => {
          setVerifications(prev => prev.map(item => 
            item.id === 'video' 
              ? { ...item, status: 'ai_reviewing' as const }
              : item
          ));
        }, 1000);
        
        // 使用AI服务分析视频
        try {
          const aiResult = await aiVerificationService.processVerification({
            type: 'video',
            videoBlob: blob
          });
          
          setVerifications(prev => {
            const updated = prev.map(item => 
              item.id === 'video' 
                ? { 
                    ...item, 
                    status: aiResult.needsHumanReview ? 'human_reviewing' as const : 'verified' as const,
                    aiScore: aiResult.score,
                    humanReview: aiResult.needsHumanReview,
                    aiAnalysis: {
                      confidence: aiResult.confidence,
                      checks: aiResult.checks,
                      extractedData: aiResult.extractedData,
                      flags: aiResult.flags
                    },
                    reviewNotes: aiResult.needsHumanReview 
                      ? `AI Analysis: ${aiResult.reviewReason}` 
                      : `AI Verified: Face match ${aiResult.checks.faceMatch?.toFixed(1)}%`
                  }
                : item
            );
            
            if (!aiResult.needsHumanReview) {
              updateVerificationStatus(updated);
            }
            
            return updated;
          });
          
          if (aiResult.needsHumanReview) {
            setTimeout(() => {
              setVerifications(prev => {
                const final = prev.map(item => 
                  item.id === 'video' 
                    ? { 
                        ...item, 
                        status: 'verified' as const,
                        reviewNotes: `Human Verified: ${aiResult.reviewReason} - Video manually reviewed and approved`
                      }
                    : item
                );
                updateVerificationStatus(final);
                return final;
              });
            }, 4000 + Math.random() * 3000);
          }
        } catch (error) {
          console.error('Video AI analysis failed:', error);
          setVerifications(prev => prev.map(item => 
            item.id === 'video' 
              ? { ...item, status: 'rejected' as const, reviewNotes: 'Video analysis failed' }
              : item
          ));
        }
      };
      
      setMediaRecorder(recorder);
      recorder.start();
      setIsRecording(true);
      setRecordingTime(0);
      
      // Auto-stop after 5 seconds
      const timer = setInterval(() => {
        setRecordingTime(prev => {
          if (prev >= 4) {
            clearInterval(timer);
            recorder.stop();
            setIsRecording(false);
            return 5;
          }
          return prev + 1;
        });
      }, 1000);
      
    } catch (error) {
      alert('Camera access denied. Please allow camera access to complete video verification.');
    }
  };
  
  const stopVideoRecording = () => {
    if (mediaRecorder && isRecording) {
      mediaRecorder.stop();
      setIsRecording(false);
    }
  };

  const performCrossValidation = async (verifications: VerificationItem[]) => {
    const completedVerifications = verifications.filter(v => v.aiAnalysis && v.status === 'verified');
    
    if (completedVerifications.length >= 2) {
      try {
        const aiResults = completedVerifications.map(v => ({
          score: v.aiScore || 0,
          confidence: v.aiAnalysis?.confidence || 0,
          checks: v.aiAnalysis?.checks || {},
          extractedData: v.aiAnalysis?.extractedData || {},
          flags: v.aiAnalysis?.flags || [],
          needsHumanReview: false
        }));
        
        const crossValidation = await aiVerificationService.crossValidateDocuments(aiResults);
        
        setVerifications(prev => prev.map(item => ({
          ...item,
          crossValidation: completedVerifications.some(v => v.id === item.id) ? crossValidation : item.crossValidation
        })));
        
        if (!crossValidation.consistent) {
          console.warn('Cross-validation issues detected:', crossValidation.issues);
        }
      } catch (error) {
        console.error('Cross-validation failed:', error);
      }
    }
  };

  const updateVerificationStatus = (updatedVerifications: VerificationItem[]) => {
    const status = {
      identityProof: updatedVerifications.find(v => v.id === 'identity')?.status === 'verified',
      addressProof: updatedVerifications.find(v => v.id === 'address')?.status === 'verified',
      documentProof: updatedVerifications.find(v => v.id === 'document')?.status === 'verified',
      phoneVerification: updatedVerifications.find(v => v.id === 'phone')?.status === 'verified',
      emailVerification: updatedVerifications.find(v => v.id === 'email')?.status === 'verified',
      videoVerification: updatedVerifications.find(v => v.id === 'video')?.status === 'verified'
    };
    localStorage.setItem('verificationStatus', JSON.stringify(status));
  };

  const completedVerifications = verifications.filter(v => v.status === 'verified').length;
  const totalVerifications = verifications.length;
  const progressPercentage = (completedVerifications / totalVerifications) * 100;

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Account Verification</h1>
        <p className="text-gray-600">Complete all verification steps to enable withdrawals and unlock full platform features.</p>
        <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2 flex items-center space-x-2">
            <Brain className="h-5 w-5" />
            <span>AI + Human Dual Review System</span>
          </h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>• <strong>AI Analysis:</strong> Document quality, text extraction, authenticity checks</p>
            <p>• <strong>Cross-validation:</strong> Data consistency across multiple documents</p>
            <p>• <strong>Human Review:</strong> Complex cases and quality assurance checks</p>
            <p>• <strong>Security:</strong> Multi-layer verification prevents fraud</p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Verification Progress</h2>
          <span className="text-sm text-gray-600">{completedVerifications}/{totalVerifications} completed</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-blue-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600 mt-2">{progressPercentage.toFixed(0)}% complete</p>
        
        {/* Review Process Explanation */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
          <div className="flex items-center space-x-2 text-purple-600">
            <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
            <span>1. AI Analysis</span>
          </div>
          <div className="flex items-center space-x-2 text-orange-600">
            <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
            <span>2. Human Review (if needed)</span>
          </div>
          <div className="flex items-center space-x-2 text-green-600">
            <div className="w-2 h-2 bg-green-600 rounded-full"></div>
            <span>3. Verification Complete</span>
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {verifications.map((verification) => (
          <div key={verification.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0 p-2 bg-blue-100 rounded-lg text-blue-600">
                  {verification.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {verification.title}
                    {verification.required && <span className="text-red-500 ml-1">*</span>}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">{verification.description}</p>
                  
                  {verification.id === 'phone' && verification.status === 'pending' && (
                    <button
                      onClick={handlePhoneVerification}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                    >
                      Verify Phone Number
                    </button>
                  )}
                  
                  {verification.id === 'video' && verification.status === 'pending' && (
                    <div className="space-y-4">
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <h4 className="font-medium text-yellow-800 mb-2">Video Verification Instructions:</h4>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          <li>• Hold your ID document next to your face</li>
                          <li>• Ensure both your face and ID are clearly visible</li>
                          <li>• Recording will be 3-5 seconds</li>
                          <li>• Make sure you're in good lighting</li>
                        </ul>
                      </div>
                      
                      {recordedVideo ? (
                        <div className="space-y-3">
                          <video 
                            src={recordedVideo} 
                            controls 
                            className="w-full max-w-sm rounded-lg border"
                          />
                          <div className="flex space-x-2">
                            <button
                              onClick={() => {
                                setRecordedVideo(null);
                                setRecordingTime(0);
                              }}
                              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors text-sm"
                            >
                              Record Again
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={isRecording ? stopVideoRecording : startVideoRecording}
                            disabled={verification.status === 'submitted'}
                            className={`px-4 py-2 rounded-lg transition-colors text-sm flex items-center space-x-2 ${
                              isRecording 
                                ? 'bg-red-600 text-white hover:bg-red-700' 
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                            } disabled:opacity-50`}
                          >
                            <Camera className="h-4 w-4" />
                            <span>
                              {isRecording 
                                ? `Recording... ${recordingTime + 1}s` 
                                : 'Start Video Recording'
                              }
                            </span>
                          </button>
                          {isRecording && (
                            <div className="text-sm text-red-600 font-medium">
                              Hold your ID next to your face
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {['identity', 'address', 'document'].includes(verification.id) && verification.status === 'pending' && (
                    <div className="flex items-center space-x-3">
                      <input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleFileUpload(verification.id, file);
                          }
                        }}
                        className="hidden"
                        id={`file-${verification.id}`}
                      />
                      <label
                        htmlFor={`file-${verification.id}`}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors cursor-pointer text-sm flex items-center space-x-2"
                      >
                        <Upload className="h-4 w-4" />
                        <span>Upload Document</span>
                      </label>
                      <span className="text-xs text-gray-500">PDF, JPG, PNG (max 10MB)</span>
                    </div>
                  )}
                </div>
              </div>
              
              <div className="flex flex-col items-end space-y-2">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(verification.status)}
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(verification.status)}`}>
                    {getStatusText(verification.status)}
                  </span>
                </div>
                
                {verification.aiScore && (
                  <div className="text-xs text-gray-500">
                    AI Score: {verification.aiScore.toFixed(1)}%
                  </div>
                )}
                
                {verification.aiAnalysis && (
                  <div className="text-xs space-y-1">
                    <div className="text-gray-500">
                      Confidence: {verification.aiAnalysis.confidence.toFixed(1)}%
                    </div>
                    {verification.aiAnalysis.flags.length > 0 && (
                      <div className="text-orange-600">
                        Issues: {verification.aiAnalysis.flags.length}
                      </div>
                    )}
                  </div>
                )}
                
                {verification.crossValidation && !verification.crossValidation.consistent && (
                  <div className="text-xs text-red-600 font-medium">
                    ⚠️ Cross-validation issues
                  </div>
                )}
                
                {verification.reviewNotes && (
                  <div className="text-xs text-gray-600 max-w-xs text-right">
                    {verification.reviewNotes}
                  </div>
                )}
                
                {verification.humanReview && verification.status !== 'verified' && (
                  <div className="text-xs text-orange-600 font-medium flex items-center space-x-1">
                    <Eye className="h-3 w-3" />
                    <span>Human Review</span>
                  </div>
                )}
              </div>
            </div>
            
            {/* AI Analysis Details */}
            {verification.aiAnalysis && (
              <AIAnalysisDetails 
                analysis={verification.aiAnalysis}
                crossValidation={verification.crossValidation}
              />
            )}
          </div>
        ))}
      </div>

      {completedVerifications === totalVerifications ? (
        <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div>
              <h3 className="text-lg font-semibold text-green-800">Verification Complete!</h3>
              <p className="text-green-700">You can now make withdrawals and access all platform features.</p>
              <button
                onClick={() => router.push('/withdraw')}
                className="mt-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                Go to Withdrawals
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <Shield className="h-8 w-8 text-yellow-600" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">Verification Required</h3>
              <p className="text-yellow-700">Complete all verification steps above to enable withdrawals.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}