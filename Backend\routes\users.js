const express = require('express');
const router = express.Router();
const { User, Follow, Article, Comment, Activity } = require('../models');
const { authenticateToken, authenticateOptional } = require('../middleware/auth');
const { Op } = require('sequelize');
const bcrypt = require('bcryptjs');
const { logger } = require('../config/logger');
const { body } = require('express-validator');

// Validation rules
const registerValidation = [
  body('username')
    .isLength({ min: 3, max: 30 })
    .withMessage('Username must be between 3 and 30 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Username can only contain letters, numbers, underscores, and hyphens'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),
];

const loginValidation = [
  body('identifier').notEmpty().withMessage('Email or username is required'),
  body('password').notEmpty().withMessage('Password is required'),
];

const passwordResetRequestValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email address'),
];

const passwordResetValidation = [
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      'Password must contain at least one lowercase letter, one uppercase letter, and one number'
    ),
];

// 注意：注册路由已移至 /api/auth/register

// 注意：登录路由已移至 /api/auth/login

// Verify email
router.get('/verify-email/:token', verifyEmailToken, async (req, res) => {
  try {
    const user = req.user;

    // Update user verification status
    await user.update({
      isEmailVerified: true,
      emailVerificationToken: null,
      emailVerificationExpires: null,
    });

    // Send welcome email
    try {
      await sendWelcomeEmail(user);
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError);
    }

    res.json({
      success: true,
      message: 'Email verified successfully! Welcome to Newzora.',
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Email verification failed. Please try again.',
    });
  }
});

// Resend email verification
router.post('/resend-verification', emailVerificationLimiter, async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Email is required',
      });
    }

    const user = await User.findOne({ where: { email: email.toLowerCase() } });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    if (user.isEmailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified',
      });
    }

    // Generate new verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email
    await sendEmailVerification(user, verificationToken);

    res.json({
      success: true,
      message: 'Verification email sent successfully',
    });
  } catch (error) {
    console.error('Resend verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to resend verification email',
    });
  }
});

// Request password reset
router.post(
  '/forgot-password',
  passwordResetLimiter,
  passwordResetRequestValidation,
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { email } = req.body;
      const user = await User.findOne({ where: { email: email.toLowerCase() } });

      // Always return success to prevent email enumeration
      const successResponse = {
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      };

      if (!user) {
        return res.json(successResponse);
      }

      // Generate password reset token
      const resetToken = user.generatePasswordResetToken();
      await user.save();

      // Send password reset email
      try {
        await sendPasswordReset(user, resetToken);
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError);
      }

      res.json(successResponse);
    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({
        success: false,
        message: 'Password reset request failed',
      });
    }
  }
);

// Verify reset token
router.post('/verify-reset-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Reset token is required',
      });
    }

    const user = await User.findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          [Op.gt]: new Date(),
        },
      },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
      });
    }

    res.json({
      success: true,
      message: 'Reset token is valid',
    });
  } catch (error) {
    console.error('Verify reset token error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
    });
  }
});

// Reset password
router.post('/reset-password', passwordResetValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array(),
      });
    }

    const { token, password } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Reset token is required',
      });
    }

    // Find user with valid reset token
    const user = await User.findOne({
      where: {
        passwordResetToken: token,
        passwordResetExpires: {
          [Op.gt]: new Date(),
        },
      },
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
      });
    }

    // Update password and clear reset token
    await user.update({
      password,
      passwordResetToken: null,
      passwordResetExpires: null,
      loginAttempts: 0,
      lockUntil: null,
    });

    res.json({
      success: true,
      message: 'Password reset successfully',
    });
  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      success: false,
      message: 'Password reset failed',
    });
  }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = req.user;
    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        preferences: user.preferences,
        createdAt: user.createdAt,
        lastLogin: user.lastLogin,
      },
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user profile',
    });
  }
});

// Update user profile
router.put(
  '/profile',
  authenticateToken,
  [
    body('username')
      .optional()
      .isLength({ min: 3, max: 30 })
      .withMessage('Username must be between 3 and 30 characters')
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('Username can only contain letters, numbers, underscores, and hyphens'),
    body('avatar').optional().isURL().withMessage('Avatar must be a valid URL'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const user = req.user;
      const { username, avatar } = req.body;
      const updates = {};

      if (username && username !== user.username) {
        // Check if username is already taken
        const existingUser = await User.findOne({ where: { username } });
        if (existingUser && existingUser.id !== user.id) {
          return res.status(400).json({
            success: false,
            message: 'Username already taken',
          });
        }
        updates.username = username;
      }

      if (avatar !== undefined) {
        updates.avatar = avatar;
      }

      if (Object.keys(updates).length > 0) {
        await user.update(updates);
      }

      res.json({
        success: true,
        message: 'Profile updated successfully',
        user: {
          id: user.id,
          username: user.username,
          email: user.email,
          avatar: user.avatar,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
        },
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update profile',
      });
    }
  }
);

// Optional authentication middleware
const optionalAuth = (req, res, next) => {
  const token = req.header('Authorization')?.replace('Bearer ', '');
  if (token) {
    try {
      const jwt = require('jsonwebtoken');
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      req.user = decoded;
    } catch (error) {
      // Token is invalid, but we continue without authentication
    }
  }
  next();
};

// Get user by ID
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findByPk(id, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Get follow counts
    const Follow = require('../models/Follow');
    const [followersCount, followingCount] = await Promise.all([
      Follow.count({ where: { followingId: user.id, status: 'active' } }),
      Follow.count({ where: { followerId: user.id, status: 'active' } }),
    ]);

    // Check if current user is following this user (if authenticated)
    let isFollowing = false;
    if (req.user && req.user.id !== user.id) {
      const followRecord = await Follow.findOne({
        where: {
          followerId: req.user.id,
          followingId: user.id,
          status: 'active',
        },
      });
      isFollowing = !!followRecord;
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        name: user.username, // Use username as name for now
        avatar: user.avatar,
        bio: user.bio || '',
        followers: followersCount,
        following: followingCount,
        works: 0, // TODO: Get actual works count
        joinedAt: user.createdAt,
        isFollowing,
        isVerified: user.isEmailVerified,
      },
    });
  } catch (error) {
    console.error('Get user by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user information',
    });
  }
});

// Get user by username
router.get('/profile/:username', optionalAuth, async (req, res) => {
  try {
    const { username } = req.params;
    const user = await User.findOne({
      where: { username },
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found',
      });
    }

    // Get follow counts
    const Follow = require('../models/Follow');
    const [followersCount, followingCount] = await Promise.all([
      Follow.count({ where: { followingId: user.id, status: 'active' } }),
      Follow.count({ where: { followerId: user.id, status: 'active' } }),
    ]);

    // Check if current user is following this user (if authenticated)
    let isFollowing = false;
    if (req.user && req.user.id !== user.id) {
      const followRecord = await Follow.findOne({
        where: {
          followerId: req.user.id,
          followingId: user.id,
          status: 'active',
        },
      });
      isFollowing = !!followRecord;
    }

    res.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        name: user.username, // Use username as name for now
        avatar: user.avatar,
        bio: user.bio || '',
        followers: followersCount,
        following: followingCount,
        works: 0, // TODO: Get actual works count
        joinedAt: user.createdAt,
        isFollowing,
        isVerified: user.isEmailVerified,
      },
    });
  } catch (error) {
    console.error('Get user by username error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user information',
    });
  }
});

// Admin: Get all users
router.get('/admin/users', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, search, role } = req.query;
    const offset = (page - 1) * limit;

    const whereConditions = {};

    if (search) {
      whereConditions[Op.or] = [
        { username: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
      ];
    }

    if (role) {
      whereConditions.role = role;
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereConditions,
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['createdAt', 'DESC']],
    });

    res.json({
      success: true,
      users,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      total: count,
    });
  } catch (error) {
    console.error('Admin get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get users',
    });
  }
});

// Admin: Update user role
router.put(
  '/admin/users/:id/role',
  authenticateToken,
  requireAdmin,
  [
    body('role')
      .isIn(['user', 'admin', 'moderator'])
      .withMessage('Role must be user, admin, or moderator'),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { role } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      await user.update({ role });

      res.json({
        success: true,
        message: 'User role updated successfully',
      });
    } catch (error) {
      console.error('Admin update user role error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update user role',
      });
    }
  }
);

// Admin: Deactivate/activate user
router.put(
  '/admin/users/:id/status',
  authenticateToken,
  requireAdmin,
  [body('isActive').isBoolean().withMessage('isActive must be a boolean')],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { isActive } = req.body;

      const user = await User.findByPk(id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      await user.update({ isActive });

      res.json({
        success: true,
        message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error) {
      console.error('Admin update user status error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update user status',
      });
    }
  }
);

// 注意：社交登录路由已移至 /api/auth 路由

// Link social account to existing account
router.post('/link-social', authenticateToken, async (req, res) => {
  try {
    const { provider, socialId } = req.body;
    const user = req.user;

    if (!['google', 'facebook'].includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid provider',
      });
    }

    const field = provider === 'google' ? 'googleId' : 'facebookId';

    // Check if social account is already linked to another user
    const existingUser = await User.findOne({ where: { [field]: socialId } });
    if (existingUser && existingUser.id !== user.id) {
      return res.status(400).json({
        success: false,
        message: `This ${provider} account is already linked to another user`,
      });
    }

    // Link social account
    await user.update({ [field]: socialId });

    res.json({
      success: true,
      message: `${provider} account linked successfully`,
    });
  } catch (error) {
    console.error('Link social account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to link social account',
    });
  }
});

// Unlink social account
router.delete('/unlink-social/:provider', authenticateToken, async (req, res) => {
  try {
    const { provider } = req.params;
    const user = req.user;

    if (!['google', 'facebook'].includes(provider)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid provider',
      });
    }

    // Check if user has password or other social login
    const hasPassword = !!user.password;
    const hasOtherSocial = provider === 'google' ? !!user.facebookId : !!user.googleId;

    if (!hasPassword && !hasOtherSocial) {
      return res.status(400).json({
        success: false,
        message: 'Cannot unlink the only login method. Please set a password first.',
      });
    }

    const field = provider === 'google' ? 'googleId' : 'facebookId';
    await user.update({ [field]: null });

    res.json({
      success: true,
      message: `${provider} account unlinked successfully`,
    });
  } catch (error) {
    console.error('Unlink social account error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unlink social account',
    });
  }
});

module.exports = router;
