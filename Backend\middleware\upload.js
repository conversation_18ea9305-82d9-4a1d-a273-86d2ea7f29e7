const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegStatic = require('ffmpeg-static');
const mime = require('mime-types');

// 设置 FFmpeg 路径
ffmpeg.setFfmpegPath(ffmpegStatic);

// 确保上传目录存在
const ensureUploadDir = async (dir) => {
  try {
    await fs.access(dir);
  } catch (error) {
    await fs.mkdir(dir, { recursive: true });
  }
};

// 文件类型检查
const fileFilter = (req, file, cb) => {
  const allowedTypes = {
    image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    video: ['video/mp4', 'video/avi', 'video/mov', 'video/wmv', 'video/webm'],
    audio: ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac'],
    document: [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
  };

  const allAllowedTypes = Object.values(allowedTypes).flat();

  if (allAllowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error(`File type ${file.mimetype} is not allowed`), false);
  }
};

// 文件大小限制
const getFileSizeLimit = (mimetype) => {
  if (mimetype.startsWith('image/')) {
    return 10 * 1024 * 1024; // 10MB for images
  } else if (mimetype.startsWith('video/')) {
    return 100 * 1024 * 1024; // 100MB for videos
  } else if (mimetype.startsWith('audio/')) {
    return 50 * 1024 * 1024; // 50MB for audio
  } else {
    return 20 * 1024 * 1024; // 20MB for documents
  }
};

// 存储配置
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../uploads');
    const typeDir = path.join(uploadDir, file.mimetype.split('/')[0]);

    try {
      await ensureUploadDir(typeDir);
      cb(null, typeDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const ext = path.extname(file.originalname);
    cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`);
  },
});

// Multer 配置
const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB 最大限制
  },
});

// 图片处理函数
const processImage = async (filePath, options = {}) => {
  const { width = 1920, height = 1080, quality = 80, format = 'jpeg' } = options;

  const processedPath = filePath.replace(/\.[^/.]+$/, `_processed.${format}`);

  await sharp(filePath)
    .resize(width, height, {
      fit: 'inside',
      withoutEnlargement: true,
    })
    .jpeg({ quality })
    .toFile(processedPath);

  // 生成缩略图
  const thumbnailPath = filePath.replace(/\.[^/.]+$/, `_thumb.${format}`);
  await sharp(filePath)
    .resize(300, 300, {
      fit: 'cover',
      position: 'center',
    })
    .jpeg({ quality: 70 })
    .toFile(thumbnailPath);

  return {
    processedPath,
    thumbnailPath,
  };
};

// 视频处理函数
const processVideo = async (filePath) => {
  return new Promise((resolve, reject) => {
    const thumbnailPath = filePath.replace(/\.[^/.]+$/, '_thumb.jpg');
    const processedPath = filePath.replace(/\.[^/.]+$/, '_processed.mp4');

    // 生成缩略图
    ffmpeg(filePath)
      .screenshots({
        timestamps: ['00:00:01'],
        filename: path.basename(thumbnailPath),
        folder: path.dirname(thumbnailPath),
        size: '300x300',
      })
      .on('end', () => {
        // 获取视频信息
        ffmpeg.ffprobe(filePath, (err, metadata) => {
          if (err) {
            reject(err);
            return;
          }

          const duration = metadata.format.duration;
          const dimensions = {
            width: metadata.streams[0].width,
            height: metadata.streams[0].height,
          };

          // 检查视频时长（限制10分钟）
          if (duration > 600) {
            reject(new Error('Video duration exceeds 10 minutes limit'));
            return;
          }

          // 压缩视频（如果需要）
          if (metadata.format.size > 50 * 1024 * 1024) {
            // 50MB
            ffmpeg(filePath)
              .videoCodec('libx264')
              .audioCodec('aac')
              .size('1280x720')
              .videoBitrate('1000k')
              .audioBitrate('128k')
              .output(processedPath)
              .on('end', () => {
                resolve({
                  thumbnailPath,
                  processedPath,
                  duration,
                  dimensions,
                });
              })
              .on('error', reject)
              .run();
          } else {
            resolve({
              thumbnailPath,
              processedPath: filePath,
              duration,
              dimensions,
            });
          }
        });
      })
      .on('error', reject);
  });
};

// 音频处理函数
const processAudio = async (filePath) => {
  return new Promise((resolve, reject) => {
    ffmpeg.ffprobe(filePath, (err, metadata) => {
      if (err) {
        reject(err);
        return;
      }

      const duration = metadata.format.duration;

      // 检查音频时长（限制10分钟）
      if (duration > 600) {
        reject(new Error('Audio duration exceeds 10 minutes limit'));
        return;
      }

      const processedPath = filePath.replace(/\.[^/.]+$/, '_processed.mp3');

      // 转换为 MP3 格式并压缩
      ffmpeg(filePath)
        .audioCodec('mp3')
        .audioBitrate('128k')
        .output(processedPath)
        .on('end', () => {
          resolve({
            processedPath,
            duration,
          });
        })
        .on('error', reject)
        .run();
    });
  });
};

// 获取文件类型
const getFileType = (mimetype) => {
  if (mimetype.startsWith('image/')) return 'image';
  if (mimetype.startsWith('video/')) return 'video';
  if (mimetype.startsWith('audio/')) return 'audio';
  return 'document';
};

// 文件处理中间件
const processUploadedFile = async (req, res, next) => {
  if (!req.file) {
    return next();
  }

  try {
    const { file } = req;
    const fileType = getFileType(file.mimetype);

    let processResult = {};

    switch (fileType) {
      case 'image':
        processResult = await processImage(file.path);
        break;
      case 'video':
        processResult = await processVideo(file.path);
        break;
      case 'audio':
        processResult = await processAudio(file.path);
        break;
      default:
        // 文档文件不需要特殊处理
        break;
    }

    // 将处理结果添加到请求对象
    req.fileProcessResult = processResult;
    req.fileType = fileType;

    next();
  } catch (error) {
    console.error('File processing error:', error);
    next(error);
  }
};

module.exports = {
  upload,
  processUploadedFile,
  getFileType,
  ensureUploadDir,
};
