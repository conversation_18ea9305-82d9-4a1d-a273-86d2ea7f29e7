'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import ThumbnailSelector from '@/components/ThumbnailSelector';
import AuthGuard from '@/components/AuthGuard';
import RichTextEditor from '@/components/RichTextEditor';
import MediaUploader from '@/components/MediaUploader';

function CreateContent() {
  const router = useRouter();
  const [contentType, setContentType] = useState<'article' | 'video' | 'audio'>('article');
  const [editId, setEditId] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // 检查是否为编辑模式
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const editParam = urlParams.get('edit');
    const typeParam = urlParams.get('type') as 'article' | 'video' | 'audio';
    
    if (editParam) {
      setEditId(editParam);
      setIsEditMode(true);
      if (typeParam) {
        setContentType(typeParam);
      }
      loadEditData(editParam);
    }
  }, []);

  const loadEditData = (id: string) => {
    const mockDataMap: { [key: string]: any } = {
      '1': {
        title: 'The Future of AI in Everyday Life',
        description: 'Exploring how AI will transform our daily routines and interactions',
        content: '<h2>Introduction</h2><p>Artificial Intelligence is rapidly becoming an integral part of our daily lives...</p>',
        category: 'technology',
        tags: 'AI, artificial intelligence, technology, future'
      },
      '2': {
        title: 'Cybersecurity Best Practices',
        description: 'A comprehensive guide to staying safe online in 2024',
        content: '<h2>Introduction</h2><p>In today\'s digital age, cybersecurity has become more important than ever...</p>',
        category: 'technology',
        tags: 'cybersecurity, security, privacy, technology'
      },
      '3': {
        title: 'Travel Guide to Southeast Asia',
        description: 'Complete travel guide with tips and recommendations',
        content: 'Video content about Southeast Asia travel...',
        category: 'travel',
        tags: 'travel, asia, guide, tourism'
      },
      '4': {
        title: 'Meditation Music Collection',
        description: 'Relaxing sounds for meditation and focus',
        content: 'Audio content for meditation...',
        category: 'lifestyle',
        tags: 'meditation, music, relaxation, wellness'
      }
    };

    const data = mockDataMap[id];
    if (data) {
      setFormData(prev => ({ ...prev, ...data }));
    }
  };
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    content: '',
    category: '',
    tags: '',
    mediaFile: null as File | null,
    thumbnail: null as File | null,
    videoQuality: '1080p' as '480p' | '720p' | '1080p' | '1440p' | '2160p' | '4320p',
    audioBitrate: '320' as '128' | '192' | '256' | '320',
    autoThumbnail: '', // Auto-generated thumbnail
  });
  const [videoInfo, setVideoInfo] = useState<{
    duration: number;
    width: number;
    height: number;
    size: number;
  } | null>(null);
  const [showThumbnailSelector, setShowThumbnailSelector] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [aiReviewResult, setAiReviewResult] = useState<{
    passed: boolean;
    issues: string[];
    suggestions: string[];
  } | null>(null);
  const [showAiReview, setShowAiReview] = useState(false);

  // 使用与主页左侧栏相同的类别
  const categories = [
    { value: 'technology', label: 'Technology' },
    { value: 'finance', label: 'Finance' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'education', label: 'Education' },
    { value: 'health', label: 'Health & Fitness' },
    { value: 'travel', label: 'Travel' },
    { value: 'food', label: 'Food' },
    { value: 'sports', label: 'Sports' },
    { value: 'business', label: 'Business' },
    { value: 'science', label: 'Science' },
    { value: 'politics', label: 'Politics' },
    { value: 'history', label: 'History' },
    { value: 'news', label: 'News' },
  ];

  // Trending topics data - using actual mockWorks IDs and titles
  const trendingTopics = [
    { id: 1, title: 'The Future of Artificial Intelligence in 2024', category: 'Technology', views: '15.4K', trend: '+15%', type: 'article' },
    { id: 2, title: 'Sustainable Travel: A Guide to Eco-Friendly Adventures', category: 'Travel', views: '8.8K', trend: '+22%', type: 'article' },
    { id: 11, title: 'Quantum Computing: The Next Frontier', category: 'Science', views: '12.3K', trend: '+8%', type: 'article' },
    { id: 14, title: 'Startup Success: Lessons from Silicon Valley', category: 'Business', views: '16.7K', trend: '+12%', type: 'article' },
    { id: 21, title: 'Stock Market Analysis: Top Picks for 2024', category: 'Finance', views: '28.9K', trend: '+18%', type: 'article' },
  ];

  // Handle trending topic click - navigate to existing articles
  const handleTrendingTopicClick = (topic: typeof trendingTopics[0]) => {
    // Navigate to existing articles in mockWorks
    router.push(`/article/${topic.id}`);
  };

  // Quick Tips Component with collapsible content
  const QuickTipsCard = () => {
    const [expandedTip, setExpandedTip] = useState<number | null>(null);
    
    const tips = [
      {
        title: 'Use engaging titles to attract more readers',
        content: 'Create compelling headlines that clearly describe your content. Use action words, numbers, and emotional triggers. Keep titles under 60 characters for better SEO.'
      },
      {
        title: 'Add relevant tags to improve discoverability',
        content: 'Use 3-5 specific tags that accurately describe your content. Mix popular and niche tags. Research trending hashtags in your category for better reach.'
      },
      {
        title: 'Proofread your content before publishing',
        content: 'Check for grammar, spelling, and formatting errors. Read your content aloud to catch awkward phrasing. Use tools like Grammarly for additional help.'
      },
      {
        title: 'Use high-quality images and media',
        content: 'Choose clear, relevant images that support your content. Optimize file sizes for faster loading. Ensure you have proper rights to use all media.'
      }
    ];

    return (
      <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">💡 Quick Tips</h2>
        <div className="space-y-3">
          {tips.map((tip, index) => (
            <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => setExpandedTip(expandedTip === index ? null : index)}
                className="w-full flex items-start justify-between p-3 text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start">
                  <span className="text-blue-500 mr-2 mt-1">•</span>
                  <span className="text-gray-700 text-sm font-medium">{tip.title}</span>
                </div>
                <svg 
                  className={`w-4 h-4 text-gray-400 transition-transform ${
                    expandedTip === index ? 'rotate-180' : ''
                  }`} 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {expandedTip === index && (
                <div className="px-3 pb-3 pl-8">
                  <p className="text-sm text-gray-600 leading-relaxed">{tip.content}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // AI Content Review System
  const runAiContentReview = async (content: string, title: string) => {
    const sensitiveKeywords = [
      'political sensitive',
      'violent content',
      'false information',
      'hate speech',
      'adult content',
      'fraud',
      'drugs',
      'weapons',
      'terrorism',
      'racial discrimination',
    ];

    const issues: string[] = [];
    const suggestions: string[] = [];

    // Simulate AI detection
    const fullText = `${title} ${content}`.toLowerCase();

    if (fullText.includes('political') || fullText.includes('government')) {
      issues.push('Political sensitive content detected');
      suggestions.push('Please avoid political topics, focus on technology or lifestyle content');
    }

    if (fullText.includes('violent') || fullText.includes('blood')) {
      issues.push('Violent content detected');
      suggestions.push('Please use more moderate expressions');
    }

    if (fullText.includes('fake') || fullText.includes('false')) {
      issues.push('Potentially false information detected');
      suggestions.push('Please ensure content is truthful and reliable, provide credible sources');
    }

    // Content quality check
    if (content.length < 100) {
      suggestions.push('Suggest increasing content length for more detailed information');
    }

    if (!title.trim()) {
      issues.push('Title cannot be empty');
    }

    return {
      passed: issues.length === 0,
      issues,
      suggestions,
    };
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleAiReview = async () => {
    setShowAiReview(true);
    const result = await runAiContentReview(formData.content, formData.title);
    setAiReviewResult(result);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 先进行AI审核
      const reviewResult = await runAiContentReview(formData.content, formData.title);

      if (!reviewResult.passed) {
        setAiReviewResult(reviewResult);
        setShowAiReview(true);
        setIsSubmitting(false);
        return;
      }

      // 模拟提交
      await new Promise((resolve) => setTimeout(resolve, 2000));
      console.log('Content submitted:', { ...formData, contentType });
      
      if (isEditMode) {
        alert('Content updated successfully!');
        router.push('/content');
      } else {
        alert('Content published successfully!');
        router.push('/');
      }
    } catch (error) {
      console.error('Error submitting content:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileUpload = (field: 'mediaFile' | 'thumbnail', file: File) => {
    setFormData((prev) => ({ ...prev, [field]: file }));

    // 如果是视频文件，显示缩略图选择器
    if (field === 'mediaFile' && contentType === 'video') {
      setShowThumbnailSelector(true);
    }
  };

  const handleThumbnailSelect = (thumbnail: string) => {
    setFormData((prev) => ({ ...prev, autoThumbnail: thumbnail }));
  };

  const handleVideoInfo = (info: {
    duration: number;
    width: number;
    height: number;
    size: number;
  }) => {
    setVideoInfo(info);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex max-w-7xl mx-auto px-6 py-8 gap-8">
        {/* Main Content */}
        <main className="flex-1">
          <div className="mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-600 bg-clip-text text-transparent mb-4">
              {isEditMode ? 'Edit Content' : 'Create New Content'}
            </h1>
            <p className="text-lg text-gray-600">
              {isEditMode ? 'Update your content and republish when ready.' : 'Share your thoughts, insights, and stories with the world and inspire others.'}
            </p>
          </div>

          {/* Content Type Selection */}
          <div className="mb-8">
            <div className="flex space-x-2 bg-white p-2 rounded-xl border border-gray-200 shadow-sm w-fit">
              <button
                onClick={() => setContentType('article')}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer ${
                  contentType === 'article'
                    ? 'bg-blue-600 text-white'
                    : 'border-2 border-gray-200 text-gray-600 hover:bg-gray-100'
                }`}
              >
                📝 Article
              </button>
              <button
                onClick={() => setContentType('video')}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer ${
                  contentType === 'video'
                    ? 'bg-red-600 text-white'
                    : 'border-2 border-gray-200 text-gray-600 hover:bg-gray-100'
                }`}
              >
                🎥 Video
              </button>
              <button
                onClick={() => setContentType('audio')}
                className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer ${
                  contentType === 'audio'
                    ? 'bg-green-600 text-white'
                    : 'border-2 border-gray-200 text-gray-600 hover:bg-gray-100'
                }`}
              >
                🎵 Audio
              </button>
            </div>
          </div>

          {/* Content Creation Form */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
            <form onSubmit={handleSubmit}>
              <div className="p-8">
                {/* Title */}
                <div className="mb-6">
                  <label htmlFor="title" className="block text-lg font-semibold text-gray-900 mb-2">
                    Title <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                    placeholder="Enter a compelling title for your content"
                    required
                  />
                </div>

                {/* Description */}
                <div className="mb-6">
                  <label htmlFor="description" className="block text-lg font-semibold text-gray-900 mb-2">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                    placeholder="Briefly describe what your content is about"
                  />
                </div>

                {/* Category */}
                <div className="mb-6">
                  <label htmlFor="category" className="block text-lg font-semibold text-gray-900 mb-2">
                    Category <span className="text-red-500">*</span>
                  </label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                    required
                  >
                    <option value="">Select a category</option>
                    {categories.map((category) => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Tags */}
                <div className="mb-6">
                  <label htmlFor="tags" className="block text-lg font-semibold text-gray-900 mb-2">
                    Tags
                  </label>
                  <input
                    type="text"
                    id="tags"
                    value={formData.tags}
                    onChange={(e) => handleInputChange('tags', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                    placeholder="Enter tags separated by commas (e.g., technology, AI, future)"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Tags help users discover your content. Separate multiple tags with commas.
                  </p>
                </div>

                {/* Content based on type */}
                {contentType === 'article' && (
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <label htmlFor="content" className="block text-lg font-semibold text-gray-900">
                        Content <span className="text-red-500">*</span>
                      </label>
                      <div className="text-sm text-gray-500">
                        Enhanced Editor
                      </div>
                    </div>
                    <RichTextEditor
                      value={formData.content}
                      onChange={(content) => handleInputChange('content', content)}
                      placeholder="Write your article content here..."
                      className="border border-gray-300 rounded-xl"
                    />
                  </div>
                )}

                {(contentType === 'video' || contentType === 'audio') && (
                  <div className="mb-6">
                    <label className="block text-lg font-semibold text-gray-900 mb-2">
                      Upload {contentType === 'video' ? 'Video' : 'Audio'} <span className="text-red-500">*</span>
                    </label>
                    <MediaUploader
                      type={contentType}
                      onFileSelect={(file) => handleFileUpload('mediaFile', file)}
                      onVideoInfo={contentType === 'video' ? handleVideoInfo : undefined}
                    />
                    
                    {videoInfo && (
                      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-900 mb-2">Media Information</h4>
                        <div className="grid grid-cols-2 gap-2 text-sm text-blue-800">
                          <div>Duration: {Math.floor(videoInfo.duration / 60)}:{String(Math.floor(videoInfo.duration % 60)).padStart(2, '0')}</div>
                          <div>Resolution: {videoInfo.width}×{videoInfo.height}</div>
                          <div>Size: {(videoInfo.size / (1024 * 1024)).toFixed(2)} MB</div>
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {/* Thumbnail for video/audio */}
                {(contentType === 'video' || contentType === 'audio') && (
                  <div className="mb-6">
                    <label className="block text-base font-medium text-gray-900 mb-3">
                      Thumbnail
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="border border-gray-200 rounded-lg p-4 text-center hover:border-gray-300 hover:bg-gray-50 transition-colors cursor-pointer">
                        <div className="flex flex-col items-center justify-center">
                          <svg className="w-6 h-6 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <p className="text-gray-600 text-sm mb-2">Upload custom thumbnail</p>
                          <label className="px-3 py-1 border border-gray-300 text-gray-700 rounded-md cursor-pointer text-sm hover:border-gray-400 hover:text-gray-900 transition-all transform hover:scale-105">
                            Choose Image
                            <input
                              type="file"
                              className="hidden"
                              accept="image/*"
                              onChange={(e) => {
                                if (e.target.files && e.target.files[0]) {
                                  handleFileUpload('thumbnail', e.target.files[0]);
                                }
                              }}
                            />
                          </label>
                        </div>
                      </div>
                      
                      <button
                        type="button"
                        onClick={() => setShowThumbnailSelector(true)}
                        className="border border-gray-200 rounded-lg p-4 text-center hover:border-gray-300 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex flex-col items-center justify-center">
                          <svg className="w-6 h-6 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                          </svg>
                          <p className="text-gray-600 text-sm">Auto-generate thumbnail</p>
                        </div>
                      </button>
                    </div>
                  </div>
                )}

                {/* Video Quality Settings */}
                {contentType === 'video' && (
                  <div className="mb-6">
                    <label htmlFor="videoQuality" className="block text-base font-medium text-gray-900 mb-3">
                      Video Quality
                    </label>
                    <select
                      id="videoQuality"
                      value={formData.videoQuality}
                      onChange={(e) => setFormData(prev => ({ ...prev, videoQuality: e.target.value as typeof formData.videoQuality }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                    >
                      <option value="480p">480p</option>
                      <option value="720p">720p HD</option>
                      <option value="1080p">1080p Full HD</option>
                      <option value="1440p">1440p 2K</option>
                      <option value="2160p">2160p 4K</option>
                      <option value="4320p">4320p 8K</option>
                    </select>
                  </div>
                )}

                {/* Audio Quality Settings */}
                {contentType === 'audio' && (
                  <div className="mb-6">
                    <label htmlFor="audioBitrate" className="block text-base font-medium text-gray-900 mb-3">
                      Audio Bitrate
                    </label>
                    <select
                      id="audioBitrate"
                      value={formData.audioBitrate}
                      onChange={(e) => setFormData(prev => ({ ...prev, audioBitrate: e.target.value as typeof formData.audioBitrate }))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                    >
                      <option value="128">128 kbps</option>
                      <option value="192">192 kbps</option>
                      <option value="256">256 kbps</option>
                      <option value="320">320 kbps</option>
                    </select>
                  </div>
                )}

                {/* AI Review Button */}
                <div className="mb-6">
                  <button
                    type="button"
                    onClick={handleAiReview}
                    className="w-full py-3 bg-blue-600 text-white rounded-xl font-medium hover:bg-blue-700 transition-all cursor-pointer"
                  >
                    🔍 Run AI Content Review
                  </button>
                </div>

                {/* AI Review Results */}
                {showAiReview && aiReviewResult && (
                  <div className={`mb-6 p-4 rounded-xl border ${
                    aiReviewResult.passed 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-red-50 border-red-200'
                  }`}>
                    <h3 className={`font-semibold text-lg mb-2 ${
                      aiReviewResult.passed ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {aiReviewResult.passed ? '✅ Content Approved' : '❌ Content Issues Detected'}
                    </h3>
                    
                    {aiReviewResult.issues.length > 0 && (
                      <div className="mb-3">
                        <h4 className="font-medium text-red-700 mb-1">Issues Found:</h4>
                        <ul className="list-disc list-inside text-red-600 space-y-1">
                          {aiReviewResult.issues.map((issue, index) => (
                            <li key={index}>{issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    
                    {aiReviewResult.suggestions.length > 0 && (
                      <div>
                        <h4 className="font-medium text-purple-700 mb-1">Suggestions:</h4>
                        <ul className="list-disc list-inside text-purple-600 space-y-1">
                          {aiReviewResult.suggestions.map((suggestion, index) => (
                            <li key={index}>{suggestion}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}

                {/* Submit Button */}
                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className={`px-8 py-3 bg-blue-600 text-white rounded-xl font-medium transition-all hover:bg-blue-700 ${
                      isSubmitting ? 'opacity-70 cursor-not-allowed' : 'cursor-pointer'
                    }`}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Publishing...
                      </span>
                    ) : (
                      isEditMode ? '💾 Update Content' : '🚀 Publish Content'
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </main>

        {/* Sidebar */}
        <aside className="w-80 space-y-6">
          {/* Quick Tips */}
          <QuickTipsCard />

          {/* Trending Topics */}
          <div className="bg-white rounded-2xl border border-gray-200 shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">🔥 Trending Topics</h2>
            <div className="space-y-4">
              {trendingTopics.map((topic) => (
                <div 
                  key={topic.id}
                  onClick={() => handleTrendingTopicClick(topic)}
                  className="p-3 border border-gray-200 rounded-xl hover:bg-gray-100 cursor-pointer transition-colors group"
                >
                  <div className="flex justify-between items-start">
                    <h3 className="font-medium text-gray-900 group-hover:text-blue-600">{topic.title}</h3>
                    <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded-full">
                      {topic.type}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm text-gray-500">{topic.category}</span>
                    <div className="flex items-center text-sm">
                      <span className="text-gray-500 mr-2">{topic.views} views</span>
                      <span className="text-green-600 font-medium">{topic.trend}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </aside>
      </div>

      {/* Thumbnail Selector Modal */}
      {showThumbnailSelector && (
        <ThumbnailSelector 
          onClose={() => setShowThumbnailSelector(false)}
          onSelect={handleThumbnailSelect}
          videoFile={formData.mediaFile}
        />
      )}
    </div>
  );
}

export default function CreatePage() {
  return (
    <AuthGuard 
      requireAuth={true} 
      redirectTo="/auth/login"
    >
      <CreateContent />
    </AuthGuard>
  );
}