import { Article } from '@/types';

export const mockArticles: Article[] = [
  // Technology Articles
  {
    id: 1,
    type: 'article' as const,
    title: 'The Future of AI in Everyday Life',
    description:
      'Explore how artificial intelligence is transforming our daily routines, from smart homes to personalized healthcare.',
    content:
      'Artificial intelligence is no longer a concept confined to science fiction. Today, AI is seamlessly integrated into our daily lives, revolutionizing how we interact with technology and each other.',
    category: 'technology',
    image:
      'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=400&fit=crop&crop=center',
    author: 'Tech Insider',
    publishedAt: '2024-01-15T10:00:00Z',
    tags: ['AI', 'Technology', 'Future'],
    readTime: 5,
    likes: 42,
    views: 1250,
    published: true,
    createdAt: '2024-01-15T09:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: 2,
    type: 'article' as const,
    title: 'Cybersecurity in the Digital Age',
    description:
      'Stay informed about the latest cybersecurity threats and learn how to protect your personal data online.',
    content:
      'As our lives become increasingly digital, cybersecurity has never been more critical. From protecting personal information to securing business data, understanding cybersecurity threats and best practices is essential for everyone.',
    category: 'technology',
    image:
      'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=600&h=400&fit=crop&crop=center',
    author: 'Security Expert',
    publishedAt: '2024-01-11T10:00:00Z',
    tags: ['Cybersecurity', 'Privacy', 'Digital'],
    readTime: 6,
    likes: 43,
    views: 1120,
    published: true,
    createdAt: '2024-01-11T09:00:00Z',
    updatedAt: '2024-01-11T10:00:00Z',
  },
  // Finance Articles
  {
    id: 3,
    type: 'article' as const,
    title: 'Cryptocurrency Market Trends 2024',
    description:
      'Analyze the latest trends in cryptocurrency markets and understand the factors driving digital asset prices.',
    content:
      'The cryptocurrency market continues to evolve rapidly, with new trends and developments shaping the digital asset landscape. From institutional adoption to regulatory changes, understanding these market dynamics is crucial for investors.',
    category: 'finance',
    image:
      'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=600&h=400&fit=crop&crop=center',
    author: 'Crypto Analyst',
    publishedAt: '2024-01-10T10:00:00Z',
    tags: ['Cryptocurrency', 'Investment', 'Market'],
    readTime: 7,
    likes: 89,
    views: 2340,
    published: true,
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T10:00:00Z',
  },
  {
    id: 4,
    type: 'article' as const,
    title: 'Personal Finance Tips for Young Adults',
    description:
      'Essential financial advice for young adults starting their career and building wealth for the future.',
    content:
      'Starting your financial journey as a young adult can be overwhelming, but with the right strategies and mindset, you can build a solid foundation for long-term financial success.',
    category: 'finance',
    image:
      'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=600&h=400&fit=crop&crop=center',
    author: 'Financial Advisor',
    publishedAt: '2024-01-09T10:00:00Z',
    tags: ['Personal Finance', 'Budgeting', 'Investment'],
    readTime: 5,
    likes: 156,
    views: 3420,
    published: true,
    createdAt: '2024-01-09T09:00:00Z',
    updatedAt: '2024-01-09T10:00:00Z',
  },
  // Entertainment Articles
  {
    id: 5,
    type: 'article' as const,
    title: 'The Rise of Streaming Platforms',
    description:
      'How streaming services have revolutionized entertainment consumption and changed the media landscape.',
    content:
      'The entertainment industry has undergone a dramatic transformation with the rise of streaming platforms. From Netflix to Disney+, these services have changed how we consume content.',
    category: 'entertainment',
    image:
      'https://images.unsplash.com/photo-1522869635100-9f4c5e86aa37?w=600&h=400&fit=crop&crop=center',
    author: 'Media Critic',
    publishedAt: '2024-01-08T10:00:00Z',
    tags: ['Streaming', 'Entertainment', 'Media'],
    readTime: 6,
    likes: 78,
    views: 1890,
    published: true,
    createdAt: '2024-01-08T09:00:00Z',
    updatedAt: '2024-01-08T10:00:00Z',
  },
  // Travel Articles
  {
    id: 6,
    type: 'article' as const,
    title: 'Discover Hidden Gems in Southeast Asia',
    description:
      'Uncover the lesser-known destinations in Southeast Asia, offering unique cultural experiences and breathtaking landscapes.',
    content:
      'Southeast Asia is a treasure trove of hidden gems waiting to be discovered. Beyond the popular tourist destinations, this region offers countless opportunities for authentic cultural experiences.',
    category: 'travel',
    image:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center',
    author: 'Travel Explorer',
    publishedAt: '2024-01-14T10:00:00Z',
    tags: ['Travel', 'Asia', 'Adventure'],
    readTime: 7,
    likes: 38,
    views: 980,
    published: true,
    createdAt: '2024-01-14T09:00:00Z',
    updatedAt: '2024-01-14T10:00:00Z',
  },
  // Lifestyle Articles
  {
    id: 7,
    type: 'article' as const,
    title: 'Mindfulness Practices for a Balanced Life',
    description:
      'Learn effective mindfulness techniques to reduce stress, enhance focus, and cultivate a sense of inner peace.',
    content:
      'In our fast-paced world, mindfulness has become an essential practice for maintaining mental health and well-being. These simple techniques can transform your daily experience.',
    category: 'lifestyle',
    image:
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop&crop=center',
    author: 'Wellness Coach',
    publishedAt: '2024-01-13T10:00:00Z',
    tags: ['Mindfulness', 'Wellness', 'Mental Health'],
    readTime: 4,
    likes: 38,
    views: 890,
    published: true,
    createdAt: '2024-01-13T09:00:00Z',
    updatedAt: '2024-01-13T10:00:00Z',
  },
  // Food Articles
  {
    id: 8,
    type: 'article' as const,
    title: 'The Ultimate Guide to Vegan Cooking',
    description:
      'Master the art of vegan cuisine with this comprehensive guide, featuring delicious recipes and essential cooking tips.',
    content:
      'Vegan cooking is an art that combines creativity, nutrition, and sustainability. This guide will help you create delicious plant-based meals that satisfy both your taste buds and your values.',
    category: 'food',
    image:
      'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=600&h=400&fit=crop&crop=center',
    author: 'Chef Verde',
    publishedAt: '2024-01-12T10:00:00Z',
    tags: ['Vegan', 'Cooking', 'Healthy'],
    readTime: 8,
    likes: 52,
    views: 1340,
    published: true,
    createdAt: '2024-01-12T09:00:00Z',
    updatedAt: '2024-01-12T10:00:00Z',
  },
  // Sports Articles
  {
    id: 9,
    type: 'article' as const,
    title: 'Home Workout Routines for Busy Professionals',
    description:
      'Effective exercise routines that can be done at home with minimal equipment, perfect for busy schedules.',
    content:
      "Staying fit doesn't require a gym membership or hours of free time. These efficient home workout routines are designed for busy professionals who want to maintain their health.",
    category: 'sports',
    image:
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=600&h=400&fit=crop&crop=center',
    author: 'Fitness Trainer',
    publishedAt: '2024-01-11T10:00:00Z',
    tags: ['Fitness', 'Home Workout', 'Health'],
    readTime: 6,
    likes: 45,
    views: 1150,
    published: true,
    createdAt: '2024-01-11T09:00:00Z',
    updatedAt: '2024-01-11T10:00:00Z',
  },
];
