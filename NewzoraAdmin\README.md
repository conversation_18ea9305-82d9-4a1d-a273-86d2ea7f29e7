# 🚀 Newzora Admin - 后台管理系统

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://postgresql.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14+-black.svg)](https://nextjs.org/)

Newzora内容平台的后台管理系统，提供用户管理、内容审核、数据分析等功能。

## ✨ 功能特性

### 🔐 管理员认证
- **JWT身份验证** - 安全的管理员登录系统
- **角色权限管理** - 超级管理员、管理员、审核员等角色
- **会话管理** - 自动登录状态维护

### 📊 数据仪表板
- **实时统计** - 用户数量、文章数量、评论统计
- **数据可视化** - 图表展示系统运行状态
- **快速操作** - 常用管理功能快捷入口

### 👥 用户管理
- **用户列表** - 查看所有注册用户
- **用户详情** - 用户资料和活动记录
- **用户操作** - 禁用、启用、删除用户

### 📝 内容管理
- **文章审核** - 待审核内容管理
- **内容编辑** - 修改和管理已发布内容
- **分类标签** - 内容分类和标签管理

### 📈 数据分析
- **用户行为分析** - 用户活跃度统计
- **内容统计** - 文章发布和阅读数据
- **系统监控** - 服务器性能和状态监控

## 🚀 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 13+
- npm 或 yarn

### 安装步骤

1. **克隆仓库**
   ```bash
   git clone https://github.com/Jacken22/Newzora-Admin.git
   cd Newzora-Admin
   ```

2. **安装依赖**
   ```bash
   # 安装后端依赖
   cd Backend
   npm install
   
   # 安装前端依赖
   cd ../Frontend
   npm install
   ```

3. **配置环境变量**
   ```bash
   # 后端配置
   cd Backend
   cp .env.example .env
   # 编辑 .env 文件，配置数据库连接等信息
   ```

4. **初始化数据库**
   ```bash
   cd Backend
   npm run init-db
   ```

5. **启动服务**
   ```bash
   # 启动后端服务 (端口: 5001)
   cd Backend
   npm run dev
   
   # 启动前端服务 (端口: 3001)
   cd ../Frontend
   npm run dev
   ```

6. **访问系统**
   - 管理后台: http://localhost:3001
   - 测试页面: http://localhost:3001/test
   - API接口: http://localhost:5001

## 🔑 测试账户

```
超级管理员:
Email: <EMAIL>
Password: admin123456

管理员:
Email: <EMAIL>
Password: test123456

审核员:
Email: <EMAIL>
Password: mod123456
```

## 📁 项目结构

```
Newzora-Admin/
├── Backend/                 # Node.js/Express 后端服务
│   ├── config/             # 数据库和应用配置
│   ├── middleware/         # 中间件 (认证、日志等)
│   ├── models/             # 数据库模型
│   ├── routes/             # API路由
│   ├── services/           # 业务逻辑服务
│   ├── scripts/            # 数据库初始化脚本
│   └── server.js           # 服务器入口文件
├── Frontend/               # Next.js 14 前端应用
│   ├── src/
│   │   ├── app/           # Next.js App Router 页面
│   │   ├── components/    # React 组件
│   │   ├── contexts/      # React Context
│   │   ├── lib/           # 工具库
│   │   ├── services/      # API 服务层
│   │   ├── types/         # TypeScript 类型定义
│   │   └── utils/         # 工具函数
│   └── package.json
├── scripts/               # 数据库管理脚本
├── .gitignore
├── package.json
└── README.md
```

## 🛠️ 技术栈

### 后端技术
- **Node.js** - JavaScript 运行时
- **Express.js** - Web 应用框架
- **PostgreSQL** - 关系型数据库
- **Sequelize** - ORM 数据库操作
- **JWT** - 身份验证
- **bcryptjs** - 密码加密

### 前端技术
- **Next.js 14** - React 框架 (App Router)
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 实用优先的 CSS 框架
- **Lucide React** - 图标库
- **Axios** - HTTP 客户端

## 📚 API 文档

### 认证接口
```
POST /api/auth/login        # 管理员登录
POST /api/auth/logout       # 管理员登出
GET  /api/auth/me          # 获取当前管理员信息
```

### 管理接口
```
GET  /api/admin/dashboard   # 获取仪表板数据
GET  /api/admin/users       # 获取用户列表
GET  /api/admin/content     # 获取内容列表
GET  /api/admin/analytics   # 获取分析数据
GET  /api/admin/settings    # 获取系统设置
```

## 🔧 开发指南

### 本地开发
```bash
# 后端开发模式
cd Backend
npm run dev

# 前端开发模式
cd Frontend
npm run dev
```

### 数据库管理
```bash
# 初始化数据库
npm run init-db

# 创建数据库备份
npm run db:backup

# 检查数据库状态
npm run db:status
```

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码

## 🚀 部署

### 生产环境部署
```bash
# 构建前端
cd Frontend
npm run build

# 启动生产服务
npm start
```

### Docker 部署
```bash
# 构建镜像
docker build -t newzora-admin .

# 运行容器
docker run -p 3001:3001 -p 5001:5001 newzora-admin
```

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 基于现代 Web 技术构建
- 感谢开源社区的贡献
- 持续改进和优化

## 📞 支持

如需支持，请发送邮件至 <EMAIL> 或在 GitHub 上创建 issue。

---

**Made with ❤️ by the Newzora Team**