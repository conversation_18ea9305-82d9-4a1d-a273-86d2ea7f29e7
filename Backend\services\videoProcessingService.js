const ffmpeg = require('fluent-ffmpeg');
const ffmpegStatic = require('ffmpeg-static');
const path = require('path');
const fs = require('fs').promises;
const { MediaFile } = require('../models');

// 设置FFmpeg路径
ffmpeg.setFfmpegPath(ffmpegStatic);

class VideoProcessingService {
  constructor() {
    this.processingQueue = new Map(); // 存储处理中的视频状态
    this.outputDir = path.join(__dirname, '../../uploads/processed');
    this.thumbnailDir = path.join(__dirname, '../../uploads/thumbnails');
    
    // 确保输出目录存在
    this.ensureDirectories();
  }

  async ensureDirectories() {
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
      await fs.mkdir(this.thumbnailDir, { recursive: true });
    } catch (error) {
      console.error('创建目录失败:', error);
    }
  }

  /**
   * 开始处理视频
   * @param {string} videoId - 视频ID
   * @param {string} inputPath - 输入文件路径
   * @param {Object} options - 处理选项
   */
  async processVideo(videoId, inputPath, options = {}) {
    try {
      // 更新处理状态
      this.updateProcessingStatus(videoId, 'processing', '开始处理视频');

      // 获取视频信息
      const videoInfo = await this.getVideoInfo(inputPath);
      
      // 生成输出文件路径
      const outputPath = path.join(this.outputDir, `${videoId}_processed.mp4`);
      const thumbnailPath = path.join(this.thumbnailDir, `${videoId}_thumb.jpg`);

      // 处理视频
      await this.transcodeVideo(videoId, inputPath, outputPath, videoInfo, options);
      
      // 生成缩略图
      this.updateProcessingStatus(videoId, 'generating_thumbnail', '正在生成缩略图');
      await this.generateThumbnail(inputPath, thumbnailPath);

      // 更新数据库
      await this.updateMediaFile(videoId, {
        processedPath: outputPath,
        thumbnailPath: thumbnailPath,
        duration: videoInfo.duration,
        resolution: `${videoInfo.width}x${videoInfo.height}`,
        bitrate: videoInfo.bitrate,
        status: 'completed'
      });

      // 完成处理
      this.updateProcessingStatus(videoId, 'completed', '处理完成', {
        processedPath: outputPath,
        thumbnailPath: thumbnailPath,
        thumbnailUrl: `/api/media/thumbnail/${videoId}`,
        videoUrl: `/api/media/video/${videoId}`,
        duration: videoInfo.duration,
        resolution: `${videoInfo.width}x${videoInfo.height}`
      });

      console.log(`视频处理完成: ${videoId}`);
      return true;

    } catch (error) {
      console.error(`视频处理失败 ${videoId}:`, error);
      
      // 更新错误状态
      this.updateProcessingStatus(videoId, 'error', error.message);
      
      // 更新数据库状态
      await this.updateMediaFile(videoId, {
        status: 'error',
        errorMessage: error.message
      });

      return false;
    }
  }

  /**
   * 获取视频信息
   */
  async getVideoInfo(inputPath) {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(inputPath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        if (!videoStream) {
          reject(new Error('未找到视频流'));
          return;
        }

        resolve({
          duration: metadata.format.duration,
          width: videoStream.width,
          height: videoStream.height,
          bitrate: metadata.format.bit_rate,
          codec: videoStream.codec_name,
          fps: eval(videoStream.r_frame_rate) // 计算帧率
        });
      });
    });
  }

  /**
   * 转码视频
   */
  async transcodeVideo(videoId, inputPath, outputPath, videoInfo, options) {
    return new Promise((resolve, reject) => {
      let command = ffmpeg(inputPath);

      // 设置视频编码参数
      const { width, height } = this.calculateOutputResolution(videoInfo.width, videoInfo.height, options.maxResolution);
      
      command = command
        .videoCodec('libx264')
        .audioCodec('aac')
        .size(`${width}x${height}`)
        .videoBitrate(this.calculateBitrate(width, height))
        .audioBitrate('128k')
        .fps(Math.min(videoInfo.fps || 30, 30)) // 限制最大帧率为30fps
        .format('mp4')
        .addOption('-preset', 'medium') // 平衡质量和速度
        .addOption('-crf', '23') // 恒定质量因子
        .addOption('-movflags', '+faststart'); // 优化网络播放

      // 监听进度
      command.on('progress', (progress) => {
        const percentage = Math.round(progress.percent || 0);
        this.updateProcessingStatus(videoId, 'processing', `转码中... ${percentage}%`, {
          percentage
        });
      });

      // 监听错误
      command.on('error', (err) => {
        console.error('FFmpeg错误:', err);
        reject(err);
      });

      // 监听完成
      command.on('end', () => {
        console.log(`视频转码完成: ${videoId}`);
        resolve(outputPath);
      });

      // 开始处理
      command.save(outputPath);
    });
  }

  /**
   * 生成缩略图
   */
  async generateThumbnail(inputPath, thumbnailPath) {
    return new Promise((resolve, reject) => {
      ffmpeg(inputPath)
        .screenshots({
          timestamps: ['10%'], // 在视频10%位置截取
          filename: path.basename(thumbnailPath),
          folder: path.dirname(thumbnailPath),
          size: '320x240'
        })
        .on('error', (err) => {
          console.error('生成缩略图错误:', err);
          reject(err);
        })
        .on('end', () => {
          console.log('缩略图生成完成');
          resolve(thumbnailPath);
        });
    });
  }

  /**
   * 计算输出分辨率
   */
  calculateOutputResolution(originalWidth, originalHeight, maxResolution = '1080p') {
    const resolutions = {
      '480p': { width: 854, height: 480 },
      '720p': { width: 1280, height: 720 },
      '1080p': { width: 1920, height: 1080 }
    };

    const target = resolutions[maxResolution] || resolutions['720p'];
    
    // 保持宽高比
    const aspectRatio = originalWidth / originalHeight;
    
    let width = target.width;
    let height = target.height;
    
    if (originalWidth < target.width && originalHeight < target.height) {
      // 如果原视频分辨率更小，保持原分辨率
      width = originalWidth;
      height = originalHeight;
    } else {
      // 按比例缩放
      if (aspectRatio > target.width / target.height) {
        height = Math.round(width / aspectRatio);
      } else {
        width = Math.round(height * aspectRatio);
      }
    }

    // 确保是偶数（H.264要求）
    width = width % 2 === 0 ? width : width - 1;
    height = height % 2 === 0 ? height : height - 1;

    return { width, height };
  }

  /**
   * 计算比特率
   */
  calculateBitrate(width, height) {
    const pixels = width * height;
    
    if (pixels <= 854 * 480) {
      return '1000k'; // 480p
    } else if (pixels <= 1280 * 720) {
      return '2500k'; // 720p
    } else {
      return '5000k'; // 1080p
    }
  }

  /**
   * 更新处理状态
   */
  updateProcessingStatus(videoId, status, message, data = {}) {
    this.processingQueue.set(videoId, {
      status,
      message,
      data,
      updatedAt: new Date()
    });
  }

  /**
   * 获取处理状态
   */
  getProcessingStatus(videoId) {
    return this.processingQueue.get(videoId) || {
      status: 'not_found',
      message: '未找到处理记录'
    };
  }

  /**
   * 更新数据库中的媒体文件信息
   */
  async updateMediaFile(videoId, updates) {
    try {
      await MediaFile.update(updates, {
        where: { id: videoId }
      });
    } catch (error) {
      console.error('更新媒体文件失败:', error);
    }
  }

  /**
   * 清理处理状态（可选，用于内存管理）
   */
  cleanupProcessingStatus(videoId) {
    this.processingQueue.delete(videoId);
  }

  /**
   * 获取所有处理中的视频
   */
  getAllProcessingVideos() {
    const processing = [];
    for (const [videoId, status] of this.processingQueue.entries()) {
      processing.push({
        videoId,
        ...status
      });
    }
    return processing;
  }

  /**
   * 批量处理视频队列
   */
  async processQueue() {
    try {
      // 获取待处理的视频
      const pendingVideos = await MediaFile.findAll({
        where: {
          fileType: 'video',
          status: 'pending'
        },
        limit: 5 // 限制并发处理数量
      });

      for (const video of pendingVideos) {
        // 异步处理，不等待完成
        this.processVideo(video.id, video.filePath).catch(error => {
          console.error(`处理视频队列错误 ${video.id}:`, error);
        });
      }

    } catch (error) {
      console.error('处理视频队列错误:', error);
    }
  }
}

// 创建单例实例
const videoProcessingService = new VideoProcessingService();

// 定期处理队列（每分钟检查一次）
setInterval(() => {
  videoProcessingService.processQueue();
}, 60000);

module.exports = videoProcessingService;