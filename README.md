# 🚀 Newzora - Modern Content Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-13+-blue.svg)](https://postgresql.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14+-black.svg)](https://nextjs.org/)

A modern, full-featured content platform built with Next.js, Node.js, and PostgreSQL. Newzora provides a complete solution for content creation, user management, and social interaction.

## ✨ Features

### 🔐 Authentication & User Management
- **JWT-based Authentication** - Secure token-based authentication
- **User Registration & Login** - Complete user account management
- **Social Login UI** - Google, Facebook, X, Apple integration (UI ready)
- **Password Reset** - Email-based password recovery
- **Profile Management** - User profiles with avatars and bio

### ✏️ Content Creation & Management
- **Rich Text Editor** - Custom contentEditable-based editor with formatting
- **Article Management** - Create, edit, publish, and manage articles
- **Media Support** - Image insertion and media handling
- **Auto-save** - Automatic draft saving functionality
- **Categories & Tags** - Organize content with categories and tags

### 📱 User Experience
- **Responsive Design** - Mobile-first, fully responsive interface
- **Infinite Scroll** - Smooth content loading on homepage and explore page
- **Real-time Interactions** - Like, comment, share functionality
- **Search & Discovery** - Advanced search with filters and suggestions
- **Notifications** - Real-time, push, and email notifications
- **Dark/Light Mode** - Theme switching support
- **PWA Ready** - Progressive Web App capabilities

### 🏗️ Technical Features
- **PostgreSQL Database** - 25+ tables with optimized relationships
- **RESTful API** - 50+ endpoints with comprehensive error handling
- **Security** - JWT auth, rate limiting, input validation, CORS protection
- **Performance** - Query optimization, caching, monitoring
- **Real-time** - Socket.IO for live updates and messaging
- **Analytics** - User behavior tracking and content statistics
- **Monitoring** - Health checks, logging, and performance metrics
- **Testing** - Unit, integration, and API tests with Jest

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL 13+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Jacken22/Newzora.git
   cd Newzora
   ```

2. **Install all dependencies**
   ```bash
   # Install root dependencies and all sub-projects
   npm run install:all
   
   # Or install individually
   npm install
   cd Backend && npm install
   cd ../Frontend && npm install
   ```

3. **Setup environment variables**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit .env with your configuration
   # Backend configuration (Main Website)
   DATABASE_URL=postgresql://postgres:wasd080980!@localhost:5432/PostgreSQL-newzora_web
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRES_IN=7d
   PORT=5000
   
   # Admin Backend configuration
   ADMIN_DATABASE_URL=postgresql://postgres:QWasd080980!@localhost:5433/PostgreSQL-newzora_admin
   ADMIN_JWT_SECRET=your-admin-secret-jwt-key
   ADMIN_PORT=5001
   
   # Frontend configuration
   NEXT_PUBLIC_API_URL=http://localhost:5000
   NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
   NEXT_PUBLIC_ADMIN_API_URL=http://localhost:5001
   NEXT_PUBLIC_ADMIN_FRONTEND_URL=http://localhost:3001
   ```

4. **Initialize databases**
   ```bash
   # Quick setup (recommended)
   node scripts/database-manager.js setup
   
   # Or manually initialize each database
   cd Backend
   npm run init-db
   npm run seed  # Optional: Add sample data
   
   cd ../NewzoraAdmin/Backend
   npm run init-db
   ```

5. **Start the application**
   ```bash
   # Start both frontend and backend concurrently
   npm run dev
   
   # Or start individually
   # Terminal 1 - Backend
   cd Backend && npm run dev
   
   # Terminal 2 - Frontend  
   cd Frontend && npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## 👤 Test Accounts

```
Admin Account:
Email: <EMAIL>
Password: admin123456

Demo Account:
Email: <EMAIL>
Password: demo123456
```

## 📁 Project Structure

```
Newzora/
├── Backend/                 # Node.js/Express API server
│   ├── config/             # Database and app configuration
│   ├── middleware/         # Authentication, security, rate limiting
│   ├── models/             # Sequelize database models (25+ tables)
│   ├── routes/             # API route handlers (50+ endpoints)
│   ├── services/           # Business logic services
│   ├── scripts/            # Database initialization and utilities
│   └── server.js           # Main server file
├── Frontend/               # Next.js 14 React application
│   ├── src/
│   │   ├── app/           # Next.js app router pages (30+ pages)
│   │   ├── components/    # Reusable React components (80+ components)
│   │   ├── contexts/      # React context providers
│   │   ├── lib/           # Utility libraries and configurations
│   │   ├── services/      # API service layer
│   │   ├── types/         # TypeScript type definitions
│   │   └── utils/         # Utility functions
│   └── package.json
├── deployment/            # Docker and deployment configurations
├── docs/                  # Documentation and reports
├── tools/                 # Development tools and scripts
├── .eslintrc.json         # ESLint configuration
├── .prettierrc            # Prettier configuration
├── tsconfig.json          # TypeScript configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── next.config.js         # Next.js configuration
```

## 🛠️ Technology Stack

### Frontend
- **Next.js 14** - React framework with app router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **React Hooks** - Modern React state management

### Backend
- **Node.js** - JavaScript runtime
- **Express.js** - Web application framework
- **PostgreSQL** - Relational database
- **Sequelize** - ORM for database operations
- **JWT** - JSON Web Tokens for authentication
- **bcrypt** - Password hashing

### DevOps & Tools
- **Docker** - Containerization
- **Jest** - Testing framework
- **ESLint** - Code linting
- **Prettier** - Code formatting

## 📚 API Documentation

### Authentication Endpoints
```
POST /api/auth/register         # User registration
POST /api/auth/login            # User login
POST /api/auth/logout           # User logout
GET  /api/auth/me              # Get current user
PUT  /api/auth/profile         # Update user profile
PUT  /api/auth/change-password # Change password
POST /api/auth/forgot-password # Password reset request
POST /api/auth/reset-password  # Password reset confirmation
POST /api/auth/verify-email    # Email verification
```

### Article Endpoints
```
GET    /api/articles       # Get articles list
GET    /api/articles/:id   # Get single article
POST   /api/articles       # Create article (auth required)
PUT    /api/articles/:id   # Update article (auth required)
DELETE /api/articles/:id   # Delete article (auth required)
```

### Social & Interaction Endpoints
```
# Comments
GET    /api/comments/:articleId # Get article comments
POST   /api/comments           # Create comment (auth required)
PUT    /api/comments/:id       # Update comment (auth required)
DELETE /api/comments/:id       # Delete comment (auth required)

# Follow System
POST   /api/follows            # Follow user (auth required)
DELETE /api/follows/:userId    # Unfollow user (auth required)
GET    /api/follows/followers  # Get followers list
GET    /api/follows/following  # Get following list

# Messages
GET    /api/messages           # Get conversations (auth required)
POST   /api/messages           # Send message (auth required)
GET    /api/messages/:userId   # Get conversation with user

# Notifications
GET    /api/notifications      # Get user notifications (auth required)
PUT    /api/notifications/:id  # Mark notification as read
POST   /api/notifications/preferences # Update notification settings
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Backend tests
cd Backend
npm test
npm run test:coverage  # With coverage report

# Frontend tests
cd Frontend
npm test
npm run test:watch     # Watch mode

# Lint and format
npm run lint           # Check code quality
npm run format         # Format code
npm run typecheck      # TypeScript check
```

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production
```bash
# Using Docker (Recommended)
docker-compose -f deployment/docker/docker-compose.prod.yml up -d

# Manual deployment
npm run build
npm start

# Quick deployment scripts
./deployment/scripts/deploy-production.sh  # Linux/Mac
.\deployment\scripts\deploy-production.ps1  # Windows
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built with modern web technologies
- Inspired by contemporary content platforms
- Community-driven development approach

## 📞 Support

For support, email <EMAIL> or create an issue on GitHub.

---

**Made with ❤️ by the Newzora Team**
