'use client';

import { useState } from 'react';
import { Ch<PERSON><PERSON>Down, Ch<PERSON>ronUp, <PERSON>, CheckCircle, AlertTriangle, Info } from 'lucide-react';

interface AIAnalysisDetailsProps {
  analysis: {
    confidence: number;
    checks: {
      documentQuality: number;
      textReadability: number;
      faceMatch?: number;
      documentAuthenticity: number;
      dataConsistency: number;
      securityFeatures?: number;
    };
    extractedData: any;
    flags: string[];
  };
  crossValidation?: {
    consistent: boolean;
    issues: string[];
    confidence: number;
  };
}

export default function AIAnalysisDetails({ analysis, crossValidation }: AIAnalysisDetailsProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 85) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (score >= 70) return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  return (
    <div className="mt-3 border border-gray-200 rounded-lg">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full flex items-center justify-between p-3 text-left hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Brain className="h-4 w-4 text-purple-600" />
          <span className="text-sm font-medium text-gray-700">AI Analysis Details</span>
          <span className="text-xs text-gray-500">({analysis.confidence.toFixed(1)}% confidence)</span>
        </div>
        {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
      </button>

      {isExpanded && (
        <div className="border-t border-gray-200 p-4 space-y-4">
          {/* Quality Checks */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 mb-2">Quality Checks</h4>
            <div className="space-y-2">
              {Object.entries(analysis.checks).map(([key, value]) => {
                if (typeof value !== 'number') return null;
                const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                return (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {getScoreIcon(value)}
                      <span className="text-sm text-gray-700">{label}</span>
                    </div>
                    <span className={`text-sm font-medium ${getScoreColor(value)}`}>
                      {value.toFixed(1)}%
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Extracted Data */}
          {Object.keys(analysis.extractedData).length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Extracted Information</h4>
              <div className="bg-gray-50 rounded-lg p-3 space-y-1">
                {Object.entries(analysis.extractedData).map(([key, value]) => {
                  if (!value) return null;
                  const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
                  return (
                    <div key={key} className="flex justify-between">
                      <span className="text-xs text-gray-600">{label}:</span>
                      <span className="text-xs text-gray-900 font-medium">{String(value)}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Flags */}
          {analysis.flags.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Issues Detected</h4>
              <div className="space-y-1">
                {analysis.flags.map((flag, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <AlertTriangle className="h-3 w-3 text-orange-500" />
                    <span className="text-xs text-orange-700">{flag}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Cross Validation */}
          {crossValidation && (
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-2">Cross-Document Validation</h4>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {crossValidation.consistent ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="text-sm font-medium">
                      {crossValidation.consistent ? 'Data Consistent' : 'Inconsistencies Found'}
                    </span>
                  </div>
                  <span className="text-xs text-gray-600">
                    {crossValidation.confidence.toFixed(1)}% confidence
                  </span>
                </div>
                {crossValidation.issues.length > 0 && (
                  <div className="space-y-1">
                    {crossValidation.issues.map((issue, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Info className="h-3 w-3 text-blue-500" />
                        <span className="text-xs text-gray-700">{issue}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}