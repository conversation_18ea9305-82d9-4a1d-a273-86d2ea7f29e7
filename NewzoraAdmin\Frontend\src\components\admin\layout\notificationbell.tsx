'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Bell, Check, X, AlertCircle, Info, AlertTriangle, Clock, User } from 'lucide-react';
import api from '@/lib/api';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  timestamp: string;
  read: boolean;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'user' | 'content' | 'system' | 'security';
  sender?: {
    id: string;
    name: string;
    avatar?: string;
  };
  metadata?: Record<string, any>;
}

const NotificationBell: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'urgent'>('all');
  const notificationRef = useRef<HTMLDivElement>(null);

  // Initialize notifications and set up real-time updates
  useEffect(() => {
    fetchNotifications();

    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchNotifications, 30000);

    return () => clearInterval(interval);
  }, []);

  const formatTimestamp = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;

    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  const fetchNotifications = async () => {
    try {
      setLoading(true);

      // Enhanced mock data with real-time timestamps
      const mockNotifications: Notification[] = [
        {
          id: '1',
          title: 'New User Registration',
          message: 'User John Smith has just completed registration',
          type: 'info',
          timestamp: formatTimestamp(new Date(Date.now() - 2 * 60 * 1000)),
          read: false,
          actionUrl: '/admin/users',
          priority: 'medium',
          category: 'user',
          sender: {
            id: 'system',
            name: 'System'
          },
          metadata: {
            userId: 'user123',
            registrationMethod: 'email'
          }
        },
        {
          id: '2',
          title: 'Content Pending Review',
          message: 'There are 5 articles waiting for review',
          type: 'warning',
          timestamp: formatTimestamp(new Date(Date.now() - 10 * 60 * 1000)),
          read: false,
          actionUrl: '/admin/content/reviews',
          priority: 'high',
          category: 'content',
          sender: {
            id: 'system',
            name: 'Content System'
          },
          metadata: {
            pendingCount: 5
          }
        },
        {
          id: '3',
          title: 'System Maintenance Alert',
          message: 'System will undergo maintenance from 23:00-24:00 tonight',
          type: 'info',
          timestamp: formatTimestamp(new Date(Date.now() - 60 * 60 * 1000)),
          read: true,
          priority: 'urgent',
          category: 'system',
          sender: {
            id: 'admin',
            name: 'System Administrator'
          }
        },
        {
          id: '4',
          title: 'Security Alert',
          message: 'Multiple failed login attempts detected',
          type: 'error',
          timestamp: formatTimestamp(new Date(Date.now() - 2 * 60 * 60 * 1000)),
          read: false,
          actionUrl: '/admin/security/logs',
          priority: 'urgent',
          category: 'security',
          sender: {
            id: 'security-system',
            name: 'Security Monitor'
          },
          metadata: {
            ipAddress: '*************',
            attemptCount: 15
          }
        }
      ];

      setNotifications(mockNotifications);
      updateUnreadCount(mockNotifications);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 模拟实时通知
  useEffect(() => {
    // 在实际应用中，这里应该使用 WebSocket 或轮询 API 来获取实时通知
    const interval = setInterval(() => {
      // 模拟新通知
      if (Math.random() > 0.8) {
        const newNotification: Notification = {
          id: Date.now().toString(),
          title: 'New Activity',
          message: 'System detected new user activity',
          type: 'info',
          timestamp: 'Just now',
          read: false
        };
        
        setNotifications(prev => [newNotification, ...prev]);
        setUnreadCount(prev => prev + 1);
      }
    }, 30000); // 每30秒检查一次新通知

    return () => clearInterval(interval);
  }, []);

  const updateUnreadCount = (notifs: Notification[]) => {
    setUnreadCount(notifs.filter(n => !n.read).length);
  };

  const getFilteredNotifications = () => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => !n.read);
      case 'urgent':
        return notifications.filter(n => n.priority === 'urgent');
      default:
        return notifications;
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'high':
        return <AlertCircle className="w-4 h-4 text-orange-500" />;
      case 'medium':
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return <Info className="w-4 h-4 text-gray-500" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'user':
        return <User className="w-4 h-4" />;
      case 'security':
        return <AlertTriangle className="w-4 h-4" />;
      case 'system':
        return <Info className="w-4 h-4" />;
      default:
        return <Bell className="w-4 h-4" />;
    }
  };

  const markAsRead = (id: string) => {
    const updatedNotifications = notifications.map(notification =>
      notification.id === id ? { ...notification, read: true } : notification
    );
    
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map(notification => ({
      ...notification,
      read: true
    }));
    
    setNotifications(updatedNotifications);
    setUnreadCount(0);
  };

  const removeNotification = (id: string) => {
    const updatedNotifications = notifications.filter(
      notification => notification.id !== id
    );
    
    setNotifications(updatedNotifications);
    updateUnreadCount(updatedNotifications);
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }
    
    if (notification.actionUrl) {
      // In real applications, this should use router navigation
      // window.location.href = notification.actionUrl;
      console.log(`Navigate to: ${notification.actionUrl}`);
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'bg-green-500';
      case 'error':
        return 'bg-red-500';
      case 'warning':
        return 'bg-yellow-500';
      case 'info':
      default:
        return 'bg-blue-500';
    }
  };

  return (
    <div className="relative" ref={notificationRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-1 text-gray-600 hover:text-gray-900 focus:outline-none"
      >
        <Bell size={20} />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-md shadow-lg overflow-hidden z-50">
          <div className="py-2 px-4 bg-gray-50 flex justify-between items-center border-b">
            <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
            <div className="flex items-center space-x-2">
              {loading && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              )}
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                >
                  <Check size={12} className="mr-1" />
                  Mark all as read
                </button>
              )}
            </div>
          </div>

          {/* Filter Tabs */}
          <div className="flex border-b bg-gray-50">
            {[
              { key: 'all', label: 'All', count: notifications.length },
              { key: 'unread', label: 'Unread', count: notifications.filter(n => !n.read).length },
              { key: 'urgent', label: 'Urgent', count: notifications.filter(n => n.priority === 'urgent').length }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setFilter(tab.key as 'all' | 'unread' | 'urgent')}
                className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
                  filter === tab.key
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.label} {tab.count > 0 && `(${tab.count})`}
              </button>
            ))}
          </div>
          
          <div className="max-h-96 overflow-y-auto">
            {getFilteredNotifications().length === 0 ? (
              <div className="py-6 text-center text-gray-500 text-sm">
                {filter === 'all' ? 'No notifications' : `No ${filter} notifications`}
              </div>
            ) : (
              getFilteredNotifications().map((notification) => (
                <div
                  key={notification.id}
                  className={`border-b hover:bg-gray-50 transition-colors duration-200 ${
                    !notification.read ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="py-3 px-4 flex">
                    <div className="flex-shrink-0 mr-3">
                      <div className="flex items-center space-x-1">
                        {notification.category && getCategoryIcon(notification.category)}
                        {notification.priority && getPriorityIcon(notification.priority)}
                      </div>
                      <div
                        className={`w-2 h-2 rounded-full mt-1 ${getNotificationIcon(
                          notification.type
                        )}`}
                      ></div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div
                          className="text-sm font-medium text-gray-900 cursor-pointer flex-1"
                          onClick={() => handleNotificationClick(notification)}
                        >
                          {notification.title}
                          {!notification.read && (
                            <span className="inline-block w-2 h-2 bg-blue-500 rounded-full ml-2"></span>
                          )}
                        </div>
                        {notification.sender && (
                          <div className="text-xs text-gray-400 ml-2">
                            {notification.sender.name}
                          </div>
                        )}
                      </div>
                      <div
                        className="text-sm text-gray-500 mt-1 cursor-pointer"
                        onClick={() => handleNotificationClick(notification)}
                      >
                        {notification.message}
                      </div>
                      <div className="mt-2 flex justify-between items-center">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-3 h-3 text-gray-400" />
                          <span className="text-xs text-gray-400">
                            {notification.timestamp}
                          </span>
                          {notification.priority === 'urgent' && (
                            <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Urgent
                            </span>
                          )}
                        </div>
                        <div className="flex space-x-2">
                          {!notification.read && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                markAsRead(notification.id);
                              }}
                              className="text-gray-400 hover:text-gray-600"
                              title="Mark as read"
                            >
                              <Check size={14} />
                            </button>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              removeNotification(notification.id);
                            }}
                            className="text-gray-400 hover:text-gray-600"
                            title="Delete notification"
                          >
                            <X size={14} />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
          
          <div className="py-2 px-4 bg-gray-50 text-center border-t">
            <button className="text-sm text-blue-600 hover:text-blue-800">
              View all notifications
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;