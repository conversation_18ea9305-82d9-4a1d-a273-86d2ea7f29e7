#!/usr/bin/env node

/**
 * Newzora 项目健康检查脚本
 * 检查项目配置、依赖和基本功能状态
 */

const fs = require('fs');
const path = require('path');

class HealthChecker {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      issues: []
    };
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    const prefix = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };

    console.log(`${colors[type]}${prefix[type]} ${message}${colors.reset}`);
  }

  checkFile(filePath, description) {
    if (fs.existsSync(filePath)) {
      this.log(`${description} - 存在`, 'success');
      this.results.passed++;
      return true;
    } else {
      this.log(`${description} - 缺失`, 'error');
      this.results.failed++;
      this.results.issues.push(`缺失文件: ${filePath}`);
      return false;
    }
  }

  checkDirectory(dirPath, description) {
    if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
      this.log(`${description} - 存在`, 'success');
      this.results.passed++;
      return true;
    } else {
      this.log(`${description} - 缺失`, 'error');
      this.results.failed++;
      this.results.issues.push(`缺失目录: ${dirPath}`);
      return false;
    }
  }

  checkPackageJson(packagePath, description) {
    if (this.checkFile(packagePath, description)) {
      try {
        const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const requiredScripts = ['dev', 'build', 'start'];
        const missingScripts = requiredScripts.filter(script => !pkg.scripts[script]);
        
        if (missingScripts.length === 0) {
          this.log(`${description} 脚本完整`, 'success');
          this.results.passed++;
        } else {
          this.log(`${description} 缺少脚本: ${missingScripts.join(', ')}`, 'warning');
          this.results.warnings++;
        }
        return true;
      } catch (error) {
        this.log(`${description} JSON 格式错误`, 'error');
        this.results.failed++;
        return false;
      }
    }
    return false;
  }

  async runChecks() {
    this.log('🚀 开始 Newzora 项目健康检查...', 'info');
    this.log('', 'info');

    // 检查根目录配置文件
    this.log('📋 检查根目录配置文件:', 'info');
    this.checkFile('package.json', '根目录 package.json');
    this.checkFile('tsconfig.json', 'TypeScript 配置');
    this.checkFile('.eslintrc.json', 'ESLint 配置');
    this.checkFile('.prettierrc', 'Prettier 配置');
    this.checkFile('.env.example', '环境变量示例');
    this.checkFile('next.config.js', 'Next.js 配置');
    this.checkFile('tailwind.config.js', 'Tailwind 配置');
    this.checkFile('.gitignore', 'Git 忽略文件');
    this.log('', 'info');

    // 检查项目文档
    this.log('📚 检查项目文档:', 'info');
    this.checkFile('README.md', '项目说明文档');
    this.checkFile('PROJECT_STATUS.md', '项目状态文档');
    this.checkFile('PROJECT_ROADMAP.md', '项目路线图');
    this.checkFile('PROJECT_CLEANUP_REPORT.md', '清理报告');
    this.checkFile('LICENSE', '许可证文件');
    this.log('', 'info');

    // 检查主要目录结构
    this.log('🏗️ 检查目录结构:', 'info');
    this.checkDirectory('Backend', '后端目录');
    this.checkDirectory('Frontend', '前端目录');
    this.checkDirectory('docs', '文档目录');
    this.checkDirectory('deployment', '部署配置目录');
    this.checkDirectory('tools', '工具目录');
    this.log('', 'info');

    // 检查 Backend 结构
    this.log('🔧 检查后端结构:', 'info');
    this.checkDirectory('Backend/config', '后端配置目录');
    this.checkDirectory('Backend/models', '数据模型目录');
    this.checkDirectory('Backend/routes', 'API 路由目录');
    this.checkDirectory('Backend/middleware', '中间件目录');
    this.checkDirectory('Backend/services', '服务目录');
    this.checkFile('Backend/server.js', '后端服务器文件');
    this.checkPackageJson('Backend/package.json', '后端 package.json');
    this.log('', 'info');

    // 检查 Frontend 结构
    this.log('🎨 检查前端结构:', 'info');
    this.checkDirectory('Frontend/src', '前端源码目录');
    this.checkDirectory('Frontend/src/app', 'Next.js 应用目录');
    this.checkDirectory('Frontend/src/components', '组件目录');
    this.checkDirectory('Frontend/src/contexts', 'Context 目录');
    this.checkDirectory('Frontend/src/lib', '库目录');
    this.checkDirectory('Frontend/src/types', '类型定义目录');
    this.checkDirectory('Frontend/public', '静态资源目录');
    this.checkPackageJson('Frontend/package.json', '前端 package.json');
    this.log('', 'info');

    // 检查关键文件内容
    this.log('📄 检查关键文件内容:', 'info');
    this.checkTsConfig();
    this.checkEslintConfig();
    this.checkGitignore();
    this.log('', 'info');

    // 输出总结
    this.printSummary();
  }

  checkTsConfig() {
    try {
      const tsconfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
      if (tsconfig.compilerOptions && tsconfig.compilerOptions.strict) {
        this.log('TypeScript 严格模式已启用', 'success');
        this.results.passed++;
      } else {
        this.log('TypeScript 严格模式未启用', 'warning');
        this.results.warnings++;
      }
    } catch (error) {
      this.log('TypeScript 配置检查失败', 'error');
      this.results.failed++;
    }
  }

  checkEslintConfig() {
    try {
      const eslintrc = JSON.parse(fs.readFileSync('.eslintrc.json', 'utf8'));
      if (eslintrc.rules && eslintrc.rules['@typescript-eslint/no-explicit-any']) {
        this.log('ESLint any 类型检查已配置', 'success');
        this.results.passed++;
      } else {
        this.log('ESLint any 类型检查未配置', 'warning');
        this.results.warnings++;
      }
    } catch (error) {
      this.log('ESLint 配置检查失败', 'error');
      this.results.failed++;
    }
  }

  checkGitignore() {
    try {
      const gitignore = fs.readFileSync('.gitignore', 'utf8');
      const requiredEntries = ['node_modules/', '.env', '.next/', 'dist/', 'build/'];
      const missingEntries = requiredEntries.filter(entry => !gitignore.includes(entry));
      
      if (missingEntries.length === 0) {
        this.log('Git 忽略文件配置完整', 'success');
        this.results.passed++;
      } else {
        this.log(`Git 忽略文件缺少条目: ${missingEntries.join(', ')}`, 'warning');
        this.results.warnings++;
      }
    } catch (error) {
      this.log('Git 忽略文件检查失败', 'error');
      this.results.failed++;
    }
  }

  printSummary() {
    this.log('📊 健康检查总结:', 'info');
    this.log(`✅ 通过: ${this.results.passed}`, 'success');
    this.log(`⚠️  警告: ${this.results.warnings}`, 'warning');
    this.log(`❌ 失败: ${this.results.failed}`, 'error');
    this.log('', 'info');

    if (this.results.issues.length > 0) {
      this.log('🔍 发现的问题:', 'info');
      this.results.issues.forEach(issue => {
        this.log(`  • ${issue}`, 'error');
      });
      this.log('', 'info');
    }

    const total = this.results.passed + this.results.warnings + this.results.failed;
    const healthScore = Math.round((this.results.passed / total) * 100);
    
    if (healthScore >= 90) {
      this.log(`🎉 项目健康度: ${healthScore}% - 优秀`, 'success');
    } else if (healthScore >= 75) {
      this.log(`👍 项目健康度: ${healthScore}% - 良好`, 'success');
    } else if (healthScore >= 60) {
      this.log(`⚠️  项目健康度: ${healthScore}% - 需要改进`, 'warning');
    } else {
      this.log(`❌ 项目健康度: ${healthScore}% - 需要修复`, 'error');
    }

    this.log('', 'info');
    this.log('🚀 健康检查完成!', 'info');
    
    // 返回退出码
    process.exit(this.results.failed > 0 ? 1 : 0);
  }
}

// 运行健康检查
const checker = new HealthChecker();
checker.runChecks().catch(error => {
  console.error('❌ 健康检查执行失败:', error);
  process.exit(1);
});