Write-Host "🚀 Newzora GitHub部署脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 配置Git用户信息
Write-Host "⚙️ 配置Git用户信息..." -ForegroundColor Yellow
git config user.name "Jacken22"
git config user.email "<EMAIL>"

# 初始化Git仓库（如果需要）
if (-not (Test-Path ".git")) {
    Write-Host "🔧 初始化Git仓库..." -ForegroundColor Yellow
    git init
    Write-Host "✅ Git仓库初始化完成" -ForegroundColor Green
}
else {
    Write-Host "✅ Git仓库已存在" -ForegroundColor Green
}

# 配置远程仓库
Write-Host "🔗 配置远程仓库..." -ForegroundColor Yellow
$remoteExists = git remote get-url origin 2>$null
if ($remoteExists) {
    git remote set-url origin https://github.com/Jacken22/Newzora.git
    Write-Host "✅ 远程仓库URL已更新" -ForegroundColor Green
}
else {
    git remote add origin https://github.com/Jacken22/Newzora.git
    Write-Host "✅ 远程仓库已添加" -ForegroundColor Green
}

# 检查状态
Write-Host "📋 检查Git状态..." -ForegroundColor Yellow
git status --short

# 添加所有文件
Write-Host "📦 添加文件到Git..." -ForegroundColor Yellow
git add .

# 提交更改
Write-Host "💾 提交更改..." -ForegroundColor Yellow
$commitResult = git commit -m "Initial commit - Newzora project with complete features"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 提交成功" -ForegroundColor Green

    # 推送到GitHub
    Write-Host "🚀 推送到GitHub..." -ForegroundColor Yellow
    Write-Host "⚠️ 如果这是第一次推送，可能需要输入GitHub用户名和密码/token" -ForegroundColor Yellow

    git branch -M main
    $pushResult = git push -u origin main

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 成功推送到GitHub!" -ForegroundColor Green
        Write-Host "🌐 项目地址: https://github.com/Jacken22/Newzora" -ForegroundColor Cyan
        Write-Host "📖 查看项目: https://github.com/Jacken22/Newzora/blob/main/README.md" -ForegroundColor Cyan
    }
    else {
        Write-Host "❌ 推送失败" -ForegroundColor Red
        Write-Host "💡 可能的解决方案:" -ForegroundColor Yellow
        Write-Host "   1. 确保GitHub仓库存在且有写入权限" -ForegroundColor Yellow
        Write-Host "   2. 检查网络连接" -ForegroundColor Yellow
        Write-Host "   3. 配置GitHub认证（Personal Access Token）" -ForegroundColor Yellow
    }
}
else {
    Write-Host "ℹ️ 没有新的更改需要提交" -ForegroundColor Blue
}

Write-Host "" -ForegroundColor White
Write-Host "📋 部署状态检查:" -ForegroundColor Green
Write-Host "✅ Git仓库已配置" -ForegroundColor Green
Write-Host "✅ 用户信息已设置" -ForegroundColor Green
Write-Host "✅ 远程仓库已配置" -ForegroundColor Green
Write-Host "✅ .gitignore文件已配置" -ForegroundColor Green
Write-Host "✅ 环境变量模板已创建" -ForegroundColor Green
Write-Host "✅ GitHub Actions工作流已配置" -ForegroundColor Green
Write-Host "✅ 贡献指南已创建" -ForegroundColor Green

Write-Host "" -ForegroundColor White
Write-Host "🎉 部署脚本执行完成!" -ForegroundColor Green
