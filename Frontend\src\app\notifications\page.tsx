'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import LoadingState from '@/components/ui/LoadingState';
import ErrorAlert from '@/components/ui/ErrorAlert';
import socketService from '@/services/socketService';

interface Notification {
  id: number;
  type: 'like' | 'comment' | 'follow' | 'message' | 'system' | 'article' | 'warning' | 'error' | 'work';
  title: string;
  content: string;
  data?: any;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  actionUrl?: string;
  imageUrl?: string;
  isRead: boolean;
  readAt?: string;
  createdAt: string;
}

const NOTIFICATION_TYPES = {
  like: { icon: '❤️', color: 'text-red-500', bgColor: 'bg-red-50', label: '点赞' },
  comment: { icon: '💬', color: 'text-blue-500', bgColor: 'bg-blue-50', label: '评论' },
  follow: { icon: '👥', color: 'text-green-500', bgColor: 'bg-green-50', label: '关注' },
  message: { icon: '📩', color: 'text-purple-500', bgColor: 'bg-purple-50', label: '消息' },
  system: { icon: '🔔', color: 'text-gray-500', bgColor: 'bg-gray-50', label: '系统' },
  article: { icon: '📝', color: 'text-indigo-500', bgColor: 'bg-indigo-50', label: '文章' },
  work: { icon: '🎬', color: 'text-yellow-500', bgColor: 'bg-yellow-50', label: '作品' },
  warning: { icon: '⚠️', color: 'text-yellow-500', bgColor: 'bg-yellow-50', label: '警告' },
  error: { icon: '❌', color: 'text-red-500', bgColor: 'bg-red-50', label: '错误' }
};

const mockNotifications: Notification[] = [
  {
    id: 1,
    type: 'like',
    title: '新的点赞',
    content: '用户 John 点赞了你的文章《JavaScript 最佳实践》',
    priority: 'normal',
    isRead: false,
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    data: { workId: 1, workType: 'article' },
    actionUrl: '/article/1'
  },
  {
    id: 2,
    type: 'comment',
    title: '新的评论',
    content: '用户 Sarah 在你的视频《React Hooks 深度解析》下发表了评论',
    priority: 'normal',
    isRead: false,
    createdAt: new Date(Date.now() - 7200000).toISOString(),
    data: { workId: 2, workType: 'video' },
    actionUrl: '/video/2'
  },
  {
    id: 3,
    type: 'follow',
    title: '新的关注者',
    content: '用户 Mike 关注了你',
    priority: 'normal',
    isRead: true,
    createdAt: new Date(Date.now() - 86400000).toISOString(),
    data: { userId: 101 },
    actionUrl: '/profile/mike'
  },
  {
    id: 4,
    type: 'system',
    title: '系统维护通知',
    content: '平台将于今晚 23:00-24:00 进行系统维护，请提前做好准备',
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - 172800000).toISOString(),
    actionUrl: '/notifications'
  },
  {
    id: 5,
    type: 'work',
    title: '作品审核通过',
    content: '你的文章《Next.js 14 新特性详解》已通过审核并发布',
    priority: 'normal',
    isRead: true,
    createdAt: new Date(Date.now() - 259200000).toISOString(),
    data: { workId: 3, workType: 'article' },
    actionUrl: '/article/3'
  },
  {
    id: 6,
    type: 'message',
    title: '私信消息',
    content: '用户 Alice 给你发送了一条私信',
    priority: 'normal',
    isRead: false,
    createdAt: new Date(Date.now() - 345600000).toISOString(),
    data: { conversationId: 201 },
    actionUrl: '/messages'
  }
];

export default function NotificationsPage() {
  const router = useRouter();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'system' | 'work' | 'history'>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // 加载通知列表
  const loadNotifications = async (pageNum = 1, isRefresh = false) => {
    try {
      setLoading(true);
      
      // 模拟 API 请求
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (isRefresh || pageNum === 1) {
        setNotifications(mockNotifications);
      } else {
        setNotifications(prev => [...prev, ...mockNotifications]);
      }
      
      setHasMore(false); // 模拟没有更多数据
      
      // 计算未读数量
      const unread = mockNotifications.filter(n => !n.isRead).length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('加载通知失败:', error);
      setError('加载通知失败');
    } finally {
      setLoading(false);
    }
  };

  // 标记通知为已读
  const markAsRead = async (notificationId: number) => {
    try {
      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId 
            ? { ...n, isRead: true, readAt: new Date().toISOString() }
            : n
        )
      );
      
      const newUnreadCount = unreadCount - 1;
      setUnreadCount(Math.max(0, newUnreadCount));

      // 通过Socket.IO同步状态
      if (socketService.isSocketConnected()) {
        socketService.markNotificationAsRead(notificationId);
      }
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    try {
      setNotifications(prev => 
        prev.map(n => ({ ...n, isRead: true, readAt: new Date().toISOString() }))
      );
      
      setUnreadCount(0);
      console.log('所有通知已标记为已读');

      // 通过Socket.IO同步状态
      if (socketService.isSocketConnected()) {
        socketService.markAllNotificationsAsRead();
      }
    } catch (error) {
      console.error('标记所有已读失败:', error);
      setError('操作失败');
    }
  };

  // 处理通知点击
  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }
    
    // 根据通知类型导航到相应页面
    try {
      if (notification.type === 'system') {
        router.push('/notifications/system');
      } else if (notification.data?.workId && notification.data?.workType) {
        router.push(`/${notification.data.workType}/${notification.data.workId}`);
      } else if (notification.data?.userId) {
        router.push(`/profile/user_${notification.data.userId}`);
      } else if (notification.data?.conversationId) {
        router.push(`/messages`);
      } else if (notification.actionUrl) {
        router.push(notification.actionUrl);
      } else {
        router.push('/notifications');
      }
    } catch (error) {
      console.error('Navigation error:', error);
      console.error('无法打开相关页面');
    }
  };

  // 过滤通知
  const filterNotifications = (notifications: Notification[]) => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => !n.isRead);
      case 'system':
        return notifications.filter(n => n.type === 'system' || n.type === 'warning' || n.type === 'error');
      case 'work':
        return notifications.filter(n => n.type === 'work' || n.type === 'like' || n.type === 'comment');
      case 'history':
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        return notifications.filter(n => new Date(n.createdAt) < oneWeekAgo);
      default:
        return notifications;
    }
  };

  // 格式化时间
  const formatTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  // Socket.IO实时通知监听
  useEffect(() => {
    // 监听新通知
    const handleNewNotification = (notification: any) => {
      setNotifications(prev => [notification, ...prev]);
      setUnreadCount(prev => prev + 1);
    };

    // 监听通知已读状态更新
    const handleNotificationRead = (data: any) => {
      setNotifications(prev => 
        prev.map(n => 
          n.id === data.notificationId 
            ? { ...n, isRead: true, readAt: new Date().toISOString() }
            : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    };

    socketService.on('new_notification', handleNewNotification);
    socketService.on('notification_read', handleNotificationRead);

    return () => {
      socketService.off('new_notification', handleNewNotification);
      socketService.off('notification_read', handleNotificationRead);
    };
  }, [unreadCount]);

  // 初始加载
  useEffect(() => {
    loadNotifications(1, true);
  }, [filter]);

  const filteredNotifications = filterNotifications(notifications);

  // 删除通知
  const deleteNotification = async (notificationId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      console.log(`已删除通知 ${notificationId}`);
    } catch (error) {
      console.error('删除通知失败:', error);
    }
  };



  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🔔 通知中心</h1>
            <p className="text-gray-600 mt-1">
              {unreadCount > 0 ? `你有 ${unreadCount} 条未读通知` : '暂无未读通知'}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => loadNotifications(1, true)}
              disabled={loading}
              className="text-sm text-gray-600 hover:text-gray-800 font-medium flex items-center space-x-1"
            >
              <svg
                className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              <span>刷新</span>
            </button>
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                全部已读
              </button>
            )}
          </div>
        </div>

        {/* 过滤器 */}
        <div className="flex border-b border-gray-200 mb-6">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-3 text-sm font-medium relative ${
              filter === 'all'
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            全部
            {filter === 'all' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
            )}
          </button>
          <button
            onClick={() => setFilter('unread')}
            className={`px-4 py-3 text-sm font-medium relative ${
              filter === 'unread'
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            未读
            {filter === 'unread' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
            )}
          </button>
          <button
            onClick={() => setFilter('system')}
            className={`px-4 py-3 text-sm font-medium relative ${
              filter === 'system'
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            系统消息
            {filter === 'system' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
            )}
          </button>
          <button
            onClick={() => setFilter('work')}
            className={`px-4 py-3 text-sm font-medium relative ${
              filter === 'work'
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            作品消息
            {filter === 'work' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
            )}
          </button>
          <button
            onClick={() => setFilter('history')}
            className={`px-4 py-3 text-sm font-medium relative ${
              filter === 'history'
                ? 'text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            历史消息
            {filter === 'history' && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
            )}
          </button>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center py-12">
            <LoadingState 
              isLoading={true} 
              loadingText="Loading notifications..." 
              size="md" 
            />
          </div>
        )}

        {/* Error State */}
        <ErrorAlert 
          error={error}
          onDismiss={() => setError(null)}
          onRetry={() => loadNotifications(1, true)}
          className="mb-6"
        />

        {/* 通知列表 */}
        {!loading && !error && (
          <div className="divide-y divide-gray-200">
            {filteredNotifications.map((notification) => {
              const notificationType = NOTIFICATION_TYPES[notification.type] || NOTIFICATION_TYPES.system;
              
              return (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                    !notification.isRead ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-10 h-10 rounded-full ${notificationType.bgColor} flex items-center justify-center`}>
                      <span className="text-base">{notificationType.icon}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <p className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </p>
                          <span className="ml-2 text-xs px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded">
                            {notificationType.label}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                          <button
                            onClick={(e) => deleteNotification(notification.id, e)}
                            className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 hover:bg-red-100 rounded-full"
                            title="删除通知"
                          >
                            <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <p className="mt-2 text-gray-600 text-sm">{notification.content}</p>
                      <p className="mt-3 text-xs text-gray-500 flex items-center">
                        <span>{formatTime(notification.createdAt)}</span>
                        {(notification.priority === 'high' || notification.priority === 'urgent') && (
                          <span className="ml-2 px-1.5 py-0.5 bg-red-100 text-red-600 text-xs rounded">
                            重要
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 空状态 */}
        {!loading && !error && filteredNotifications.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h10v-1a3 3 0 00-3-3H7a3 3 0 00-3 3v1zM6 10a3 3 0 116 0 3 3 0 01-6 0z" />
              </svg>
            </div>
            <p className="text-lg font-medium text-gray-900 mb-2">暂无通知</p>
            <p className="text-sm text-gray-500">这里会显示你的通知消息</p>
          </div>
        )}
        
        {/* 底部信息 */}
        {!loading && !error && filteredNotifications.length > 0 && (
          <div className="mt-8 p-4 border-t border-gray-200 bg-gray-50 text-center">
            <p className="text-xs text-gray-500">
              通知会保留30天，重要通知请及时处理
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
