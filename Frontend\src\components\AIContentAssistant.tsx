'use client';

import React, { useState, useEffect } from 'react';
import { aiService } from '@/services/aiService';
import { 
  ContentTag, 
  ContentModerationResult, 
  AICreationSuggestion,
  TranslationResult
} from '@/types';

interface AIContentAssistantProps {
  content: string;
  title?: string;
  onTagsGenerated?: (tags: ContentTag[]) => void;
  onModerationComplete?: (result: ContentModerationResult) => void;
  onSuggestionsGenerated?: (suggestions: AICreationSuggestion[]) => void;
}

export default function AIContentAssistant({
  content,
  title,
  onTagsGenerated,
  onModerationComplete,
  onSuggestionsGenerated
}: AIContentAssistantProps) {
  const [tags, setTags] = useState<ContentTag[]>([]);
  const [moderationResult, setModerationResult] = useState<ContentModerationResult | null>(null);
  const [suggestions, setSuggestions] = useState<AICreationSuggestion[]>([]);
  const [translation, setTranslation] = useState<TranslationResult | null>(null);
  const [activeTab, setActiveTab] = useState<'tags' | 'moderation' | 'suggestions' | 'translation'>('tags');
  const [isProcessing, setIsProcessing] = useState(false);
  const [targetLanguage, setTargetLanguage] = useState('en');
  const [translatedText, setTranslatedText] = useState('');

  // 初始化时生成标签和内容审核
  useEffect(() => {
    if (content) {
      handleGenerateTags();
      handleModerateContent();
    }
  }, [content]);

  const handleGenerateTags = async () => {
    setIsProcessing(true);
    try {
      const generatedTags = await aiService.generateContentTags(content, title);
      setTags(generatedTags);
      if (onTagsGenerated) {
        onTagsGenerated(generatedTags);
      }
    } catch (error) {
      console.error('生成标签失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleModerateContent = async () => {
    setIsProcessing(true);
    try {
      const result = await aiService.moderateContent(content, title);
      setModerationResult(result);
      if (onModerationComplete) {
        onModerationComplete(result);
      }
    } catch (error) {
      console.error('内容审核失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGenerateSuggestions = async () => {
    setIsProcessing(true);
    try {
      const generatedSuggestions = await aiService.generateCreationSuggestions(content, title);
      setSuggestions(generatedSuggestions);
      if (onSuggestionsGenerated) {
        onSuggestionsGenerated(generatedSuggestions);
      }
    } catch (error) {
      console.error('生成创作建议失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTranslate = async () => {
    setIsProcessing(true);
    try {
      const result = await aiService.translateText(content, targetLanguage);
      setTranslation(result);
      setTranslatedText(result.translatedText);
    } catch (error) {
      console.error('翻译失败:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getTagTypeColor = (type: string) => {
    switch (type) {
      case 'topic': return 'bg-blue-100 text-blue-800';
      case 'entity': return 'bg-green-100 text-green-800';
      case 'sentiment': return 'bg-purple-100 text-purple-800';
      case 'category': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getModerationStatusColor = (action: string) => {
    switch (action) {
      case 'approve': return 'bg-green-100 text-green-800';
      case 'review': return 'bg-yellow-100 text-yellow-800';
      case 'reject': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="border-b border-gray-200">
        <nav className="flex -mb-px">
          <button
            onClick={() => setActiveTab('tags')}
            className={`py-3 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'tags'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            智能标签
          </button>
          <button
            onClick={() => setActiveTab('moderation')}
            className={`py-3 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'moderation'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            内容审核
          </button>
          <button
            onClick={() => setActiveTab('suggestions')}
            className={`py-3 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'suggestions'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            创作建议
          </button>
          <button
            onClick={() => setActiveTab('translation')}
            className={`py-3 px-6 text-center border-b-2 font-medium text-sm ${
              activeTab === 'translation'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            智能翻译
          </button>
        </nav>
      </div>

      <div className="p-6">
        {isProcessing && (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        )}

        {!isProcessing && activeTab === 'tags' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">内容标签</h3>
              <button
                onClick={handleGenerateTags}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                重新生成
              </button>
            </div>
            
            {tags.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <span
                    key={tag.id}
                    className={`px-3 py-1 rounded-full text-sm font-medium ${getTagTypeColor(tag.type)}`}
                    title={`置信度: ${(tag.confidence * 100).toFixed(1)}%`}
                  >
                    {tag.name}
                  </span>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>暂无标签</p>
              </div>
            )}
          </div>
        )}

        {!isProcessing && activeTab === 'moderation' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">内容审核</h3>
              <button
                onClick={handleModerateContent}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                重新审核
              </button>
            </div>
            
            {moderationResult ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">审核结果</h4>
                    <p className="text-sm text-gray-600">
                      总体合规分数: {(moderationResult.overallScore * 100).toFixed(1)}%
                    </p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getModerationStatusColor(moderationResult.recommendedAction)}`}>
                    {moderationResult.recommendedAction === 'approve' && '通过'}
                    {moderationResult.recommendedAction === 'review' && '需要复审'}
                    {moderationResult.recommendedAction === 'reject' && '拒绝'}
                  </span>
                </div>
                
                {moderationResult.violations.length > 0 && (
                  <div className="border border-red-200 rounded-lg p-4">
                    <h4 className="font-medium text-red-800 mb-2">发现的问题</h4>
                    <div className="space-y-3">
                      {moderationResult.violations.map((violation, index) => (
                        <div key={index} className="bg-red-50 p-3 rounded">
                          <div className="flex justify-between">
                            <span className="font-medium text-red-700">
                              {violation.type === 'violence' && '暴力内容'}
                              {violation.type === 'adult' && '成人内容'}
                              {violation.type === 'hate_speech' && '仇恨言论'}
                              {violation.type === 'spam' && '垃圾内容'}
                              {violation.type === 'copyright' && '版权问题'}
                              {violation.type === 'other' && '其他问题'}
                            </span>
                            <span className="text-sm text-red-600">
                              置信度: {(violation.confidence * 100).toFixed(1)}%
                            </span>
                          </div>
                          <p className="text-sm text-red-600 mt-1">{violation.description}</p>
                          {violation.highlight && (
                            <div className="mt-2 p-2 bg-white border border-red-200 rounded text-sm">
                              <p className="text-red-800">{violation.highlight}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>暂无审核结果</p>
              </div>
            )}
          </div>
        )}

        {!isProcessing && activeTab === 'suggestions' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">创作建议</h3>
              <button
                onClick={handleGenerateSuggestions}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                重新生成
              </button>
            </div>
            
            {suggestions.length > 0 ? (
              <div className="space-y-4">
                {suggestions.map((suggestion) => (
                  <div key={suggestion.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium text-gray-900">
                        {suggestion.type === 'title' && '标题建议'}
                        {suggestion.type === 'outline' && '大纲建议'}
                        {suggestion.type === 'paragraph' && '段落建议'}
                        {suggestion.type === 'conclusion' && '结论建议'}
                        {suggestion.type === 'tags' && '标签建议'}
                        {suggestion.type === 'seo' && 'SEO建议'}
                      </h4>
                      <span className="text-xs text-gray-500">
                        置信度: {(suggestion.confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                    <div className="text-gray-700 whitespace-pre-line">
                      {suggestion.content}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>暂无创作建议</p>
              </div>
            )}
          </div>
        )}

        {!isProcessing && activeTab === 'translation' && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">智能翻译</h3>
            
            <div className="flex space-x-2">
              <select
                value={targetLanguage}
                onChange={(e) => setTargetLanguage(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="en">英语</option>
                <option value="zh">中文</option>
                <option value="es">西班牙语</option>
                <option value="ja">日语</option>
              </select>
              <button
                onClick={handleTranslate}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                翻译
              </button>
            </div>
            
            {translation && (
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-500">
                    {translation.detectedLanguage === 'zh' && '中文'}
                    {translation.detectedLanguage === 'en' && '英语'}
                    {translation.detectedLanguage === 'es' && '西班牙语'}
                    {translation.detectedLanguage === 'ja' && '日语'}
                    → 
                    {translation.targetLanguage === 'zh' && '中文'}
                    {translation.targetLanguage === 'en' && '英语'}
                    {translation.targetLanguage === 'es' && '西班牙语'}
                    {translation.targetLanguage === 'ja' && '日语'}
                  </span>
                  <span className="text-sm text-gray-500">
                    置信度: {(translation.confidence * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <p className="text-gray-700">{translation.translatedText}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}