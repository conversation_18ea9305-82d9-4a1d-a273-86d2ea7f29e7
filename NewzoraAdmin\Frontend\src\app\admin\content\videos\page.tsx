'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Play, Eye, Heart, MessageCircle, Calendar, User, Upload, Download, Edit, Trash2, Settings } from 'lucide-react';
import DataTable, { DataTableColumn } from '@/components/admin/common/DataTable';
import VideoUploadModal from '@/components/admin/media/VideoUploadModal';
import VideoPreviewModal from '@/components/admin/media/VideoPreviewModal';
import VideoEditModal from '@/components/admin/media/VideoEditModal';

interface Video {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  duration: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  category: string;
  tags: string[];
  status: 'published' | 'draft' | 'processing' | 'failed';
  publishedAt?: string;
  createdAt: string;
  views: number;
  likes: number;
  comments: number;
  fileSize: string;
  resolution: string;
}

const VideosPage: React.FC = () => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedVideos, setSelectedVideos] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20
  });

  // Modal states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);

  const columns: DataTableColumn<Video>[] = [
    {
      key: 'title',
      title: 'Video Info',
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img 
              src={record.thumbnail} 
              alt={record.title}
              className="w-16 h-12 object-cover rounded"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <Play className="w-4 h-4 text-white" />
            </div>
            <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
              {record.duration}
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900 truncate max-w-xs">{record.title}</div>
            <div className="text-sm text-gray-500 flex items-center">
              <User className="w-3 h-3 mr-1" />
              {record.author.name}
            </div>
            <div className="text-xs text-gray-400">{record.resolution} • {record.fileSize}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-purple-100 text-purple-800">
          {value || 'Uncategorized'}
        </span>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value) => {
        const statusConfig = {
          published: { bg: 'bg-green-100', text: 'text-green-800', label: 'Published' },
          draft: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Draft' },
          processing: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Processing' },
          failed: { bg: 'bg-red-100', text: 'text-red-800', label: 'Failed' }
        };
        const config = statusConfig[value as keyof typeof statusConfig];
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.bg} ${config.text}`}>
            {config.label}
          </span>
        );
      }
    },
    {
      key: 'views',
      title: 'Statistics',
      render: (_, record) => (
        <div className="text-sm text-gray-900">
          <div className="flex items-center">
            <Eye className="w-3 h-3 mr-1" />
            {record.views.toLocaleString()}
          </div>
          <div className="flex items-center">
            <Heart className="w-3 h-3 mr-1" />
            {record.likes.toLocaleString()}
          </div>
          <div className="flex items-center">
            <MessageCircle className="w-3 h-3 mr-1" />
            {record.comments.toLocaleString()}
          </div>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          <div className="flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            {new Date(value).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handlePreview(record)}
            className="text-blue-600 hover:text-blue-900 text-sm flex items-center"
            title="Preview Video"
          >
            <Play className="w-3 h-3 mr-1" />
            Preview
          </button>
          <button
            onClick={() => handleEdit(record)}
            className="text-green-600 hover:text-green-900 text-sm flex items-center"
            title="Edit Video"
          >
            <Edit className="w-3 h-3 mr-1" />
            Edit
          </button>
          <button
            onClick={() => handleDownload(record)}
            className="text-purple-600 hover:text-purple-900 text-sm flex items-center"
            title="Download Video"
          >
            <Download className="w-3 h-3 mr-1" />
            Download
          </button>
          <button
            onClick={() => handleDelete(record)}
            className="text-red-600 hover:text-red-900 text-sm flex items-center"
            title="Delete Video"
          >
            <Trash2 className="w-3 h-3 mr-1" />
            Delete
          </button>
        </div>
      )
    }
  ];

  const fetchVideos = async () => {
    try {
      setLoading(true);
      // Mock data - in real app, this would fetch from API
      const mockVideos: Video[] = [
        {
          id: '1',
          title: 'Introduction to React Hooks',
          description: 'Learn the basics of React Hooks in this comprehensive tutorial',
          thumbnail: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=300&h=200&fit=crop',
          duration: '15:32',
          author: { id: '1', name: 'John Smith' },
          category: 'Technology',
          tags: ['React', 'JavaScript', 'Tutorial'],
          status: 'published',
          publishedAt: '2024-01-15T10:00:00Z',
          createdAt: '2024-01-15T09:00:00Z',
          views: 12450,
          likes: 892,
          comments: 156,
          fileSize: '245 MB',
          resolution: '1080p'
        },
        {
          id: '2',
          title: 'Advanced TypeScript Patterns',
          description: 'Deep dive into advanced TypeScript patterns and best practices',
          thumbnail: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=200&fit=crop',
          duration: '28:45',
          author: { id: '2', name: 'Sarah Johnson' },
          category: 'Technology',
          tags: ['TypeScript', 'Programming'],
          status: 'published',
          publishedAt: '2024-01-14T14:30:00Z',
          createdAt: '2024-01-14T13:00:00Z',
          views: 8760,
          likes: 654,
          comments: 89,
          fileSize: '512 MB',
          resolution: '1080p'
        },
        {
          id: '3',
          title: 'Building Modern Web Apps',
          description: 'Complete guide to building modern web applications',
          thumbnail: 'https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=300&h=200&fit=crop',
          duration: '42:18',
          author: { id: '3', name: 'Mike Chen' },
          category: 'Technology',
          tags: ['Web Development', 'JavaScript'],
          status: 'processing',
          createdAt: '2024-01-13T16:00:00Z',
          views: 0,
          likes: 0,
          comments: 0,
          fileSize: '1.2 GB',
          resolution: '4K'
        }
      ];

      setVideos(mockVideos);
      setPagination(prev => ({
        ...prev,
        total: mockVideos.length
      }));
    } catch (error) {
      console.error('Failed to fetch videos:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos();
  }, [pagination.current, searchQuery, categoryFilter, statusFilter]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchVideos();
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on videos:`, selectedVideos);
    alert(`Bulk ${action} action performed on ${selectedVideos.length} videos`);
  };

  // Action handlers
  const handlePreview = (video: Video) => {
    setSelectedVideo(video);
    setShowPreviewModal(true);
  };

  const handleEdit = (video: Video) => {
    setSelectedVideo(video);
    setShowEditModal(true);
  };

  const handleDownload = async (video: Video) => {
    try {
      // In a real app, this would download the video file
      const link = document.createElement('a');
      link.href = `/api/media/download/${video.id}`;
      link.download = video.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const handleDelete = async (video: Video) => {
    if (confirm(`Are you sure you want to delete "${video.title}"?`)) {
      try {
        // In a real app, this would call the delete API
        setVideos(prev => prev.filter(v => v.id !== video.id));
        alert('Video deleted successfully');
      } catch (error) {
        console.error('Delete failed:', error);
        alert('Delete failed. Please try again.');
      }
    }
  };

  const handleUploadSuccess = (newVideo: Video) => {
    setVideos(prev => [newVideo, ...prev]);
    setShowUploadModal(false);
  };

  const handleEditSuccess = (updatedVideo: Video) => {
    setVideos(prev => prev.map(v => v.id === updatedVideo.id ? updatedVideo : v));
    setShowEditModal(false);
    setSelectedVideo(null);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Video Management</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowUploadModal(true)}
            className="btn-primary flex items-center"
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload Video
          </button>
          <button className="btn-secondary flex items-center">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search video titles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Categories</option>
            <option value="technology">Technology</option>
            <option value="education">Education</option>
            <option value="entertainment">Entertainment</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="processing">Processing</option>
            <option value="failed">Failed</option>
          </select>
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* Bulk Actions */}
      {selectedVideos.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedVideos.length} videos
            </span>
            <div className="flex space-x-2">
              <button onClick={() => handleBulkAction('publish')} className="btn-sm btn-primary">Publish</button>
              <button onClick={() => handleBulkAction('unpublish')} className="btn-sm btn-secondary">Unpublish</button>
              <button onClick={() => handleBulkAction('delete')} className="btn-sm btn-danger">Delete</button>
            </div>
          </div>
        </div>
      )}

      {/* Video List */}
      <DataTable
        data={videos}
        columns={columns}
        loading={loading}
        pagination={{
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page) => setPagination(prev => ({ ...prev, current: page }))
        }}
        selection={{
          selectedRowKeys: selectedVideos,
          onChange: setSelectedVideos
        }}
      />

      {/* Modals */}
      {showUploadModal && (
        <VideoUploadModal
          onClose={() => setShowUploadModal(false)}
          onSuccess={handleUploadSuccess}
        />
      )}

      {showPreviewModal && selectedVideo && (
        <VideoPreviewModal
          video={selectedVideo}
          onClose={() => {
            setShowPreviewModal(false);
            setSelectedVideo(null);
          }}
        />
      )}

      {showEditModal && selectedVideo && (
        <VideoEditModal
          video={selectedVideo}
          onClose={() => {
            setShowEditModal(false);
            setSelectedVideo(null);
          }}
          onSuccess={handleEditSuccess}
        />
      )}
    </div>
  );
};

export default VideosPage;
