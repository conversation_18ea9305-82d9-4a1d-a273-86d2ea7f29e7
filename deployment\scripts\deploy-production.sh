#!/bin/bash

# OneNews 生产环境部署脚本
# 包含安全检查、备份、部署、健康检查等完整流程

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
PROJECT_NAME="OneNews"
BACKUP_DIR="/var/backups/onenews"
LOG_FILE="/var/log/onenews-deploy.log"
HEALTH_CHECK_URL="https://onenews.com/api/health"
ROLLBACK_ENABLED=true

echo -e "${BLUE}🚀 $PROJECT_NAME 生产环境部署开始${NC}"
echo "=================================="

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 错误处理
error_exit() {
    echo -e "${RED}❌ 错误: $1${NC}" | tee -a "$LOG_FILE"
    if [ "$ROLLBACK_ENABLED" = true ]; then
        echo -e "${YELLOW}🔄 开始回滚...${NC}"
        rollback
    fi
    exit 1
}

# 成功消息
success() {
    echo -e "${GREEN}✅ $1${NC}" | tee -a "$LOG_FILE"
}

# 警告消息
warning() {
    echo -e "${YELLOW}⚠️  $1${NC}" | tee -a "$LOG_FILE"
}

# 信息消息
info() {
    echo -e "${BLUE}ℹ️  $1${NC}" | tee -a "$LOG_FILE"
}

# 检查必需的工具
check_prerequisites() {
    info "检查部署前置条件..."
    
    local tools=("docker" "docker-compose" "git" "curl" "jq")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            error_exit "缺少必需工具: $tool"
        fi
    done
    
    # 检查Docker服务
    if ! docker info &> /dev/null; then
        error_exit "Docker服务未运行"
    fi
    
    success "前置条件检查通过"
}

# 环境变量验证
validate_environment() {
    info "验证环境变量..."
    
    if [ ! -f ".env.production" ]; then
        error_exit "缺少生产环境配置文件 .env.production"
    fi
    
    # 运行环境变量验证脚本
    if ! node Backend/scripts/validateEnv.js; then
        error_exit "环境变量验证失败"
    fi
    
    success "环境变量验证通过"
}

# 安全检查
security_check() {
    info "执行安全检查..."
    
    # 检查SSL证书
    if [ ! -f "config/ssl/prod/certificate.crt" ] || [ ! -f "config/ssl/prod/private.key" ]; then
        warning "SSL证书文件不存在，请确保已正确配置"
    fi
    
    # 检查敏感文件权限
    local sensitive_files=(".env.production" "config/ssl/prod/private.key")
    for file in "${sensitive_files[@]}"; do
        if [ -f "$file" ]; then
            chmod 600 "$file"
            info "已设置 $file 的安全权限"
        fi
    done
    
    success "安全检查完成"
}

# 创建备份
create_backup() {
    info "创建部署前备份..."
    
    local backup_timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/$backup_timestamp"
    
    mkdir -p "$backup_path"
    
    # 备份数据库
    if docker-compose -f docker-compose.prod.yml exec -T postgres pg_dump -U "$DB_USER" "$DB_NAME" > "$backup_path/database.sql"; then
        success "数据库备份完成"
    else
        warning "数据库备份失败"
    fi
    
    # 备份上传文件
    if [ -d "uploads" ]; then
        cp -r uploads "$backup_path/"
        success "文件备份完成"
    fi
    
    # 备份配置文件
    cp .env.production "$backup_path/"
    
    echo "$backup_path" > .last_backup
    success "备份创建完成: $backup_path"
}

# 构建镜像
build_images() {
    info "构建Docker镜像..."
    
    # 构建前端镜像
    if docker build -f Frontend/Dockerfile.prod -t onenews-frontend:latest Frontend/; then
        success "前端镜像构建完成"
    else
        error_exit "前端镜像构建失败"
    fi
    
    # 构建后端镜像
    if docker build -f Backend/Dockerfile.prod -t onenews-backend:latest Backend/; then
        success "后端镜像构建完成"
    else
        error_exit "后端镜像构建失败"
    fi
}

# 部署服务
deploy_services() {
    info "部署服务..."
    
    # 停止现有服务
    docker-compose -f docker-compose.prod.yml down
    
    # 启动新服务
    if docker-compose -f docker-compose.prod.yml up -d; then
        success "服务部署完成"
    else
        error_exit "服务部署失败"
    fi
    
    # 等待服务启动
    info "等待服务启动..."
    sleep 30
}

# 健康检查
health_check() {
    info "执行健康检查..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$HEALTH_CHECK_URL" > /dev/null; then
            success "健康检查通过"
            return 0
        fi
        
        info "健康检查尝试 $attempt/$max_attempts 失败，等待重试..."
        sleep 10
        attempt=$((attempt + 1))
    done
    
    error_exit "健康检查失败，服务可能未正常启动"
}

# 运行测试
run_tests() {
    info "运行部署后测试..."
    
    # API测试
    local api_tests=(
        "/api/health"
        "/api/articles"
        "/api/categories"
    )
    
    for endpoint in "${api_tests[@]}"; do
        if curl -f -s "https://onenews.com$endpoint" > /dev/null; then
            success "API测试通过: $endpoint"
        else
            warning "API测试失败: $endpoint"
        fi
    done
}

# 回滚函数
rollback() {
    warning "开始回滚到上一个版本..."
    
    if [ -f ".last_backup" ]; then
        local backup_path=$(cat .last_backup)
        
        # 停止当前服务
        docker-compose -f docker-compose.prod.yml down
        
        # 恢复数据库
        if [ -f "$backup_path/database.sql" ]; then
            docker-compose -f docker-compose.prod.yml exec -T postgres psql -U "$DB_USER" -d "$DB_NAME" < "$backup_path/database.sql"
            success "数据库回滚完成"
        fi
        
        # 恢复文件
        if [ -d "$backup_path/uploads" ]; then
            rm -rf uploads
            cp -r "$backup_path/uploads" .
            success "文件回滚完成"
        fi
        
        # 启动服务
        docker-compose -f docker-compose.prod.yml up -d
        
        success "回滚完成"
    else
        error_exit "无法找到备份文件，回滚失败"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    info "清理旧备份..."
    
    # 保留最近7天的备份
    find "$BACKUP_DIR" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    
    success "旧备份清理完成"
}

# 发送通知
send_notification() {
    local status=$1
    local message=$2
    
    # 这里可以集成Slack、钉钉等通知服务
    info "部署通知: $status - $message"
    
    # 示例：发送到Slack
    # curl -X POST -H 'Content-type: application/json' \
    #   --data "{\"text\":\"$PROJECT_NAME 部署$status: $message\"}" \
    #   "$SLACK_WEBHOOK_URL"
}

# 主部署流程
main() {
    local start_time=$(date +%s)
    
    log "开始 $PROJECT_NAME 生产环境部署"
    
    # 执行部署步骤
    check_prerequisites
    validate_environment
    security_check
    create_backup
    build_images
    deploy_services
    health_check
    run_tests
    cleanup_old_backups
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    success "部署完成！耗时: ${duration}秒"
    send_notification "成功" "部署完成，耗时${duration}秒"
    
    # 显示服务状态
    echo -e "\n${BLUE}📊 服务状态:${NC}"
    docker-compose -f docker-compose.prod.yml ps
    
    echo -e "\n${GREEN}🎉 $PROJECT_NAME 已成功部署到生产环境！${NC}"
    echo -e "${BLUE}🌐 访问地址: https://onenews.com${NC}"
    echo -e "${BLUE}📊 监控地址: https://onenews.com:3001${NC}"
}

# 脚本参数处理
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "rollback")
        rollback
        ;;
    "health")
        health_check
        ;;
    "backup")
        create_backup
        ;;
    *)
        echo "用法: $0 {deploy|rollback|health|backup}"
        exit 1
        ;;
esac
