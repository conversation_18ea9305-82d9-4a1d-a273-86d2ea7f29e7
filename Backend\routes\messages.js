const express = require('express');
const router = express.Router();
const { Message, User } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

// Send a message
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { receiverId, content, messageType = 'text', attachmentUrl } = req.body;
    const senderId = req.user.id;

    // Validate input
    if (!receiverId || !content) {
      return res.status(400).json({
        success: false,
        message: 'Receiver ID and content are required',
      });
    }

    // Check if receiver exists
    const receiver = await User.findByPk(receiverId);
    if (!receiver) {
      return res.status(404).json({
        success: false,
        message: 'Receiver not found',
      });
    }

    // Prevent sending message to self
    if (senderId === parseInt(receiverId)) {
      return res.status(400).json({
        success: false,
        message: 'You cannot send a message to yourself',
      });
    }

    // Create message
    const message = await Message.create({
      senderId,
      receiverId,
      content,
      messageType,
      attachmentUrl,
    });

    // Include sender and receiver info in response
    const messageWithUsers = await Message.findByPk(message.id, {
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar'],
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'avatar'],
        },
      ],
    });

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: messageWithUsers,
    });
  } catch (error) {
    console.error('Error sending message:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get conversation between two users
router.get('/conversation/:userId', authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;

    const { count, rows: messages } = await Message.findAndCountAll({
      where: {
        [Op.or]: [
          { senderId: currentUserId, receiverId: parseInt(userId) },
          { senderId: parseInt(userId), receiverId: currentUserId },
        ],
        isDeleted: false,
        deletedBy: {
          [Op.not]: {
            [Op.contains]: [currentUserId],
          },
        },
      },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar'],
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'avatar'],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset,
    });

    // Mark messages as read
    await Message.update(
      { isRead: true, readAt: new Date() },
      {
        where: {
          senderId: parseInt(userId),
          receiverId: currentUserId,
          isRead: false,
        },
      }
    );

    res.json({
      success: true,
      data: {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get all conversations for current user
router.get('/conversations', authenticateToken, async (req, res) => {
  try {
    const currentUserId = req.user.id;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Get latest message for each conversation
    const conversations = await Message.findAll({
      where: {
        [Op.or]: [{ senderId: currentUserId }, { receiverId: currentUserId }],
        isDeleted: false,
        deletedBy: {
          [Op.not]: {
            [Op.contains]: [currentUserId],
          },
        },
      },
      include: [
        {
          model: User,
          as: 'sender',
          attributes: ['id', 'username', 'avatar'],
        },
        {
          model: User,
          as: 'receiver',
          attributes: ['id', 'username', 'avatar'],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: 1000, // Get enough messages to find unique conversations
    });

    // Group by conversation partner
    const conversationMap = new Map();

    conversations.forEach((message) => {
      const partnerId = message.senderId === currentUserId ? message.receiverId : message.senderId;

      if (!conversationMap.has(partnerId)) {
        const partner = message.senderId === currentUserId ? message.receiver : message.sender;

        conversationMap.set(partnerId, {
          partner,
          lastMessage: message,
          unreadCount: 0,
        });
      }
    });

    // Count unread messages for each conversation
    for (const [partnerId, conversation] of conversationMap) {
      const unreadCount = await Message.count({
        where: {
          senderId: partnerId,
          receiverId: currentUserId,
          isRead: false,
          isDeleted: false,
        },
      });
      conversation.unreadCount = unreadCount;
    }

    // Convert to array and paginate
    const conversationsArray = Array.from(conversationMap.values())
      .sort((a, b) => new Date(b.lastMessage.createdAt) - new Date(a.lastMessage.createdAt))
      .slice(offset, offset + limit);

    res.json({
      success: true,
      data: {
        conversations: conversationsArray,
        pagination: {
          page,
          limit,
          total: conversationMap.size,
          pages: Math.ceil(conversationMap.size / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Mark message as read
router.patch('/:messageId/read', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const currentUserId = req.user.id;

    const message = await Message.findByPk(messageId);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found',
      });
    }

    // Only receiver can mark message as read
    if (message.receiverId !== currentUserId) {
      return res.status(403).json({
        success: false,
        message: 'You can only mark your own received messages as read',
      });
    }

    await message.update({
      isRead: true,
      readAt: new Date(),
    });

    res.json({
      success: true,
      message: 'Message marked as read',
    });
  } catch (error) {
    console.error('Error marking message as read:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Delete message
router.delete('/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const currentUserId = req.user.id;

    const message = await Message.findByPk(messageId);
    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found',
      });
    }

    // Add user to deletedBy array
    const deletedBy = message.deletedBy || [];
    if (!deletedBy.includes(currentUserId)) {
      deletedBy.push(currentUserId);
    }

    // If both users have deleted, mark as deleted
    const isDeleted =
      deletedBy.length === 2 ||
      (deletedBy.includes(message.senderId) && deletedBy.includes(message.receiverId));

    await message.update({
      deletedBy,
      isDeleted,
    });

    res.json({
      success: true,
      message: 'Message deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get unread message count
router.get('/unread/count', authenticateToken, async (req, res) => {
  try {
    const currentUserId = req.user.id;

    const unreadCount = await Message.count({
      where: {
        receiverId: currentUserId,
        isRead: false,
        isDeleted: false,
      },
    });

    res.json({
      success: true,
      data: { unreadCount },
    });
  } catch (error) {
    console.error('Error fetching unread count:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

module.exports = router;


