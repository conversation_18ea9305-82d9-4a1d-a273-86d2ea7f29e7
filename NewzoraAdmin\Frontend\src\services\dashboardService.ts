import { supabaseService } from './supabaseService';
import { DashboardStats } from '@/types/admin';
import { DateRange } from '@/components/admin/common/DateRangeSelector';

interface DashboardFilters {
  dateRange?: DateRange;
  timeRange?: string;
}

export const dashboardService = {
  // 获取仪表板统计数据
  async getStats(filters?: DashboardFilters): Promise<DashboardStats> {
    try {
      return await supabaseService.getDashboardStats(filters);
    } catch (error) {
      console.error('获取仪表板统计失败:', error);
      throw error;
    }
  },

  // 获取用户增长趋势
  async getUserGrowthTrend(filters?: DashboardFilters) {
    try {
      const params = new URLSearchParams();
      if (filters?.dateRange) {
        params.append('startDate', filters.dateRange.startDate.toISOString());
        params.append('endDate', filters.dateRange.endDate.toISOString());
      }
      if (filters?.timeRange) {
        params.append('timeRange', filters.timeRange);
      }

      const response = await api.get(`/api/admin/dashboard/user-growth?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('获取用户增长趋势失败:', error);
      throw error;
    }
  },

  // 获取内容发布趋势
  async getContentTrend(filters?: DashboardFilters) {
    try {
      const params = new URLSearchParams();
      if (filters?.dateRange) {
        params.append('startDate', filters.dateRange.startDate.toISOString());
        params.append('endDate', filters.dateRange.endDate.toISOString());
      }
      if (filters?.timeRange) {
        params.append('timeRange', filters.timeRange);
      }

      const response = await api.get(`/api/admin/dashboard/content-trend?${params.toString()}`);
      return response.data.data;
    } catch (error) {
      console.error('获取内容趋势失败:', error);
      throw error;
    }
  },

  // 获取分类统计
  async getCategoryStats() {
    try {
      const response = await api.get('/api/admin/dashboard/category-stats');
      return response.data.data;
    } catch (error) {
      console.error('获取分类统计失败:', error);
      throw error;
    }
  }
};