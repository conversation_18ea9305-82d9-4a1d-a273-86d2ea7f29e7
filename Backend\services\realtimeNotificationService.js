const Notification = require('../models/Notification');
const User = require('../models/User');
const Article = require('../models/Article');

class RealtimeNotificationService {
  constructor(io) {
    this.io = io;
    this.userSockets = new Map(); // userId -> socketId mapping
  }

  // 用户连接时注册socket
  registerUser(userId, socketId) {
    this.userSockets.set(userId, socketId);
    console.log(`用户 ${userId} 已连接，socket: ${socketId}`);
  }

  // 用户断开连接时清理
  unregisterUser(userId) {
    this.userSockets.delete(userId);
    console.log(`用户 ${userId} 已断开连接`);
  }

  // 创建并发送通知
  async createAndSendNotification(notificationData) {
    try {
      // 创建通知记录
      const notification = await Notification.create(notificationData);
      
      // 获取完整的通知信息（包含关联数据）
      const fullNotification = await this.getNotificationWithDetails(notification.id);
      
      // 实时发送给用户
      this.sendToUser(notificationData.userId, 'new_notification', fullNotification);
      
      return fullNotification;
    } catch (error) {
      console.error('创建通知失败:', error);
      throw error;
    }
  }

  // 获取通知详细信息
  async getNotificationWithDetails(notificationId) {
    const notification = await Notification.findByPk(notificationId, {
      include: [
        {
          model: User,
          as: 'fromUser',
          attributes: ['id', 'username', 'display_name', 'avatar_url'],
        },
        {
          model: Article,
          as: 'article',
          attributes: ['id', 'title', 'image'],
        },
      ],
    });

    return notification;
  }

  // 发送通知给特定用户
  sendToUser(userId, event, data) {
    const socketId = this.userSockets.get(userId);
    if (socketId) {
      this.io.to(socketId).emit(event, data);
      console.log(`通知已发送给用户 ${userId}:`, event);
    }
  }

  // 广播通知给所有在线用户
  broadcast(event, data) {
    this.io.emit(event, data);
  }

  // 创建点赞通知
  async createLikeNotification(articleId, fromUserId) {
    const article = await Article.findByPk(articleId);
    if (!article) return;

    const fromUser = await User.findByPk(fromUserId);
    if (!fromUser) return;

    // 不给自己发通知
    if (article.authorId === fromUserId) return;

    return await this.createAndSendNotification({
      userId: article.authorId,
      type: 'like',
      title: '新的点赞',
      content: `${fromUser.display_name || fromUser.username} 点赞了您的文章《${article.title}》`,
      articleId: articleId,
      fromUserId: fromUserId,
      actionUrl: `/articles/${articleId}`,
      data: {
        articleTitle: article.title,
        fromUserName: fromUser.display_name || fromUser.username,
      },
    });
  }

  // 创建评论通知
  async createCommentNotification(articleId, fromUserId, commentContent) {
    const article = await Article.findByPk(articleId);
    if (!article) return;

    const fromUser = await User.findByPk(fromUserId);
    if (!fromUser) return;

    // 不给自己发通知
    if (article.authorId === fromUserId) return;

    return await this.createAndSendNotification({
      userId: article.authorId,
      type: 'comment',
      title: '新的评论',
      content: `${fromUser.display_name || fromUser.username} 评论了您的文章《${article.title}》: ${commentContent.substring(0, 50)}...`,
      articleId: articleId,
      fromUserId: fromUserId,
      actionUrl: `/articles/${articleId}#comments`,
      data: {
        articleTitle: article.title,
        fromUserName: fromUser.display_name || fromUser.username,
        commentContent: commentContent,
      },
    });
  }

  // 创建关注通知
  async createFollowNotification(followedUserId, fromUserId) {
    const fromUser = await User.findByPk(fromUserId);
    if (!fromUser) return;

    return await this.createAndSendNotification({
      userId: followedUserId,
      type: 'follow',
      title: '新的关注者',
      content: `${fromUser.display_name || fromUser.username} 开始关注您`,
      fromUserId: fromUserId,
      actionUrl: `/profile/${fromUser.username}`,
      data: {
        fromUserName: fromUser.display_name || fromUser.username,
      },
    });
  }

  // 获取用户未读通知数量
  async getUnreadCount(userId) {
    return await Notification.getUnreadCount(userId);
  }

  // 获取用户通知列表
  async getUserNotifications(userId, options = {}) {
    return await Notification.getUserNotifications(userId, options);
  }

  // 标记通知为已读
  async markAsRead(notificationId, userId) {
    const notification = await Notification.findOne({
      where: { id: notificationId, userId },
    });

    if (notification && !notification.isRead) {
      await notification.markAsRead();
      
      // 发送未读数量更新
      const unreadCount = await this.getUnreadCount(userId);
      this.sendToUser(userId, 'unread_count_update', { count: unreadCount });
    }

    return notification;
  }

  // 标记所有通知为已读
  async markAllAsRead(userId) {
    await Notification.markAllAsRead(userId);
    this.sendToUser(userId, 'unread_count_update', { count: 0 });
  }
}

module.exports = RealtimeNotificationService;