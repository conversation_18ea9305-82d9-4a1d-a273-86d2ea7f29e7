/**
 * 用户体验测试套件
 */

const request = require('supertest');

// 模拟应用用于UX测试
const createUXTestApp = () => {
  const express = require('express');
  const app = express();
  
  app.use(express.json());
  
  // 用户认证端点
  app.post('/api/auth/login', (req, res) => {
    const { email, password } = req.body;
    
    // 输入验证
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required',
        errors: {
          email: !email ? 'Email is required' : null,
          password: !password ? 'Password is required' : null
        }
      });
    }
    
    // 模拟认证
    if (email === '<EMAIL>' && password === 'password123') {
      res.json({
        success: true,
        message: 'Login successful',
        user: { id: 1, email, name: 'Test User' },
        token: 'fake-jwt-token'
      });
    } else {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }
  });
  
  // 用户注册端点
  app.post('/api/auth/register', (req, res) => {
    const { username, email, password } = req.body;
    
    const errors = {};
    
    // 验证用户名
    if (!username) {
      errors.username = 'Username is required';
    } else if (username.length < 3) {
      errors.username = 'Username must be at least 3 characters long';
    }
    
    // 验证邮箱
    if (!email) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
    }
    
    // 验证密码
    if (!password) {
      errors.password = 'Password is required';
    } else if (password.length < 8) {
      errors.password = 'Password must be at least 8 characters long';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password)) {
      errors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }
    
    if (Object.keys(errors).length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }
    
    // 模拟成功注册
    res.status(201).json({
      success: true,
      message: 'Registration successful. Please check your email for verification.',
      user: { id: 2, username, email }
    });
  });
  
  // 文章创建端点
  app.post('/api/articles', (req, res) => {
    const { title, content, tags } = req.body;
    
    const errors = {};
    
    if (!title) {
      errors.title = 'Title is required';
    } else if (title.length < 5) {
      errors.title = 'Title must be at least 5 characters long';
    } else if (title.length > 200) {
      errors.title = 'Title must not exceed 200 characters';
    }
    
    if (!content) {
      errors.content = 'Content is required';
    } else if (content.length < 50) {
      errors.content = 'Content must be at least 50 characters long';
    }
    
    if (Object.keys(errors).length > 0) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
    }
    
    res.status(201).json({
      success: true,
      message: 'Article created successfully',
      article: {
        id: Date.now(),
        title,
        content,
        tags: tags || [],
        createdAt: new Date().toISOString()
      }
    });
  });
  
  // 搜索端点
  app.get('/api/search', (req, res) => {
    const { q, page = 1, limit = 10 } = req.query;
    
    if (!q || q.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }
    
    // 模拟搜索结果
    const mockResults = [
      { id: 1, title: `Article about ${q}`, excerpt: `This article discusses ${q}...` },
      { id: 2, title: `Understanding ${q}`, excerpt: `A comprehensive guide to ${q}...` }
    ];
    
    res.json({
      success: true,
      results: mockResults,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: mockResults.length,
        totalPages: 1
      }
    });
  });
  
  return app;
};

describe('User Experience Tests', () => {
  let app;

  beforeAll(() => {
    app = createUXTestApp();
  });

  describe('Error Handling and User Feedback', () => {
    test('should provide clear error messages for missing fields', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBeDefined();
      expect(response.body.errors).toBeDefined();
      expect(response.body.errors.email).toContain('required');
      expect(response.body.errors.password).toContain('required');
    });

    test('should provide specific validation errors', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'ab', // 太短
          email: 'invalid-email', // 无效格式
          password: '123' // 太短且不符合复杂度要求
        });

      expect(response.status).toBe(400);
      expect(response.body.errors.username).toContain('at least 3 characters');
      expect(response.body.errors.email).toContain('valid email');
      expect(response.body.errors.password).toContain('at least 8 characters');
    });

    test('should provide helpful success messages', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: 'Password123'
        });

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toContain('Registration successful');
      expect(response.body.message).toContain('check your email');
    });
  });

  describe('Input Validation and User Guidance', () => {
    test('should validate article creation with helpful messages', async () => {
      const response = await request(app)
        .post('/api/articles')
        .send({
          title: 'Hi', // 太短
          content: 'Short content' // 太短
        });

      expect(response.status).toBe(400);
      expect(response.body.errors.title).toContain('at least 5 characters');
      expect(response.body.errors.content).toContain('at least 50 characters');
    });

    test('should accept valid article creation', async () => {
      const validArticle = {
        title: 'This is a valid article title',
        content: 'This is a valid article content that is long enough to meet the minimum requirements for article creation.',
        tags: ['test', 'article']
      };

      const response = await request(app)
        .post('/api/articles')
        .send(validArticle);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.article.title).toBe(validArticle.title);
      expect(response.body.article.content).toBe(validArticle.content);
    });
  });

  describe('Search Experience', () => {
    test('should handle empty search queries gracefully', async () => {
      const response = await request(app)
        .get('/api/search')
        .query({ q: '' });

      expect(response.status).toBe(400);
      expect(response.body.message).toContain('Search query is required');
    });

    test('should provide search results with pagination', async () => {
      const response = await request(app)
        .get('/api/search')
        .query({ q: 'javascript', page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.results).toBeDefined();
      expect(response.body.pagination).toBeDefined();
      expect(response.body.pagination.page).toBe(1);
      expect(response.body.pagination.limit).toBe(10);
    });
  });

  describe('Response Time and Performance UX', () => {
    test('should respond quickly to login requests', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });
      
      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(500); // 应该在500ms内响应
    });

    test('should handle concurrent user requests efficiently', async () => {
      const concurrentUsers = 10;
      const requests = [];
      
      for (let i = 0; i < concurrentUsers; i++) {
        requests.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'password123'
            })
        );
      }
      
      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const totalTime = Date.now() - startTime;
      
      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
      
      // 并发处理应该高效
      expect(totalTime).toBeLessThan(2000); // 10个并发请求应该在2秒内完成
    });
  });

  describe('API Consistency and Standards', () => {
    test('should follow consistent response format', async () => {
      const responses = await Promise.all([
        request(app).post('/api/auth/login').send({ email: '<EMAIL>', password: 'password123' }),
        request(app).post('/api/auth/register').send({ username: 'test', email: '<EMAIL>', password: 'Password123' }),
        request(app).get('/api/search').query({ q: 'test' })
      ]);

      responses.forEach(response => {
        expect(response.body).toHaveProperty('success');
        expect(response.body).toHaveProperty('message');
        expect(typeof response.body.success).toBe('boolean');
        expect(typeof response.body.message).toBe('string');
      });
    });

    test('should use appropriate HTTP status codes', async () => {
      // 成功创建
      const createResponse = await request(app)
        .post('/api/articles')
        .send({
          title: 'Test Article Title',
          content: 'This is a test article content that meets the minimum length requirements.'
        });
      expect(createResponse.status).toBe(201);

      // 验证错误
      const validationResponse = await request(app)
        .post('/api/articles')
        .send({ title: 'Hi' });
      expect(validationResponse.status).toBe(400);

      // 认证错误
      const authResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'wrongpassword' });
      expect(authResponse.status).toBe(401);
    });
  });

  describe('Accessibility and Usability', () => {
    test('should provide meaningful error codes for programmatic handling', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({});

      expect(response.body).toHaveProperty('errors');
      expect(response.body.errors).toBeInstanceOf(Object);
      
      // 错误应该有具体的字段映射
      Object.keys(response.body.errors).forEach(field => {
        expect(['email', 'password']).toContain(field);
        expect(typeof response.body.errors[field]).toBe('string');
      });
    });

    test('should handle special characters in input gracefully', async () => {
      const specialCharsInput = {
        username: 'test用户123',
        email: '<EMAIL>',
        password: 'Password123!@#'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(specialCharsInput);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
    });

    test('should provide search suggestions for empty results', async () => {
      const response = await request(app)
        .get('/api/search')
        .query({ q: 'nonexistentterm12345' });

      expect(response.status).toBe(200);
      expect(response.body.results).toBeDefined();
      // 即使没有结果，也应该返回结构化的响应
      expect(Array.isArray(response.body.results)).toBe(true);
    });
  });

  describe('Mobile and Responsive Experience', () => {
    test('should handle mobile user agents appropriately', async () => {
      const mobileUserAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15';
      
      const response = await request(app)
        .get('/api/search')
        .set('User-Agent', mobileUserAgent)
        .query({ q: 'mobile test' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      // API应该对所有设备类型返回相同的数据结构
    });

    test('should handle slow network conditions gracefully', async () => {
      // 模拟慢网络条件下的请求
      const timeout = 5000; // 5秒超时
      
      const response = await request(app)
        .get('/api/search')
        .query({ q: 'test' })
        .timeout(timeout);

      expect(response.status).toBe(200);
      // 响应应该在合理时间内返回
    });
  });
});