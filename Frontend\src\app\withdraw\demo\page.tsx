'use client';

import { useState } from 'react';
import { 
  DollarSign, 
  CreditCard, 
  Shield, 
  CheckCircle, 
  Clock, 
  Star,
  Wallet,
  TrendingUp,
  Eye,
  EyeOff,
  ArrowRight,
  Globe,
  Zap
} from 'lucide-react';

export default function WithdrawDemoPage() {
  const [showBalance, setShowBalance] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedMethod, setSelectedMethod] = useState('bank_card');
  const [amount, setAmount] = useState('500');

  const balance = {
    availableBalance: 2450.75,
    pendingBalance: 150.00,
    totalEarnings: 12850.50
  };

  const paymentMethods = {
    bank_card: { 
      name: 'Bank Transfer', 
      fee: 2.5, 
      time: '1-3 business days', 
      icon: CreditCard,
      description: 'Direct transfer to your bank account',
      popular: true,
      security: 'High'
    },
    paypal: { 
      name: 'PayPal', 
      fee: 3.5, 
      time: 'Instant', 
      icon: DollarSign,
      description: 'Fast and secure PayPal transfer',
      popular: false,
      security: 'High'
    },
    crypto_btc: { 
      name: 'Bitcoin (BTC)', 
      fee: 1.5, 
      time: '30min-2hrs', 
      icon: Shield,
      description: 'Cryptocurrency transfer to your wallet',
      popular: true,
      security: 'Very High'
    },
    crypto_usdt: { 
      name: 'USDT (TRC20)', 
      fee: 1.0, 
      time: '5-15min', 
      icon: Zap,
      description: 'Fast stablecoin transfer',
      popular: true,
      security: 'Very High'
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            Withdraw Funds - Demo
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience our new modern withdrawal interface design
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4 mb-6">
            {[
              { step: 1, title: 'Amount', icon: DollarSign },
              { step: 2, title: 'Method', icon: CreditCard },
              { step: 3, title: 'Verify', icon: Shield },
              { step: 4, title: 'Confirm', icon: CheckCircle }
            ].map((item, index) => {
              const Icon = item.icon;
              const isActive = currentStep === item.step;
              const isCompleted = currentStep > item.step;
              
              return (
                <div key={item.step} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                    isCompleted 
                      ? 'bg-green-500 border-green-500 text-white' 
                      : isActive 
                        ? 'bg-blue-500 border-blue-500 text-white' 
                        : 'bg-white border-gray-300 text-gray-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="w-6 h-6" />
                    ) : (
                      <Icon className="w-6 h-6" />
                    )}
                  </div>
                  <span className={`ml-2 text-sm font-medium ${
                    isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    {item.title}
                  </span>
                  {index < 3 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      currentStep > item.step ? 'bg-green-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Account Balance Card */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
              <Wallet className="h-7 w-7 mr-3 text-blue-600" />
              Account Balance
            </h2>
            <button
              onClick={() => setShowBalance(!showBalance)}
              className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
            >
              {showBalance ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="relative overflow-hidden bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl p-6 text-white">
              <div className="relative z-10">
                <p className="text-green-100 text-sm font-medium mb-2">Available Balance</p>
                <p className="text-3xl font-bold">
                  {showBalance ? `$${balance.availableBalance}` : '••••••'}
                </p>
                <div className="flex items-center mt-2 text-green-100">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  <span className="text-xs">Ready to withdraw</span>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -mr-10 -mt-10" />
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl p-6 text-white">
              <div className="relative z-10">
                <p className="text-yellow-100 text-sm font-medium mb-2">Pending</p>
                <p className="text-2xl font-bold">
                  {showBalance ? `$${balance.pendingBalance}` : '••••••'}
                </p>
                <div className="flex items-center mt-2 text-yellow-100">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-xs">Processing</span>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -mr-10 -mt-10" />
            </div>
            
            <div className="relative overflow-hidden bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl p-6 text-white">
              <div className="relative z-10">
                <p className="text-blue-100 text-sm font-medium mb-2">Total Earned</p>
                <p className="text-2xl font-bold">
                  {showBalance ? `$${balance.totalEarnings}` : '••••••'}
                </p>
                <div className="flex items-center mt-2 text-blue-100">
                  <Star className="h-4 w-4 mr-1" />
                  <span className="text-xs">All time</span>
                </div>
              </div>
              <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -mr-10 -mt-10" />
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8">
          <div className="flex items-center mb-8">
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900">Withdraw Funds</h2>
              <p className="text-gray-600">Choose amount and payment method</p>
            </div>
          </div>
          
          {/* Amount Input */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Withdrawal Amount (USD)
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <DollarSign className="h-6 w-6 text-gray-400" />
              </div>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full pl-12 pr-4 py-4 text-xl font-semibold border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white/80 backdrop-blur-sm"
                placeholder="100.00"
              />
            </div>
            <div className="flex justify-between items-center mt-3">
              <p className="text-sm text-gray-600">
                Available: <span className="font-semibold text-green-600">${balance.availableBalance}</span>
              </p>
              <div className="flex space-x-2">
                {[100, 500, 1000].map((preset) => (
                  <button
                    key={preset}
                    onClick={() => setAmount(preset.toString())}
                    className="px-3 py-1 text-xs bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    ${preset}
                  </button>
                ))}
                <button
                  onClick={() => setAmount(balance.availableBalance.toString())}
                  className="px-3 py-1 text-xs bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  Max
                </button>
              </div>
            </div>
          </div>

          {/* Payment Methods */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Payment Method</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(paymentMethods).map(([key, method]) => {
                const Icon = method.icon;
                const isSelected = selectedMethod === key;
                return (
                  <button
                    key={key}
                    onClick={() => setSelectedMethod(key)}
                    className={`relative p-6 border-2 rounded-xl text-left transition-all duration-300 transform hover:scale-105 ${
                      isSelected
                        ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-lg'
                        : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                    }`}
                  >
                    {method.popular && (
                      <div className="absolute -top-2 -right-2 bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-2 py-1 rounded-full font-bold">
                        Popular
                      </div>
                    )}
                    
                    <div className="flex items-center mb-3">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center mr-3 ${
                        isSelected ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'
                      }`}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div>
                        <span className="font-semibold text-gray-900">{method.name}</span>
                        {isSelected && <CheckCircle className="inline h-4 w-4 text-blue-500 ml-2" />}
                      </div>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{method.description}</p>
                    
                    <div className="flex justify-between items-center text-sm">
                      <div className="flex items-center text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {method.time}
                      </div>
                      <div className="flex items-center">
                        <span className="text-gray-500 mr-1">Fee:</span>
                        <span className="font-semibold text-gray-900">{method.fee}%</span>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Action Button */}
          <button
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            <ArrowRight className="h-5 w-5 mr-2" />
            Continue to Verification
          </button>
        </div>

        {/* Demo Notice */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            <Star className="h-4 w-4 mr-2" />
            This is a demo of the new withdrawal interface design
          </div>
        </div>
      </div>
    </div>
  );
}
