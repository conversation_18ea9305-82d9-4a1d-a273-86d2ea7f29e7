const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Article = sequelize.define('Article', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
    validate: {
      len: [1, 255]
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  excerpt: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  authorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: [],
    allowNull: true
  },
  published: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false
  },
  featured_image: {
    type: DataTypes.STRING(500),
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  views: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  likes: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    allowNull: false
  },
  reading_time: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '预估阅读时间（分钟）'
  },
  seo_title: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  seo_description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  published_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  }
}, {
  tableName: 'articles',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  hooks: {
    beforeCreate: (article) => {
      // 自动生成摘要
      if (!article.excerpt && article.content) {
        article.excerpt = article.content.substring(0, 200) + '...';
      }
      
      // 计算阅读时间（假设每分钟200字）
      if (article.content) {
        const wordCount = article.content.length;
        article.reading_time = Math.ceil(wordCount / 200);
      }
    },
    beforeUpdate: (article) => {
      if (article.changed('content')) {
        // 更新摘要
        if (!article.excerpt) {
          article.excerpt = article.content.substring(0, 200) + '...';
        }
        
        // 重新计算阅读时间
        const wordCount = article.content.length;
        article.reading_time = Math.ceil(wordCount / 200);
      }
      
      // 设置发布时间
      if (article.changed('published') && article.published && !article.published_at) {
        article.published_at = new Date();
      }
    }
  }
});

module.exports = Article;