const express = require('express');
const router = express.Router();

// 导入子路由
const dashboardRoutes = require('./dashboard');
const userRoutes = require('./users');
const contentRoutes = require('./content');
const analyticsRoutes = require('./analytics');
const settingsRoutes = require('./settings');

// 导入中间件
const { authenticateAdmin } = require('../../middleware/adminAuth');

// 应用管理员认证中间件到所有管理路由
router.use(authenticateAdmin);

// 注册子路由
router.use('/dashboard', dashboardRoutes);
router.use('/users', userRoutes);
router.use('/content', contentRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/settings', settingsRoutes);

// 管理员信息路由
router.get('/me', (req, res) => {
  res.json({
    success: true,
    data: req.user.toSafeJSON()
  });
});

module.exports = router;