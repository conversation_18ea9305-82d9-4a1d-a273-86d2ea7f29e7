import { useState, useEffect, useCallback } from 'react';
import dataSyncService, { SyncStatus } from '@/lib/dataSync';

export interface DataSyncHookReturn {
  syncStatus: SyncStatus;
  isLoading: boolean;
  forceSyncNow: () => Promise<void>;
  retryPendingOperations: () => Promise<void>;
  clearErrors: () => void;
  startAutoSync: () => void;
  stopAutoSync: () => void;
}

export const useDataSync = (): DataSyncHookReturn => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>(dataSyncService.getSyncStatus());
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Subscribe to sync status changes
    const unsubscribe = dataSyncService.onStatusChange((status) => {
      setSyncStatus(status);
    });

    // Cleanup subscription on unmount
    return unsubscribe;
  }, []);

  const forceSyncNow = useCallback(async () => {
    setIsLoading(true);
    try {
      await dataSyncService.forceSyncNow();
    } catch (error) {
      console.error('Force sync failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const retryPendingOperations = useCallback(async () => {
    setIsLoading(true);
    try {
      await dataSyncService.retryPendingOperations();
    } catch (error) {
      console.error('Retry pending operations failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const clearErrors = useCallback(() => {
    dataSyncService.clearErrors();
  }, []);

  const startAutoSync = useCallback(() => {
    dataSyncService.startAutoSync();
  }, []);

  const stopAutoSync = useCallback(() => {
    dataSyncService.stopAutoSync();
  }, []);

  return {
    syncStatus,
    isLoading,
    forceSyncNow,
    retryPendingOperations,
    clearErrors,
    startAutoSync,
    stopAutoSync
  };
};

export default useDataSync;
