'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

// 用户类型定义
interface User {
  id: string;
  email: string;
  username: string;
  display_name?: string;
  avatar_url?: string;
  role: string;
  created_at: string;
}

// 认证上下文类型
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<boolean>;
  register: (email: string, password: string, metadata?: any) => Promise<{data: any, error: any}>;
  logout: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

// 模拟用户数据存储
const mockUsers = new Map<string, { email: string; password: string; user: User }>();

// 添加默认用户
mockUsers.set('<EMAIL>', {
  email: '<EMAIL>',
  password: 'demo123456',
  user: {
    id: '1',
    email: '<EMAIL>',
    username: 'demo',
    display_name: 'Demo User',
    role: 'user',
    created_at: new Date().toISOString()
  }
});

export function StableAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const router = useRouter();

  // 初始化认证状态
  useEffect(() => {
    const initAuth = () => {
      try {
        const savedUser = localStorage.getItem('auth_user');
        if (savedUser) {
          const userData = JSON.parse(savedUser);
          setUser(userData);
          setIsAuthenticated(true);
        }
      } catch (err) {
        console.error('Auth init error:', err);
        localStorage.removeItem('auth_user');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      const userData = mockUsers.get(email.toLowerCase());
      
      if (!userData || userData.password !== password) {
        setError('Invalid email or password');
        return false;
      }

      setUser(userData.user);
      setIsAuthenticated(true);
      localStorage.setItem('auth_user', JSON.stringify(userData.user));
      
      return true;
    } catch (err: any) {
      setError(err.message || 'Login failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (email: string, password: string, metadata?: any): Promise<{data: any, error: any}> => {
    try {
      setIsLoading(true);
      setError(null);

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      if (mockUsers.has(email.toLowerCase())) {
        const error = { message: 'User already exists' };
        setError(error.message);
        return { data: null, error };
      }

      const newUser: User = {
        id: Date.now().toString(),
        email: email.toLowerCase(),
        username: metadata?.username || email.split('@')[0],
        display_name: metadata?.name || email.split('@')[0],
        role: 'user',
        created_at: new Date().toISOString()
      };

      mockUsers.set(email.toLowerCase(), {
        email: email.toLowerCase(),
        password,
        user: newUser
      });

      setUser(newUser);
      setIsAuthenticated(true);
      localStorage.setItem('auth_user', JSON.stringify(newUser));

      return { data: { user: newUser }, error: null };
    } catch (err: any) {
      const error = { message: err.message || 'Registration failed' };
      setError(error.message);
      return { data: null, error };
    } finally {
      setIsLoading(false);
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
      localStorage.removeItem('auth_user');
    } catch (err) {
      console.error('Logout error:', err);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const value: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export const useStableAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useStableAuth must be used within StableAuthProvider');
  }
  return context;
};