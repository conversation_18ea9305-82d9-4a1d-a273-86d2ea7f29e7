'use client';

import React from 'react';
import Link from 'next/link';

interface TrendingTopicsProps {
  className?: string;
}

const trendingTopics = [
  {
    id: 1,
    name: 'AI Technology',
    count: '2.4k posts',
    trend: 'up',
    category: 'technology'
  },
  {
    id: 2,
    name: 'Web Development',
    count: '1.8k posts',
    trend: 'up',
    category: 'technology'
  },
  {
    id: 3,
    name: 'Digital Marketing',
    count: '1.2k posts',
    trend: 'stable',
    category: 'business'
  },
  {
    id: 4,
    name: 'Remote Work',
    count: '956 posts',
    trend: 'up',
    category: 'lifestyle'
  },
  {
    id: 5,
    name: 'Cryptocurrency',
    count: '743 posts',
    trend: 'down',
    category: 'finance'
  }
];

const TrendingTopics: React.FC<TrendingTopicsProps> = ({ className = '' }) => {
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <span className="text-green-500">↗</span>;
      case 'down':
        return <span className="text-red-500">↘</span>;
      default:
        return <span className="text-gray-400">→</span>;
    }
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-sm font-semibold text-gray-800 flex items-center">
          <svg className="w-4 h-4 mr-2 text-orange-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
          </svg>
          Trending Topics
        </h3>
      </div>
      <div className="p-4 space-y-2">
        {trendingTopics.map((topic, index) => (
          <Link
            key={topic.id}
            href={`/search?q=${encodeURIComponent(topic.name)}&category=${topic.category}`}
            className="flex items-center justify-between p-2 rounded-md hover:bg-gray-50 group"
          >
            <div className="flex items-center space-x-3">
              <span className="text-xs font-medium text-gray-400 w-4">
                {index + 1}
              </span>
              <div>
                <h4 className="text-sm font-medium text-gray-800 group-hover:text-blue-600">
                  {topic.name}
                </h4>
                <p className="text-xs text-gray-500">{topic.count}</p>
              </div>
            </div>
            <div className="text-sm">
              {getTrendIcon(topic.trend)}
            </div>
          </Link>
        ))}
      </div>
      <div className="p-4 border-t border-gray-200">
        <Link
          href="/explore"
          className="text-xs text-blue-600 hover:text-blue-700 font-medium"
        >
          View all trending topics →
        </Link>
      </div>
    </div>
  );
};

export default TrendingTopics;