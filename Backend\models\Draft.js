const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Draft = sequelize.define(
  'Draft',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    title: {
      type: DataTypes.STRING(500),
      allowNull: false,
      defaultValue: '未命名草稿',
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    contentHtml: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '富文本HTML内容',
    },
    excerpt: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文章摘要',
    },
    category: {
      type: DataTypes.ENUM(
        'technology',
        'business',
        'health',
        'sports',
        'entertainment',
        'politics',
        'science',
        'lifestyle'
      ),
      allowNull: true,
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      comment: '文章标签数组',
    },
    featuredImage: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '特色图片URL',
    },
    status: {
      type: DataTypes.ENUM(
        'draft',
        'auto_saved',
        'ready_for_review',
        'under_review',
        'approved',
        'rejected',
        'published'
      ),
      allowNull: false,
      defaultValue: 'draft',
    },
    authorId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: '草稿版本号',
    },
    parentDraftId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'drafts',
        key: 'id',
      },
      comment: '父草稿ID，用于版本管理',
    },
    publishedArticleId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'articles',
        key: 'id',
      },
      comment: '关联的已发布文章ID',
    },
    scheduledPublishAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '定时发布时间',
    },
    lastAutoSaveAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后自动保存时间',
    },
    wordCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '字数统计',
    },
    readingTime: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '预估阅读时间（分钟）',
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {},
      comment: '额外元数据',
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'drafts',
    timestamps: true,
    paranoid: false, // 使用软删除但不使用paranoid模式
    indexes: [
      {
        fields: ['authorId'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['parentDraftId'],
      },
      {
        fields: ['publishedArticleId'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['lastAutoSaveAt'],
      },
      {
        fields: ['isDeleted'],
      },
      {
        fields: ['authorId', 'status'],
      },
      {
        fields: ['authorId', 'isDeleted'],
      },
    ],
  }
);

// 实例方法
Draft.prototype.calculateReadingTime = function () {
  const wordsPerMinute = 200; // 平均阅读速度
  const words = this.content ? this.content.split(/\s+/).length : 0;
  this.wordCount = words;
  this.readingTime = Math.ceil(words / wordsPerMinute);
  return this.readingTime;
};

Draft.prototype.generateExcerpt = function (length = 200) {
  if (!this.content) return '';
  const plainText = this.content.replace(/<[^>]*>/g, ''); // 移除HTML标签
  return plainText.length > length ? plainText.substring(0, length) + '...' : plainText;
};

// 类方法
Draft.getUserDrafts = async function (userId, options = {}) {
  const {
    status = null,
    page = 1,
    limit = 20,
    orderBy = 'updatedAt',
    orderDirection = 'DESC',
  } = options;

  const whereClause = {
    authorId: userId,
    isDeleted: false,
  };

  if (status) {
    whereClause.status = status;
  }

  return await this.findAndCountAll({
    where: whereClause,
    order: [[orderBy, orderDirection]],
    limit,
    offset: (page - 1) * limit,
    include: [
      {
        model: sequelize.models.User,
        as: 'author',
        attributes: ['id', 'username', 'email', 'avatar'],
      },
    ],
  });
};

module.exports = Draft;
