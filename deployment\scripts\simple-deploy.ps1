Write-Host "🚀 OneNews GitHub部署脚本" -ForegroundColor Green

# 初始化Git仓库（如果需要）
if (-not (Test-Path ".git")) {
    Write-Host "🔧 初始化Git仓库..." -ForegroundColor Yellow
    git init
}

# 添加远程仓库
Write-Host "🔗 配置远程仓库..." -ForegroundColor Yellow
git remote remove origin 2>$null
git remote add origin https://github.com/Jacken22/OneNews.git

# 添加所有文件
Write-Host "📦 添加文件..." -ForegroundColor Yellow
git add .

# 提交
Write-Host "💾 提交更改..." -ForegroundColor Yellow
git commit -m "Initial commit - OneNews project"

# 推送
Write-Host "🚀 推送到GitHub..." -ForegroundColor Yellow
git branch -M main
git push -u origin main

Write-Host "✅ 部署完成!" -ForegroundColor Green
Write-Host "🌐 项目地址: https://github.com/Jacken22/OneNews" -ForegroundColor Cyan
