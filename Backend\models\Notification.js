const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define(
  'Notification',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: '接收通知的用户ID',
    },
    type: {
      type: DataTypes.ENUM(
        'like', // 点赞通知
        'comment', // 评论通知
        'follow', // 关注通知
        'message', // 私信通知
        'article_published', // 文章发布通知
        'article_approved', // 文章审核通过
        'article_rejected', // 文章审核拒绝
        'system', // 系统通知
        'promotion', // 推广通知
        'reminder', // 提醒通知
        'security', // 安全通知
        'newsletter' // 新闻简报
      ),
      allowNull: false,
      comment: '通知类型',
    },
    title: {
      type: DataTypes.STRING(200),
      allowNull: false,
      comment: '通知标题',
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: '通知内容',
    },
    data: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '通知相关数据，如文章ID、用户ID等',
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'articles',
        key: 'id',
      },
      comment: '关联的文章ID',
    },
    fromUserId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: '触发通知的用户ID',
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否已读',
    },
    readAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '阅读时间',
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      defaultValue: 'normal',
      comment: '通知优先级',
    },
    channels: {
      type: DataTypes.ARRAY(DataTypes.ENUM('web', 'email', 'push')),
      defaultValue: ['web'],
      comment: '通知渠道',
    },
    scheduledAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '计划发送时间',
    },
    sentAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '实际发送时间',
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '过期时间',
    },
    actionUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '点击通知后跳转的URL',
    },
    imageUrl: {
      type: DataTypes.STRING(500),
      allowNull: true,
      comment: '通知图片URL',
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '额外的元数据',
    },
  },
  {
    tableName: 'notifications',
    timestamps: true,
    indexes: [
      {
        fields: ['userId'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['isRead'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['scheduledAt'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['userId', 'isRead'],
      },
      {
        fields: ['userId', 'type'],
      },
      {
        fields: ['expiresAt'],
      },
    ],
  }
);

// 实例方法
Notification.prototype.markAsRead = function () {
  this.isRead = true;
  this.readAt = new Date();
  return this.save();
};

Notification.prototype.isExpired = function () {
  return this.expiresAt && new Date() > this.expiresAt;
};

// 静态方法
Notification.getUnreadCount = async function (userId) {
  return await this.count({
    where: {
      userId,
      isRead: false,
      expiresAt: {
        [sequelize.Sequelize.Op.or]: [null, { [sequelize.Sequelize.Op.gt]: new Date() }],
      },
    },
  });
};

Notification.getUserNotifications = async function (userId, options = {}) {
  const { page = 1, limit = 20, type = null, isRead = null, priority = null } = options;

  const where = {
    userId,
    expiresAt: {
      [sequelize.Sequelize.Op.or]: [null, { [sequelize.Sequelize.Op.gt]: new Date() }],
    },
  };

  if (type) where.type = type;
  if (isRead !== null) where.isRead = isRead;
  if (priority) where.priority = priority;

  return await this.findAndCountAll({
    where,
    order: [
      ['priority', 'DESC'],
      ['createdAt', 'DESC'],
    ],
    limit,
    offset: (page - 1) * limit,
  });
};

Notification.markAllAsRead = async function (userId, type = null) {
  const where = { userId, isRead: false };
  if (type) where.type = type;

  return await this.update(
    {
      isRead: true,
      readAt: new Date(),
    },
    { where }
  );
};

Notification.cleanupExpired = async function () {
  return await this.destroy({
    where: {
      expiresAt: {
        [sequelize.Sequelize.Op.lt]: new Date(),
      },
    },
  });
};

module.exports = Notification;
