import api from './api';

export interface SyncStatus {
  isConnected: boolean;
  lastSync: Date | null;
  pendingChanges: number;
  errors: string[];
}

export interface DataSyncOptions {
  autoSync: boolean;
  syncInterval: number; // in milliseconds
  retryAttempts: number;
  retryDelay: number;
}

class DataSyncService {
  private syncStatus: SyncStatus = {
    isConnected: false,
    lastSync: null,
    pendingChanges: 0,
    errors: []
  };

  private options: DataSyncOptions = {
    autoSync: true,
    syncInterval: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 5000 // 5 seconds
  };

  private syncInterval: NodeJS.Timeout | null = null;
  private listeners: Array<(status: SyncStatus) => void> = [];
  private pendingOperations: Map<string, any> = new Map();

  constructor(options?: Partial<DataSyncOptions>) {
    if (options) {
      this.options = { ...this.options, ...options };
    }
    this.initializeSync();
  }

  private initializeSync() {
    if (this.options.autoSync) {
      this.startAutoSync();
    }
    this.checkConnection();
  }

  public startAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(() => {
      this.performSync();
    }, this.options.syncInterval);
  }

  public stopAutoSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }

  public async performSync(): Promise<void> {
    try {
      await this.checkConnection();
      
      if (!this.syncStatus.isConnected) {
        throw new Error('No connection to backend');
      }

      // Sync different data types
      await Promise.all([
        this.syncUsers(),
        this.syncContent(),
        this.syncAnalytics(),
        this.syncNotifications()
      ]);

      this.updateSyncStatus({
        lastSync: new Date(),
        pendingChanges: this.pendingOperations.size,
        errors: []
      });

    } catch (error) {
      console.error('Sync failed:', error);
      this.updateSyncStatus({
        errors: [...this.syncStatus.errors, error instanceof Error ? error.message : 'Unknown error']
      });
    }
  }

  private async checkConnection(): Promise<boolean> {
    try {
      const response = await api.get('/health');
      const isConnected = response.status === 200;
      
      this.updateSyncStatus({ isConnected });
      return isConnected;
    } catch (error) {
      this.updateSyncStatus({ isConnected: false });
      return false;
    }
  }

  private async syncUsers(): Promise<void> {
    try {
      // Get latest user data from main frontend
      const frontendUsers = await this.fetchFrontendData('/api/users');
      
      // Get current admin user data
      const adminUsers = await api.get('/api/admin/users');
      
      // Compare and sync differences
      const differences = this.compareData(frontendUsers.data, adminUsers.data);
      
      if (differences.length > 0) {
        await this.applyUserUpdates(differences);
      }
    } catch (error) {
      console.error('User sync failed:', error);
      throw error;
    }
  }

  private async syncContent(): Promise<void> {
    try {
      // Sync articles, comments, categories
      const [frontendArticles, frontendComments, frontendCategories] = await Promise.all([
        this.fetchFrontendData('/api/articles'),
        this.fetchFrontendData('/api/comments'),
        this.fetchFrontendData('/api/categories')
      ]);

      const [adminArticles, adminComments, adminCategories] = await Promise.all([
        api.get('/api/admin/content/articles'),
        api.get('/api/admin/content/comments'),
        api.get('/api/admin/content/categories')
      ]);

      // Apply content updates
      await Promise.all([
        this.syncContentType('articles', frontendArticles.data, adminArticles.data),
        this.syncContentType('comments', frontendComments.data, adminComments.data),
        this.syncContentType('categories', frontendCategories.data, adminCategories.data)
      ]);
    } catch (error) {
      console.error('Content sync failed:', error);
      throw error;
    }
  }

  private async syncAnalytics(): Promise<void> {
    try {
      // Sync analytics data
      const frontendAnalytics = await this.fetchFrontendData('/api/analytics');
      await api.post('/api/admin/analytics/sync', frontendAnalytics.data);
    } catch (error) {
      console.error('Analytics sync failed:', error);
      throw error;
    }
  }

  private async syncNotifications(): Promise<void> {
    try {
      // Get new notifications from frontend
      const frontendNotifications = await this.fetchFrontendData('/api/notifications');
      
      // Send to admin system
      if (frontendNotifications.data.length > 0) {
        await api.post('/api/admin/notifications/sync', {
          notifications: frontendNotifications.data
        });
      }
    } catch (error) {
      console.error('Notification sync failed:', error);
      throw error;
    }
  }

  private async fetchFrontendData(endpoint: string): Promise<any> {
    try {
      // This would connect to the main frontend API
      const frontendApiUrl = process.env.NEXT_PUBLIC_FRONTEND_API_URL || 'http://localhost:3000';
      const response = await fetch(`${frontendApiUrl}${endpoint}`, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      });
      
      if (!response.ok) {
        throw new Error(`Frontend API error: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Failed to fetch frontend data:', error);
      throw error;
    }
  }

  private compareData(frontendData: any[], adminData: any[]): any[] {
    // Simple comparison logic - in real app, this would be more sophisticated
    const differences: any[] = [];
    
    frontendData.forEach(frontendItem => {
      const adminItem = adminData.find(item => item.id === frontendItem.id);
      
      if (!adminItem) {
        // New item in frontend
        differences.push({
          type: 'create',
          data: frontendItem
        });
      } else if (new Date(frontendItem.updatedAt) > new Date(adminItem.updatedAt)) {
        // Updated item in frontend
        differences.push({
          type: 'update',
          data: frontendItem,
          existing: adminItem
        });
      }
    });
    
    return differences;
  }

  private async applyUserUpdates(differences: any[]): Promise<void> {
    for (const diff of differences) {
      try {
        if (diff.type === 'create') {
          await api.post('/api/admin/users', diff.data);
        } else if (diff.type === 'update') {
          await api.put(`/api/admin/users/${diff.data.id}`, diff.data);
        }
      } catch (error) {
        console.error('Failed to apply user update:', error);
        this.addPendingOperation(`user_${diff.data.id}`, diff);
      }
    }
  }

  private async syncContentType(type: string, frontendData: any[], adminData: any[]): Promise<void> {
    const differences = this.compareData(frontendData, adminData);
    
    for (const diff of differences) {
      try {
        if (diff.type === 'create') {
          await api.post(`/api/admin/content/${type}`, diff.data);
        } else if (diff.type === 'update') {
          await api.put(`/api/admin/content/${type}/${diff.data.id}`, diff.data);
        }
      } catch (error) {
        console.error(`Failed to sync ${type}:`, error);
        this.addPendingOperation(`${type}_${diff.data.id}`, diff);
      }
    }
  }

  private addPendingOperation(key: string, operation: any): void {
    this.pendingOperations.set(key, operation);
    this.updateSyncStatus({
      pendingChanges: this.pendingOperations.size
    });
  }

  public async retryPendingOperations(): Promise<void> {
    const operations = Array.from(this.pendingOperations.entries());
    
    for (const [key, operation] of operations) {
      try {
        // Retry the operation based on its type
        await this.retryOperation(operation);
        this.pendingOperations.delete(key);
      } catch (error) {
        console.error(`Failed to retry operation ${key}:`, error);
      }
    }
    
    this.updateSyncStatus({
      pendingChanges: this.pendingOperations.size
    });
  }

  private async retryOperation(operation: any): Promise<void> {
    // Implement retry logic based on operation type
    // This is a simplified version
    const { type, data } = operation;
    
    if (type === 'create') {
      await api.post('/api/admin/retry', data);
    } else if (type === 'update') {
      await api.put(`/api/admin/retry/${data.id}`, data);
    }
  }

  private updateSyncStatus(updates: Partial<SyncStatus>): void {
    this.syncStatus = { ...this.syncStatus, ...updates };
    this.notifyListeners();
  }

  public onStatusChange(callback: (status: SyncStatus) => void): () => void {
    this.listeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(callback => callback(this.syncStatus));
  }

  public getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  public async forceSyncNow(): Promise<void> {
    await this.performSync();
  }

  public clearErrors(): void {
    this.updateSyncStatus({ errors: [] });
  }

  public destroy(): void {
    this.stopAutoSync();
    this.listeners = [];
    this.pendingOperations.clear();
  }
}

// Create singleton instance
export const dataSyncService = new DataSyncService();

// Export types and service
export default dataSyncService;
