'use client';

import React from 'react';
import Link from 'next/link';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export default class RegisterErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Registration form error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-100 flex items-center justify-center p-4">
          <div className="max-w-md w-full bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="mb-6">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Registration Error</h2>
              <p className="text-gray-600 mb-4">Something went wrong with the registration form.</p>
              <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg mb-4">
                <p className="font-medium mb-2">Try these solutions:</p>
                <ul className="text-left space-y-1">
                  <li>• Refresh the page and try again</li>
                  <li>• Clear your browser cache</li>
                  <li>• Use the stable registration form</li>
                  <li>• Try a different browser</li>
                </ul>
              </div>
            </div>
            <div className="space-y-3">
              <Link
                href="/auth/register-stable"
                className="w-full inline-flex justify-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                Use Stable Registration Form
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                Refresh Page
              </button>
              <Link
                href="/auth/login"
                className="w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                Go to Login
              </Link>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}