# Test Environment Configuration
NODE_ENV=test

# Database Configuration (Test)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=newzora_test
DB_USER=postgres
DB_PASSWORD=wasd080980!
DATABASE_URL=postgresql://postgres:wasd080980!@localhost:5432/newzora_test

# JWT Configuration (Test)
JWT_SECRET=test-jwt-secret-key-for-testing-only-do-not-use-in-production
SESSION_SECRET=test-session-secret-for-testing

# Security Configuration (Test)
BCRYPT_ROUNDS=4

# Email Configuration (Test - Disabled)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=test-password
EMAIL_FROM=<EMAIL>

# Frontend URL (Test)
FRONTEND_URL=http://localhost:3000

# File Upload (Test)
UPLOAD_DIR=uploads/test
MAX_FILE_SIZE=10485760

# Rate Limiting (Test - Relaxed)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Logging (Test)
LOG_LEVEL=error
LOG_FILE=logs/test.log
