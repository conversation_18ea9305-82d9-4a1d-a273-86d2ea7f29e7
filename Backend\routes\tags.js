const express = require('express');
const router = express.Router();
const { Tag, UserTag, Article } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

// Get all tags
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;
    const offset = (page - 1) * limit;
    const search = req.query.search;

    const whereClause = { isActive: true };
    if (search) {
      whereClause.name = {
        [Op.iLike]: `%${search}%`,
      };
    }

    const { count, rows: tags } = await Tag.findAndCountAll({
      where: whereClause,
      order: [
        ['usageCount', 'DESC'],
        ['name', 'ASC'],
      ],
      limit,
      offset,
    });

    res.json({
      success: true,
      data: {
        tags,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching tags:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get popular tags (must be before /:id route)
router.get('/popular', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;

    const tags = await Tag.findAll({
      where: {
        isActive: true,
        usageCount: { [Op.gt]: 0 },
      },
      order: [['usageCount', 'DESC']],
      limit,
    });

    res.json({
      success: true,
      data: { tags },
    });
  } catch (error) {
    console.error('Error fetching popular tags:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get single tag by ID
router.get('/:id', async (req, res) => {
  try {
    const tag = await Tag.findByPk(req.params.id);

    if (!tag) {
      return res.status(404).json({
        success: false,
        message: 'Tag not found',
      });
    }

    res.json({
      success: true,
      data: { tag },
    });
  } catch (error) {
    console.error('Error fetching tag:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Create new tag (admin only)
router.post('/', authenticateToken, requireRole(['admin', 'moderator']), async (req, res) => {
  try {
    const { name, description, color } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: 'Tag name is required',
      });
    }

    // Generate slug from name
    const slug = name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    const tag = await Tag.create({
      name: name.trim(),
      slug,
      description: description?.trim(),
      color: color || '#3B82F6',
    });

    res.status(201).json({
      success: true,
      message: 'Tag created successfully',
      data: tag,
    });
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        success: false,
        message: 'Tag name already exists',
      });
    }
    console.error('Error creating tag:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Add tag to user's interests
router.post('/user', authenticateToken, async (req, res) => {
  try {
    const { tagId, interestLevel = 'medium' } = req.body;
    const userId = req.user.id;

    if (!tagId) {
      return res.status(400).json({
        success: false,
        message: 'Tag ID is required',
      });
    }

    // Check if tag exists
    const tag = await Tag.findByPk(tagId);
    if (!tag) {
      return res.status(404).json({
        success: false,
        message: 'Tag not found',
      });
    }

    // Check if user already has this tag
    const existingUserTag = await UserTag.findOne({
      where: { userId, tagId },
    });

    if (existingUserTag) {
      // Update interest level
      await existingUserTag.update({ interestLevel });
      return res.json({
        success: true,
        message: 'Tag interest level updated',
        data: existingUserTag,
      });
    }

    // Create new user tag
    const userTag = await UserTag.create({
      userId,
      tagId,
      interestLevel,
      source: 'manual',
    });

    // Increment tag usage count
    await tag.increment('usageCount');

    res.status(201).json({
      success: true,
      message: 'Tag added to your interests',
      data: userTag,
    });
  } catch (error) {
    console.error('Error adding user tag:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Remove tag from user's interests
router.delete('/user/:tagId', authenticateToken, async (req, res) => {
  try {
    const { tagId } = req.params;
    const userId = req.user.id;

    const userTag = await UserTag.findOne({
      where: { userId, tagId: parseInt(tagId) },
    });

    if (!userTag) {
      return res.status(404).json({
        success: false,
        message: 'Tag not found in your interests',
      });
    }

    await userTag.destroy();

    // Decrement tag usage count
    const tag = await Tag.findByPk(tagId);
    if (tag && tag.usageCount > 0) {
      await tag.decrement('usageCount');
    }

    res.json({
      success: true,
      message: 'Tag removed from your interests',
    });
  } catch (error) {
    console.error('Error removing user tag:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get user's tags
router.get('/user/:userId?', async (req, res) => {
  try {
    const userId = req.params.userId || (req.user ? req.user.id : null);

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required',
      });
    }

    const userTags = await UserTag.findAll({
      where: { userId: parseInt(userId) },
      include: [
        {
          model: Tag,
          where: { isActive: true },
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    res.json({
      success: true,
      data: {
        tags: userTags.map((ut) => ({
          ...ut.Tag.toJSON(),
          interestLevel: ut.interestLevel,
          addedAt: ut.createdAt,
        })),
      },
    });
  } catch (error) {
    console.error('Error fetching user tags:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// Get recommended tags for user
router.get('/recommendations', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const limit = parseInt(req.query.limit) || 10;

    // Get user's current tags
    const userTagIds = await UserTag.findAll({
      where: { userId },
      attributes: ['tagId'],
    }).then((tags) => tags.map((t) => t.tagId));

    // Get popular tags that user doesn't have
    const recommendedTags = await Tag.findAll({
      where: {
        isActive: true,
        id: { [Op.notIn]: userTagIds },
        usageCount: { [Op.gt]: 0 },
      },
      order: [['usageCount', 'DESC']],
      limit,
    });

    res.json({
      success: true,
      data: { tags: recommendedTags },
    });
  } catch (error) {
    console.error('Error fetching recommended tags:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

module.exports = router;
