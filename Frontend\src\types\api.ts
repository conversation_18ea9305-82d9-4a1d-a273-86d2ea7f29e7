// 统一的API响应格式

// 基础API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  message?: string;
  timestamp: string;
}

// API错误接口
export interface ApiError {
  code: string;
  message: string;
  details?: any;
  field?: string; // 表单字段错误
}

// 分页响应接口
export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: ApiError;
  timestamp: string;
}

// 认证相关API响应
export interface AuthResponse {
  success: boolean;
  data?: {
    user: AuthUser;
    session: AuthSession;
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
  error?: ApiError;
  message?: string;
  timestamp: string;
}

export interface AuthUser {
  id: string;
  email: string;
  username: string;
  display_name: string | null;
  avatar_url: string | null;
  role: 'user' | 'admin' | 'moderator';
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login_at: string | null;
}

export interface AuthSession {
  access_token: string;
  refresh_token: string;
  expires_at: number;
  token_type: string;
  user: AuthUser;
}

// 登录请求
export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

// 注册请求
export interface RegisterRequest {
  email: string;
  password: string;
  username: string;
  display_name?: string;
}

// 重置密码请求
export interface ResetPasswordRequest {
  email: string;
}

// 更新密码请求
export interface UpdatePasswordRequest {
  current_password: string;
  new_password: string;
}

// 社交登录请求
export interface SocialLoginRequest {
  provider: 'google' | 'facebook' | 'twitter' | 'apple';
  redirect_url?: string;
}

// 用户资料更新请求
export interface UpdateProfileRequest {
  username?: string;
  display_name?: string;
  bio?: string;
  website?: string;
  location?: string;
  avatar_url?: string;
}

// 作品相关API
export interface WorkResponse {
  success: boolean;
  data?: WorkData;
  error?: ApiError;
  timestamp: string;
}

export interface WorkData {
  id: string;
  title: string;
  description: string | null;
  content: string;
  type: 'article' | 'video' | 'audio' | 'image';
  status: 'draft' | 'published' | 'archived';
  author: {
    id: string;
    username: string;
    display_name: string | null;
    avatar_url: string | null;
  };
  category: {
    id: string;
    name: string;
    slug: string;
  } | null;
  tags: string[];
  metadata: any;
  stats: {
    view_count: number;
    like_count: number;
    comment_count: number;
  };
  created_at: string;
  updated_at: string;
  published_at: string | null;
}

// 创建作品请求
export interface CreateWorkRequest {
  title: string;
  description?: string;
  content: string;
  type: 'article' | 'video' | 'audio' | 'image';
  status?: 'draft' | 'published';
  category_id?: string;
  tags?: string[];
  metadata?: any;
}

// 更新作品请求
export interface UpdateWorkRequest {
  title?: string;
  description?: string;
  content?: string;
  status?: 'draft' | 'published' | 'archived';
  category_id?: string;
  tags?: string[];
  metadata?: any;
}

// 评论相关API
export interface CommentResponse {
  success: boolean;
  data?: CommentData;
  error?: ApiError;
  timestamp: string;
}

export interface CommentData {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    display_name: string | null;
    avatar_url: string | null;
  };
  work_id: string;
  parent_id: string | null;
  replies?: CommentData[];
  like_count: number;
  is_liked: boolean;
  is_approved: boolean;
  created_at: string;
  updated_at: string;
}

// 创建评论请求
export interface CreateCommentRequest {
  content: string;
  work_id: string;
  parent_id?: string;
}

// File upload response
export interface UploadResponse {
  success: boolean;
  data?: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
  error?: ApiError;
  timestamp: string;
}

// Search request
export interface SearchRequest {
  query: string;
  type?: 'all' | 'article' | 'video' | 'audio' | 'image';
  category?: string;
  tags?: string[];
  author?: string;
  sort?: 'relevance' | 'date' | 'popularity';
  page?: number;
  limit?: number;
}

// Search response
export interface SearchResponse {
  success: boolean;
  data?: {
    works: WorkData[];
    users: AuthUser[];
    total_works: number;
    total_users: number;
  };
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  error?: ApiError;
  timestamp: string;
}

// 通知相关
export interface NotificationData {
  id: string;
  type: 'like' | 'comment' | 'follow' | 'mention' | 'system';
  title: string;
  message: string;
  data: any;
  is_read: boolean;
  created_at: string;
}

// 统计数据
export interface StatsResponse {
  success: boolean;
  data?: {
    total_works: number;
    total_users: number;
    total_comments: number;
    total_likes: number;
    recent_activity: any[];
  };
  error?: ApiError;
  timestamp: string;
}

// Error code enumeration
export enum ApiErrorCode {
  // Authentication errors
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_EMAIL = 'INVALID_EMAIL',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  PASSWORD_TOO_WEAK = 'PASSWORD_TOO_WEAK',
  
  // Resource errors
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  CONFLICT = 'CONFLICT',
  
  // Server errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMITED = 'RATE_LIMITED',
  
  // Business logic errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  FEATURE_DISABLED = 'FEATURE_DISABLED',
}

// HTTP状态码映射
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;
