@echo off
echo 🚀 Newzora Frontend Development Server Starter
echo ================================================

echo.
echo 🔍 Checking port 3000...

:: 检查端口3000是否被占用
netstat -ano | findstr :3000 > nul
if %errorlevel% == 0 (
    echo ⚠️  Port 3000 is already in use!
    echo.
    echo 📋 Processes using port 3000:
    netstat -ano | findstr :3000
    echo.
    
    set /p choice="Do you want to kill the process and continue? (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🔄 Finding and killing processes on port 3000...
        
        :: 获取占用端口3000的PID
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
            echo Killing process with PID: %%a
            taskkill /PID %%a /F > nul 2>&1
        )
        
        echo ✅ Processes killed successfully!
        timeout /t 2 > nul
    ) else (
        echo.
        echo 🔄 Starting on alternative port 3001...
        echo 📝 You can access the app at: http://localhost:3001
        echo.
        npm run dev -- -p 3001
        goto :end
    )
)

echo.
echo ✅ Port 3000 is available!
echo 🚀 Starting Newzora Frontend on http://localhost:3000...
echo.

:: 启动开发服务器
npm run dev

:end
echo.
echo 👋 Development server stopped.
pause
