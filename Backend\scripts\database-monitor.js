/**
 * OneNews 数据库监控脚本
 * 实时监控数据库性能、连接状态和健康指标
 */

const { Pool } = require('pg');
const fs = require('fs').promises;
const path = require('path');

// 配置
const config = {
  // 数据库连接配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'onenews',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    max: 5, // 监控用连接池较小
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 10000,
  },

  // 监控配置
  monitoring: {
    interval: parseInt(process.env.MONITOR_INTERVAL) || 60000, // 监控间隔 (毫秒)
    logFile: process.env.MONITOR_LOG_FILE || './logs/database-monitor.log',
    alertThresholds: {
      connectionUsage: 80, // 连接使用率阈值 (%)
      slowQueryTime: 5000, // 慢查询时间阈值 (毫秒)
      diskUsage: 85, // 磁盘使用率阈值 (%)
      memoryUsage: 90, // 内存使用率阈值 (%)
      lockWaitTime: 10000, // 锁等待时间阈值 (毫秒)
      replicationLag: 60000, // 复制延迟阈值 (毫秒)
    },
  },
};

// 创建数据库连接池
const pool = new Pool(config.database);

// 日志函数
const log = async (level, message, data = null) => {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    data,
  };

  const logLine = JSON.stringify(logEntry) + '\n';

  // 输出到控制台
  console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }

  // 写入日志文件
  try {
    await fs.mkdir(path.dirname(config.monitoring.logFile), { recursive: true });
    await fs.appendFile(config.monitoring.logFile, logLine);
  } catch (error) {
    console.error('写入日志文件失败:', error.message);
  }
};

// 数据库健康检查
const healthCheck = async () => {
  try {
    const client = await pool.connect();
    const startTime = Date.now();

    const result = await client.query('SELECT NOW() as current_time, version() as version');
    const responseTime = Date.now() - startTime;

    client.release();

    return {
      status: 'healthy',
      responseTime,
      timestamp: result.rows[0].current_time,
      version: result.rows[0].version,
      pool: {
        total: pool.totalCount,
        idle: pool.idleCount,
        waiting: pool.waitingCount,
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

// 连接状态监控
const monitorConnections = async () => {
  try {
    const client = await pool.connect();

    const query = `
      SELECT 
        count(*) as total_connections,
        count(*) FILTER (WHERE state = 'active') as active_connections,
        count(*) FILTER (WHERE state = 'idle') as idle_connections,
        count(*) FILTER (WHERE state = 'idle in transaction') as idle_in_transaction,
        count(*) FILTER (WHERE wait_event IS NOT NULL) as waiting_connections,
        max(extract(epoch from (now() - query_start))) as longest_query_time,
        max(extract(epoch from (now() - state_change))) as longest_idle_time
      FROM pg_stat_activity 
      WHERE datname = $1;
    `;

    const result = await client.query(query, [config.database.database]);
    client.release();

    const stats = result.rows[0];

    // 计算连接使用率
    const maxConnections = await getMaxConnections();
    const connectionUsage = (stats.total_connections / maxConnections) * 100;

    // 检查阈值
    if (connectionUsage > config.monitoring.alertThresholds.connectionUsage) {
      await log('warn', `连接使用率过高: ${connectionUsage.toFixed(1)}%`, stats);
    }

    if (stats.longest_query_time > config.monitoring.alertThresholds.slowQueryTime / 1000) {
      await log('warn', `检测到慢查询: ${stats.longest_query_time.toFixed(2)}秒`, stats);
    }

    return {
      ...stats,
      max_connections: maxConnections,
      connection_usage_percent: connectionUsage.toFixed(1),
    };
  } catch (error) {
    await log('error', '连接监控失败', { error: error.message });
    return null;
  }
};

// 获取最大连接数
const getMaxConnections = async () => {
  try {
    const client = await pool.connect();
    const result = await client.query('SHOW max_connections');
    client.release();
    return parseInt(result.rows[0].max_connections);
  } catch (error) {
    return 100; // 默认值
  }
};

// 慢查询监控
const monitorSlowQueries = async () => {
  try {
    const client = await pool.connect();

    const query = `
      SELECT 
        query,
        calls,
        total_time,
        mean_time,
        rows,
        100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
      FROM pg_stat_statements 
      WHERE mean_time > $1
      ORDER BY mean_time DESC 
      LIMIT 10;
    `;

    const result = await client.query(query, [config.monitoring.alertThresholds.slowQueryTime]);
    client.release();

    if (result.rows.length > 0) {
      await log('warn', `发现 ${result.rows.length} 个慢查询`, result.rows);
    }

    return result.rows;
  } catch (error) {
    // pg_stat_statements 可能未启用
    await log('debug', '慢查询监控失败 (可能未启用 pg_stat_statements)', { error: error.message });
    return [];
  }
};

// 锁监控
const monitorLocks = async () => {
  try {
    const client = await pool.connect();

    const query = `
      SELECT 
        blocked_locks.pid AS blocked_pid,
        blocked_activity.usename AS blocked_user,
        blocking_locks.pid AS blocking_pid,
        blocking_activity.usename AS blocking_user,
        blocked_activity.query AS blocked_statement,
        blocking_activity.query AS current_statement_in_blocking_process,
        blocked_activity.application_name AS blocked_application,
        blocking_activity.application_name AS blocking_application,
        extract(epoch from (now() - blocked_activity.query_start)) AS blocked_duration
      FROM pg_catalog.pg_locks blocked_locks
      JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
      JOIN pg_catalog.pg_locks blocking_locks 
        ON blocking_locks.locktype = blocked_locks.locktype
        AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
        AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
        AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
        AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
        AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
        AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
        AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
        AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
        AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
        AND blocking_locks.pid != blocked_locks.pid
      JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
      WHERE NOT blocked_locks.GRANTED;
    `;

    const result = await client.query(query);
    client.release();

    if (result.rows.length > 0) {
      const longLocks = result.rows.filter(
        (row) => row.blocked_duration * 1000 > config.monitoring.alertThresholds.lockWaitTime
      );

      if (longLocks.length > 0) {
        await log('warn', `检测到长时间锁等待`, longLocks);
      }
    }

    return result.rows;
  } catch (error) {
    await log('error', '锁监控失败', { error: error.message });
    return [];
  }
};

// 磁盘使用监控
const monitorDiskUsage = async () => {
  try {
    const client = await pool.connect();

    const query = `
      SELECT 
        schemaname,
        tablename,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
        pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
      FROM pg_tables 
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
      LIMIT 10;
    `;

    const result = await client.query(query);

    // 获取数据库总大小
    const dbSizeQuery = `SELECT pg_size_pretty(pg_database_size($1)) as database_size`;
    const dbSizeResult = await client.query(dbSizeQuery, [config.database.database]);

    client.release();

    return {
      database_size: dbSizeResult.rows[0].database_size,
      largest_tables: result.rows,
    };
  } catch (error) {
    await log('error', '磁盘使用监控失败', { error: error.message });
    return null;
  }
};

// 索引使用情况监控
const monitorIndexUsage = async () => {
  try {
    const client = await pool.connect();

    const query = `
      SELECT 
        schemaname,
        tablename,
        indexname,
        idx_tup_read,
        idx_tup_fetch,
        idx_scan,
        CASE 
          WHEN idx_scan = 0 THEN 'Unused'
          WHEN idx_scan < 100 THEN 'Low Usage'
          ELSE 'Active'
        END as usage_status,
        pg_size_pretty(pg_relation_size(indexrelid)) as index_size
      FROM pg_stat_user_indexes
      WHERE schemaname = 'public'
      ORDER BY idx_scan ASC
      LIMIT 20;
    `;

    const result = await client.query(query);
    client.release();

    const unusedIndexes = result.rows.filter((row) => row.usage_status === 'Unused');
    if (unusedIndexes.length > 0) {
      await log('info', `发现 ${unusedIndexes.length} 个未使用的索引`, unusedIndexes);
    }

    return result.rows;
  } catch (error) {
    await log('error', '索引使用监控失败', { error: error.message });
    return [];
  }
};

// 生成监控报告
const generateReport = async (data) => {
  const report = {
    timestamp: new Date().toISOString(),
    health: data.health,
    connections: data.connections,
    slow_queries: data.slowQueries,
    locks: data.locks,
    disk_usage: data.diskUsage,
    index_usage: data.indexUsage,
  };

  // 保存报告到文件
  const reportFile = `./logs/database-report-${new Date().toISOString().split('T')[0]}.json`;
  try {
    await fs.mkdir(path.dirname(reportFile), { recursive: true });
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
    await log('info', `监控报告已生成: ${reportFile}`);
  } catch (error) {
    await log('error', '生成监控报告失败', { error: error.message });
  }

  return report;
};

// 主监控循环
const startMonitoring = async () => {
  await log('info', 'OneNews 数据库监控启动', {
    interval: config.monitoring.interval,
    thresholds: config.monitoring.alertThresholds,
  });

  const monitor = async () => {
    try {
      await log('debug', '开始监控周期');

      // 收集所有监控数据
      const [health, connections, slowQueries, locks, diskUsage, indexUsage] = await Promise.all([
        healthCheck(),
        monitorConnections(),
        monitorSlowQueries(),
        monitorLocks(),
        monitorDiskUsage(),
        monitorIndexUsage(),
      ]);

      // 生成报告
      const report = await generateReport({
        health,
        connections,
        slowQueries,
        locks,
        diskUsage,
        indexUsage,
      });

      await log('debug', '监控周期完成', {
        health_status: health.status,
        total_connections: connections?.total_connections,
        slow_queries_count: slowQueries.length,
        locks_count: locks.length,
      });
    } catch (error) {
      await log('error', '监控周期执行失败', { error: error.message });
    }
  };

  // 立即执行一次
  await monitor();

  // 设置定时监控
  setInterval(monitor, config.monitoring.interval);
};

// 优雅关闭
const gracefulShutdown = async () => {
  await log('info', '数据库监控正在关闭...');

  try {
    await pool.end();
    await log('info', '数据库连接池已关闭');
  } catch (error) {
    await log('error', '关闭数据库连接池失败', { error: error.message });
  }

  process.exit(0);
};

// 信号处理
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// 未捕获异常处理
process.on('uncaughtException', async (error) => {
  await log('error', '未捕获异常', { error: error.message, stack: error.stack });
  process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
  await log('error', '未处理的 Promise 拒绝', { reason, promise });
});

// 启动监控
if (require.main === module) {
  startMonitoring().catch(async (error) => {
    await log('error', '启动监控失败', { error: error.message });
    process.exit(1);
  });
}

module.exports = {
  healthCheck,
  monitorConnections,
  monitorSlowQueries,
  monitorLocks,
  monitorDiskUsage,
  monitorIndexUsage,
  startMonitoring,
};
