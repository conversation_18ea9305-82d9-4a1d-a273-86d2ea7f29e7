const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const MediaFile = sequelize.define(
  'MediaFile',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    filename: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '原始文件名',
    },
    originalName: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '用户上传时的文件名',
    },
    mimeType: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'MIME类型',
    },
    fileType: {
      type: DataTypes.ENUM('image', 'video', 'audio', 'document', 'other'),
      allowNull: false,
      comment: '文件类型分类',
    },
    size: {
      type: DataTypes.BIGINT,
      allowNull: false,
      comment: '文件大小（字节）',
    },
    path: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '文件存储路径',
    },
    url: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '文件访问URL',
    },
    thumbnailUrl: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '缩略图URL（图片/视频）',
    },
    duration: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '媒体时长（秒）- 音频/视频',
    },
    dimensions: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '尺寸信息 {width, height} - 图片/视频',
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {},
      comment: '文件元数据',
    },
    uploaderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    folder: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'uploads',
      comment: '文件夹分类',
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      comment: '文件标签',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '文件描述',
    },
    altText: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '图片替代文本',
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否公开访问',
    },
    downloadCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '下载次数',
    },
    viewCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '查看次数',
    },
    status: {
      type: DataTypes.ENUM('uploading', 'processing', 'ready', 'failed', 'deleted'),
      allowNull: false,
      defaultValue: 'uploading',
    },
    processingInfo: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '处理信息（压缩、转码等）',
    },
    expiresAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '过期时间（临时文件）',
    },
    isDeleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'media_files',
    timestamps: true,
    indexes: [
      {
        fields: ['uploaderId'],
      },
      {
        fields: ['fileType'],
      },
      {
        fields: ['mimeType'],
      },
      {
        fields: ['folder'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['isPublic'],
      },
      {
        fields: ['isDeleted'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['expiresAt'],
      },
      {
        fields: ['uploaderId', 'fileType'],
      },
      {
        fields: ['uploaderId', 'isDeleted'],
      },
    ],
  }
);

// 实例方法
MediaFile.prototype.getFormattedSize = function () {
  const bytes = this.size;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
};

MediaFile.prototype.isImage = function () {
  return this.fileType === 'image';
};

MediaFile.prototype.isVideo = function () {
  return this.fileType === 'video';
};

MediaFile.prototype.isAudio = function () {
  return this.fileType === 'audio';
};

MediaFile.prototype.incrementView = async function () {
  this.viewCount += 1;
  await this.save();
};

MediaFile.prototype.incrementDownload = async function () {
  this.downloadCount += 1;
  await this.save();
};

// 类方法
MediaFile.getUserFiles = async function (userId, options = {}) {
  const {
    fileType = null,
    folder = null,
    page = 1,
    limit = 20,
    orderBy = 'createdAt',
    orderDirection = 'DESC',
  } = options;

  const whereClause = {
    uploaderId: userId,
    isDeleted: false,
    status: 'ready',
  };

  if (fileType) {
    whereClause.fileType = fileType;
  }

  if (folder) {
    whereClause.folder = folder;
  }

  return await this.findAndCountAll({
    where: whereClause,
    order: [[orderBy, orderDirection]],
    limit,
    offset: (page - 1) * limit,
    include: [
      {
        model: sequelize.models.User,
        as: 'uploader',
        attributes: ['id', 'username', 'avatar'],
      },
    ],
  });
};

MediaFile.getFilesByType = async function (fileType, limit = 50) {
  return await this.findAll({
    where: {
      fileType,
      isDeleted: false,
      status: 'ready',
      isPublic: true,
    },
    order: [['createdAt', 'DESC']],
    limit,
  });
};

module.exports = MediaFile;
