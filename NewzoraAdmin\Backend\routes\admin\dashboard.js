const express = require('express');
const { supabase } = require('../../config/database');

const router = express.Router();

// 获取仪表板统计数据
router.get('/stats', async (req, res) => {
  try {
    // 获取用户统计
    const { count: totalUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    const { count: activeUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('last_login', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

    // 获取文章统计
    const { count: totalArticles } = await supabase
      .from('articles')
      .select('*', { count: 'exact', head: true });

    const { count: publishedArticles } = await supabase
      .from('articles')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'published');

    // 获取评论统计
    const { count: totalComments } = await supabase
      .from('comments')
      .select('*', { count: 'exact', head: true });

    res.json({
      success: true,
      data: {
        users: {
          total: totalUsers || 0,
          activeUsers: activeUsers || 0
        },
        content: {
          totalArticles: totalArticles || 0,
          publishedArticles: publishedArticles || 0
        },
        engagement: {
          totalComments: totalComments || 0
        }
      }
    });
  } catch (error) {
    console.error('获取统计数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

module.exports = router;