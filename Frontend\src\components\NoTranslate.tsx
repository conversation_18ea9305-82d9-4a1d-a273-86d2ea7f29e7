'use client';

import React, { ReactNode, useEffect, useState } from 'react';

interface NoTranslateProps {
    children: ReactNode;
    className?: string;
    as?: keyof JSX.IntrinsicElements;
    fallback?: ReactNode;
}

/**
 * NoTranslate 组件 - 防止内容被浏览器翻译插件翻译
 * 同时解决 Next.js 水合错误问题
 */
export default function NoTranslate({
    children,
    className = '',
    as: Component = 'span',
    fallback
}: NoTranslateProps) {
    const [isClient, setIsClient] = useState(false);

    useEffect(() => {
        setIsClient(true);
    }, []);

    // 服务器端渲染时使用简化版本
    if (!isClient) {
        return React.createElement(
            Component,
            {
                className,
                suppressHydrationWarning: true,
            },
            fallback || children
        );
    }

    // 客户端渲染时使用完整的防翻译版本
    return React.createElement(
        Component,
        {
            className,
            // 防止Google翻译
            translate: 'no',
            // 防止其他翻译工具
            'data-translate': 'no',
            'data-notranslate': 'true',
            // 防止Microsoft翻译
            'data-ms-translate': 'no',
            // 防止百度翻译
            'data-bd-translate': 'no',
            // 防止有道翻译
            'data-yd-translate': 'no',
            // 添加lang属性明确语言
            lang: 'en',
            // 防止内容被修改
            contentEditable: false,
            // 添加CSS类防止翻译
            style: {
                // 使用CSS防止翻译
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none',
                userSelect: 'none',
            },
        },
        children
    );
}

/**
 * 专门用于品牌名称的组件
 */
export const BrandName = ({
    children,
    className = '',
    size = 'md'
}: {
    children: ReactNode;
    className?: string;
    size?: 'sm' | 'md' | 'lg' | 'xl';
}) => {
    const sizeClasses = {
        sm: 'text-lg',
        md: 'text-2xl',
        lg: 'text-4xl',
        xl: 'text-6xl',
    };

    return (
        <NoTranslate
            className={`${sizeClasses[size]} font-normal text-gray-900 ${className}`}
            fallback={children}
        >
            {children}
        </NoTranslate>
    );
};