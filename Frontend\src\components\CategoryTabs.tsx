'use client';

interface CategoryTabsProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

const categories = ['Trending', 'Technology', 'Lifestyle', 'Travel', 'Food'];

export default function CategoryTabs({ selectedCategory, onCategoryChange }: CategoryTabsProps) {
  return (
    <div className="flex justify-center mt-8">
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {categories.map((category) => {
          const isSelected = selectedCategory === category;

          return (
            <button
              key={category}
              onClick={() => onCategoryChange(category)}
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                isSelected ? 'text-white bg-blue-600' : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {category}
            </button>
          );
        })}
      </div>
    </div>
  );
}
