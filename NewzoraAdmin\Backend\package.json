{"name": "newzora-admin-backend", "version": "1.0.0", "description": "Newzora管理后台API服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/initDatabase.js", "seed": "node scripts/seedData.js", "db:create": "node ../scripts/database-manager.js create", "db:init": "node ../scripts/database-manager.js init", "db:setup": "node ../scripts/database-manager.js setup", "db:backup": "node ../scripts/database-manager.js backup", "db:status": "node ../scripts/database-manager.js status"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7"}, "devDependencies": {"nodemon": "^3.0.2"}}