/**
 * 统一的表单验证规则
 * 确保前后端验证规则一致
 */

const { body, query, param } = require('express-validator');

// 基础验证规则
const ValidationRules = {
  // 用户名验证
  username: () => 
    body('username')
      .isLength({ min: 3, max: 30 })
      .withMessage('Username must be between 3 and 30 characters')
      .matches(/^[a-zA-Z0-9_-]+$/)
      .withMessage('Username can only contain letters, numbers, underscores, and hyphens')
      .trim(),

  // 邮箱验证
  email: () =>
    body('email')
      .isEmail()
      .withMessage('Please provide a valid email address')
      .normalizeEmail()
      .isLength({ max: 255 })
      .withMessage('Email must not exceed 255 characters'),

  // 密码验证（注册时）
  password: () =>
    body('password')
      .isLength({ min: 8, max: 128 })
      .withMessage('Password must be between 8 and 128 characters')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character'),

  // 密码验证（登录时，较宽松）
  loginPassword: () =>
    body('password')
      .notEmpty()
      .withMessage('Password is required')
      .isLength({ min: 1, max: 128 })
      .withMessage('Password must not exceed 128 characters'),

  // 确认密码验证
  confirmPassword: () =>
    body('confirmPassword')
      .custom((value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Password confirmation does not match password');
        }
        return true;
      }),

  // 显示名称验证
  displayName: () =>
    body('displayName')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('Display name must be between 1 and 100 characters')
      .trim(),

  // 分页验证
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt()
  ],

  // ID参数验证
  id: (paramName = 'id') =>
    param(paramName)
      .isInt({ min: 1 })
      .withMessage(`${paramName} must be a positive integer`)
      .toInt(),

  // 搜索查询验证
  search: () =>
    query('search')
      .optional()
      .isLength({ min: 1, max: 100 })
      .withMessage('Search query must be between 1 and 100 characters')
      .trim(),

  // 角色验证
  role: () =>
    body('role')
      .optional()
      .isIn(['user', 'moderator', 'admin'])
      .withMessage('Role must be one of: user, moderator, admin'),

  // 文章标题验证
  articleTitle: () =>
    body('title')
      .isLength({ min: 1, max: 255 })
      .withMessage('Title must be between 1 and 255 characters')
      .trim(),

  // 文章内容验证
  articleContent: () =>
    body('content')
      .isLength({ min: 1, max: 50000 })
      .withMessage('Content must be between 1 and 50000 characters')
      .trim(),

  // 标签验证
  tags: () =>
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array')
      .custom((tags) => {
        if (tags.length > 10) {
          throw new Error('Maximum 10 tags allowed');
        }
        for (const tag of tags) {
          if (typeof tag !== 'string' || tag.length < 1 || tag.length > 50) {
            throw new Error('Each tag must be a string between 1 and 50 characters');
          }
        }
        return true;
      }),

  // URL验证
  url: (fieldName = 'url') =>
    body(fieldName)
      .optional()
      .isURL()
      .withMessage(`${fieldName} must be a valid URL`)
      .isLength({ max: 2048 })
      .withMessage(`${fieldName} must not exceed 2048 characters`),

  // 重置密码token验证
  resetToken: () =>
    body('token')
      .notEmpty()
      .withMessage('Reset token is required')
      .isLength({ min: 32, max: 128 })
      .withMessage('Invalid reset token format'),

  // 邮箱验证token验证
  emailVerificationToken: () =>
    query('token')
      .notEmpty()
      .withMessage('Verification token is required')
      .isLength({ min: 32, max: 128 })
      .withMessage('Invalid verification token format')
};

// 组合验证规则
const ValidationSets = {
  // 用户注册验证
  register: [
    ValidationRules.username(),
    ValidationRules.email(),
    ValidationRules.password(),
    ValidationRules.confirmPassword(),
    ValidationRules.displayName()
  ],

  // 用户登录验证
  login: [
    body('email')
      .notEmpty()
      .withMessage('Email or username is required')
      .trim(),
    ValidationRules.loginPassword()
  ],

  // 密码重置请求验证
  forgotPassword: [
    ValidationRules.email()
  ],

  // 密码重置验证
  resetPassword: [
    ValidationRules.resetToken(),
    ValidationRules.password(),
    ValidationRules.confirmPassword()
  ],

  // 修改密码验证
  changePassword: [
    body('currentPassword')
      .notEmpty()
      .withMessage('Current password is required'),
    ValidationRules.password(),
    ValidationRules.confirmPassword()
  ],

  // 更新用户资料验证
  updateProfile: [
    ValidationRules.displayName(),
    ValidationRules.url('avatar'),
    body('bio')
      .optional()
      .isLength({ max: 500 })
      .withMessage('Bio must not exceed 500 characters')
      .trim()
  ],

  // 创建文章验证
  createArticle: [
    ValidationRules.articleTitle(),
    ValidationRules.articleContent(),
    ValidationRules.tags(),
    body('categoryId')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Category ID must be a positive integer')
      .toInt()
  ],

  // 分页查询验证
  paginatedQuery: [
    ...ValidationRules.pagination(),
    ValidationRules.search()
  ]
};

// 前端验证规则（用于生成前端验证逻辑）
const FrontendRules = {
  username: {
    required: true,
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: 'Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens'
  },
  
  email: {
    required: true,
    type: 'email',
    maxLength: 255,
    message: 'Please enter a valid email address'
  },
  
  password: {
    required: true,
    minLength: 8,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must be 8-128 characters with at least one lowercase, uppercase, number, and special character'
  },
  
  confirmPassword: {
    required: true,
    mustMatch: 'password',
    message: 'Password confirmation must match password'
  }
};

module.exports = {
  ValidationRules,
  ValidationSets,
  FrontendRules
};
