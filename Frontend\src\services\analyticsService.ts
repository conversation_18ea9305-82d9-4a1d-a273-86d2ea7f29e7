import { Work } from '@/types';
import { UserBehavior } from './recommendationService';

// 分析数据接口
export interface AnalyticsData {
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  totalComments: number;
  totalBookmarks: number;
  engagementRate: number;
  averageViewDuration: number;
  topCategories: Array<{ category: string; count: number; percentage: number }>;
  topTags: Array<{ tag: string; count: number; percentage: number }>;
  userGrowth: Array<{ date: string; users: number }>;
  contentPerformance: Array<{ workId: number; title: string; score: number }>;
}

// 实时统计数据接口
export interface RealTimeStats {
  activeUsers: number;
  currentViews: number;
  trendsLastHour: Array<{ category: string; growth: number }>;
  popularContent: Array<{ workId: number; title: string; currentViews: number }>;
}

// 用户画像接口
export interface UserProfile {
  userId: string;
  demographics: {
    ageGroup: string;
    location: string;
    deviceType: string;
  };
  interests: Array<{ category: string; score: number }>;
  behaviorPattern: {
    activeHours: number[];
    sessionDuration: number;
    contentPreference: string[];
  };
  engagementLevel: 'low' | 'medium' | 'high';
}

// 内容分析服务类
export class AnalyticsService {
  private userBehaviors: UserBehavior[] = [];
  private userProfiles: Map<string, UserProfile> = new Map();
  private realTimeData: Map<string, any> = new Map();

  // 添加用户行为数据
  addUserBehavior(behavior: UserBehavior) {
    this.userBehaviors.push(behavior);
    this.updateRealTimeStats(behavior);
  }

  // 更新实时统计
  private updateRealTimeStats(behavior: UserBehavior) {
    const now = new Date();
    const hourKey = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}`;
    
    if (!this.realTimeData.has(hourKey)) {
      this.realTimeData.set(hourKey, {
        views: 0,
        likes: 0,
        shares: 0,
        comments: 0,
        categories: new Map<string, number>()
      });
    }

    const hourData = this.realTimeData.get(hourKey);
    hourData[behavior.action]++;
    
    const categoryCount = hourData.categories.get(behavior.category) || 0;
    hourData.categories.set(behavior.category, categoryCount + 1);
  }

  // 生成综合分析报告
  generateAnalyticsReport(works: Work[]): AnalyticsData {
    const totalViews = this.userBehaviors.filter(b => b.action === 'view').length;
    const totalLikes = this.userBehaviors.filter(b => b.action === 'like').length;
    const totalShares = this.userBehaviors.filter(b => b.action === 'share').length;
    const totalComments = this.userBehaviors.filter(b => b.action === 'comment').length;
    const totalBookmarks = this.userBehaviors.filter(b => b.action === 'bookmark').length;

    const engagementRate = totalViews > 0 ? 
      (totalLikes + totalShares + totalComments + totalBookmarks) / totalViews : 0;

    const viewBehaviors = this.userBehaviors.filter(b => b.action === 'view' && b.duration);
    const averageViewDuration = viewBehaviors.length > 0 ?
      viewBehaviors.reduce((sum, b) => sum + (b.duration || 0), 0) / viewBehaviors.length : 0;

    // 分析热门分类
    const categoryStats = new Map<string, number>();
    this.userBehaviors.forEach(behavior => {
      const count = categoryStats.get(behavior.category) || 0;
      categoryStats.set(behavior.category, count + 1);
    });

    const topCategories = Array.from(categoryStats.entries())
      .map(([category, count]) => ({
        category,
        count,
        percentage: (count / this.userBehaviors.length) * 100
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // 分析热门标签
    const tagStats = new Map<string, number>();
    this.userBehaviors.forEach(behavior => {
      behavior.tags.forEach(tag => {
        const count = tagStats.get(tag) || 0;
        tagStats.set(tag, count + 1);
      });
    });

    const topTags = Array.from(tagStats.entries())
      .map(([tag, count]) => ({
        tag,
        count,
        percentage: (count / this.userBehaviors.length) * 100
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 20);

    // 生成用户增长数据（模拟）
    const userGrowth = this.generateUserGrowthData();

    // 分析内容表现
    const contentPerformance = this.analyzeContentPerformance(works);

    return {
      totalViews,
      totalLikes,
      totalShares,
      totalComments,
      totalBookmarks,
      engagementRate,
      averageViewDuration,
      topCategories,
      topTags,
      userGrowth,
      contentPerformance
    };
  }

  // 生成用户增长数据
  private generateUserGrowthData(): Array<{ date: string; users: number }> {
    const data = [];
    const now = new Date();
    
    for (let i = 30; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      
      // 模拟用户增长数据
      const baseUsers = 1000;
      const growth = Math.floor(Math.random() * 50) + 10;
      const users = baseUsers + (30 - i) * growth;
      
      data.push({ date: dateStr, users });
    }
    
    return data;
  }

  // 分析内容表现
  private analyzeContentPerformance(works: Work[]): Array<{ workId: number; title: string; score: number }> {
    const workStats = new Map<number, { views: number; likes: number; shares: number; comments: number; bookmarks: number }>();

    // 统计每个作品的行为数据
    this.userBehaviors.forEach(behavior => {
      if (!workStats.has(behavior.workId)) {
        workStats.set(behavior.workId, { views: 0, likes: 0, shares: 0, comments: 0, bookmarks: 0 });
      }
      const stats = workStats.get(behavior.workId)!;
      stats[behavior.action]++;
    });

    // 计算表现分数
    return works.map(work => {
      const stats = workStats.get(work.id) || { views: 0, likes: 0, shares: 0, comments: 0, bookmarks: 0 };
      
      // 综合评分算法
      const score = 
        stats.views * 1 +
        stats.likes * 3 +
        stats.shares * 5 +
        stats.comments * 4 +
        stats.bookmarks * 6;

      return {
        workId: work.id,
        title: work.title,
        score
      };
    }).sort((a, b) => b.score - a.score);
  }

  // 获取实时统计数据
  getRealTimeStats(): RealTimeStats {
    const now = new Date();
    const currentHour = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours()}`;
    const lastHour = `${now.getFullYear()}-${now.getMonth()}-${now.getDate()}-${now.getHours() - 1}`;

    const currentData = this.realTimeData.get(currentHour) || { views: 0, categories: new Map() };
    const lastHourData = this.realTimeData.get(lastHour) || { views: 0, categories: new Map() };

    // 计算趋势
    const trendsLastHour = Array.from(currentData.categories.entries()).map(([category, count]) => {
      const lastCount = lastHourData.categories.get(category) || 0;
      const growth = lastCount > 0 ? ((count - lastCount) / lastCount) * 100 : 0;
      return { category, growth };
    }).sort((a, b) => b.growth - a.growth);

    // 模拟活跃用户和热门内容
    const activeUsers = Math.floor(Math.random() * 500) + 100;
    const popularContent = [
      { workId: 1, title: 'AI Revolution in 2024', currentViews: Math.floor(Math.random() * 100) + 50 },
      { workId: 2, title: 'Sustainable Living Tips', currentViews: Math.floor(Math.random() * 80) + 30 },
      { workId: 3, title: 'Crypto Market Analysis', currentViews: Math.floor(Math.random() * 60) + 20 }
    ];

    return {
      activeUsers,
      currentViews: currentData.views,
      trendsLastHour: trendsLastHour.slice(0, 5),
      popularContent
    };
  }

  // 生成用户画像
  generateUserProfile(userId: string): UserProfile | null {
    const userBehaviors = this.userBehaviors.filter(b => b.userId === userId);
    if (userBehaviors.length === 0) return null;

    // 分析用户兴趣
    const categoryInterests = new Map<string, number>();
    userBehaviors.forEach(behavior => {
      const weight = this.getActionWeight(behavior.action);
      const current = categoryInterests.get(behavior.category) || 0;
      categoryInterests.set(behavior.category, current + weight);
    });

    const interests = Array.from(categoryInterests.entries())
      .map(([category, score]) => ({ category, score }))
      .sort((a, b) => b.score - a.score);

    // 分析行为模式
    const hourlyActivity = new Array(24).fill(0);
    userBehaviors.forEach(behavior => {
      const hour = new Date(behavior.timestamp).getHours();
      hourlyActivity[hour]++;
    });

    const activeHours = hourlyActivity
      .map((count, hour) => ({ hour, count }))
      .filter(item => item.count > 0)
      .sort((a, b) => b.count - a.count)
      .slice(0, 8)
      .map(item => item.hour);

    // 计算参与度等级
    const totalActions = userBehaviors.length;
    const engagementActions = userBehaviors.filter(b => b.action !== 'view').length;
    const engagementRatio = totalActions > 0 ? engagementActions / totalActions : 0;

    let engagementLevel: 'low' | 'medium' | 'high' = 'low';
    if (engagementRatio > 0.3) engagementLevel = 'high';
    else if (engagementRatio > 0.1) engagementLevel = 'medium';

    return {
      userId,
      demographics: {
        ageGroup: '25-34', // 模拟数据
        location: 'US',
        deviceType: 'desktop'
      },
      interests,
      behaviorPattern: {
        activeHours,
        sessionDuration: 15, // 模拟平均会话时长（分钟）
        contentPreference: interests.slice(0, 3).map(i => i.category)
      },
      engagementLevel
    };
  }

  // 获取行为权重
  private getActionWeight(action: UserBehavior['action']): number {
    const weights = {
      view: 1,
      like: 3,
      share: 5,
      comment: 4,
      bookmark: 6,
      download: 7
    };
    return weights[action] || 1;
  }

  // 预测内容趋势
  predictContentTrends(works: Work[]): Array<{ category: string; trend: 'rising' | 'stable' | 'declining'; score: number }> {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;
    const oneWeekMs = 7 * oneDayMs;

    const categoryTrends = new Map<string, { recent: number; older: number }>();

    this.userBehaviors.forEach(behavior => {
      const behaviorTime = new Date(behavior.timestamp).getTime();
      const daysSince = (now - behaviorTime) / oneDayMs;

      if (!categoryTrends.has(behavior.category)) {
        categoryTrends.set(behavior.category, { recent: 0, older: 0 });
      }

      const trends = categoryTrends.get(behavior.category)!;
      if (daysSince <= 3) {
        trends.recent++;
      } else if (daysSince <= 7) {
        trends.older++;
      }
    });

    return Array.from(categoryTrends.entries()).map(([category, data]) => {
      const recentRate = data.recent / 3; // 最近3天的日均活动
      const olderRate = data.older / 4; // 之前4天的日均活动
      
      let trend: 'rising' | 'stable' | 'declining' = 'stable';
      let score = 0;

      if (recentRate > olderRate * 1.2) {
        trend = 'rising';
        score = (recentRate - olderRate) / olderRate;
      } else if (recentRate < olderRate * 0.8) {
        trend = 'declining';
        score = (olderRate - recentRate) / olderRate;
      } else {
        score = recentRate;
      }

      return { category, trend, score };
    }).sort((a, b) => b.score - a.score);
  }
}

// 创建全局分析服务实例
export const analyticsService = new AnalyticsService();
