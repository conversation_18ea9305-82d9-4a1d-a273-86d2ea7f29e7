'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';

interface PatrolAlert {
  id: number;
  type: 'spam' | 'fake_engagement' | 'duplicate_content' | 'revenue_fraud';
  severity: 'high' | 'critical';
  userId: number;
  username: string;
  contentId?: number;
  contentTitle?: string;
  description: string;
  detectedAt: string;
  status: 'pending' | 'investigating' | 'resolved';
  evidence: {
    suspiciousMetrics: string[];
    patterns: string[];
    riskScore: number;
  };
}

interface PatrolStats {
  totalScanned: number;
  alertsGenerated: number;
  fraudPrevented: number;
  accuracyRate: number;
}

export default function AIPatrolPage() {
  const [alerts, setAlerts] = useState<PatrolAlert[]>([]);
  const [stats, setStats] = useState<PatrolStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'critical'>('all');

  useEffect(() => {
    loadPatrolData();
  }, []);

  const loadPatrolData = async () => {
    try {
      setLoading(true);
      
      const mockStats: PatrolStats = {
        totalScanned: 45230,
        alertsGenerated: 127,
        fraudPrevented: 89,
        accuracyRate: 94.2
      };

      const mockAlerts: PatrolAlert[] = [
        {
          id: 1,
          type: 'fake_engagement',
          severity: 'critical',
          userId: 1001,
          username: 'suspicious_user_1',
          contentId: 2001,
          contentTitle: 'AI Technology Trends 2024',
          description: '检测到异常点赞和评论模式，疑似使用机器人刷量',
          detectedAt: new Date(Date.now() - 1800000).toISOString(),
          status: 'pending',
          evidence: {
            suspiciousMetrics: ['点赞速度异常(100次/分钟)', '评论内容重复度85%', 'IP地址集中'],
            patterns: ['短时间内大量互动', '评论质量低', '用户行为不自然'],
            riskScore: 92
          }
        },
        {
          id: 2,
          type: 'revenue_fraud',
          severity: 'high',
          userId: 1002,
          username: 'creator_abc',
          contentId: 2002,
          contentTitle: 'Web Development Tutorial',
          description: '检测到收益异常，疑似通过虚假流量获取收益',
          detectedAt: new Date(Date.now() - 3600000).toISOString(),
          status: 'investigating',
          evidence: {
            suspiciousMetrics: ['观看时长异常短', '跳出率100%', '广告点击率异常高'],
            patterns: ['流量来源可疑', '用户留存率为0', '收益与质量不匹配'],
            riskScore: 87
          }
        }
      ];

      setTimeout(() => {
        setStats(mockStats);
        setAlerts(mockAlerts);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('加载AI巡逻数据失败:', error);
      setLoading(false);
    }
  };

  const handleAlertAction = async (alertId: number, action: 'investigate' | 'resolve') => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId 
        ? { ...alert, status: action === 'investigate' ? 'investigating' : 'resolved' }
        : alert
    ));
  };

  const getSeverityColor = (severity: string) => {
    return severity === 'critical' 
      ? 'bg-red-100 text-red-800 border-red-200'
      : 'bg-orange-100 text-orange-800 border-orange-200';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'fake_engagement': return '🤖';
      case 'revenue_fraud': return '💰';
      case 'duplicate_content': return '📋';
      default: return '⚠️';
    }
  };

  const filteredAlerts = alerts.filter(alert => {
    if (filter === 'all') return true;
    if (filter === 'pending') return alert.status === 'pending';
    return alert.severity === filter;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">🤖 AI巡逻系统</h1>
          <p className="text-gray-600 mt-1">智能监测平台异常行为和欺诈活动</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">扫描总数</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.totalScanned.toLocaleString()}</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 text-xl">🔍</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">生成警报</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.alertsGenerated}</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                <span className="text-red-600 text-xl">⚠️</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">阻止欺诈</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.fraudPrevented}</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600 text-xl">🛡️</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">准确率</p>
                <p className="text-2xl font-bold text-gray-900">{stats?.accuracyRate}%</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600 text-xl">🎯</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex border-b border-gray-200 mb-6">
          {[
            { key: 'all', label: '全部' },
            { key: 'pending', label: '待处理' },
            { key: 'critical', label: '严重' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`px-4 py-3 text-sm font-medium relative ${
                filter === tab.key
                  ? 'text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
              {filter === tab.key && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
              )}
            </button>
          ))}
        </div>

        <div className="space-y-4">
          {filteredAlerts.map((alert) => (
            <div key={alert.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="text-2xl">{getTypeIcon(alert.type)}</div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{alert.description}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getSeverityColor(alert.severity)}`}>
                        {alert.severity.toUpperCase()}
                      </span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-600">用户: <span className="font-medium">{alert.username}</span></p>
                        {alert.contentTitle && (
                          <p className="text-sm text-gray-600">内容: <span className="font-medium">{alert.contentTitle}</span></p>
                        )}
                        <p className="text-sm text-gray-600">风险评分: <span className="font-bold text-red-600">{alert.evidence.riskScore}/100</span></p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">检测时间: {new Date(alert.detectedAt).toLocaleString()}</p>
                        <p className="text-sm text-gray-600">状态: 
                          <span className={`ml-1 font-medium ${
                            alert.status === 'pending' ? 'text-yellow-600' :
                            alert.status === 'investigating' ? 'text-blue-600' : 'text-green-600'
                          }`}>
                            {alert.status}
                          </span>
                        </p>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">可疑指标:</h4>
                      <div className="flex flex-wrap gap-2">
                        {alert.evidence.suspiciousMetrics.map((metric, index) => (
                          <span key={index} className="px-2 py-1 bg-red-50 text-red-700 text-xs rounded-full border border-red-200">
                            {metric}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">行为模式:</h4>
                      <div className="flex flex-wrap gap-2">
                        {alert.evidence.patterns.map((pattern, index) => (
                          <span key={index} className="px-2 py-1 bg-orange-50 text-orange-700 text-xs rounded-full border border-orange-200">
                            {pattern}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {alert.status === 'pending' && (
                <div className="flex space-x-3 mt-4 pt-4 border-t border-gray-200">
                  <button
                    onClick={() => handleAlertAction(alert.id, 'investigate')}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700"
                  >
                    开始调查
                  </button>
                  <button
                    onClick={() => handleAlertAction(alert.id, 'resolve')}
                    className="px-4 py-2 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700"
                  >
                    标记解决
                  </button>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}