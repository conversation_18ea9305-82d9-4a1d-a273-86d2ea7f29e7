'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Create follows table
    await queryInterface.createTable('follows', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      followerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      followingId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      status: {
        type: Sequelize.ENUM('active', 'blocked'),
        defaultValue: 'active',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Create messages table
    await queryInterface.createTable('messages', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      senderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      receiverId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      content: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      messageType: {
        type: Sequelize.ENUM('text', 'image', 'file'),
        defaultValue: 'text',
      },
      attachmentUrl: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      isRead: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      readAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      isDeleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      deletedBy: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        defaultValue: [],
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Create tags table
    await queryInterface.createTable('tags', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
      },
      slug: {
        type: Sequelize.STRING(60),
        allowNull: false,
        unique: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      color: {
        type: Sequelize.STRING(7),
        defaultValue: '#3B82F6',
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      usageCount: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Create user_tags table
    await queryInterface.createTable('user_tags', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      tagId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'tags',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      interestLevel: {
        type: Sequelize.ENUM('low', 'medium', 'high'),
        defaultValue: 'medium',
      },
      source: {
        type: Sequelize.ENUM('manual', 'auto', 'imported'),
        defaultValue: 'manual',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Create activities table
    await queryInterface.createTable('activities', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      activityType: {
        type: Sequelize.ENUM(
          'article_created',
          'article_liked',
          'article_shared',
          'user_followed',
          'comment_created',
          'profile_updated'
        ),
        allowNull: false,
      },
      targetType: {
        type: Sequelize.ENUM('article', 'user', 'comment'),
        allowNull: true,
      },
      targetId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      isPublic: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Create shares table
    await queryInterface.createTable('shares', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      articleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'articles',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      platform: {
        type: Sequelize.ENUM(
          'facebook',
          'twitter',
          'linkedin',
          'whatsapp',
          'telegram',
          'email',
          'copy_link',
          'other'
        ),
        allowNull: false,
      },
      ipAddress: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      referrer: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Add indexes for better performance
    await queryInterface.addIndex('follows', ['followerId', 'followingId'], { unique: true });
    await queryInterface.addIndex('follows', ['followerId']);
    await queryInterface.addIndex('follows', ['followingId']);
    await queryInterface.addIndex('follows', ['status']);
    await queryInterface.addIndex('follows', ['createdAt']);

    await queryInterface.addIndex('messages', ['senderId']);
    await queryInterface.addIndex('messages', ['receiverId']);
    await queryInterface.addIndex('messages', ['senderId', 'receiverId']);
    await queryInterface.addIndex('messages', ['isRead']);
    await queryInterface.addIndex('messages', ['createdAt']);
    await queryInterface.addIndex('messages', ['isDeleted']);

    await queryInterface.addIndex('tags', ['name']);
    await queryInterface.addIndex('tags', ['slug']);
    await queryInterface.addIndex('tags', ['isActive']);
    await queryInterface.addIndex('tags', ['usageCount']);

    await queryInterface.addIndex('user_tags', ['userId', 'tagId'], { unique: true });
    await queryInterface.addIndex('user_tags', ['userId']);
    await queryInterface.addIndex('user_tags', ['tagId']);
    await queryInterface.addIndex('user_tags', ['interestLevel']);

    await queryInterface.addIndex('activities', ['userId']);
    await queryInterface.addIndex('activities', ['activityType']);
    await queryInterface.addIndex('activities', ['targetType', 'targetId']);
    await queryInterface.addIndex('activities', ['createdAt']);
    await queryInterface.addIndex('activities', ['isPublic']);
    await queryInterface.addIndex('activities', ['userId', 'createdAt']);

    await queryInterface.addIndex('shares', ['userId']);
    await queryInterface.addIndex('shares', ['articleId']);
    await queryInterface.addIndex('shares', ['platform']);
    await queryInterface.addIndex('shares', ['createdAt']);
    await queryInterface.addIndex('shares', ['articleId', 'platform']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('shares');
    await queryInterface.dropTable('activities');
    await queryInterface.dropTable('user_tags');
    await queryInterface.dropTable('tags');
    await queryInterface.dropTable('messages');
    await queryInterface.dropTable('follows');
  },
};
