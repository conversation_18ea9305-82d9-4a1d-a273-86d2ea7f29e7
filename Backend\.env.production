# Newzora 生产环境配置文件
# 警告：此文件包含敏感信息，请勿提交到版本控制系统

# 应用基础配置
NODE_ENV=production
PORT=5000

# 数据库配置 - 生产环境
# 注意：请使用环境变量或密钥管理服务来管理这些敏感信息
DB_HOST=your_production_db_host
DB_PORT=5432
DB_NAME=newzora_production
DB_USER=your_production_db_user
DB_PASSWORD=your_production_db_password

# 或者使用数据库URL（推荐用于云部署）
# DATABASE_URL=postgresql://username:password@host:port/database

# JWT 安全配置 - 生产环境
# 示例密钥（请替换为您自己生成的密钥）
JWT_SECRET=UAGqpb6EvJqyTu5Djp++hSe8vU+gBNV3+F1LGunDPdJDaCq+APkZ4nTfwj0fGI+ySlspTwVfhvQR7vLLoveKew==
JWT_EXPIRES_IN=7d

# 前端URL配置
FRONTEND_URL=https://your-domain.com

# 邮件服务配置 - 生产环境
# 选择邮件服务提供商: gmail, sendgrid, aws_ses, outlook, custom
EMAIL_PROVIDER=gmail

# 选项1：Gmail App Password (推荐用于开发和小规模部署)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-gmail-app-password
EMAIL_FROM=Newzora <<EMAIL>>

# 选项2：SendGrid (推荐用于生产环境)
# EMAIL_PROVIDER=sendgrid
# EMAIL_HOST=smtp.sendgrid.net
# EMAIL_PORT=587
# EMAIL_USER=apikey
# EMAIL_PASS=your-sendgrid-api-key
# <AUTHOR> <EMAIL>

# 选项3：AWS SES (推荐用于AWS部署)
# EMAIL_PROVIDER=aws_ses
# EMAIL_HOST=email-smtp.us-east-1.amazonaws.com
# EMAIL_PORT=587
# EMAIL_USER=your-smtp-username
# EMAIL_PASS=your-smtp-password
# <AUTHOR> <EMAIL>
# AWS_SES_HOST=email-smtp.us-east-1.amazonaws.com  # 根据AWS区域调整

# 选项4：Microsoft Outlook
# EMAIL_PROVIDER=outlook
# EMAIL_HOST=smtp-mail.outlook.com
# EMAIL_PORT=587
# EMAIL_USER=<EMAIL>
# EMAIL_PASS=your-outlook-password
# <AUTHOR> <EMAIL>

# OAuth 社交登录配置 - 生产环境
# Google OAuth
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret

# Facebook OAuth
FACEBOOK_APP_ID=your-production-facebook-app-id
FACEBOOK_APP_SECRET=your-production-facebook-app-secret

# 安全配置
BCRYPT_ROUNDS=12
PASSWORD_RESET_EXPIRES=3600000
EMAIL_VERIFICATION_EXPIRES=86400000

# Web Push 通知配置
# 示例VAPID密钥（请替换为您自己生成的密钥）
VAPID_PUBLIC_KEY=BFkZV3caq146y8YyOw6nMUhctN1M4D10-Oc5-afXvj_8RWJG71LtIPmQk4TrA0Prd1bdzAkfzidlLzoWJfEsIR4
VAPID_PRIVATE_KEY=84asPOYq_CfuKmFmzcj_E0_nMfBSIC-bcSATmILRIlk
VAPID_SUBJECT=mailto:<EMAIL>

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads

# 速率限制配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 日志配置
LOG_LEVEL=info
LOG_FILE=/app/logs/app.log

# Redis 缓存配置（可选）
# REDIS_URL=redis://your-redis-host:6379
# REDIS_PASSWORD=your-redis-password

# 监控配置（可选）
# SENTRY_DSN=your-sentry-dsn
# NEW_RELIC_LICENSE_KEY=your-newrelic-key

# SSL/TLS 配置
# SSL_CERT_PATH=/path/to/ssl/cert.pem
# SSL_KEY_PATH=/path/to/ssl/private.key

# 数据库连接池配置
DB_POOL_MAX=20
DB_POOL_MIN=5
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# CORS 配置
CORS_ORIGIN=https://your-domain.com,https://www.your-domain.com

# Session 配置
# 示例Session密钥（请替换为您自己生成的密钥）
SESSION_SECRET=NuyfVND1Hj4SGATk40LD1/ffmtYUcyJmok5B6/7rvnU=
SESSION_SECURE=true
SESSION_MAX_AGE=86400000

# 备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_REGION=your-aws-region
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
