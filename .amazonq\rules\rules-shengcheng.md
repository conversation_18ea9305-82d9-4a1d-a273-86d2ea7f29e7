Newzora 项目代码生成规则（rule-newzora.md）

✅ 技术栈与架构规范

前端框架：Next.js 14（使用 App Router）

编程语言：TypeScript（必须强类型、无 any）

样式：Tailwind CSS（className 应用、无内联 style）

认证系统：Supabase Auth（Email/Password）

状态管理：React Context API

数据存取：@supabase/supabase-js

表单校验：自定义验证（推荐 Zod / Yup）

📁 项目结构规则

所有页面文件放在 /app/ 路由结构中，使用 page.tsx 命名。

所有可复用组件存放于 /components/，按模块文件夹归类。

所有 API 封装放在 /lib/api.ts 或 /lib/api/*.ts。

所有上下文封装（如 Auth）放在 /contexts/ 目录。

所有类型定义统一放在 /types/ 或 *.d.ts 中。

🧠 代码生成规则

必须使用函数组件（Function Component）+ TypeScript。

所有 Props 必须使用显式类型定义（ComponentNameProps）。

所有异步操作使用 async/await，包裹 try/catch 错误处理。

所有组件必须开箱即用：包含完整 import、export、路径正确。

不允许使用 any、window、localStorage 等易错方案。

所有文件必须通过 TypeScript 类型检查（tsc）。

每段代码需包含注释，解释关键逻辑或变量作用，生成一次性完整正确稳定，可行的代码，减少反修复的次数

表单应为受控组件，并具备基本校验逻辑。

所有页面组件应支持 SEO（如 <title>、<meta> 标签）。

表单或请求提交后应有 Loading 状态或成功/失败反馈。

所有用户操作（如注册、登录、退出）应具备状态提示。

🔐 安全规则

禁止暴露任何 Supabase service_role 密钥或管理 token。

API 请求需使用受限权限 key（如客户端默认 Key）。

用户输入必须进行校验，防止注入、XSS、逻辑绕过。

必须启用 Supabase RLS（行级安全）来隔离多用户数据。

🛠️ 自动自检机制

每次生成代码后，必须包含以下 Checklist：
✅ 代码生成完整性检查：

- [x] 所有 import 完整且路径正确
- [x] 无 TypeScript 类型错误（tsc）
- [x] 使用 try/catch 捕获异步错误
- [x] 组件 props 明确定义类型
- [x] 页面/组件具备 SEO 标签或可访问路径
- [x] 样式使用 Tailwind，兼容 Chrome/Edge/Safari/Firefox
- [x] 提供基本的用户交互反馈（如加载、错误、成功提示）
- [x] 删除或精简代码/依赖前已判断是否影响现有功能