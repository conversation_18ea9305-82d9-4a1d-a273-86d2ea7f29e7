const User = require('./User');
const Article = require('./Article');
const Comment = require('./Comment');
const Follow = require('./Follow');
const Message = require('./Message');
const Tag = require('./Tag');
const UserTag = require('./UserTag');
const Activity = require('./Activity');
const Share = require('./Share');
const Draft = require('./Draft');
const MediaFile = require('./MediaFile');
const ContentReview = require('./ContentReview');
const ReviewRule = require('./ReviewRule');
const UserBehavior = require('./UserBehavior');
const ReadingStats = require('./ReadingStats');
const SearchLog = require('./SearchLog');
const UserProfile = require('./UserProfile');
const Notification = require('./Notification');
const NotificationPreference = require('./NotificationPreference');
const PushSubscription = require('./PushSubscription');
const WithdrawalRequest = require('./WithdrawalRequest');
const UserBalance = require('./UserBalance');

// User associations
// User.hasMany(Article, { foreignKey: 'authorId', as: 'articles' }); // Commented out - no authorId in articles table
User.hasMany(Comment, { foreignKey: 'authorId', as: 'comments' });
User.hasMany(Activity, { foreignKey: 'userId', as: 'activities' });
User.hasMany(Share, { foreignKey: 'userId', as: 'shares' });

// Follow associations
User.belongsToMany(User, {
  through: Follow,
  as: 'following',
  foreignKey: 'followerId',
  otherKey: 'followingId',
});

User.belongsToMany(User, {
  through: Follow,
  as: 'followers',
  foreignKey: 'followingId',
  otherKey: 'followerId',
});

Follow.belongsTo(User, { as: 'follower', foreignKey: 'followerId' });
Follow.belongsTo(User, { as: 'following', foreignKey: 'followingId' });

// Message associations
User.hasMany(Message, { foreignKey: 'senderId', as: 'sentMessages' });
User.hasMany(Message, { foreignKey: 'receiverId', as: 'receivedMessages' });
Message.belongsTo(User, { as: 'sender', foreignKey: 'senderId' });
Message.belongsTo(User, { as: 'receiver', foreignKey: 'receiverId' });

// Tag associations
User.belongsToMany(Tag, {
  through: UserTag,
  as: 'tags',
  foreignKey: 'userId',
  otherKey: 'tagId',
});

Tag.belongsToMany(User, {
  through: UserTag,
  as: 'users',
  foreignKey: 'tagId',
  otherKey: 'userId',
});

UserTag.belongsTo(User, { foreignKey: 'userId' });
UserTag.belongsTo(Tag, { foreignKey: 'tagId' });

// Article associations
// Article.belongsTo(User, { foreignKey: 'authorId', as: 'authorUser' }); // Commented out - no authorId in articles table
Article.hasMany(Comment, { foreignKey: 'articleId', as: 'comments' });
Article.hasMany(Share, { foreignKey: 'articleId', as: 'shares' });

// Comment associations
Comment.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });
Comment.belongsTo(User, { foreignKey: 'authorId', as: 'authorUser' });

// Activity associations
Activity.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Share associations
Share.belongsTo(User, { foreignKey: 'userId', as: 'user' });
Share.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });

// Draft associations
User.hasMany(Draft, { foreignKey: 'authorId', as: 'drafts' });
Draft.belongsTo(User, { foreignKey: 'authorId', as: 'author' });

Draft.belongsTo(Draft, { foreignKey: 'parentDraftId', as: 'parentDraft' });
Draft.hasMany(Draft, { foreignKey: 'parentDraftId', as: 'versions' });

Draft.belongsTo(Article, { foreignKey: 'publishedArticleId', as: 'publishedArticle' });
Article.hasOne(Draft, { foreignKey: 'publishedArticleId', as: 'sourceDraft' });

// MediaFile associations
User.hasMany(MediaFile, { foreignKey: 'uploaderId', as: 'uploadedFiles' });
MediaFile.belongsTo(User, { foreignKey: 'uploaderId', as: 'uploader' });

// ContentReview associations
User.hasMany(ContentReview, { foreignKey: 'submitterId', as: 'submittedReviews' });
User.hasMany(ContentReview, { foreignKey: 'reviewerId', as: 'reviewedContent' });
ContentReview.belongsTo(User, { foreignKey: 'submitterId', as: 'submitter' });
ContentReview.belongsTo(User, { foreignKey: 'reviewerId', as: 'reviewer' });

// ReviewRule associations
User.hasMany(ReviewRule, { foreignKey: 'createdBy', as: 'createdRules' });
User.hasMany(ReviewRule, { foreignKey: 'updatedBy', as: 'updatedRules' });
ReviewRule.belongsTo(User, { foreignKey: 'createdBy', as: 'creator' });
ReviewRule.belongsTo(User, { foreignKey: 'updatedBy', as: 'updater' });

// Analytics associations
User.hasMany(UserBehavior, { foreignKey: 'userId', as: 'behaviors' });
User.hasMany(ReadingStats, { foreignKey: 'userId', as: 'readingStats' });
User.hasMany(SearchLog, { foreignKey: 'userId', as: 'searchLogs' });
User.hasOne(UserProfile, { foreignKey: 'userId', as: 'profile' });

UserBehavior.belongsTo(User, { foreignKey: 'userId', as: 'user' });
ReadingStats.belongsTo(User, { foreignKey: 'userId', as: 'user' });
ReadingStats.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });
SearchLog.belongsTo(User, { foreignKey: 'userId', as: 'user' });
UserProfile.belongsTo(User, { foreignKey: 'userId', as: 'user' });

Article.hasMany(ReadingStats, { foreignKey: 'articleId', as: 'readingStats' });

// Notification associations
User.hasMany(Notification, { foreignKey: 'userId', as: 'notifications' });
Notification.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasOne(NotificationPreference, { foreignKey: 'userId', as: 'notificationPreference' });
NotificationPreference.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasMany(PushSubscription, { foreignKey: 'userId', as: 'pushSubscriptions' });
PushSubscription.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Withdrawal and Balance associations
User.hasMany(WithdrawalRequest, { foreignKey: 'userId', as: 'withdrawalRequests' });
WithdrawalRequest.belongsTo(User, { foreignKey: 'userId', as: 'user' });

User.hasOne(UserBalance, { foreignKey: 'userId', as: 'balance' });
UserBalance.belongsTo(User, { foreignKey: 'userId', as: 'user' });

// Notification associations with articles and users
Notification.belongsTo(User, { foreignKey: 'fromUserId', as: 'fromUser' });
Notification.belongsTo(Article, { foreignKey: 'articleId', as: 'article' });

module.exports = {
  User,
  Article,
  Comment,
  Follow,
  Message,
  Tag,
  UserTag,
  Activity,
  Share,
  Draft,
  MediaFile,
  ContentReview,
  ReviewRule,
  UserBehavior,
  ReadingStats,
  SearchLog,
  UserProfile,
  Notification,
  NotificationPreference,
  PushSubscription,
  WithdrawalRequest,
  UserBalance,
};
