# OneNews 工具和脚本

这个目录包含了 OneNews 项目的各种工具、脚本和配置文件。

## 📁 目录结构

### `scripts/` - 脚本文件
- `cleanup.ps1` - 项目清理脚本
- `database-management.ps1` - 数据库管理脚本
- `docker-dev.sh` / `docker-prod.sh` - Docker 脚本
- `nginx-test.ps1` / `nginx-test.sh` - Nginx 测试脚本
- `test-*.js` - 各种测试脚本

### `nginx/` - Nginx 配置
- `nginx.conf` - 主配置文件
- `conf.d/` - 站点配置
- `ssl/` - SSL 证书目录

### `docker/` - Docker 相关文件
- 预留给 Docker 相关的配置文件

## 🔧 常用脚本

### 项目清理
```powershell
# 清理项目，删除不必要的文件
.\tools\scripts\cleanup.ps1
```

### 数据库管理
```powershell
# 数据库相关操作
.\tools\scripts\database-management.ps1
```

### Docker 操作
```bash
# 开发环境
.\tools\scripts\docker-dev.sh

# 生产环境
.\tools\scripts\docker-prod.sh
```

### Nginx 测试
```powershell
# 测试 Nginx 配置
.\tools\scripts\nginx-test.ps1
```

## 📝 使用说明

1. **从根目录运行脚本**：所有脚本都应该从项目根目录运行
2. **权限设置**：PowerShell 脚本可能需要执行权限
3. **路径引用**：脚本中的路径都是相对于项目根目录的

## 🔍 脚本详情

### cleanup.ps1
- 清理根目录的临时文件
- 删除不必要的 node_modules
- 检查项目结构完整性

### database-management.ps1
- 数据库连接测试
- 数据迁移和种子数据
- 数据库备份和恢复

### nginx-test.ps1
- 测试 Nginx 配置语法
- 检查反向代理设置
- SSL 证书验证

## ⚠️ 注意事项

- 运行脚本前请确保已安装相关依赖
- 生产环境脚本请谨慎使用
- 建议在测试环境先验证脚本功能
