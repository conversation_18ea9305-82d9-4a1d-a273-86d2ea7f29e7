'use client';

import React from 'react';
import { useTheme, themes } from '@/contexts/ThemeContext';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { cn } from '@/lib/utils';

export function ThemePreview() {
  const { theme, setTheme, actualTheme } = useTheme();

  const themeColors = {
    light: {
      primary: '#3b82f6',
      secondary: '#a855f7',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
    },
    dark: {
      primary: '#818cf8',
      secondary: '#a78bfa',
      background: '#0f172a',
      surface: '#1e293b',
      text: '#f1f5f9',
    },
    cyberpunk: {
      primary: '#00f2fe',
      secondary: '#4facfe',
      background: '#0a0a0a',
      surface: '#1a1a2e',
      text: '#00f2fe',
    },
    nature: {
      primary: '#10b981',
      secondary: '#16a34a',
      background: '#f0fdf4',
      surface: '#dcfce7',
      text: '#064e3b',
    },
    ocean: {
      primary: '#0ea5e9',
      secondary: '#0284c7',
      background: '#f0f9ff',
      surface: '#e0f2fe',
      text: '#0c4a6e',
    },
    sunset: {
      primary: '#f97316',
      secondary: '#dc2626',
      background: '#fef3f2',
      surface: '#fed7d7',
      text: '#7c2d12',
    },
  };

  return (
    <div className="space-y-6">
      {/* 当前主题信息 */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-text-primary mb-2">Theme Customization</h2>
        <p className="text-text-secondary">
          Current theme: <span className="font-semibold">{themes[theme].name}</span>
        </p>
      </div>

      {/* 主题网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Object.entries(themes).map(([themeKey, themeConfig]) => {
          if (themeKey === 'auto') return null;

          const colors = themeColors[themeKey as keyof typeof themeColors];
          const isSelected = theme === themeKey;

          return (
            <Card
              key={themeKey}
              variant={isSelected ? 'elevated' : 'default'}
              hover="lift"
              padding="md"
              className={cn(
                'cursor-pointer transition-all duration-300',
                isSelected && 'ring-2 ring-primary ring-offset-2'
              )}
              onClick={() => setTheme(themeKey as any)}
            >
              {/* 主题预览 */}
              <div
                className="w-full h-24 rounded-lg mb-4 relative overflow-hidden"
                style={{ backgroundColor: colors.background }}
              >
                {/* 背景层 */}
                <div
                  className="absolute inset-0"
                  style={{ backgroundColor: colors.surface, opacity: 0.8 }}
                />

                {/* 色彩条 */}
                <div className="absolute bottom-0 left-0 right-0 h-3 flex">
                  <div className="flex-1" style={{ backgroundColor: colors.primary }} />
                  <div className="flex-1" style={{ backgroundColor: colors.secondary }} />
                </div>

                {/* 文本示例 */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-sm font-medium" style={{ color: colors.text }}>
                    {themeConfig.icon} {themeConfig.name}
                  </div>
                </div>

                {/* Selection indicator */}
                {isSelected && (
                  <div className="absolute top-2 right-2">
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <svg
                        className="w-4 h-4 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                  </div>
                )}
              </div>

              {/* Theme information */}
              <div className="text-center">
                <h3 className="font-semibold text-text-primary mb-1">{themeConfig.name}</h3>
                <p className="text-sm text-text-muted">{themeConfig.description}</p>
              </div>
            </Card>
          );
        })}
      </div>

      {/* 自动主题选项 */}
      <Card variant="outlined" padding="md">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span className="text-2xl">{themes.auto.icon}</span>
            <div>
              <h3 className="font-semibold text-text-primary">{themes.auto.name}</h3>
              <p className="text-sm text-text-muted">{themes.auto.description}</p>
            </div>
          </div>
          <Button
            variant={theme === 'auto' ? 'primary' : 'outline'}
            size="sm"
            onClick={() => setTheme('auto')}
          >
            {theme === 'auto' ? 'Active' : 'Enable'}
          </Button>
        </div>
      </Card>

      {/* 主题特性展示 */}
      <div className="grid md:grid-cols-2 gap-6">
        <Card variant="glass" padding="lg">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Theme Features</h3>
          <ul className="space-y-2 text-sm text-text-secondary">
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 bg-primary rounded-full"></span>
              Automatic dark/light mode detection
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 bg-secondary rounded-full"></span>
              Smooth theme transitions
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 bg-accent rounded-full"></span>
              Persistent theme preferences
            </li>
            <li className="flex items-center gap-2">
              <span className="w-2 h-2 bg-success rounded-full"></span>
              Accessibility compliant colors
            </li>
          </ul>
        </Card>

        <Card variant="glass" padding="lg">
          <h3 className="text-lg font-semibold text-text-primary mb-4">Current Theme Info</h3>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span className="text-text-muted">Selected:</span>
              <span className="text-text-primary font-medium">{themes[theme].name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-muted">Active:</span>
              <span className="text-text-primary font-medium">{themes[actualTheme].name}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-text-muted">System:</span>
              <span className="text-text-primary font-medium">
                {window.matchMedia('(prefers-color-scheme: dark)').matches ? 'Dark' : 'Light'}
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
