'use client';

import React, { useState } from 'react';

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  isOpen: boolean;
  onClose: () => void;
}

const EmojiPicker = ({ onEmojiSelect, isOpen, onClose }: EmojiPickerProps) => {
  const [activeTab, setActiveTab] = useState<'emoji' | 'sticker' | 'custom'>('emoji');

  const emojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬',
    '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗',
    '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
    '👆', '🖕', '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏',
    '🙌', '🤲', '🤝', '🙏', '✍️', '💪', '🦾', '🦿', '🦵', '🦶',
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️'
  ];

  const stickers = [
    '🎉', '🎊', '🎈', '🎁', '🎂', '🍰', '🎯', '🎪', '🎨', '🎭',
    '🎪', '🎨', '🎭', '🎪', '🎨', '🎭', '🎪', '🎨', '🎭', '🎪'
  ];

  const customMedia = [
    { type: 'gif', url: 'https://i.giphy.com/3o7abKhOpu0NwenH3O.gif', name: 'Happy' },
    { type: 'gif', url: 'https://i.giphy.com/l0MYt5jPR6QX5pnqM.gif', name: 'Thumbs Up' },
    { type: 'gif', url: 'https://i.giphy.com/26u4cqiYI30juCOGY.gif', name: 'Clap' },
    { type: 'gif', url: 'https://i.giphy.com/3o6Zt4HU9uwXmXSAuI.gif', name: 'Love' },
    { type: 'gif', url: 'https://i.giphy.com/xT9IgG50Fb7Mi0prBC.gif', name: 'Wow' },
    { type: 'gif', url: 'https://i.giphy.com/l3q2K5jinAlChoCLS.gif', name: 'Laugh' }
  ];

  if (!isOpen) return null;

  return (
    <div className="absolute bottom-full mb-2 left-0 bg-white rounded-lg shadow-lg border border-gray-200 p-4 w-80 z-50">
      {/* Tabs */}
      <div className="flex border-b border-gray-200 mb-3">
        <button
          onClick={() => setActiveTab('emoji')}
          className={`px-3 py-2 text-sm font-medium ${
            activeTab === 'emoji' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'
          }`}
        >
          😀 Emoji
        </button>
        <button
          onClick={() => setActiveTab('sticker')}
          className={`px-3 py-2 text-sm font-medium ${
            activeTab === 'sticker' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'
          }`}
        >
          🎉 Stickers
        </button>
        <button
          onClick={() => setActiveTab('custom')}
          className={`px-3 py-2 text-sm font-medium ${
            activeTab === 'custom' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'
          }`}
        >
          🎬 GIFs
        </button>
      </div>

      {/* Content */}
      <div className="h-48 overflow-y-auto">
        {activeTab === 'emoji' && (
          <div className="grid grid-cols-8 gap-2">
            {emojis.map((emoji, index) => (
              <button
                key={index}
                onClick={() => {
                  onEmojiSelect(emoji);
                  onClose();
                }}
                className="text-xl hover:bg-gray-100 rounded p-1 transition-colors"
              >
                {emoji}
              </button>
            ))}
          </div>
        )}

        {activeTab === 'sticker' && (
          <div className="grid grid-cols-6 gap-2">
            {stickers.map((sticker, index) => (
              <button
                key={index}
                onClick={() => {
                  onEmojiSelect(sticker);
                  onClose();
                }}
                className="text-2xl hover:bg-gray-100 rounded p-2 transition-colors"
              >
                {sticker}
              </button>
            ))}
          </div>
        )}

        {activeTab === 'custom' && (
          <div className="grid grid-cols-2 gap-2">
            {customMedia.map((media, index) => (
              <button
                key={index}
                onClick={() => {
                  onEmojiSelect(`![${media.name}](${media.url})`);
                  onClose();
                }}
                className="hover:bg-gray-100 rounded p-1 transition-colors"
              >
                <img
                  src={media.url}
                  alt={media.name}
                  className="w-full h-16 object-cover rounded"
                />
                <span className="text-xs text-gray-600 mt-1 block">{media.name}</span>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default EmojiPicker;