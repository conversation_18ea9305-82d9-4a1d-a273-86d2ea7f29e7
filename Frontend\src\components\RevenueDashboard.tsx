'use client';

import React, { useState, useEffect } from 'react';
import { revenueService } from '@/services/revenueService';
import { CreatorRevenue, PayoutRecord, RevenueShareConfig } from '@/types';

export default function RevenueDashboard({ userId }: { userId: number }) {
  const [revenue, setRevenue] = useState<CreatorRevenue | null>(null);
  const [payoutHistory, setPayoutHistory] = useState<PayoutRecord[]>([]);
  const [payoutAmount, setPayoutAmount] = useState('');
  const [isPayoutModalOpen, setIsPayoutModalOpen] = useState(false);
  const [payoutStatus, setPayoutStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [config, setConfig] = useState<RevenueShareConfig | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      const revenueData = revenueService.getCreatorRevenue(userId);
      const history = await revenueService.getPayoutHistory(userId);
      const configData = revenueService.getRevenueShareConfig();
      
      setRevenue(revenueData);
      setPayoutHistory(history);
      setConfig(configData);
    };

    fetchData();
  }, [userId]);

  const handlePayoutRequest = async () => {
    if (!revenue || !payoutAmount) return;
    
    const amount = parseFloat(payoutAmount);
    if (amount <= 0 || amount > revenue.pendingPayout) {
      alert('请输入有效的提现金额');
      return;
    }
    
    setPayoutStatus('processing');
    
    try {
      const payout = await revenueService.requestPayout(userId, amount);
      setPayoutHistory([payout, ...payoutHistory]);
      
      // 更新本地状态
      setRevenue({
        ...revenue,
        pendingPayout: revenue.pendingPayout - amount
      });
      
      setPayoutStatus('success');
      setPayoutAmount('');
      
      // 3秒后关闭模态框
      setTimeout(() => {
        setIsPayoutModalOpen(false);
        setPayoutStatus('idle');
      }, 3000);
    } catch (error) {
      setPayoutStatus('error');
    }
  };

  if (!revenue || !config) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">收入管理</h2>
      
      {/* 收入概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-600">总收入</p>
              <p className="text-2xl font-bold text-green-900">${revenue.totalRevenue.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xl">💰</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-600">您的收入</p>
              <p className="text-2xl font-bold text-blue-900">${revenue.creatorShare.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xl">👤</span>
            </div>
          </div>
        </div>
        
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-purple-600">待提现</p>
              <p className="text-2xl font-bold text-purple-900">${revenue.pendingPayout.toFixed(2)}</p>
            </div>
            <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xl">📤</span>
            </div>
          </div>
        </div>
      </div>
      
      {/* 分成规则 */}
      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">收入分成规则</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex justify-between">
            <span className="text-gray-600">平台分成比例:</span>
            <span className="font-medium">{config.platformPercentage}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">您的分成比例:</span>
            <span className="font-medium">{config.creatorPercentage}%</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">最低提现金额:</span>
            <span className="font-medium">${config.minPayoutAmount}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">结算周期:</span>
            <span className="font-medium capitalize">
              {config.payoutFrequency === 'daily' && '每日'}
              {config.payoutFrequency === 'weekly' && '每周'}
              {config.payoutFrequency === 'monthly' && '每月'}
            </span>
          </div>
        </div>
      </div>
      
      {/* 提现操作 */}
      <div className="flex justify-end mb-8">
        <button
          onClick={() => setIsPayoutModalOpen(true)}
          disabled={revenue.pendingPayout < config.minPayoutAmount}
          className={`px-6 py-3 rounded-lg font-medium ${
            revenue.pendingPayout < config.minPayoutAmount
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          申请提现
        </button>
      </div>
      
      {/* 提现记录 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">提现记录</h3>
        {payoutHistory.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>暂无提现记录</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">申请时间</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">金额</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">支付方式</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">状态</th>
                </tr>
              </thead>
              <tbody>
                {payoutHistory.map((record) => (
                  <tr key={record.id} className="border-b border-gray-100">
                    <td className="py-3 px-4 text-gray-600">
                      {new Date(record.createdAt).toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4 font-medium">${record.amount.toFixed(2)}</td>
                    <td className="py-3 px-4 text-gray-600">{record.paymentMethod}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        record.status === 'completed' 
                          ? 'bg-green-100 text-green-800' 
                          : record.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : record.status === 'processing'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-red-100 text-red-800'
                      }`}>
                        {record.status === 'completed' && '已完成'}
                        {record.status === 'pending' && '待处理'}
                        {record.status === 'processing' && '处理中'}
                        {record.status === 'failed' && '失败'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
      
      {/* 提现模态框 */}
      {isPayoutModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg w-full max-w-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">申请提现</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提现金额 (USD)
                  </label>
                  <input
                    type="number"
                    value={payoutAmount}
                    onChange={(e) => setPayoutAmount(e.target.value)}
                    max={revenue.pendingPayout}
                    min="0"
                    step="0.01"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="请输入提现金额"
                  />
                  <div className="mt-1 text-sm text-gray-500">
                    可提现金额: ${revenue.pendingPayout.toFixed(2)}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    支付方式
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option>PayPal</option>
                    <option>Bank Transfer</option>
                    <option>Alipay</option>
                    <option>WeChat Pay</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 px-6 py-3 rounded-b-lg flex justify-end space-x-3">
              <button
                onClick={() => setIsPayoutModalOpen(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100"
              >
                取消
              </button>
              <button
                onClick={handlePayoutRequest}
                disabled={payoutStatus === 'processing'}
                className={`px-4 py-2 rounded-md ${
                  payoutStatus === 'processing'
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {payoutStatus === 'processing' ? '处理中...' : '确认提现'}
              </button>
            </div>
            
            {payoutStatus === 'success' && (
              <div className="px-6 pb-4">
                <div className="bg-green-50 border border-green-200 rounded-md p-3">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm font-medium text-green-800">
                        提现申请已提交，我们会尽快处理您的请求。
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}