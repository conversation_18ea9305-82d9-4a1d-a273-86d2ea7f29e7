'use client';

import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Flag,
  User,
  Calendar,
  Filter,
  Search,
  AlertTriangle,
  MessageSquare,
  FileText
} from 'lucide-react';

interface ReviewItem {
  id: string;
  type: 'article' | 'comment' | 'user_report';
  title: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  submittedAt: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  category?: string;
  reportReason?: string;
  reportedBy?: {
    id: string;
    name: string;
  };
  flags: string[];
  reviewedBy?: {
    id: string;
    name: string;
  };
  reviewedAt?: string;
  reviewNotes?: string;
}

const ReviewQueuePage: React.FC = () => {
  const [reviewItems, setReviewItems] = useState<ReviewItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [selectedItem, setSelectedItem] = useState<ReviewItem | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [reviewNotes, setReviewNotes] = useState('');

  useEffect(() => {
    fetchReviewItems();
  }, []);

  const fetchReviewItems = async () => {
    try {
      // Mock data - in real app, this would fetch from API
      const mockItems: ReviewItem[] = [
        {
          id: '1',
          type: 'article',
          title: 'Advanced React Patterns You Should Know',
          content: 'This article covers advanced React patterns including render props, higher-order components, and custom hooks...',
          author: { id: '1', name: 'John Smith' },
          submittedAt: '2024-01-15T10:30:00Z',
          priority: 'medium',
          status: 'pending',
          category: 'Technology',
          flags: ['potential_spam']
        },
        {
          id: '2',
          type: 'comment',
          title: 'Comment on "Getting Started with TypeScript"',
          content: 'This is a really helpful tutorial! I learned a lot about TypeScript interfaces and generics.',
          author: { id: '2', name: 'Sarah Johnson' },
          submittedAt: '2024-01-15T09:15:00Z',
          priority: 'low',
          status: 'pending',
          flags: []
        },
        {
          id: '3',
          type: 'user_report',
          title: 'Report: Inappropriate Content',
          content: 'User reported this article for containing inappropriate language and offensive content.',
          author: { id: '3', name: 'Mike Brown' },
          submittedAt: '2024-01-15T08:45:00Z',
          priority: 'high',
          status: 'pending',
          reportReason: 'Inappropriate content',
          reportedBy: { id: '4', name: 'Emily Davis' },
          flags: ['inappropriate_content', 'offensive_language']
        },
        {
          id: '4',
          type: 'article',
          title: 'Building Scalable Web Applications',
          content: 'Learn how to build scalable web applications using modern frameworks and best practices...',
          author: { id: '5', name: 'Alex Wilson' },
          submittedAt: '2024-01-14T16:20:00Z',
          priority: 'urgent',
          status: 'flagged',
          category: 'Development',
          flags: ['duplicate_content', 'plagiarism']
        },
        {
          id: '5',
          type: 'comment',
          title: 'Comment on "JavaScript Best Practices"',
          content: 'Great article! The examples are very clear and easy to understand.',
          author: { id: '6', name: 'Lisa Chen' },
          submittedAt: '2024-01-14T14:10:00Z',
          priority: 'low',
          status: 'approved',
          reviewedBy: { id: 'admin1', name: 'Admin User' },
          reviewedAt: '2024-01-14T15:30:00Z',
          reviewNotes: 'Content is appropriate and adds value to the discussion.',
          flags: []
        }
      ];
      
      setReviewItems(mockItems);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch review items:', error);
      setLoading(false);
    }
  };

  const handleReview = async (itemId: string, action: 'approve' | 'reject', notes: string = '') => {
    try {
      const newStatus = action === 'approve' ? 'approved' : 'rejected';
      
      setReviewItems(prev => prev.map(item => 
        item.id === itemId 
          ? {
              ...item,
              status: newStatus,
              reviewedBy: { id: 'current-admin', name: 'Current Admin' },
              reviewedAt: new Date().toISOString(),
              reviewNotes: notes
            }
          : item
      ));
      
      setShowModal(false);
      setSelectedItem(null);
      setReviewNotes('');
      
      // In real app, this would call API
      console.log(`Item ${itemId} ${action}ed with notes: ${notes}`);
    } catch (error) {
      console.error('Failed to review item:', error);
    }
  };

  const openReviewModal = (item: ReviewItem) => {
    setSelectedItem(item);
    setReviewNotes('');
    setShowModal(true);
  };

  const getPriorityBadge = (priority: ReviewItem['priority']) => {
    const styles = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[priority]}`}>
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </span>
    );
  };

  const getStatusBadge = (status: ReviewItem['status']) => {
    const styles = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800',
      flagged: 'bg-orange-100 text-orange-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getTypeIcon = (type: ReviewItem['type']) => {
    switch (type) {
      case 'article':
        return <FileText className="w-4 h-4" />;
      case 'comment':
        return <MessageSquare className="w-4 h-4" />;
      case 'user_report':
        return <Flag className="w-4 h-4" />;
      default:
        return <FileText className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const filteredItems = reviewItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.author.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = typeFilter === 'all' || item.type === typeFilter;
    const matchesPriority = priorityFilter === 'all' || item.priority === priorityFilter;
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    
    return matchesSearch && matchesType && matchesPriority && matchesStatus;
  });

  const pendingCount = reviewItems.filter(item => item.status === 'pending').length;
  const flaggedCount = reviewItems.filter(item => item.status === 'flagged').length;

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Review Queue</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Review Queue</h1>
          <p className="text-gray-600 mt-1">Review and moderate content submissions</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full">
              <Clock className="w-4 h-4 mr-1" />
              <span className="text-sm font-medium">{pendingCount} Pending</span>
            </div>
            <div className="flex items-center px-3 py-1 bg-red-100 text-red-800 rounded-full">
              <Flag className="w-4 h-4 mr-1" />
              <span className="text-sm font-medium">{flaggedCount} Flagged</span>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search items..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="form-input pl-10"
            />
          </div>
          
          <select
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">All Types</option>
            <option value="article">Articles</option>
            <option value="comment">Comments</option>
            <option value="user_report">User Reports</option>
          </select>
          
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">All Priorities</option>
            <option value="urgent">Urgent</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">All Status</option>
            <option value="pending">Pending</option>
            <option value="flagged">Flagged</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
          </select>
          
          <button className="btn-secondary flex items-center justify-center">
            <Filter className="w-4 h-4 mr-2" />
            Advanced
          </button>
        </div>
      </div>

      {/* Review Items */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Content
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Priority
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Submitted
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredItems.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mr-3 mt-1">
                        {getTypeIcon(item.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {item.title}
                        </div>
                        <div className="text-sm text-gray-500 mt-1 line-clamp-2">
                          {item.content}
                        </div>
                        {item.flags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {item.flags.map((flag, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-red-100 text-red-800"
                              >
                                <AlertTriangle className="w-3 h-3 mr-1" />
                                {flag.replace('_', ' ')}
                              </span>
                            ))}
                          </div>
                        )}
                        {item.reportReason && (
                          <div className="mt-2">
                            <span className="text-xs text-red-600 font-medium">
                              Report: {item.reportReason}
                            </span>
                            {item.reportedBy && (
                              <span className="text-xs text-gray-500 ml-2">
                                by {item.reportedBy.name}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{item.author.name}</div>
                        <div className="text-sm text-gray-500 capitalize">{item.type}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getPriorityBadge(item.priority)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(item.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDate(item.submittedAt)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => openReviewModal(item)}
                        className="text-blue-600 hover:text-blue-900"
                        title="Review"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {item.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleReview(item.id, 'approve')}
                            className="text-green-600 hover:text-green-900"
                            title="Approve"
                          >
                            <CheckCircle className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleReview(item.id, 'reject')}
                            className="text-red-600 hover:text-red-900"
                            title="Reject"
                          >
                            <XCircle className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <div className="text-gray-500">No items found matching your criteria</div>
          </div>
        )}
      </div>

      {/* Review Modal */}
      {showModal && selectedItem && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Review Content</h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900">{selectedItem.title}</h4>
                <p className="text-sm text-gray-600 mt-1">{selectedItem.content}</p>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Author:</span> {selectedItem.author.name}
                </div>
                <div>
                  <span className="font-medium">Type:</span> {selectedItem.type}
                </div>
                <div>
                  <span className="font-medium">Priority:</span> {selectedItem.priority}
                </div>
                <div>
                  <span className="font-medium">Submitted:</span> {formatDate(selectedItem.submittedAt)}
                </div>
              </div>
              
              {selectedItem.flags.length > 0 && (
                <div>
                  <span className="font-medium text-red-600">Flags:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedItem.flags.map((flag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-red-100 text-red-800"
                      >
                        {flag.replace('_', ' ')}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Review Notes
                </label>
                <textarea
                  value={reviewNotes}
                  onChange={(e) => setReviewNotes(e.target.value)}
                  className="form-input"
                  rows={3}
                  placeholder="Add notes about your review decision..."
                />
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  onClick={() => setShowModal(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleReview(selectedItem.id, 'reject', reviewNotes)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 flex items-center"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Reject
                </button>
                <button
                  onClick={() => handleReview(selectedItem.id, 'approve', reviewNotes)}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Approve
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReviewQueuePage;
