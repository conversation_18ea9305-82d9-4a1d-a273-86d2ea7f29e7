#!/bin/bash

# OneNews Docker 开发环境管理脚本
# 用于管理开发环境的Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="onenews"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}OneNews Docker 开发环境管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动开发环境"
    echo "  stop      停止开发环境"
    echo "  restart   重启开发环境"
    echo "  build     重新构建镜像"
    echo "  logs      查看日志"
    echo "  status    查看服务状态"
    echo "  clean     清理容器和镜像"
    echo "  shell     进入后端容器shell"
    echo "  db        进入数据库shell"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start          # 启动开发环境"
    echo "  $0 logs backend   # 查看后端日志"
    echo "  $0 shell          # 进入后端容器"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误: Docker未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}错误: Docker Compose未安装${NC}"
        exit 1
    fi
}

# 启动开发环境
start_dev() {
    echo -e "${GREEN}启动OneNews开发环境...${NC}"
    docker-compose -f docker-compose.dev.yml up -d
    
    echo -e "${GREEN}等待服务启动...${NC}"
    sleep 10
    
    echo -e "${BLUE}服务状态:${NC}"
    docker-compose -f docker-compose.dev.yml ps
    
    echo -e "${GREEN}开发环境已启动!${NC}"
    echo -e "${YELLOW}前端地址: http://localhost:3000${NC}"
    echo -e "${YELLOW}后端地址: http://localhost:5000${NC}"
    echo -e "${YELLOW}数据库管理: http://localhost:8080${NC}"
    echo -e "${YELLOW}Redis管理: http://localhost:8081${NC}"
}

# 停止开发环境
stop_dev() {
    echo -e "${YELLOW}停止OneNews开发环境...${NC}"
    docker-compose -f docker-compose.dev.yml down
    echo -e "${GREEN}开发环境已停止${NC}"
}

# 重启开发环境
restart_dev() {
    echo -e "${YELLOW}重启OneNews开发环境...${NC}"
    docker-compose -f docker-compose.dev.yml restart
    echo -e "${GREEN}开发环境已重启${NC}"
}

# 重新构建镜像
build_dev() {
    echo -e "${YELLOW}重新构建开发环境镜像...${NC}"
    docker-compose -f docker-compose.dev.yml build --no-cache
    echo -e "${GREEN}镜像构建完成${NC}"
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        echo -e "${BLUE}显示所有服务日志:${NC}"
        docker-compose -f docker-compose.dev.yml logs -f
    else
        echo -e "${BLUE}显示 $service 服务日志:${NC}"
        docker-compose -f docker-compose.dev.yml logs -f "$service"
    fi
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}OneNews开发环境服务状态:${NC}"
    docker-compose -f docker-compose.dev.yml ps
    
    echo -e "\n${BLUE}Docker镜像:${NC}"
    docker images | grep onenews
    
    echo -e "\n${BLUE}Docker卷:${NC}"
    docker volume ls | grep onenews
}

# 清理容器和镜像
clean_dev() {
    echo -e "${YELLOW}清理OneNews开发环境...${NC}"
    
    # 停止并删除容器
    docker-compose -f docker-compose.dev.yml down -v
    
    # 删除镜像
    echo -e "${YELLOW}删除相关镜像...${NC}"
    docker images | grep onenews | awk '{print $3}' | xargs -r docker rmi -f
    
    # 清理未使用的资源
    echo -e "${YELLOW}清理未使用的Docker资源...${NC}"
    docker system prune -f
    
    echo -e "${GREEN}清理完成${NC}"
}

# 进入后端容器shell
enter_shell() {
    echo -e "${BLUE}进入后端容器shell...${NC}"
    docker-compose -f docker-compose.dev.yml exec backend sh
}

# 进入数据库shell
enter_db() {
    echo -e "${BLUE}进入PostgreSQL数据库...${NC}"
    docker-compose -f docker-compose.dev.yml exec postgres psql -U postgres -d onenews_dev
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        start)
            start_dev
            ;;
        stop)
            stop_dev
            ;;
        restart)
            restart_dev
            ;;
        build)
            build_dev
            ;;
        logs)
            show_logs "$2"
            ;;
        status)
            show_status
            ;;
        clean)
            clean_dev
            ;;
        shell)
            enter_shell
            ;;
        db)
            enter_db
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
