/**
 * OneNews 日志系统配置
 * 基于 Winston 的生产级日志管理
 */

const winston = require('winston');
const path = require('path');
const fs = require('fs');
const { sanitizeLogInput, sanitizeLogObject, createSafeLogMessage } = require('../utils/logSanitizer');

// 确保日志目录存在
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// 控制台输出格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss',
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += '\n' + JSON.stringify(meta, null, 2);
    }
    return msg;
  })
);

// 创建 Winston logger 实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: {
    service: 'OneNews',
    environment: process.env.NODE_ENV || 'development',
  },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true,
    }),

    // 警告日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'warn.log'),
      level: 'warn',
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      tailable: true,
    }),

    // 综合日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 10,
      tailable: true,
    }),

    // HTTP 访问日志
    new winston.transports.File({
      filename: path.join(logDir, 'access.log'),
      level: 'http',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true,
    }),
  ],

  // 异常处理
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'exceptions.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 3,
    }),
  ],

  // 未捕获的 Promise 拒绝
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logDir, 'rejections.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 3,
    }),
  ],
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
      level: 'debug',
    })
  );
} else {
  // 生产环境只在错误时输出到控制台
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
      level: 'error',
    })
  );
}

// 创建专门的访问日志记录器
const accessLogger = winston.createLogger({
  level: 'http',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'access.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 5,
      tailable: true,
    }),
  ],
});

// 创建数据库日志记录器
const dbLogger = winston.createLogger({
  level: 'debug',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'database.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      tailable: true,
    }),
  ],
});

// 创建安全日志记录器
const securityLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'security.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true,
    }),
  ],
});

// 性能日志记录器
const performanceLogger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(winston.format.timestamp(), winston.format.json()),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'performance.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 3,
      tailable: true,
    }),
  ],
});

// 辅助函数：记录 HTTP 请求
const logRequest = (req, res, responseTime) => {
  const logData = {
    method: sanitizeLogInput(req.method),
    url: sanitizeLogInput(req.originalUrl),
    status: res.statusCode,
    responseTime: `${responseTime}ms`,
    userAgent: sanitizeLogInput(req.get('User-Agent') || ''),
    ip: sanitizeLogInput(req.ip || req.connection.remoteAddress || ''),
    userId: req.user ? req.user.id : null,
    timestamp: new Date().toISOString(),
  };

  accessLogger.http('HTTP Request', sanitizeLogObject(logData));
};

// 辅助函数：记录数据库查询
const logDatabaseQuery = (query, duration, error = null) => {
  const logData = {
    query: query.length > 1000 ? query.substring(0, 1000) + '...' : query,
    duration: `${duration}ms`,
    error: error ? error.message : null,
    timestamp: new Date().toISOString(),
  };

  if (error) {
    dbLogger.error('Database Query Error', logData);
  } else if (duration > 1000) {
    dbLogger.warn('Slow Database Query', logData);
  } else {
    dbLogger.debug('Database Query', logData);
  }
};

// 辅助函数：记录安全事件
const logSecurityEvent = (event, details, req = null) => {
  const logData = {
    event: sanitizeLogInput(event),
    details: sanitizeLogObject(details),
    ip: req ? sanitizeLogInput(req.ip || req.connection.remoteAddress || '') : null,
    userAgent: req ? sanitizeLogInput(req.get('User-Agent') || '') : null,
    userId: req && req.user ? req.user.id : null,
    timestamp: new Date().toISOString(),
  };

  securityLogger.warn('Security Event', sanitizeLogObject(logData));
};

// 辅助函数：记录性能指标
const logPerformance = (metric, value, context = {}) => {
  const logData = {
    metric,
    value,
    context,
    timestamp: new Date().toISOString(),
  };

  performanceLogger.info('Performance Metric', logData);
};

// 日志清理函数
const cleanupLogs = () => {
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
  const now = Date.now();

  fs.readdir(logDir, (err, files) => {
    if (err) {
      logger.error('Error reading log directory', { error: err.message });
      return;
    }

    files.forEach((file) => {
      const filePath = path.join(logDir, file);
      fs.stat(filePath, (err, stats) => {
        if (err) return;

        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlink(filePath, (err) => {
            if (err) {
              logger.error('Error deleting old log file', { file, error: err.message });
            } else {
              logger.info('Deleted old log file', { file });
            }
          });
        }
      });
    });
  });
};

// 每天清理一次旧日志
setInterval(cleanupLogs, 24 * 60 * 60 * 1000);

module.exports = {
  logger,
  accessLogger,
  dbLogger,
  securityLogger,
  performanceLogger,
  logRequest,
  logDatabaseQuery,
  logSecurityEvent,
  logPerformance,
  cleanupLogs,
};
