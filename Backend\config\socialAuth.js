const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const FacebookStrategy = require('passport-facebook').Strategy;
const SimpleUser = require('../models/SimpleUser');

/**
 * 社交登录配置
 */

// 序列化用户
passport.serializeUser((user, done) => {
  done(null, user.id);
});

// 反序列化用户
passport.deserializeUser(async (id, done) => {
  try {
    const user = await SimpleUser.findByPk(id);
    done(null, user);
  } catch (error) {
    done(error, null);
  }
});

// Google OAuth 策略
passport.use(new GoogleStrategy({
  clientID: process.env.GOOGLE_CLIENT_ID || 'your-google-client-id',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || 'your-google-client-secret',
  callbackURL: process.env.GOOGLE_CALLBACK_URL || 'http://localhost:5000/api/simple-auth/google/callback'
}, async (accessToken, refreshToken, profile, done) => {
  try {
    console.log('🔍 Google OAuth 回调:', profile.id, profile.emails[0].value);

    // 查找现有用户
    let user = await SimpleUser.findOne({
      where: { email: profile.emails[0].value }
    });

    if (user) {
      console.log('✅ 找到现有用户:', user.email);
      return done(null, user);
    }

    // 创建新用户
    user = await SimpleUser.createUser({
      username: `google_${profile.id}`,
      email: profile.emails[0].value,
      password: `google_oauth_${Date.now()}`, // 随机密码，不会被使用
      role: 'user'
    });

    console.log('✅ 创建新的Google用户:', user.email);
    done(null, user);

  } catch (error) {
    console.error('❌ Google OAuth 错误:', error);
    done(error, null);
  }
}));

// Facebook OAuth 策略
passport.use(new FacebookStrategy({
  clientID: process.env.FACEBOOK_APP_ID || 'your-facebook-app-id',
  clientSecret: process.env.FACEBOOK_APP_SECRET || 'your-facebook-app-secret',
  callbackURL: process.env.FACEBOOK_CALLBACK_URL || 'http://localhost:5000/api/simple-auth/facebook/callback',
  profileFields: ['id', 'emails', 'name']
}, async (accessToken, refreshToken, profile, done) => {
  try {
    console.log('🔍 Facebook OAuth 回调:', profile.id, profile.emails[0].value);

    // 查找现有用户
    let user = await SimpleUser.findOne({
      where: { email: profile.emails[0].value }
    });

    if (user) {
      console.log('✅ 找到现有用户:', user.email);
      return done(null, user);
    }

    // 创建新用户
    user = await SimpleUser.createUser({
      username: `facebook_${profile.id}`,
      email: profile.emails[0].value,
      password: `facebook_oauth_${Date.now()}`, // 随机密码，不会被使用
      role: 'user'
    });

    console.log('✅ 创建新的Facebook用户:', user.email);
    done(null, user);

  } catch (error) {
    console.error('❌ Facebook OAuth 错误:', error);
    done(error, null);
  }
}));

module.exports = passport;
