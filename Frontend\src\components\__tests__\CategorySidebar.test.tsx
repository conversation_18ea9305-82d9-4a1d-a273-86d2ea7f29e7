import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import CategorySidebar from '../CategorySidebar';
import { SimpleAuthProvider } from '@/contexts/SimpleAuthContext';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    refresh: jest.fn(),
  }),
}));

// Mock Supabase
jest.mock('@supabase/supabase-js', () => ({
  createClient: () => ({
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      onAuthStateChange: () => ({ data: { subscription: { unsubscribe: jest.fn() } } }),
    },
  }),
}));

const MockedCategorySidebar = ({ selectedCategory = 'trending', onCategoryChange = jest.fn() }) => (
  <SimpleAuthProvider>
    <CategorySidebar
      selectedCategory={selectedCategory}
      onCategoryChange={onCategoryChange}
    />
  </SimpleAuthProvider>
);

describe('CategorySidebar', () => {
  test('renders all required elements', () => {
    render(<MockedCategorySidebar />);
    
    // Check if categories are rendered
    expect(screen.getByText('Trending')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
    
    // Check if platform links are rendered
    expect(screen.getByText('About Platform')).toBeInTheDocument();
    expect(screen.getByText('Help Center')).toBeInTheDocument();
    expect(screen.getByText('Feedback')).toBeInTheDocument();
    expect(screen.getByText('Privacy Policy')).toBeInTheDocument();
    expect(screen.getByText('Community Rules')).toBeInTheDocument();
    expect(screen.getByText('Advertising')).toBeInTheDocument();
    
    // Check if social sharing section exists
    expect(screen.getByText('Share Newzora')).toBeInTheDocument();
  });

  test('calls onCategoryChange when category is clicked', () => {
    const mockOnCategoryChange = jest.fn();
    render(<MockedCategorySidebar onCategoryChange={mockOnCategoryChange} />);
    
    fireEvent.click(screen.getByText('Technology'));
    expect(mockOnCategoryChange).toHaveBeenCalledWith('technology');
  });

  test('highlights selected category', () => {
    render(<MockedCategorySidebar selectedCategory="technology" />);
    
    const technologyButton = screen.getByText('Technology');
    expect(technologyButton).toHaveClass('bg-blue-50', 'text-blue-600');
  });
});