import { supabase } from '@/lib/supabase';
import { DashboardStats } from '@/types/admin';
import { DateRange } from '@/components/admin/common/DateRangeSelector';
import { filterByDateRange, isDateInRange } from '@/utils/dateFilters';

interface DashboardFilters {
  dateRange?: DateRange;
  timeRange?: string;
}

export const supabaseService = {
  // 仪表板数据 - 使用模拟数据，但支持日期过滤
  async getDashboardStats(filters?: DashboardFilters): Promise<DashboardStats> {
    // 生成基于日期范围的模拟数据
    const generateStatsForDateRange = (dateRange?: DateRange) => {
      if (!dateRange) {
        // 默认统计数据
        return {
          users: {
            total: 156,
            todayNew: 12,
            activeUsers: 89
          },
          content: {
            totalArticles: 234,
            todayPublished: 8,
            pendingReview: 5
          },
          engagement: {
            totalComments: 567,
            todayComments: 23,
            averageReadTime: 5
          }
        };
      }

      // 根据日期范围计算天数
      const daysDiff = Math.ceil(
        (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      // 基于天数调整统计数据
      const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

      return {
        users: {
          total: Math.floor(156 * multiplier),
          todayNew: Math.floor(12 * Math.min(1, multiplier)),
          activeUsers: Math.floor(89 * multiplier)
        },
        content: {
          totalArticles: Math.floor(234 * multiplier),
          todayPublished: Math.floor(8 * Math.min(1, multiplier)),
          pendingReview: Math.floor(5 * Math.min(1, multiplier))
        },
        engagement: {
          totalComments: Math.floor(567 * multiplier),
          todayComments: Math.floor(23 * Math.min(1, multiplier)),
          averageReadTime: 5 // 平均阅读时间不受日期范围影响
        }
      };
    };

    return generateStatsForDateRange(filters?.dateRange);
  },

  // 用户管理 - 使用模拟数据
  async getUsers(params: any = {}) {
    const mockUsers = [
      {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        display_name: '超级管理员',
        role: 'super_admin',
        is_active: true,
        email_verified: true,
        created_at: '2024-01-01',
        last_login_at: '2024-12-20'
      },
      {
        id: '2',
        username: 'user1',
        email: '<EMAIL>',
        display_name: '测试用户1',
        role: 'user',
        is_active: true,
        email_verified: true,
        created_at: '2024-01-15',
        last_login_at: '2024-12-19'
      }
    ];

    return {
      users: mockUsers,
      pagination: {
        current: 1,
        total: mockUsers.length,
        pageSize: 20,
        totalPages: 1
      }
    };
  },

  async updateUserStatus(userId: string, isActive: boolean) {
    console.log('更新用户状态:', userId, isActive);
    return { success: true };
  },

  async updateUserRole(userId: string, role: string) {
    console.log('更新用户角色:', userId, role);
    return { success: true };
  },

  async bulkUpdateUsers(userIds: string[], action: string, value?: any) {
    console.log('批量更新用户:', userIds, action, value);
    return { updated: userIds.length };
  },

  // Article management - using mock data
  async getArticles(params: any = {}) {
    const mockArticles = [
      {
        id: '1',
        title: 'React 18 New Features Explained',
        content: 'This is an article about React 18...',
        author_id: '1',
        category: 'Technology',
        published: true,
        views: 1234,
        likes: 89,
        created_at: '2024-12-01',
        profiles: {
          username: 'admin',
          display_name: 'Super Admin'
        }
      },
      {
        id: '2',
        title: 'TypeScript Best Practices',
        content: 'This is an article about TypeScript...',
        author_id: '2',
        category: 'Technology',
        published: false,
        views: 567,
        likes: 34,
        created_at: '2024-12-02',
        profiles: {
          username: 'user1',
          display_name: 'Test User 1'
        }
      }
    ];

    return {
      articles: mockArticles,
      pagination: {
        current: 1,
        total: mockArticles.length,
        pageSize: 20,
        totalPages: 1
      }
    };
  },

  async updateArticleStatus(articleId: string, published: boolean) {
    console.log('更新文章状态:', articleId, published);
    return { success: true };
  }
};