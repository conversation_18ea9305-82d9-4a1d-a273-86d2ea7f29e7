/**
 * 安全策略检查工具
 * 验证系统安全配置的完整性和一致性
 */

const { SECURITY_CONFIG } = require('../config/security');

class SecurityChecker {
  /**
   * 检查环境变量安全配置
   */
  static checkEnvironmentSecurity() {
    const issues = [];
    const recommendations = [];

    // 检查JWT密钥
    if (!process.env.JWT_SECRET) {
      issues.push('JWT_SECRET environment variable is not set');
    } else if (process.env.JWT_SECRET.length < 32) {
      issues.push('JWT_SECRET is too short (should be at least 32 characters)');
    }

    // 检查会话密钥
    if (!process.env.SESSION_SECRET) {
      recommendations.push('SESSION_SECRET should be set for better security');
    }

    // 检查数据库密码
    if (!process.env.DB_PASSWORD || process.env.DB_PASSWORD === 'password') {
      issues.push('Database password is weak or not set');
    }

    // 检查生产环境配置
    if (process.env.NODE_ENV === 'production') {
      if (!process.env.FRONTEND_URL || process.env.FRONTEND_URL.includes('localhost')) {
        issues.push('FRONTEND_URL should be set to production domain');
      }
      
      if (!process.env.FRONTEND_URL?.startsWith('https://')) {
        issues.push('FRONTEND_URL should use HTTPS in production');
      }
    }

    return { issues, recommendations };
  }

  /**
   * 检查密码策略
   */
  static checkPasswordPolicy() {
    const config = SECURITY_CONFIG.PASSWORD;
    const issues = [];
    const recommendations = [];

    // 检查密码长度要求
    if (config.MIN_LENGTH < 8) {
      issues.push('Minimum password length should be at least 8 characters');
    }

    // 检查bcrypt轮数
    if (config.BCRYPT_ROUNDS < 10) {
      recommendations.push('Consider increasing bcrypt rounds to at least 12 for better security');
    } else if (config.BCRYPT_ROUNDS > 15) {
      recommendations.push('Bcrypt rounds might be too high, could impact performance');
    }

    // 检查密码复杂度要求
    const complexityChecks = [
      { key: 'REQUIRE_UPPERCASE', name: 'uppercase letters' },
      { key: 'REQUIRE_LOWERCASE', name: 'lowercase letters' },
      { key: 'REQUIRE_NUMBERS', name: 'numbers' },
      { key: 'REQUIRE_SPECIAL_CHARS', name: 'special characters' }
    ];

    complexityChecks.forEach(check => {
      if (!config[check.key]) {
        recommendations.push(`Consider requiring ${check.name} in passwords`);
      }
    });

    return { issues, recommendations };
  }

  /**
   * 检查JWT配置
   */
  static checkJWTSecurity() {
    const config = SECURITY_CONFIG.JWT;
    const issues = [];
    const recommendations = [];

    // 检查算法
    if (config.ALGORITHM !== 'HS256' && config.ALGORITHM !== 'RS256') {
      recommendations.push('Consider using HS256 or RS256 for JWT signing');
    }

    // 检查token过期时间
    if (process.env.NODE_ENV === 'production') {
      if (config.ACCESS_TOKEN_EXPIRES === '7d') {
        issues.push('Access token expiration is too long for production (should be 1-2 hours)');
      }
    }

    // 检查issuer和audience
    if (!config.ISSUER || !config.AUDIENCE) {
      recommendations.push('Set JWT issuer and audience for better token validation');
    }

    return { issues, recommendations };
  }

  /**
   * 检查会话安全
   */
  static checkSessionSecurity() {
    const config = SECURITY_CONFIG.SESSION;
    const issues = [];
    const recommendations = [];

    // 检查生产环境会话配置
    if (process.env.NODE_ENV === 'production') {
      if (!config.SECURE) {
        issues.push('Session cookies should be secure in production');
      }
      
      if (config.SAME_SITE !== 'strict') {
        recommendations.push('Consider using strict SameSite policy in production');
      }
    }

    // 检查HttpOnly
    if (!config.HTTP_ONLY) {
      issues.push('Session cookies should be HttpOnly to prevent XSS');
    }

    // 检查会话过期时间
    if (config.MAX_AGE > 7 * 24 * 60 * 60 * 1000) {
      recommendations.push('Consider shorter session expiration time');
    }

    return { issues, recommendations };
  }

  /**
   * 检查速率限制配置
   */
  static checkRateLimitSecurity() {
    const issues = [];
    const recommendations = [];

    // 这里可以添加速率限制检查逻辑
    // 由于速率限制配置在middleware/security.js中，需要导入检查

    if (process.env.NODE_ENV === 'development') {
      recommendations.push('Rate limiting is relaxed in development mode');
    }

    return { issues, recommendations };
  }

  /**
   * 检查CORS配置
   */
  static checkCORSSecurity() {
    const issues = [];
    const recommendations = [];

    const frontendUrl = process.env.FRONTEND_URL;
    
    if (!frontendUrl) {
      issues.push('FRONTEND_URL not set for CORS configuration');
    } else if (frontendUrl === '*') {
      issues.push('CORS origin should not be wildcard in production');
    }

    if (process.env.NODE_ENV === 'production' && frontendUrl?.includes('localhost')) {
      issues.push('CORS origin should not include localhost in production');
    }

    return { issues, recommendations };
  }

  /**
   * 检查输入验证
   */
  static checkInputValidation() {
    const issues = [];
    const recommendations = [];

    // 检查是否使用了express-validator
    try {
      require('express-validator');
      recommendations.push('Express-validator is properly configured');
    } catch (error) {
      issues.push('Express-validator not found - input validation may be incomplete');
    }

    return { issues, recommendations };
  }

  /**
   * 运行完整的安全检查
   */
  static runFullSecurityAudit() {
    console.log('🔍 Running security audit...\n');

    const checks = [
      { name: 'Environment Variables', check: this.checkEnvironmentSecurity },
      { name: 'Password Policy', check: this.checkPasswordPolicy },
      { name: 'JWT Security', check: this.checkJWTSecurity },
      { name: 'Session Security', check: this.checkSessionSecurity },
      { name: 'Rate Limiting', check: this.checkRateLimitSecurity },
      { name: 'CORS Configuration', check: this.checkCORSSecurity },
      { name: 'Input Validation', check: this.checkInputValidation }
    ];

    const auditResults = {
      totalIssues: 0,
      totalRecommendations: 0,
      checks: {}
    };

    checks.forEach(({ name, check }) => {
      console.log(`\n📋 Checking ${name}...`);
      const result = check.call(this);
      
      auditResults.checks[name] = result;
      auditResults.totalIssues += result.issues.length;
      auditResults.totalRecommendations += result.recommendations.length;

      // 输出结果
      if (result.issues.length === 0 && result.recommendations.length === 0) {
        console.log('✅ No issues found');
      } else {
        if (result.issues.length > 0) {
          console.log('❌ Issues found:');
          result.issues.forEach(issue => console.log(`   - ${issue}`));
        }
        
        if (result.recommendations.length > 0) {
          console.log('💡 Recommendations:');
          result.recommendations.forEach(rec => console.log(`   - ${rec}`));
        }
      }
    });

    // 总结
    console.log('\n📊 Security Audit Summary:');
    console.log(`   Critical Issues: ${auditResults.totalIssues}`);
    console.log(`   Recommendations: ${auditResults.totalRecommendations}`);
    
    if (auditResults.totalIssues === 0) {
      console.log('🎉 No critical security issues found!');
    } else {
      console.log('⚠️  Please address the critical issues above');
    }

    return auditResults;
  }

  /**
   * 生成安全配置报告
   */
  static generateSecurityReport() {
    const audit = this.runFullSecurityAudit();
    
    const report = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      audit,
      securityConfig: {
        jwtAlgorithm: SECURITY_CONFIG.JWT.ALGORITHM,
        passwordMinLength: SECURITY_CONFIG.PASSWORD.MIN_LENGTH,
        bcryptRounds: SECURITY_CONFIG.PASSWORD.BCRYPT_ROUNDS,
        maxLoginAttempts: SECURITY_CONFIG.ACCOUNT_LOCK.MAX_LOGIN_ATTEMPTS,
        sessionMaxAge: SECURITY_CONFIG.SESSION.MAX_AGE
      }
    };

    return report;
  }
}

module.exports = SecurityChecker;
