const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ReviewRule = sequelize.define(
  'ReviewRule',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '规则名称',
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '规则描述',
    },
    contentType: {
      type: DataTypes.ENUM('article', 'draft', 'comment', 'media_file', 'user_profile', 'all'),
      allowNull: false,
      defaultValue: 'all',
      comment: '适用内容类型',
    },
    ruleType: {
      type: DataTypes.ENUM(
        'keyword',
        'pattern',
        'ai_model',
        'user_behavior',
        'content_length',
        'custom'
      ),
      allowNull: false,
      comment: '规则类型',
    },
    conditions: {
      type: DataTypes.JSON,
      allowNull: false,
      comment: '规则条件配置',
    },
    action: {
      type: DataTypes.ENUM(
        'auto_approve',
        'auto_reject',
        'flag_for_review',
        'escalate',
        'quarantine'
      ),
      allowNull: false,
      comment: '触发动作',
    },
    severity: {
      type: DataTypes.ENUM('info', 'warning', 'error', 'critical'),
      allowNull: false,
      defaultValue: 'warning',
      comment: '严重程度',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
      comment: '是否启用',
    },
    priority: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 100,
      comment: '优先级（数字越小优先级越高）',
    },
    triggerCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '触发次数统计',
    },
    lastTriggeredAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后触发时间',
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: '创建者ID',
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: '最后更新者ID',
    },
    tags: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      comment: '规则标签',
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {},
      comment: '额外配置',
    },
  },
  {
    tableName: 'review_rules',
    timestamps: true,
    indexes: [
      {
        fields: ['contentType'],
      },
      {
        fields: ['ruleType'],
      },
      {
        fields: ['action'],
      },
      {
        fields: ['severity'],
      },
      {
        fields: ['isActive'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['createdBy'],
      },
      {
        fields: ['isActive', 'priority'],
      },
      {
        fields: ['contentType', 'isActive'],
      },
    ],
  }
);

// 实例方法
ReviewRule.prototype.incrementTrigger = async function () {
  this.triggerCount += 1;
  this.lastTriggeredAt = new Date();
  await this.save();
};

ReviewRule.prototype.checkCondition = function (content, metadata = {}) {
  try {
    const { conditions } = this;

    switch (this.ruleType) {
      case 'keyword':
        return this.checkKeywordCondition(content, conditions);
      case 'pattern':
        return this.checkPatternCondition(content, conditions);
      case 'content_length':
        return this.checkLengthCondition(content, conditions);
      case 'user_behavior':
        return this.checkUserBehaviorCondition(metadata, conditions);
      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking rule condition:', error);
    return false;
  }
};

ReviewRule.prototype.checkKeywordCondition = function (content, conditions) {
  const { keywords = [], caseSensitive = false, matchType = 'any' } = conditions;
  const text = caseSensitive ? content : content.toLowerCase();
  const keywordList = caseSensitive ? keywords : keywords.map((k) => k.toLowerCase());

  if (matchType === 'all') {
    return keywordList.every((keyword) => text.includes(keyword));
  } else {
    return keywordList.some((keyword) => text.includes(keyword));
  }
};

ReviewRule.prototype.checkPatternCondition = function (content, conditions) {
  const { patterns = [], flags = 'gi' } = conditions;

  return patterns.some((pattern) => {
    try {
      const regex = new RegExp(pattern, flags);
      return regex.test(content);
    } catch (error) {
      console.error('Invalid regex pattern:', pattern, error);
      return false;
    }
  });
};

ReviewRule.prototype.checkLengthCondition = function (content, conditions) {
  const { minLength, maxLength } = conditions;
  const length = content.length;

  if (minLength && length < minLength) return true;
  if (maxLength && length > maxLength) return true;

  return false;
};

ReviewRule.prototype.checkUserBehaviorCondition = function (metadata, conditions) {
  const { maxPostsPerDay, minAccountAge, minKarma, suspiciousPatterns = [] } = conditions;

  const { user = {} } = metadata;

  // 检查发帖频率
  if (maxPostsPerDay && user.postsToday > maxPostsPerDay) {
    return true;
  }

  // 检查账户年龄
  if (minAccountAge && user.accountAgeDays < minAccountAge) {
    return true;
  }

  // 检查用户声誉
  if (minKarma && user.karma < minKarma) {
    return true;
  }

  return false;
};

// 类方法
ReviewRule.getActiveRules = async function (contentType = 'all') {
  const whereClause = {
    isActive: true,
  };

  if (contentType !== 'all') {
    whereClause[sequelize.Sequelize.Op.or] = [{ contentType }, { contentType: 'all' }];
  }

  return await this.findAll({
    where: whereClause,
    order: [
      ['priority', 'ASC'],
      ['createdAt', 'ASC'],
    ],
  });
};

ReviewRule.evaluateContent = async function (content, contentType, metadata = {}) {
  const rules = await this.getActiveRules(contentType);
  const triggeredRules = [];

  for (const rule of rules) {
    if (rule.checkCondition(content, metadata)) {
      await rule.incrementTrigger();
      triggeredRules.push({
        rule,
        action: rule.action,
        severity: rule.severity,
        message: rule.description,
      });
    }
  }

  return triggeredRules;
};

module.exports = ReviewRule;
