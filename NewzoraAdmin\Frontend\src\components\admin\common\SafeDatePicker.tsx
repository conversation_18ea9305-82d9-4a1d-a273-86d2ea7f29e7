'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, X } from 'lucide-react';

export interface DateRange {
  startDate: Date;
  endDate: Date;
  label: string;
}

interface SafeDatePickerProps {
  value: DateRange | null;
  onChange: (dateRange: DateRange) => void;
  className?: string;
  placeholder?: string;
}

/**
 * 完全安全的日期选择器 - 使用原生HTML input避免DOM操作问题
 * 遵循代码生成规则：TypeScript强类型、Tailwind CSS、完整错误处理
 */
const SafeDatePicker: React.FC<SafeDatePickerProps> = ({
  value,
  onChange,
  className = '',
  placeholder = 'Select date range'
}) => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [isOpen, setIsOpen] = useState(false);

  // 同步外部值到内部状态
  useEffect(() => {
    if (value) {
      setStartDate(value.startDate.toISOString().split('T')[0]);
      setEndDate(value.endDate.toISOString().split('T')[0]);
    } else {
      setStartDate('');
      setEndDate('');
    }
  }, [value]);

  // 格式化显示值
  const formatDisplayValue = (): string => {
    if (!value) return placeholder;
    
    const start = value.startDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    
    const end = value.endDate.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
    
    if (start === end) return start;
    return `${start} - ${end}`;
  };

  // 计算天数差
  const calculateDays = (): number => {
    if (!value) return 0;
    const diffTime = Math.abs(value.endDate.getTime() - value.startDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
  };

  // 处理开始日期变化
  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = event.target.value;
    setStartDate(newStartDate);
    
    if (newStartDate && endDate) {
      updateDateRange(newStartDate, endDate);
    }
  };

  // 处理结束日期变化
  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = event.target.value;
    setEndDate(newEndDate);
    
    if (startDate && newEndDate) {
      updateDateRange(startDate, newEndDate);
    }
  };

  // 更新日期范围
  const updateDateRange = (start: string, end: string) => {
    try {
      const startDateObj = new Date(start);
      const endDateObj = new Date(end);
      
      // 确保开始日期不晚于结束日期
      const finalStartDate = startDateObj <= endDateObj ? startDateObj : endDateObj;
      const finalEndDate = startDateObj <= endDateObj ? endDateObj : startDateObj;
      
      const newRange: DateRange = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        label: 'Custom Range'
      };
      
      onChange(newRange);
    } catch (error) {
      console.error('Error updating date range:', error);
    }
  };

  // 清除选择
  const clearSelection = () => {
    setStartDate('');
    setEndDate('');
    setIsOpen(false);
  };

  // 快速选择选项
  const quickSelectOptions = [
    {
      label: 'Today',
      getValue: () => {
        const today = new Date();
        return { start: today, end: today };
      }
    },
    {
      label: 'Yesterday',
      getValue: () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        return { start: yesterday, end: yesterday };
      }
    },
    {
      label: 'Last 7 days',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 6);
        return { start, end };
      }
    },
    {
      label: 'Last 30 days',
      getValue: () => {
        const end = new Date();
        const start = new Date();
        start.setDate(start.getDate() - 29);
        return { start, end };
      }
    },
    {
      label: 'This month',
      getValue: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth(), 1);
        const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        return { start, end };
      }
    }
  ];

  // 处理快速选择
  const handleQuickSelect = (option: typeof quickSelectOptions[0]) => {
    try {
      const { start, end } = option.getValue();
      const newRange: DateRange = {
        startDate: start,
        endDate: end,
        label: option.label
      };
      onChange(newRange);
      setIsOpen(false);
    } catch (error) {
      console.error('Error in quick select:', error);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* 触发按钮 */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
      >
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className={value ? 'text-gray-900' : 'text-gray-500'}>
            {formatDisplayValue()}
          </span>
        </div>
        {value && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              clearSelection();
            }}
            className="ml-2 text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </button>

      {/* 下拉面板 */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50 p-4 min-w-80">
          {/* 当前选择显示 */}
          {value && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-center">
                <p className="text-sm font-medium text-blue-900">
                  Selected: {calculateDays()} days
                </p>
                <p className="text-xs text-blue-700 mt-1">
                  {formatDisplayValue()}
                </p>
              </div>
            </div>
          )}

          {/* 快速选择 */}
          <div className="mb-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Quick Select</h4>
            <div className="grid grid-cols-2 gap-2">
              {quickSelectOptions.map((option, index) => (
                <button
                  key={index}
                  type="button"
                  onClick={() => handleQuickSelect(option)}
                  className="px-3 py-2 text-sm text-left border border-gray-200 rounded hover:bg-gray-50 hover:border-blue-300 transition-colors"
                >
                  {option.label}
                </button>
              ))}
            </div>
          </div>

          {/* 自定义日期选择 */}
          <div className="border-t pt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Custom Range</h4>
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  End Date
                </label>
                <input
                  type="date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  min={startDate}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center justify-between pt-4 border-t mt-4">
            <div className="text-xs text-gray-600">
              {startDate && endDate ? 'Range selected' : 'Select start and end dates'}
            </div>
            <div className="flex space-x-2">
              {(startDate || endDate) && (
                <button
                  type="button"
                  onClick={clearSelection}
                  className="px-3 py-1 text-xs text-red-600 hover:text-red-800 border border-red-200 rounded hover:bg-red-50 transition-colors"
                >
                  Clear
                </button>
              )}
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SafeDatePicker;
