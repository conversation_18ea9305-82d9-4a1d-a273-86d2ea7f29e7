'use client';

import React from 'react';

interface BrandNameProps {
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 防翻译的品牌名称组件
 * 使用多重防护机制确保品牌名称不被翻译工具翻译
 */
export default function BrandName({ className = '', style }: BrandNameProps) {
  // 使用字符编码方式生成品牌名称，防止翻译工具识别
  const brandName = String.fromCharCode(78, 101, 119, 122, 111, 114, 97); // "Newzora"
  
  return (
    <span
      className={`brand-name notranslate ${className}`}
      translate="no"
      data-translate="no"
      data-notranslate="true"
      lang="en"
      style={{
        fontFamily: 'inherit',
        ...style
      }}
      suppressHydrationWarning={true}
    >
      {brandName}
    </span>
  );
}

/**
 * 内联品牌名称组件 - 用于在文本中嵌入
 */
export function InlineBrandName({ className = '' }: { className?: string }) {
  return (
    <BrandName 
      className={`inline ${className}`}
      style={{ display: 'inline' }}
    />
  );
}