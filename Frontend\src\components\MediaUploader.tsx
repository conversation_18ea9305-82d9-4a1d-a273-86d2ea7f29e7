'use client';

import React, { useState, useRef, useEffect } from 'react';
import { 
  ArrowPathIcon, 
  PhotoIcon, 
  VideoCameraIcon, 
  MusicalNoteIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

interface MediaUploaderProps {
  type: 'video' | 'audio';
  onFileSelect: (file: File) => void;
  onVideoInfo?: (info: { duration: number; width: number; height: number; size: number }) => void;
  className?: string;
}

const MediaUploader: React.FC<MediaUploaderProps> = ({ 
  type, 
  onFileSelect, 
  onVideoInfo,
  className = '' 
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [videoInfo, setVideoInfo] = useState<{
    duration: number;
    width: number;
    height: number;
    size: number;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);

  // 处理文件拖拽事件
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  // 处理文件 dropped
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  };

  // 处理文件选择
  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  // 处理文件
  const handleFile = (selectedFile: File) => {
    // 验证文件类型
    if (type === 'video' && !selectedFile.type.startsWith('video/')) {
      setError('Please select a video file');
      return;
    }
    
    if (type === 'audio' && !selectedFile.type.startsWith('audio/')) {
      setError('Please select an audio file');
      return;
    }
    
    // 验证文件大小
    const maxSize = type === 'video' ? 5 * 1024 * 1024 * 1024 : 1024 * 1024 * 1024; // 5GB for video, 1GB for audio
    if (selectedFile.size > maxSize) {
      setError(`File size exceeds limit (${type === 'video' ? '5GB' : '1GB'})`);
      return;
    }
    
    setFile(selectedFile);
    onFileSelect(selectedFile);
    setError('');
    
    // 创建预览
    const url = URL.createObjectURL(selectedFile);
    setPreviewUrl(url);
    
    // 获取视频信息
    if (type === 'video') {
      getVideoInfo(selectedFile);
    }
  };

  // 获取视频信息
  const getVideoInfo = (videoFile: File) => {
    setLoading(true);
    
    const video = document.createElement('video');
    video.preload = 'metadata';
    video.src = URL.createObjectURL(videoFile);
    
    video.onloadedmetadata = () => {
      const info = {
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        size: videoFile.size
      };
      
      setVideoInfo(info);
      onVideoInfo?.(info);
      setLoading(false);
      
      // 清理
      URL.revokeObjectURL(video.src);
    };
    
    video.onerror = () => {
      setError('Failed to load video information');
      setLoading(false);
      URL.revokeObjectURL(video.src);
    };
  };

  // 移除文件
  const removeFile = () => {
    setFile(null);
    setPreviewUrl('');
    setVideoInfo(null);
    setError('');
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 格式化时间
  const formatDuration = (seconds: number) => {
    const h = Math.floor(seconds / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);
    
    if (h > 0) {
      return `${h}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')}`;
    }
    return `${m}:${s.toString().padStart(2, '0')}`;
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取视频质量标签
  const getQualityLabel = (width: number, height: number) => {
    if (height >= 4320) return '8K';
    if (height >= 2160) return '4K';
    if (height >= 1440) return 'QHD';
    if (height >= 1080) return 'FHD';
    if (height >= 720) return 'HD';
    if (height >= 480) return 'SD';
    return 'Low';
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg ${className}`}>
      {!file ? (
        // 上传区域
        <div 
          className={`p-8 text-center cursor-pointer transition-all duration-200 ${
            dragActive 
              ? 'bg-blue-50 border-2 border-dashed border-blue-400' 
              : 'border-2 border-dashed border-gray-300 hover:border-gray-400 hover:bg-gray-50'
          }`}
          onDragEnter={handleDrag}
          onDragOver={handleDrag}
          onDragLeave={handleDrag}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={type === 'video' ? 'video/*' : 'audio/*'}
            onChange={handleFileInput}
            className="hidden"
          />
          
          <div className="flex flex-col items-center justify-center">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mb-4">
              {type === 'video' ? (
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
              ) : (
                <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                </svg>
              )}
            </div>
            
            <h3 className="text-base font-medium text-gray-900 mb-2">
              {type === 'video' ? 'Upload Video File' : 'Upload Audio File'}
            </h3>
            
            <p className="text-sm text-gray-600 mb-1">
              Drag and drop your {type} file here
            </p>
            
            <p className="text-sm text-gray-500 mb-4">
              or <span className="text-blue-600 font-medium">browse files</span>
            </p>
            
            <p className="text-xs text-gray-500">
              {type === 'video' 
                ? 'MP4, MOV, AVI • Max 5GB' 
                : 'MP3, WAV, FLAC • Max 1GB'}
            </p>
          </div>
        </div>
      ) : (
        // 文件预览区域
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-base font-medium text-gray-900">
              {type === 'video' ? 'Video File' : 'Audio File'}
            </h3>
            <button 
              onClick={removeFile}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* 预览 */}
          <div className="mb-4">
            {type === 'video' ? (
              <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden border border-gray-200">
                {previewUrl && (
                  <video
                    ref={videoRef}
                    src={previewUrl}
                    className="w-full h-full object-contain"
                    controls={false}
                    muted
                  />
                )}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-12 h-12 bg-white bg-opacity-90 rounded-lg flex items-center justify-center shadow-sm">
                    <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9 4h10a2 2 0 002-2V8a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4 shadow-sm">
                  <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 truncate max-w-xs" title={file.name}>{file.name}</h4>
                  <p className="text-sm text-gray-500">
                    {formatFileSize(file.size)}
                  </p>
                </div>
              </div>
            )}
          </div>
          
          {/* 简化的文件信息 */}
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between text-sm">
              <div className="flex-1 min-w-0">
                <div className="font-medium text-gray-900 truncate" title={file.name}>{file.name}</div>
                <div className="text-gray-500 text-xs mt-1">
                  {formatFileSize(file.size)}
                  {type === 'video' && videoInfo && (
                    <span className="ml-2">• {formatDuration(videoInfo.duration)}</span>
                  )}
                </div>
              </div>
              <div className="text-xs text-gray-400 uppercase">
                {file.type.split('/')[1] || 'Unknown'}
              </div>
            </div>
          </div>
          
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}
          
          <div className="flex justify-between items-center">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 text-sm text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Change File
            </button>
            
            <div className="flex items-center text-green-600">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">Ready</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaUploader;