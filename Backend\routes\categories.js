const express = require('express');
const router = express.Router();
const Article = require('../models/Article');

// Get all categories with article counts
router.get('/', async (req, res) => {
  try {
    const categories = ['Trending', 'Technology', 'Lifestyle', 'Travel', 'Food'];

    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const count = await Article.countDocuments({
          category,
          published: true,
        });
        return { name: category, count };
      })
    );

    res.json(categoriesWithCounts);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
