const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Supabase配置
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

// 兼容性包装器
const sequelize = {
  sync: async () => {
    console.log('✅ Supabase连接已建立');
    return Promise.resolve();
  },
  close: async () => {
    console.log('📴 Supabase连接已关闭');
    return Promise.resolve();
  }
};

// 测试数据库连接
const testConnection = async () => {
  try {
    console.log('✅ Supabase配置已加载');
  } catch (error) {
    console.error('❌ 配置加载失败:', error);
  }
};

module.exports = { sequelize, testConnection, supabase };