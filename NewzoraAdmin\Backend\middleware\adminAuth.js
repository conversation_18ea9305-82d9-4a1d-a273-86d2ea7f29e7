const jwt = require('jsonwebtoken');

// 测试账户数据
const testAccounts = [
  {
    id: '1',
    email: '<EMAIL>',
    password: '$2a$10$JjzCTaBPupA7lR3j2VFvZONtkq3KMgW80c.7px9v94hgBOcR4jadm',
    name: '超级管理员',
    role: 'super_admin',
    isActive: true,
    toSafeJSON: function() {
      return {
        id: this.id,
        email: this.email,
        name: this.name,
        role: this.role
      };
    }
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: '$2a$10$S8cFG8gjozWy82uENisy6.2IvjOfPwE.O52jh7bDdLwAFl/K2Q4zW',
    name: '测试管理员',
    role: 'admin',
    isActive: true,
    toSafeJSON: function() {
      return {
        id: this.id,
        email: this.email,
        name: this.name,
        role: this.role
      };
    }
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: '$2a$10$a.AOhxSfqLpOHnVlaPpuPO.1khvxTkIDkEPxcIxIaLAs7L4uDp.MC',
    name: '审核员',
    role: 'moderator',
    isActive: true,
    toSafeJSON: function() {
      return {
        id: this.id,
        email: this.email,
        name: this.name,
        role: this.role
      };
    }
  }
];

// 管理员角色权限映射
const ROLE_PERMISSIONS = {
  'super_admin': [
    'user:view', 'user:edit', 'user:delete', 'user:role_change',
    'content:view', 'content:edit', 'content:delete', 'content:publish', 'content:review',
    'analytics:view', 'analytics:export',
    'system:settings', 'system:logs'
  ],
  'admin': [
    'user:view', 'user:edit',
    'content:view', 'content:edit', 'content:delete', 'content:publish',
    'analytics:view', 'analytics:export'
  ],
  'moderator': [
    'user:view',
    'content:view', 'content:review',
    'analytics:view'
  ]
};

// 验证管理员身份
const authenticateAdmin = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问被拒绝，未提供认证令牌'
      });
    }

    // 验证JWT令牌
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 首先尝试从测试账户中查找用户
    let user = testAccounts.find(acc => acc.id === decoded.id);

    // 如果不是测试账户，则从数据库中查找
    if (!user) {
      try {
        const { User } = require('../models');
        user = await User.findByPk(decoded.id);

        if (!user) {
          return res.status(401).json({
            success: false,
            message: '无效的认证令牌'
          });
        }
      } catch (error) {
        console.error('数据库查询失败:', error);
        return res.status(401).json({
          success: false,
          message: '无效的认证令牌'
        });
      }
    }

    // 检查用户是否为管理员
    if (!['admin', 'super_admin', 'moderator'].includes(user.role)) {
      return res.status(403).json({
        success: false,
        message: '访问被拒绝，需要管理员权限'
      });
    }

    // 检查用户是否激活
    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: '账户已被禁用'
      });
    }

    // 更新最后登录时间（仅对数据库用户）
    if (!testAccounts.find(acc => acc.id === user.id)) {
      try {
        await user.update({ last_login_at: new Date() });
      } catch (error) {
        console.error('更新最后登录时间失败:', error);
      }
    }

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '无效的认证令牌'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '认证令牌已过期'
      });
    }

    console.error('管理员认证错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 检查特定权限
const requirePermission = (permission) => {
  return (req, res, next) => {
    const userRole = req.user.role;
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];

    if (!userPermissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        message: '权限不足，无法访问此资源'
      });
    }

    next();
  };
};

module.exports = {
  authenticateAdmin,
  requirePermission,
  ROLE_PERMISSIONS
};