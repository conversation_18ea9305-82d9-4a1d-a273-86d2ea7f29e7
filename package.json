{"name": "newzora", "version": "1.0.0", "description": "Newzora - Modern Content Creation and Sharing Platform", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd Backend && npm run dev", "dev:frontend": "cd Frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd Backend && npm run build", "build:frontend": "cd Frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd Backend && npm start", "start:frontend": "cd Frontend && npm start", "install:all": "npm install && cd Backend && npm install && cd ../Frontend && npm install && cd ../NewzoraAdmin/Backend && npm install && cd ../Frontend && npm install", "clean": "rimraf node_modules Backend/node_modules Frontend/node_modules NewzoraAdmin/Backend/node_modules NewzoraAdmin/Frontend/node_modules", "clean:install": "npm run clean && npm run install:all", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd Backend && npm test", "test:frontend": "cd Frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd Backend && npm run lint", "lint:frontend": "cd Frontend && npm run lint", "typecheck": "tsc --noEmit", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "health-check": "node scripts/health-check.js", "prepare": "husky install", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build", "migrate": "cd Backend && npm run migrate", "seed": "cd Backend && npm run seed", "db:setup": "node scripts/database-manager.js setup", "db:create:web": "node scripts/web-database-manager.js create", "db:create:admin": "node NewzoraAdmin/scripts/database-manager.js create", "db:init:web": "node scripts/web-database-manager.js init", "db:init:admin": "node NewzoraAdmin/scripts/database-manager.js init", "db:backup:web": "node scripts/web-database-manager.js backup", "db:backup:admin": "node NewzoraAdmin/scripts/database-manager.js backup", "db:status:web": "node scripts/web-database-manager.js status", "db:status:admin": "node NewzoraAdmin/scripts/database-manager.js status", "db:verify": "node scripts/verify-database-config.js", "quick-setup": "node scripts/quick-setup.js", "dev:admin": "concurrently \"npm run dev:admin:backend\" \"npm run dev:admin:frontend\"", "dev:admin:backend": "cd NewzoraAdmin/Backend && npm run dev", "dev:admin:frontend": "cd NewzoraAdmin/Frontend && npm run dev", "dev:all": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:admin:backend\" \"npm run dev:admin:frontend\""}, "keywords": ["content", "social", "platform", "nextjs", "nodejs", "express", "postgresql"], "author": "Newzora Team", "license": "MIT", "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "concurrently": "^8.2.2", "eslint": "^8.57.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "rimraf": "^5.0.5", "typescript": "^5.4.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/newzora/newzora.git"}, "bugs": {"url": "https://github.com/newzora/newzora/issues"}, "homepage": "https://github.com/newzora/newzora#readme", "dependencies": {"@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.0", "axios": "^1.11.0", "lodash": "^4.17.21"}}