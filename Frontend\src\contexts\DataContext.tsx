'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface UserBalance {
  availableBalance: number;
  pendingBalance: number;
  totalEarnings: number;
}

interface WithdrawalRequest {
  id: number;
  amount: number;
  netAmount: number;
  withdrawalMethod: string;
  status: string;
  country: string;
  taxAmount: number;
  feeAmount: number;
  scheduledPaymentDate: string;
  createdAt: string;
}

interface Article {
  id: number;
  title: string;
  status: 'published' | 'draft';
  type: 'article' | 'video' | 'audio';
  publishedAt?: string;
  updatedAt: string;
  views?: number;
  likes?: number;
  comments?: number;
  shares?: number;
  saves?: number;
  category: string;
}

interface WorkInteraction {
  workId: number;
  workType: 'article' | 'video' | 'audio';
  likes: number;
  comments: number;
  shares: number;
  saves: number;
  views: number;
  userLiked: boolean;
  userSaved: boolean;
}

interface DataContextType {
  // 用户余额
  balance: UserBalance;
  updateBalance: (newBalance: Partial<UserBalance>) => void;
  
  // 提现记录
  withdrawalRequests: WithdrawalRequest[];
  addWithdrawalRequest: (request: WithdrawalRequest) => void;
  updateWithdrawalRequest: (id: number, updates: Partial<WithdrawalRequest>) => void;
  
  // 文章/内容
  articles: Article[];
  updateArticle: (id: number, updates: Partial<Article>) => void;
  deleteArticle: (id: number) => void;
  addArticle: (article: Article) => void;
  
  // 作品交互数据
  workInteractions: { [key: string]: WorkInteraction };
  getWorkInteraction: (workId: number, workType: string) => WorkInteraction;
  updateWorkInteraction: (workId: number, workType: string, updates: Partial<WorkInteraction>) => void;
  toggleLike: (workId: number, workType: string) => void;
  toggleSave: (workId: number, workType: string) => void;
  incrementViews: (workId: number, workType: string) => void;
  addComment: (workId: number, workType: string) => void;
  incrementShares: (workId: number, workType: string) => void;
  
  // 通用数据刷新
  refreshData: () => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

interface DataProviderProps {
  children: ReactNode;
}

export function DataProvider({ children }: DataProviderProps) {
  // 用户余额状态
  const [balance, setBalance] = useState<UserBalance>({
    availableBalance: 1250.75,
    pendingBalance: 320.50,
    totalEarnings: 5680.25
  });

  // 提现记录状态
  const [withdrawalRequests, setWithdrawalRequests] = useState<WithdrawalRequest[]>([]);

  // 作品交互数据状态
  const [workInteractions, setWorkInteractions] = useState<{ [key: string]: WorkInteraction }>({});

  // 文章状态
  const [articles, setArticles] = useState<Article[]>([
    {
      id: 1,
      title: 'The Future of AI in Everyday Life',
      status: 'published',
      type: 'article',
      publishedAt: '2024-01-15',
      updatedAt: '2024-01-14',
      views: 1250,
      likes: 42,
      comments: 15,
      shares: 8,
      saves: 23,
      category: 'Technology',
    },
    {
      id: 2,
      title: 'Cybersecurity Best Practices',
      status: 'draft',
      type: 'article',
      updatedAt: '2024-01-14',
      comments: 0,
      shares: 0,
      saves: 0,
      category: 'Technology',
    },
    {
      id: 3,
      title: 'Travel Guide to Southeast Asia',
      status: 'published',
      type: 'video',
      publishedAt: '2024-01-12',
      updatedAt: '2024-01-11',
      views: 890,
      likes: 38,
      comments: 12,
      shares: 5,
      saves: 18,
      category: 'Travel',
    },
    {
      id: 4,
      title: 'Meditation Music Collection',
      status: 'draft',
      type: 'audio',
      updatedAt: '2024-01-10',
      comments: 0,
      shares: 0,
      saves: 0,
      category: 'Lifestyle',
    },
  ]);

  // 初始化数据
  useEffect(() => {
    // 从localStorage加载提现记录
    const savedRequests = localStorage.getItem('withdrawalRequests');
    if (savedRequests) {
      try {
        const requests = JSON.parse(savedRequests);
        setWithdrawalRequests(requests);
      } catch (error) {
        console.error('Failed to parse withdrawal requests:', error);
      }
    }

    // 从localStorage加载用户余额
    const savedBalance = localStorage.getItem('userBalance');
    if (savedBalance) {
      try {
        const balanceData = JSON.parse(savedBalance);
        setBalance(balanceData);
      } catch (error) {
        console.error('Failed to parse user balance:', error);
      }
    }

    // 从localStorage加载文章数据
    const savedArticles = localStorage.getItem('userArticles');
    if (savedArticles) {
      try {
        const articlesData = JSON.parse(savedArticles);
        setArticles(articlesData);
      } catch (error) {
        console.error('Failed to parse articles:', error);
      }
    }

    // 从localStorage加载作品交互数据
    const savedInteractions = localStorage.getItem('workInteractions');
    if (savedInteractions) {
      try {
        const interactionsData = JSON.parse(savedInteractions);
        setWorkInteractions(interactionsData);
      } catch (error) {
        console.error('Failed to parse work interactions:', error);
      }
    }
  }, []);

  // 更新余额
  const updateBalance = (newBalance: Partial<UserBalance>) => {
    setBalance(prev => {
      const updated = { ...prev, ...newBalance };
      localStorage.setItem('userBalance', JSON.stringify(updated));
      return updated;
    });
  };

  // 添加提现记录
  const addWithdrawalRequest = (request: WithdrawalRequest) => {
    setWithdrawalRequests(prev => {
      const updated = [request, ...prev];
      localStorage.setItem('withdrawalRequests', JSON.stringify(updated));
      return updated;
    });
  };

  // 更新提现记录
  const updateWithdrawalRequest = (id: number, updates: Partial<WithdrawalRequest>) => {
    setWithdrawalRequests(prev => {
      const updated = prev.map(req => 
        req.id === id ? { ...req, ...updates } : req
      );
      localStorage.setItem('withdrawalRequests', JSON.stringify(updated));
      return updated;
    });
  };

  // 更新文章
  const updateArticle = (id: number, updates: Partial<Article>) => {
    setArticles(prev => {
      const updated = prev.map(article => 
        article.id === id ? { ...article, ...updates } : article
      );
      localStorage.setItem('userArticles', JSON.stringify(updated));
      return updated;
    });
  };

  // 删除文章
  const deleteArticle = (id: number) => {
    setArticles(prev => {
      const updated = prev.filter(article => article.id !== id);
      localStorage.setItem('userArticles', JSON.stringify(updated));
      return updated;
    });
  };

  // 添加文章
  const addArticle = (article: Article) => {
    setArticles(prev => {
      const updated = [article, ...prev];
      localStorage.setItem('userArticles', JSON.stringify(updated));
      return updated;
    });
  };

  // 获取作品交互数据
  const getWorkInteraction = (workId: number, workType: string): WorkInteraction => {
    const key = `${workType}_${workId}`;
    if (!workInteractions[key]) {
      // 从articles中获取初始数据
      const article = articles.find(a => a.id === workId && a.type === workType);
      const defaultInteraction: WorkInteraction = {
        workId,
        workType: workType as 'article' | 'video' | 'audio',
        likes: article?.likes || 0,
        comments: article?.comments || 0,
        shares: article?.shares || 0,
        saves: article?.saves || 0,
        views: article?.views || 0,
        userLiked: false,
        userSaved: false,
      };
      setWorkInteractions(prev => ({ ...prev, [key]: defaultInteraction }));
      return defaultInteraction;
    }
    return workInteractions[key];
  };

  // 更新作品交互数据
  const updateWorkInteraction = (workId: number, workType: string, updates: Partial<WorkInteraction>) => {
    const key = `${workType}_${workId}`;
    setWorkInteractions(prev => {
      const updated = {
        ...prev,
        [key]: { ...prev[key], ...updates }
      };
      localStorage.setItem('workInteractions', JSON.stringify(updated));
      return updated;
    });
    
    // 同时更新articles中的数据
    updateArticle(workId, {
      likes: updates.likes,
      comments: updates.comments,
      shares: updates.shares,
      saves: updates.saves,
      views: updates.views,
    });
  };

  // 切换点赞状态
  const toggleLike = (workId: number, workType: string) => {
    const interaction = getWorkInteraction(workId, workType);
    const newLiked = !interaction.userLiked;
    const newLikes = newLiked ? interaction.likes + 1 : interaction.likes - 1;
    
    updateWorkInteraction(workId, workType, {
      userLiked: newLiked,
      likes: newLikes
    });
  };

  // 切换收藏状态
  const toggleSave = (workId: number, workType: string) => {
    const interaction = getWorkInteraction(workId, workType);
    const newSaved = !interaction.userSaved;
    const newSaves = newSaved ? interaction.saves + 1 : interaction.saves - 1;
    
    updateWorkInteraction(workId, workType, {
      userSaved: newSaved,
      saves: newSaves
    });
  };

  // 增加浏览量
  const incrementViews = (workId: number, workType: string) => {
    const interaction = getWorkInteraction(workId, workType);
    updateWorkInteraction(workId, workType, {
      views: interaction.views + 1
    });
  };

  // 添加评论
  const addComment = (workId: number, workType: string) => {
    const interaction = getWorkInteraction(workId, workType);
    updateWorkInteraction(workId, workType, {
      comments: interaction.comments + 1
    });
  };

  // 增加分享数
  const incrementShares = (workId: number, workType: string) => {
    const interaction = getWorkInteraction(workId, workType);
    updateWorkInteraction(workId, workType, {
      shares: interaction.shares + 1
    });
  };

  // 刷新所有数据
  const refreshData = () => {
    // 触发数据重新加载
    window.dispatchEvent(new Event('dataRefresh'));
  };

  const value: DataContextType = {
    balance,
    updateBalance,
    withdrawalRequests,
    addWithdrawalRequest,
    updateWithdrawalRequest,
    articles,
    updateArticle,
    deleteArticle,
    addArticle,
    workInteractions,
    getWorkInteraction,
    updateWorkInteraction,
    toggleLike,
    toggleSave,
    incrementViews,
    addComment,
    incrementShares,
    refreshData,
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
}

export function useData() {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
}