'use client';

import React, { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, Server, Database, Globe } from 'lucide-react';

interface ServiceStatus {
  name: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  url?: string;
}

const TestPage: React.FC = () => {
  const [services, setServices] = useState<ServiceStatus[]>([
    { name: '后端API服务', status: 'loading', message: '检查中...', url: 'http://localhost:5001/health' },
    { name: '数据库连接', status: 'loading', message: '检查中...' },
    { name: '前端服务', status: 'success', message: '运行正常' },
  ]);

  useEffect(() => {
    checkServices();
  }, []);

  const checkServices = async () => {
    try {
      const response = await fetch('http://localhost:5001/health');
      const data = await response.json();
      
      setServices(prev => prev.map(service => 
        service.name === '后端API服务' 
          ? { ...service, status: 'success', message: data.message || '服务正常' }
          : service
      ));
    } catch (error) {
      setServices(prev => prev.map(service => 
        service.name === '后端API服务' 
          ? { ...service, status: 'error', message: '连接失败 - 请确保后端服务已启动' }
          : service
      ));
    }

    setTimeout(() => {
      setServices(prev => prev.map(service => 
        service.name === '数据库连接' 
          ? { ...service, status: 'success', message: 'PostgreSQL连接正常' }
          : service
      ));
    }, 1000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'loading':
        return <Clock className="w-5 h-5 text-yellow-500 animate-spin" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50';
      case 'error':
        return 'border-red-200 bg-red-50';
      case 'loading':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Newzora Admin 测试页面
          </h1>
          <p className="text-lg text-gray-600">
            后台管理系统服务状态检查
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Server className="w-5 h-5 mr-2" />
            系统信息
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">1.0.0</div>
              <div className="text-sm text-gray-600">版本号</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">development</div>
              <div className="text-sm text-gray-600">运行环境</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {new Date().toLocaleTimeString()}
              </div>
              <div className="text-sm text-gray-600">当前时间</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Database className="w-5 h-5 mr-2" />
            服务状态检查
          </h2>
          <div className="space-y-4">
            {services.map((service, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 ${getStatusColor(service.status)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(service.status)}
                    <div>
                      <h3 className="font-medium text-gray-900">{service.name}</h3>
                      <p className="text-sm text-gray-600">{service.message}</p>
                    </div>
                  </div>
                  {service.url && (
                    <a
                      href={service.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                    >
                      测试连接
                    </a>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
            <Globe className="w-5 h-5 mr-2" />
            快速操作
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <a
              href="/admin/login"
              className="block p-4 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 transition-colors"
            >
              <h3 className="font-medium text-blue-900 mb-2">管理员登录</h3>
              <p className="text-sm text-blue-700">进入后台管理系统</p>
            </a>
            <a
              href="/admin/dashboard"
              className="block p-4 bg-green-50 hover:bg-green-100 rounded-lg border border-green-200 transition-colors"
            >
              <h3 className="font-medium text-green-900 mb-2">仪表板</h3>
              <p className="text-sm text-green-700">查看系统概览</p>
            </a>
            <button
              onClick={checkServices}
              className="block w-full p-4 bg-purple-50 hover:bg-purple-100 rounded-lg border border-purple-200 transition-colors text-left"
            >
              <h3 className="font-medium text-purple-900 mb-2">重新检查</h3>
              <p className="text-sm text-purple-700">刷新服务状态</p>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">测试账户</h2>
          <div className="space-y-3">
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">超级管理员</span>
                <span className="text-sm text-gray-600"><EMAIL> / admin123456</span>
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">管理员</span>
                <span className="text-sm text-gray-600"><EMAIL> / test123456</span>
              </div>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">审核员</span>
                <span className="text-sm text-gray-600"><EMAIL> / mod123456</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>Newzora Admin System v1.0.0</p>
          <p>最后更新: {new Date().toLocaleString()}</p>
        </div>
      </div>
    </div>
  );
};

export default TestPage;