'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Bold, 
  Italic, 
  Underline, 
  Link, 
  Image, 
  List, 
  ListOrdered,
  Quote,
  Code,
  Save,
  Eye,
  Upload,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import LoadingState from '@/components/ui/LoadingState';

interface ModernArticleEditorProps {
  initialContent?: string;
  initialTitle?: string;
  onSave?: (content: { title: string; content: string; excerpt: string }) => Promise<void>;
  onPreview?: (content: { title: string; content: string }) => void;
  autoSave?: boolean;
  className?: string;
}

/**
 * 现代化的文章编辑器组件
 * 支持富文本编辑、自动保存、预览等功能
 */
export default function ModernArticleEditor({
  initialContent = '',
  initialTitle = '',
  onSave,
  onPreview,
  autoSave = true,
  className = ''
}: ModernArticleEditorProps) {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  
  const editorRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLInputElement>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  // 自动保存功能
  const triggerAutoSave = useCallback(() => {
    if (!autoSave || !onSave) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(async () => {
      if (title.trim() || content.trim()) {
        setIsSaving(true);
        try {
          const excerpt = content.replace(/<[^>]*>/g, '').substring(0, 150);
          await onSave({ title, content, excerpt });
          setLastSaved(new Date());
        } catch (error) {
          console.error('Auto-save failed:', error);
        } finally {
          setIsSaving(false);
        }
      }
    }, 2000); // 2秒后自动保存
  }, [title, content, onSave, autoSave]);

  // 监听内容变化，触发自动保存
  useEffect(() => {
    triggerAutoSave();
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [triggerAutoSave]);

  // 格式化工具函数
  const formatText = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
  };

  // 插入链接
  const insertLink = () => {
    const url = prompt('请输入链接地址:');
    if (url) {
      formatText('createLink', url);
    }
  };

  // 插入图片
  const insertImage = () => {
    if (imageUrl.trim()) {
      formatText('insertImage', imageUrl);
      setImageUrl('');
      setShowImageUpload(false);
    }
  };

  // 手动保存
  const handleSave = async () => {
    if (!onSave) return;

    setIsSaving(true);
    try {
      const excerpt = content.replace(/<[^>]*>/g, '').substring(0, 150);
      await onSave({ title, content, excerpt });
      setLastSaved(new Date());
    } catch (error) {
      console.error('Save failed:', error);
    } finally {
      setIsSaving(false);
    }
  };

  // 预览功能
  const handlePreview = () => {
    if (onPreview) {
      onPreview({ title, content });
    }
  };

  // 处理编辑器内容变化
  const handleContentChange = () => {
    if (editorRef.current) {
      setContent(editorRef.current.innerHTML);
    }
  };

  // 工具栏按钮配置
  const toolbarButtons = [
    { icon: Bold, command: 'bold', title: '粗体' },
    { icon: Italic, command: 'italic', title: '斜体' },
    { icon: Underline, command: 'underline', title: '下划线' },
    { icon: List, command: 'insertUnorderedList', title: '无序列表' },
    { icon: ListOrdered, command: 'insertOrderedList', title: '有序列表' },
    { icon: Quote, command: 'formatBlock', value: 'blockquote', title: '引用' },
    { icon: Code, command: 'formatBlock', value: 'pre', title: '代码块' },
  ];

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          {/* 格式化按钮 */}
          {toolbarButtons.map((button, index) => (
            <Button
              key={index}
              variant="ghost"
              size="sm"
              onClick={() => formatText(button.command, button.value)}
              title={button.title}
              className="p-2"
            >
              <button.icon className="w-4 h-4" />
            </Button>
          ))}
          
          {/* 链接按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={insertLink}
            title="插入链接"
            className="p-2"
          >
            <Link className="w-4 h-4" />
          </Button>
          
          {/* 图片按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowImageUpload(!showImageUpload)}
            title="插入图片"
            className="p-2"
          >
            <Image className="w-4 h-4" />
          </Button>
        </div>

        {/* 右侧操作按钮 */}
        <div className="flex items-center space-x-3">
          {/* 自动保存状态 */}
          {autoSave && (
            <div className="flex items-center text-sm text-gray-500">
              {isSaving ? (
                <LoadingState 
                  isLoading={true} 
                  loadingText="保存中..." 
                  size="sm" 
                />
              ) : lastSaved ? (
                <span>已保存 {lastSaved.toLocaleTimeString()}</span>
              ) : (
                <span>未保存</span>
              )}
            </div>
          )}

          {/* 预览按钮 */}
          {onPreview && (
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              className="flex items-center"
            >
              <Eye className="w-4 h-4 mr-2" />
              预览
            </Button>
          )}

          {/* 保存按钮 */}
          {onSave && (
            <Button
              variant="primary"
              size="sm"
              onClick={handleSave}
              disabled={isSaving}
              className="flex items-center"
            >
              <Save className="w-4 h-4 mr-2" />
              保存
            </Button>
          )}
        </div>
      </div>

      {/* 图片上传面板 */}
      {showImageUpload && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <input
              type="url"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="请输入图片URL"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Button
              variant="primary"
              size="sm"
              onClick={insertImage}
              disabled={!imageUrl.trim()}
            >
              <Upload className="w-4 h-4 mr-2" />
              插入
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowImageUpload(false)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* 编辑区域 */}
      <div className="p-4">
        {/* 标题输入 */}
        <input
          ref={titleRef}
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="请输入文章标题..."
          className="w-full text-2xl font-bold border-none outline-none placeholder-gray-400 mb-4"
        />

        {/* 内容编辑器 */}
        <div
          ref={editorRef}
          contentEditable
          onInput={handleContentChange}
          dangerouslySetInnerHTML={{ __html: content }}
          className="min-h-[400px] prose prose-lg max-w-none focus:outline-none"
          style={{
            lineHeight: '1.6',
            fontSize: '16px',
            color: '#374151'
          }}
          data-placeholder="开始写作..."
        />
      </div>

      {/* 字数统计 */}
      <div className="px-4 py-2 border-t border-gray-200 bg-gray-50 text-sm text-gray-500 flex justify-between">
        <span>
          字数: {content.replace(/<[^>]*>/g, '').length} | 
          标题: {title.length}
        </span>
        <span>
          预计阅读时间: {Math.ceil(content.replace(/<[^>]*>/g, '').length / 200)} 分钟
        </span>
      </div>
    </div>
  );
}