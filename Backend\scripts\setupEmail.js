#!/usr/bin/env node

/**
 * 邮件服务配置向导
 * 帮助用户交互式配置邮件服务
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// 邮件服务提供商配置
const EMAIL_PROVIDERS = {
  1: {
    name: 'Gmail',
    provider: 'gmail',
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    description: '适合个人项目和小团队，免费但有发送限制',
  },
  2: {
    name: 'SendGrid',
    provider: 'sendgrid',
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    description: '专业邮件服务，高送达率，适合生产环境',
  },
  3: {
    name: 'AWS SES',
    provider: 'aws_ses',
    host: 'email-smtp.us-east-1.amazonaws.com',
    port: 587,
    secure: false,
    description: '与AWS生态集成，成本效益高',
  },
  4: {
    name: 'Microsoft Outlook',
    provider: 'outlook',
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false,
    description: '微软邮件服务',
  },
  5: {
    name: '自定义SMTP',
    provider: 'custom',
    description: '使用自定义SMTP服务器',
  },
};

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function displayWelcome() {
  console.log(colors.blue(colors.bold('\n📧 OneNews 邮件服务配置向导\n')));
  console.log('此向导将帮助您配置OneNews的邮件服务。');
  console.log('您可以选择Gmail、SendGrid、AWS SES等多种邮件服务提供商。\n');
}

function displayProviders() {
  console.log(colors.cyan('请选择邮件服务提供商:\n'));

  Object.entries(EMAIL_PROVIDERS).forEach(([key, provider]) => {
    console.log(`${colors.bold(key)}. ${colors.green(provider.name)}`);
    console.log(`   ${provider.description}\n`);
  });
}

async function selectProvider() {
  displayProviders();

  while (true) {
    const choice = await question('请输入选项 (1-5): ');

    if (EMAIL_PROVIDERS[choice]) {
      return EMAIL_PROVIDERS[choice];
    }

    console.log(colors.red('无效选择，请输入 1-5 之间的数字。'));
  }
}

async function getGmailConfig() {
  console.log(colors.yellow('\n📋 Gmail 配置说明:'));
  console.log('1. 登录您的Google账户');
  console.log('2. 前往 https://myaccount.google.com/security');
  console.log('3. 启用两步验证');
  console.log('4. 在"应用专用密码"中生成新密码');
  console.log('5. 选择"邮件"和"其他（自定义名称）"');
  console.log('6. 输入"OneNews"作为应用名称');
  console.log('7. 复制生成的16位密码\n');

  const email = await question('请输入您的Gmail地址: ');
  const password = await question('请输入应用专用密码 (16位): ');
  const fromEmail = await question('请输入发件人显示邮箱 (可以与Gmail地址相同): ');

  return {
    EMAIL_PROVIDER: 'gmail',
    EMAIL_HOST: 'smtp.gmail.com',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'false',
    EMAIL_USER: email,
    EMAIL_PASS: password,
    EMAIL_FROM: `OneNews <${fromEmail}>`,
  };
}

async function getSendGridConfig() {
  console.log(colors.yellow('\n📋 SendGrid 配置说明:'));
  console.log('1. 注册SendGrid账户: https://sendgrid.com/');
  console.log('2. 验证您的邮箱地址');
  console.log('3. 前往 Settings > API Keys');
  console.log('4. 创建新的API密钥');
  console.log('5. 选择"Restricted Access"并给予"Mail Send"权限');
  console.log('6. 复制生成的API密钥\n');

  const apiKey = await question('请输入SendGrid API密钥: ');
  const fromEmail = await question('请输入发件人邮箱地址: ');

  return {
    EMAIL_PROVIDER: 'sendgrid',
    EMAIL_HOST: 'smtp.sendgrid.net',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'false',
    EMAIL_USER: 'apikey',
    EMAIL_PASS: apiKey,
    EMAIL_FROM: `OneNews <${fromEmail}>`,
  };
}

async function getAWSSESConfig() {
  console.log(colors.yellow('\n📋 AWS SES 配置说明:'));
  console.log('1. 登录AWS控制台');
  console.log('2. 前往Simple Email Service (SES)');
  console.log('3. 验证发送邮箱地址或域名');
  console.log('4. 创建SMTP凭据');
  console.log('5. 记录用户名和密码\n');

  const region = await question('请输入AWS区域 (如: us-east-1): ');
  const username = await question('请输入SMTP用户名: ');
  const password = await question('请输入SMTP密码: ');
  const fromEmail = await question('请输入发件人邮箱地址: ');

  return {
    EMAIL_PROVIDER: 'aws_ses',
    EMAIL_HOST: `email-smtp.${region}.amazonaws.com`,
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'false',
    EMAIL_USER: username,
    EMAIL_PASS: password,
    EMAIL_FROM: `OneNews <${fromEmail}>`,
    AWS_SES_HOST: `email-smtp.${region}.amazonaws.com`,
  };
}

async function getOutlookConfig() {
  console.log(colors.yellow('\n📋 Outlook 配置说明:'));
  console.log('1. 使用您的Outlook/Hotmail邮箱');
  console.log('2. 可能需要启用"安全性较低的应用访问权限"\n');

  const email = await question('请输入您的Outlook邮箱地址: ');
  const password = await question('请输入邮箱密码: ');

  return {
    EMAIL_PROVIDER: 'outlook',
    EMAIL_HOST: 'smtp-mail.outlook.com',
    EMAIL_PORT: '587',
    EMAIL_SECURE: 'false',
    EMAIL_USER: email,
    EMAIL_PASS: password,
    EMAIL_FROM: `OneNews <${email}>`,
  };
}

async function getCustomConfig() {
  console.log(colors.yellow('\n📋 自定义SMTP配置:'));

  const host = await question('请输入SMTP服务器地址: ');
  const port = await question('请输入SMTP端口 (通常是587或465): ');
  const secure = await question('是否使用SSL? (y/n): ');
  const user = await question('请输入用户名: ');
  const password = await question('请输入密码: ');
  const fromEmail = await question('请输入发件人邮箱地址: ');

  return {
    EMAIL_PROVIDER: 'custom',
    EMAIL_HOST: host,
    EMAIL_PORT: port,
    EMAIL_SECURE: secure.toLowerCase() === 'y' ? 'true' : 'false',
    EMAIL_USER: user,
    EMAIL_PASS: password,
    EMAIL_FROM: `OneNews <${fromEmail}>`,
  };
}

async function getEmailConfig(provider) {
  switch (provider.provider) {
    case 'gmail':
      return await getGmailConfig();
    case 'sendgrid':
      return await getSendGridConfig();
    case 'aws_ses':
      return await getAWSSESConfig();
    case 'outlook':
      return await getOutlookConfig();
    case 'custom':
      return await getCustomConfig();
    default:
      throw new Error('未知的邮件服务提供商');
  }
}

function updateEnvFile(config) {
  const envPath = path.join(__dirname, '../.env');
  let envContent = '';

  // 读取现有的.env文件
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }

  // 更新或添加邮件配置
  Object.entries(config).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const line = `${key}=${value}`;

    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, line);
    } else {
      envContent += `\n${line}`;
    }
  });

  // 写入文件
  fs.writeFileSync(envPath, envContent.trim() + '\n');
  console.log(colors.green(`\n✅ 邮件配置已保存到 ${envPath}`));
}

async function testConfiguration() {
  const test = await question('\n是否要测试邮件配置? (y/n): ');

  if (test.toLowerCase() === 'y') {
    const testEmail = await question('请输入测试邮箱地址: ');

    console.log(colors.yellow('\n🧪 正在测试邮件配置...'));

    try {
      // 重新加载环境变量
      delete require.cache[require.resolve('dotenv')];
      require('dotenv').config();

      const { testEmailConfig, sendTestEmail } = require('../services/emailService');

      // 测试连接
      const connectionSuccess = await testEmailConfig();
      if (!connectionSuccess) {
        console.log(colors.red('❌ SMTP连接测试失败'));
        return;
      }

      // 发送测试邮件
      await sendTestEmail(testEmail);
      console.log(colors.green('✅ 测试邮件发送成功！'));
      console.log(colors.cyan(`请检查 ${testEmail} 的收件箱（包括垃圾邮件文件夹）`));
    } catch (error) {
      console.log(colors.red(`❌ 测试失败: ${error.message}`));
    }
  }
}

async function main() {
  try {
    displayWelcome();

    const provider = await selectProvider();
    console.log(colors.green(`\n您选择了: ${provider.name}`));

    const config = await getEmailConfig(provider);

    console.log(colors.cyan('\n📝 配置摘要:'));
    Object.entries(config).forEach(([key, value]) => {
      if (key.includes('PASS')) {
        console.log(`   ${key}: ${'*'.repeat(value.length)}`);
      } else {
        console.log(`   ${key}: ${value}`);
      }
    });

    const confirm = await question('\n确认保存此配置? (y/n): ');

    if (confirm.toLowerCase() === 'y') {
      updateEnvFile(config);
      await testConfiguration();

      console.log(colors.green(colors.bold('\n🎉 邮件服务配置完成！')));
      console.log('您现在可以使用OneNews的所有邮件功能。');
    } else {
      console.log(colors.yellow('配置已取消。'));
    }
  } catch (error) {
    console.error(colors.red(`\n💥 配置过程中发生错误: ${error.message}`));
  } finally {
    rl.close();
  }
}

// 运行配置向导
main();
