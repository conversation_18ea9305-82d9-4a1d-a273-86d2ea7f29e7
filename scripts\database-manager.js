#!/usr/bin/env node

/**
 * Newzora 数据库协调管理工具
 * 协调管理主站前台和后台管理系统的数据库
 */

const { execSync } = require('child_process');
const path = require('path');
require('dotenv').config();

// 数据库配置
const databases = {
  web: {
    name: 'PostgreSQL-newzora_web',
    password: 'wasd080980!',
    port: 5432,
    description: '主站前台数据库',
    scriptPath: './scripts/web-database-manager.js'
  },
  admin: {
    name: 'PostgreSQL-newzora_admin', 
    password: 'QWasd080980!',
    port: 5433,
    description: '后台管理系统数据库',
    scriptPath: './NewzoraAdmin/scripts/database-manager.js'
  }
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`🔄 ${description}...`, 'cyan');
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} 完成`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} 失败: ${error.message}`, 'red');
    throw error;
  }
}

// 创建数据库
function createDatabase(dbType) {
  const db = databases[dbType];
  if (!db) {
    throw new Error(`未知的数据库类型: ${dbType}`);
  }

  log(`\n📋 创建${db.description}`, 'bright');
  executeCommand(`node ${db.scriptPath} create`, `创建${db.description}`);
}

// 删除数据库
function dropDatabase(dbType) {
  const db = databases[dbType];
  if (!db) {
    throw new Error(`未知的数据库类型: ${dbType}`);
  }

  log(`\n🗑️  删除${db.description}`, 'bright');
  
  const dropDbCommand = `psql -U postgres -h localhost -c "DROP DATABASE IF EXISTS \\"${db.name}\\";"`; 
  executeCommand(dropDbCommand, `删除数据库 ${db.name}`);
}

// 初始化数据库
function initDatabase(dbType) {
  const db = databases[dbType];
  if (!db) {
    throw new Error(`未知的数据库类型: ${dbType}`);
  }

  log(`\n🔧 初始化${db.description}`, 'bright');
  executeCommand(`node ${db.scriptPath} init`, `初始化${db.description}`);
}

// 备份数据库
function backupDatabase(dbType) {
  const db = databases[dbType];
  if (!db) {
    throw new Error(`未知的数据库类型: ${dbType}`);
  }

  log(`\n💾 备份${db.description}`, 'bright');
  executeCommand(`node ${db.scriptPath} backup`, `备份${db.description}`);
}

// 恢复数据库
function restoreDatabase(dbType, backupFile) {
  const db = databases[dbType];
  if (!db) {
    throw new Error(`未知的数据库类型: ${dbType}`);
  }

  log(`\n🔄 恢复${db.description}`, 'bright');
  
  const restoreCommand = `psql -U postgres -h localhost -d "${db.name}" < "${backupFile}"`;
  executeCommand(restoreCommand, `从 ${backupFile} 恢复数据库`);
}

// 检查数据库状态
function checkDatabaseStatus(dbType) {
  const db = databases[dbType];
  if (!db) {
    throw new Error(`未知的数据库类型: ${dbType}`);
  }

  log(`\n📊 检查${db.description}状态`, 'bright');
  executeCommand(`node ${db.scriptPath} status`, `检查${db.description}状态`);
}

// 显示帮助信息
function showHelp() {
  log('\n🚀 Newzora 数据库管理工具', 'bright');
  log('\n用法: node database-manager.js <命令> [参数]', 'cyan');
  log('\n可用命令:', 'yellow');
  log('  create <web|admin>     - 创建数据库', 'green');
  log('  drop <web|admin>       - 删除数据库', 'red');
  log('  init <web|admin>       - 初始化数据库', 'blue');
  log('  backup <web|admin>     - 备份数据库', 'magenta');
  log('  restore <web|admin> <backup-file> - 恢复数据库', 'cyan');
  log('  status <web|admin>     - 检查数据库状态', 'yellow');
  log('  setup                  - 完整设置（创建+初始化）', 'bright');
  log('  help                   - 显示此帮助信息', 'reset');
  log('\n数据库信息:', 'yellow');
  Object.entries(databases).forEach(([key, db]) => {
    log(`  ${key}: ${db.description} (${db.name})`, 'cyan');
  });
}

// 完整设置
function fullSetup() {
  log('\n🚀 开始完整数据库设置', 'bright');
  
  try {
    // 设置主站前台数据库
    log('\n=== 设置主站前台数据库 ===', 'yellow');
    executeCommand(`node ${databases.web.scriptPath} setup`, '设置主站前台数据库');
    
    // 设置后台管理系统数据库
    log('\n=== 设置后台管理系统数据库 ===', 'yellow');
    executeCommand(`node ${databases.admin.scriptPath} setup`, '设置后台管理系统数据库');
    
    log('\n🎉 数据库设置完成！', 'green');
    log('\n📋 数据库信息:', 'cyan');
    log(`主站前台: ${databases.web.name} (端口: ${databases.web.port})`, 'cyan');
    log(`后台管理: ${databases.admin.name} (端口: ${databases.admin.port})`, 'cyan');
    
  } catch (error) {
    log(`\n❌ 设置失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const dbType = args[1];
  const extraArg = args[2];

  try {
    switch (command) {
      case 'create':
        if (!dbType || !databases[dbType]) {
          throw new Error('请指定有效的数据库类型: web 或 admin');
        }
        createDatabase(dbType);
        break;
        
      case 'drop':
        if (!dbType || !databases[dbType]) {
          throw new Error('请指定有效的数据库类型: web 或 admin');
        }
        dropDatabase(dbType);
        break;
        
      case 'init':
        if (!dbType || !databases[dbType]) {
          throw new Error('请指定有效的数据库类型: web 或 admin');
        }
        initDatabase(dbType);
        break;
        
      case 'backup':
        if (!dbType || !databases[dbType]) {
          throw new Error('请指定有效的数据库类型: web 或 admin');
        }
        backupDatabase(dbType);
        break;
        
      case 'restore':
        if (!dbType || !databases[dbType]) {
          throw new Error('请指定有效的数据库类型: web 或 admin');
        }
        if (!extraArg) {
          throw new Error('请指定备份文件路径');
        }
        restoreDatabase(dbType, extraArg);
        break;
        
      case 'status':
        if (!dbType || !databases[dbType]) {
          throw new Error('请指定有效的数据库类型: web 或 admin');
        }
        checkDatabaseStatus(dbType);
        break;
        
      case 'setup':
        fullSetup();
        break;
        
      case 'help':
      default:
        showHelp();
        break;
    }
  } catch (error) {
    log(`\n❌ 错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  createDatabase,
  dropDatabase,
  initDatabase,
  backupDatabase,
  restoreDatabase,
  checkDatabaseStatus,
  fullSetup
};