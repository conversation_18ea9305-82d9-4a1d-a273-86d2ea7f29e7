'use client';

import React, { useState, useEffect, useRef, FormEvent } from 'react';
import { toast } from 'react-hot-toast';
import socketService from '@/services/socketService';

interface Message {
  id: number;
  content: string;
  senderId: number;
  receiverId: number;
  createdAt: string;
  isRead: boolean;
  sender?: {
    id: number;
    username: string;
    avatar?: string;
  };
}

interface ChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  recipientId: number;
  recipientName: string;
  recipientAvatar?: string;
  currentUserId: number;
}

const ChatWindow = ({ 
  isOpen, 
  onClose, 
  recipientId, 
  recipientName, 
  recipientAvatar,
  currentUserId 
}: ChatWindowProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 加载聊天历史
  const loadMessages = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/messages/${recipientId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setMessages(data.data.messages || []);
        setTimeout(scrollToBottom, 100);
      }
    } catch (error) {
      console.error('加载消息失败:', error);
      toast.error('加载消息失败');
    } finally {
      setLoading(false);
    }
  };

  // 发送消息
  const sendMessage = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    const content = newMessage.trim();
    if (!content) return;

    try {
      const response = await fetch('/api/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          receiverId: recipientId,
          content
        })
      });

      const data = await response.json();
      if (data.success) {
        setNewMessage('');
        
        // 通过Socket发送实时消息
        socketService.emit('send_message', {
          receiverId: recipientId,
          content,
          messageId: data.data.message.id
        });

        // 立即添加到本地消息列表
        const newMsg: Message = {
          ...data.data.message,
          sender: {
            id: currentUserId,
            username: 'You',
            avatar: ''
          }
        };
        setMessages(prev => [...prev, newMsg]);
        setTimeout(scrollToBottom, 100);
      } else {
        toast.error(data.message || '发送失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      toast.error('发送失败，请重试');
    }
  };

  // 设置Socket事件监听
  useEffect(() => {
    if (!isOpen) return;

    // 监听新消息
    const handleNewMessage = (message: Message) => {
      if (message.senderId === recipientId || message.receiverId === recipientId) {
        setMessages(prev => {
          // 避免重复添加
          if (prev.some(m => m.id === message.id)) {
            return prev;
          }
          return [...prev, message];
        });
        setTimeout(scrollToBottom, 100);
      }
    };

    // 监听对方正在输入
    const handleUserTyping = (data: { userId: number; isTyping: boolean }) => {
      if (data.userId === recipientId) {
        setIsTyping(data.isTyping);
      }
    };

    socketService.on('new_message', handleNewMessage);
    socketService.on('user_typing', handleUserTyping);

    return () => {
      socketService.off('new_message', handleNewMessage);
      socketService.off('user_typing', handleUserTyping);
    };
  }, [isOpen, recipientId]);

  // 输入状态监听
  useEffect(() => {
    if (!isOpen) return;

    let typingTimer: NodeJS.Timeout;
    
    const handleInputChange = () => {
      socketService.emit('typing_start', { receiverId: recipientId });
      
      clearTimeout(typingTimer);
      typingTimer = setTimeout(() => {
        socketService.emit('typing_stop', { receiverId: recipientId });
      }, 1000);
    };

    const inputElement = inputRef.current;
    if (inputElement) {
      inputElement.addEventListener('input', handleInputChange);
      
      return () => {
        inputElement.removeEventListener('input', handleInputChange);
        clearTimeout(typingTimer);
        socketService.emit('typing_stop', { receiverId: recipientId });
      };
    }
  }, [isOpen, recipientId]);

  // 加载消息
  useEffect(() => {
    if (isOpen) {
      loadMessages();
      // 聚焦输入框
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, recipientId]);

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 w-80 h-96 bg-white rounded-lg shadow-xl border border-gray-200 flex flex-col z-50">
      {/* 聊天头部 */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {recipientAvatar ? (
              <img src={recipientAvatar} alt={recipientName} className="w-8 h-8 rounded-full" />
            ) : (
              recipientName.charAt(0).toUpperCase()
            )}
          </div>
          <div>
            <h3 className="font-medium text-gray-900">{recipientName}</h3>
            {isTyping && (
              <p className="text-xs text-blue-600">正在输入...</p>
            )}
          </div>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-600 p-1"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {loading ? (
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex justify-center items-center h-full text-gray-500 text-sm">
            开始对话吧！
          </div>
        ) : (
          messages.map((message) => {
            const isOwn = message.senderId === currentUserId;
            return (
              <div
                key={message.id}
                className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                    isOwn
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}
                >
                  <p>{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    isOwn ? 'text-blue-100' : 'text-gray-500'
                  }`}>
                    {new Date(message.createdAt).toLocaleTimeString('zh-CN', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入框 */}
      <form onSubmit={sendMessage} className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="输入消息..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            maxLength={500}
          />
          <button
            type="submit"
            disabled={!newMessage.trim()}
            className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default ChatWindow;