# Newzora Production Docker Compose Configuration
version: '3.8'

services:
  # PostgreSQL Database (Production)
  postgres:
    image: postgres:15-alpine
    container_name: newzora-postgres-prod
    restart: always
    environment:
      POSTGRES_DB: ${DB_NAME:-newzora_production}
      POSTGRES_USER: ${DB_USER:-newzora_user}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
      - ./Backend/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
      - ./config/postgresql/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    ports:
      - "5432:5432"
    networks:
      - newzora-network-prod
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER:-newzora_user} -d ${DB_NAME:-newzora_production}"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Redis Cache (Production)
  redis:
    image: redis:7-alpine
    container_name: newzora-redis-prod
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data_prod:/data
      - ./config/redis/redis.conf:/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - newzora-network-prod
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.25'

  # Backend API (Production)
  backend:
    build:
      context: ../Backend
      dockerfile: Dockerfile.prod
    container_name: newzora-backend-prod
    restart: always
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      DB_NAME: ${DB_NAME:-newzora_production}
      DB_USER: ${DB_USER:-newzora_user}
      DB_PASSWORD: ${DB_PASSWORD}
      REDIS_HOST: redis
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      EMAIL_HOST: ${EMAIL_HOST}
      EMAIL_USER: ${EMAIL_USER}
      EMAIL_PASS: ${EMAIL_PASS}
      FRONTEND_URL: ${FRONTEND_URL:-https://newzora.com}
    volumes:
      - backend_uploads_prod:/app/uploads
      - backend_logs_prod:/app/logs
    ports:
      - "5000:5000"
    networks:
      - newzora-network-prod
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Frontend (Production)
  frontend:
    build:
      context: ../Frontend
      dockerfile: Dockerfile.prod
      args:
        NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-https://api.newzora.com}
        NEXT_PUBLIC_FRONTEND_URL: ${NEXT_PUBLIC_FRONTEND_URL:-https://newzora.com}
    container_name: newzora-frontend-prod
    restart: always
    environment:
      NODE_ENV: production
      NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-https://api.newzora.com}
      NEXT_PUBLIC_FRONTEND_URL: ${NEXT_PUBLIC_FRONTEND_URL:-https://newzora.com}
    ports:
      - "3000:3000"
    networks:
      - newzora-network-prod
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # Nginx Reverse Proxy (Production)
  nginx:
    image: nginx:alpine
    container_name: newzora-nginx-prod
    restart: always
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs_prod:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - newzora-network-prod
    depends_on:
      - frontend
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'

# Networks
networks:
  newzora-network-prod:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: newzora-prod

# Volumes
volumes:
  postgres_data_prod:
    driver: local
  redis_data_prod:
    driver: local
  backend_uploads_prod:
    driver: local
  backend_logs_prod:
    driver: local
  nginx_logs_prod:
    driver: local
