'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import VideoPlayer from '@/components/VideoPlayer';
import CommentSection from '@/components/CommentSection';
import Avatar from '@/components/Avatar';
import RelatedVideoCard from '@/components/RelatedVideoCard';
import AdBanner from '@/components/AdBanner';
import SocialShare from '@/components/SocialShare';
import TipModal from '@/components/TipModal';
import ReportModal from '@/components/ReportModal';
import { followService } from '@/services/followService';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { Video, Comment } from '@/types';
import { mockWorks } from '@/data/mockWorks';

export default function VideoPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated, user } = useSimpleAuth();
  const { addNotification } = useNotifications();
  const [video, setVideo] = useState<Video | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isFollowing, setIsFollowing] = useState(false);
  const [showTipModal, setShowTipModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [relatedVideos, setRelatedVideos] = useState<Video[]>([]);
  const [showPrerollAd, setShowPrerollAd] = useState(true);
  const [adCountdown, setAdCountdown] = useState(3);
  const [isPictureInPicture, setIsPictureInPicture] = useState(false);
  const [videoPlayerRef, setVideoPlayerRef] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    if (params.id) {
      fetchVideo(params.id as string);
      fetchRelatedVideos(params.id as string);
    }
  }, [params.id]);
  
  // Handle scroll for picture-in-picture
  useEffect(() => {
    const handleScroll = () => {
      if (!videoPlayerRef) return;
      
      const rect = videoPlayerRef.getBoundingClientRect();
      const isVideoVisible = rect.top < window.innerHeight && rect.bottom > 0;
      
      // Auto-enable PiP when video scrolls out of view
      if (!isVideoVisible && !isPictureInPicture) {
        // Auto PiP logic can be implemented here
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [videoPlayerRef, isPictureInPicture]);
  
  // Preroll ad countdown
  useEffect(() => {
    if (showPrerollAd && adCountdown > 0) {
      const timer = setTimeout(() => {
        setAdCountdown(adCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [showPrerollAd, adCountdown]);

  const fetchVideo = async (id: string) => {
    try {
      setLoading(true);

      // 从mockWorks中获取视频数据
      const mockVideo = mockWorks.find((work) => 
        work.id.toString() === id && work.type === 'video'
      ) as Video;

      if (mockVideo) {
        setVideo(mockVideo);
        setLoading(false);
        return;
      }

      // 如果没有找到，显示404
      console.error('Video not found');
      setVideo(null);
    } catch (error) {
      console.error('Error fetching video:', error);
      setVideo(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedVideos = async (id: string) => {
    try {
      // 获取相关视频（同类型的其他视频）
      const related = mockWorks
        .filter((work) => work.type === 'video' && work.id.toString() !== id)
        .slice(0, 4) as Video[];
      setRelatedVideos(related);
    } catch (error) {
      console.error('Error fetching related videos:', error);
    }
  };

  const handleLike = () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    const newLikedState = !isLiked;
    setIsLiked(newLikedState);

    // 添加通知
    addNotification({
      type: 'success',
      title: newLikedState ? 'Video Liked' : 'Like Removed',
      message: newLikedState ? `You liked "${video?.title}"` : `You removed your like from "${video?.title}"`
    });
  };

  const handleSave = () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    const newSavedState = !isSaved;
    setIsSaved(newSavedState);

    // 添加通知
    addNotification({
      type: 'info',
      title: newSavedState ? 'Video Saved' : 'Video Removed',
      message: newSavedState ? `"${video?.title}" saved to your bookmarks` : `"${video?.title}" removed from bookmarks`
    });
  };



  const handleFollow = async () => {
    if (!isAuthenticated || !user || !video) {
      addNotification({
        type: 'warning',
        title: 'Login Required',
        message: 'Please log in to follow creators'
      });
      return;
    }

    if (typeof video.author === 'object' && user.id === video.author.id) {
      addNotification({
        type: 'info',
        title: 'Cannot Follow',
        message: 'You cannot follow yourself'
      });
      return;
    }

    try {
      const newFollowingState = !isFollowing;
      setIsFollowing(newFollowingState);
      
      addNotification({
        type: 'success',
        title: newFollowingState ? 'Following' : 'Unfollowed',
        message: newFollowingState 
          ? `You are now following ${typeof video.author === 'object' ? video.author.name : 'this creator'}`
          : `You unfollowed ${typeof video.author === 'object' ? video.author.name : 'this creator'}`
      });
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to update follow status'
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="aspect-video bg-gray-200 rounded-lg mb-6"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>
            <div className="flex gap-4 mb-6">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!video) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Video Not Found</h1>
          <p className="text-gray-600 mb-6">The video you're looking for doesn't exist.</p>
          <button
            onClick={() => router.back()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Breadcrumb Navigation */}
      <nav className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 py-6">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span>/</span>
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Videos
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">
            {video?.title
              ? video.title.length > 50
                ? video.title.substring(0, 50) + '...'
                : video.title
              : 'Loading...'}
          </span>
        </div>
      </nav>
      
      <div className="flex max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 pb-12 gap-12">
        <div className="flex-1 max-w-4xl">
          {/* Main Content */}
          <div className="w-full">
            {/* Video Player with Preroll Ad */}
            <div 
              ref={setVideoPlayerRef}
              className="aspect-video mb-6 relative"
            >
              {showPrerollAd ? (
                <div className="w-full h-full bg-black rounded-lg overflow-hidden relative">
                  {/* Preroll Ad Content */}
                  <img
                    src="https://via.placeholder.com/800x450/1F2937/FFFFFF?text=Advertisement"
                    alt="Advertisement"
                    className="w-full h-full object-cover"
                  />
                  
                  {/* Ad Overlay */}
                  <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                    <div className="text-center text-white">
                      <h3 className="text-xl font-bold mb-2">Premium Content Tools</h3>
                      <p className="text-sm mb-4">Upgrade your content creation experience</p>
                      <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Learn More
                      </button>
                    </div>
                  </div>
                  
                  {/* Skip Ad Button */}
                  <div className="absolute top-4 right-4">
                    {adCountdown > 0 ? (
                      <div className="bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm">
                        Ad ends in {adCountdown}s
                      </div>
                    ) : (
                      <button
                        onClick={() => setShowPrerollAd(false)}
                        className="bg-black bg-opacity-70 text-white px-3 py-1 rounded text-sm hover:bg-opacity-90 transition-colors"
                      >
                        Skip Ad
                      </button>
                    )}
                  </div>
                  
                  {/* Ad Label */}
                  <div className="absolute bottom-4 left-4">
                    <span className="bg-yellow-500 text-black px-2 py-1 rounded text-xs font-medium">
                      AD
                    </span>
                  </div>
                </div>
              ) : (
                <VideoPlayer
                  src={video.videoUrl}
                  poster={video.thumbnailUrl}
                  title={video.title}
                  className="w-full h-full"
                  controls={true}
                  enablePictureInPicture={true}
                  onPlay={() => console.log('Video started playing')}
                  onPause={() => console.log('Video paused')}
                  onEnded={() => console.log('Video ended')}
                  onPictureInPictureChange={setIsPictureInPicture}
                />
              )}
            </div>

            {/* Video Title */}
            <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">{video.title}</h1>

            {/* Author Info */}
            {typeof video.author === 'object' && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <img
                      src={video.author.avatar}
                      alt={video.author.name}
                      className="w-12 h-12 rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(video.author.name)}&background=6366f1&color=fff&size=48`;
                      }}
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">{video.author.name}</h3>
                      <p className="text-sm text-gray-500">
                        Published {video.publishedAt} • {Math.floor(video.duration / 60)}:{(video.duration % 60).toString().padStart(2, '0')} • {video.resolution.quality}
                      </p>
                    </div>
                  </div>
                  {isAuthenticated && user?.id !== video.author.id.toString() && (
                    <button
                      onClick={handleFollow}
                      className={`px-6 py-2 rounded-lg transition-colors font-medium ${
                        isFollowing
                          ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {isFollowing ? 'Following' : 'Follow'}
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Video Stats and Actions */}
            <div className="bg-white rounded-lg p-6 mb-6">

              {/* Interaction Stats */}
              <div className="mb-8 flex items-center justify-between">
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <span>👁️ {video.views.toLocaleString()} views</span>
                  <span>💬 Comments</span>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleLike}
                    className={`group flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 ${
                      isLiked
                        ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                        : 'bg-white border-gray-200 text-gray-600 hover:border-red-200 hover:text-red-600'
                    }`}
                  >
                    <svg className="w-4 h-4" fill={isLiked ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span className="text-sm font-medium">{video.likes.toLocaleString()}</span>
                  </button>

                  <button
                    onClick={handleSave}
                    className={`group flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 ${
                      isSaved
                        ? 'bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100'
                        : 'bg-white border-gray-200 text-gray-600 hover:border-blue-200 hover:text-blue-600'
                    }`}
                  >
                    <svg className="w-4 h-4" fill={isSaved ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                    <span className="text-sm font-medium">{isSaved ? 'Saved' : 'Save'}</span>
                  </button>

                  <SocialShare
                    title={video.title}
                    url={typeof window !== 'undefined' ? window.location.href : ''}
                    description={video.description}
                  />
                  
                  <button
                    onClick={() => setShowTipModal(true)}
                    className="group flex items-center space-x-2 px-4 py-2 rounded-full border border-amber-200 bg-amber-50 text-amber-600 hover:bg-amber-100 hover:border-amber-300 transition-all duration-200 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium">Tip</span>
                  </button>
                  
                  <button
                    onClick={() => setShowReportModal(true)}
                    className="group flex items-center space-x-2 px-4 py-2 rounded-full border border-gray-200 bg-white text-gray-500 hover:border-red-200 hover:text-red-500 transition-all duration-200 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span className="text-sm font-medium">Report</span>
                  </button>
                </div>
              </div>

              {/* Description */}
              {video.description && (
                <div className="border-t pt-4">
                  <p className="text-gray-700 leading-relaxed" translate="yes" lang="en">{video.description}</p>
                </div>
              )}
            </div>

            {/* Comments */}
            <CommentSection workId={video.id} workType="video" />
          </div>

        </div>
        
        {/* Right Sidebar */}
        <div className="w-80 hidden xl:block">
          <div className="sticky top-24 space-y-6">
            {/* Ad Banner */}
            <AdBanner position="sidebar" />
            
            {/* Related Videos */}
            <div className="bg-white rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Videos</h3>
              <div className="space-y-4">
                {relatedVideos.map((relatedVideo) => (
                  <RelatedVideoCard
                    key={relatedVideo.id}
                    video={relatedVideo}
                    onClick={() => router.push(`/video/${relatedVideo.id}`)}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Tip Modal */}
      <TipModal
        isOpen={showTipModal}
        onClose={() => setShowTipModal(false)}
        authorName={typeof video.author === 'object' ? video.author.name : 'Unknown'}
        contentTitle={video.title}
      />
      
      {/* Report Modal */}
      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        contentId={video.id}
        contentType="video"
        contentTitle={video.title}
      />
    </div>
  );
}
