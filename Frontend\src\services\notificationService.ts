import { supabase } from '@/lib/supabase';

export interface Notification {
    id: number;
    type: string;
    title: string;
    content: string;
    isRead: boolean;
    createdAt: string;
    articleId?: number;
    workId?: number;
    fromUser?: {
        id: number;
        username: string;
        display_name: string;
        avatar_url?: string;
    };
    work?: {
        id: number;
        title: string;
        type: string;
        image?: string;
    };
}

class NotificationService {
    private listeners: Set<(notifications: Notification[]) => void> = new Set();
    private unreadCountListeners: Set<(count: number) => void> = new Set();
    private currentUserId: number | null = null;
    private subscription: any = null;

    // 初始化实时监听
    initialize(userId: number) {
        this.currentUserId = userId;
        this.setupRealtimeSubscription();
    }

    // 清理监听
    cleanup() {
        if (this.subscription) {
            supabase.removeChannel(this.subscription);
            this.subscription = null;
        }
        this.currentUserId = null;
        this.listeners.clear();
        this.unreadCountListeners.clear();
    }

    // 设置实时订阅
    private setupRealtimeSubscription() {
        if (!this.currentUserId) return;

        this.subscription = supabase
            .channel('notifications')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'notifications',
                filter: `user_id=eq.${this.currentUserId}`
            }, (payload) => {
                console.log('Notification change:', payload);
                this.handleRealtimeUpdate(payload);
            })
            .subscribe();
    }

    // 处理实时更新
    private handleRealtimeUpdate(payload: any) {
        // 重新获取通知列表和未读数量
        this.fetchNotifications();
        this.fetchUnreadCount();
    }

    // 获取通知列表
    async fetchNotifications(): Promise<Notification[]> {
        if (!this.currentUserId) return [];

        try {
            // 首先尝试从Supabase获取
            const { data: notifications, error } = await supabase
                .from('notifications')
                .select(`
          id,
          type,
          title,
          content,
          is_read,
          created_at,
          work_id,
          article_id,
          from_user_id,
          works:work_id (
            id,
            title,
            type,
            thumbnail_url
          ),
          articles:article_id (
            id,
            title,
            thumbnail_url
          ),
          from_user:from_user_id (
            id,
            username,
            display_name,
            avatar_url
          )
        `)
                .eq('user_id', this.currentUserId)
                .order('created_at', { ascending: false })
                .limit(20);

            let formattedNotifications: Notification[] = [];

            if (error) {
                console.error('获取通知失败:', error);
                // 使用模拟数据作为降级
                formattedNotifications = this.getMockNotifications();
            } else {
                // 转换数据格式
                formattedNotifications = notifications?.map(notif => ({
                    id: notif.id,
                    type: notif.type,
                    title: notif.title,
                    content: notif.content,
                    isRead: notif.is_read,
                    createdAt: notif.created_at,
                    workId: notif.work_id,
                    articleId: notif.article_id,
                    work: notif.works ? {
                        id: notif.works.id,
                        title: notif.works.title,
                        type: notif.works.type,
                        image: notif.works.thumbnail_url
                    } : notif.articles ? {
                        id: notif.articles.id,
                        title: notif.articles.title,
                        type: 'article',
                        image: notif.articles.thumbnail_url
                    } : undefined,
                    fromUser: notif.from_user ? {
                        id: notif.from_user.id,
                        username: notif.from_user.username,
                        display_name: notif.from_user.display_name,
                        avatar_url: notif.from_user.avatar_url
                    } : undefined
                })) || [];
            }

            // 通知所有监听器
            this.listeners.forEach(listener => listener(formattedNotifications));

            return formattedNotifications;
        } catch (error) {
            console.error('获取通知失败:', error);
            const mockNotifications = this.getMockNotifications();
            this.listeners.forEach(listener => listener(mockNotifications));
            return mockNotifications;
        }
    }

    // 获取未读数量
    async fetchUnreadCount(): Promise<number> {
        if (!this.currentUserId) return 0;

        try {
            const { count, error } = await supabase
                .from('notifications')
                .select('*', { count: 'exact', head: true })
                .eq('user_id', this.currentUserId)
                .eq('is_read', false);

            const unreadCount = error ? 2 : (count || 0); // 降级到模拟数据

            // 通知所有监听器
            this.unreadCountListeners.forEach(listener => listener(unreadCount));

            return unreadCount;
        } catch (error) {
            console.error('获取未读数量失败:', error);
            const mockCount = 2;
            this.unreadCountListeners.forEach(listener => listener(mockCount));
            return mockCount;
        }
    }

    // 标记单个通知为已读
    async markAsRead(notificationId: number): Promise<boolean> {
        try {
            const token = localStorage.getItem('token');
            if (!token) return false;

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/notifications/${notificationId}/read`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (response.ok) {
                // 重新获取数据
                this.fetchNotifications();
                this.fetchUnreadCount();
                return true;
            }
            return false;
        } catch (error) {
            console.error('标记已读失败:', error);
            return false;
        }
    }

    // 标记所有通知为已读
    async markAllAsRead(): Promise<boolean> {
        try {
            const token = localStorage.getItem('token');
            if (!token) return false;

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/notifications/read-all`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${token}`,
                },
            });

            if (response.ok) {
                // 重新获取数据
                this.fetchNotifications();
                this.fetchUnreadCount();
                return true;
            }
            return false;
        } catch (error) {
            console.error('标记所有已读失败:', error);
            return false;
        }
    }

    // 添加通知监听器
    addNotificationListener(listener: (notifications: Notification[]) => void) {
        this.listeners.add(listener);
    }

    // 移除通知监听器
    removeNotificationListener(listener: (notifications: Notification[]) => void) {
        this.listeners.delete(listener);
    }

    // 添加未读数量监听器
    addUnreadCountListener(listener: (count: number) => void) {
        this.unreadCountListeners.add(listener);
    }

    // 移除未读数量监听器
    removeUnreadCountListener(listener: (count: number) => void) {
        this.unreadCountListeners.delete(listener);
    }

    // 获取模拟通知数据
    private getMockNotifications(): Notification[] {
        return [
            {
                id: 1,
                type: 'like',
                title: '新的点赞',
                content: 'John 点赞了你的文章',
                isRead: false,
                createdAt: new Date(Date.now() - 3600000).toISOString(),
                workId: 1,
                work: {
                    id: 1,
                    title: 'JavaScript 最佳实践指南',
                    type: 'article',
                    image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop'
                },
                fromUser: {
                    id: 2,
                    username: 'john_dev',
                    display_name: 'John Developer',
                    avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face'
                }
            },
            {
                id: 2,
                type: 'comment',
                title: '新的评论',
                content: 'Sarah 评论了你的视频',
                isRead: false,
                createdAt: new Date(Date.now() - 7200000).toISOString(),
                workId: 2,
                work: {
                    id: 2,
                    title: 'React Hooks 深度解析',
                    type: 'video',
                    image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop'
                },
                fromUser: {
                    id: 3,
                    username: 'sarah_ui',
                    display_name: 'Sarah Designer',
                    avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face'
                }
            },
            {
                id: 3,
                type: 'follow',
                title: '新的关注者',
                content: 'Mike 开始关注你',
                isRead: true,
                createdAt: new Date(Date.now() - 86400000).toISOString(),
                fromUser: {
                    id: 4,
                    username: 'mike_code',
                    display_name: 'Mike Coder',
                    avatar_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face'
                }
            }
        ];
    }

    // 创建新通知（用于测试）
    async createNotification(notification: Partial<Notification>): Promise<boolean> {
        if (!this.currentUserId) return false;

        try {
            const { error } = await supabase
                .from('notifications')
                .insert({
                    user_id: this.currentUserId,
                    type: notification.type || 'system',
                    title: notification.title || '新通知',
                    content: notification.content || '',
                    work_id: notification.workId,
                    article_id: notification.articleId,
                    from_user_id: notification.fromUser?.id,
                    is_read: false
                });

            if (error) {
                console.error('创建通知失败:', error);
                return false;
            }

            return true;
        } catch (error) {
            console.error('创建通知失败:', error);
            return false;
        }
    }
}

export const notificationService = new NotificationService();