'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { AdminRole } from '@/types/admin';
import api from '@/lib/api';

interface User {
  id: string;
  username: string;
  email: string;
  display_name: string;
  role: AdminRole;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);

  // 初始化时检查localStorage中是否有token
  useEffect(() => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      // 这里可以添加验证token有效性的逻辑
      // 暂时简单认为有token就是已登录状态
      // 实际项目中应该调用API验证token有效性
      const savedUser = localStorage.getItem('admin_user');
      if (savedUser) {
        try {
          setUser(JSON.parse(savedUser));
        } catch (error) {
          console.error('解析用户信息失败:', error);
          localStorage.removeItem('admin_token');
          localStorage.removeItem('admin_user');
        }
      }
    }
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const response = await api.post('/auth/login', { email, password });
      const { token, user } = response.data;

      // 保存token到localStorage
      localStorage.setItem('admin_token', token);
      localStorage.setItem('admin_user', JSON.stringify(user));

      // 设置用户状态
      setUser(user);
    } catch (error: any) {
      console.error('登录失败:', error);
      throw new Error(error.response?.data?.message || '登录失败，请检查邮箱和密码');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // 可以调用后端的logout API（如果有的话）
      // await api.post('/auth/logout');
    } catch (error) {
      console.error('登出时发生错误:', error);
    } finally {
      // 清除本地存储的token和用户信息
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      setUser(null);
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth必须在AuthProvider内使用');
  }
  return context;
};