'use client';

import React, { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';

export interface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  description?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'danger';
  labelPosition?: 'left' | 'right';
}

const Switch = forwardRef<HTMLInputElement, SwitchProps>(
  (
    {
      className,
      label,
      description,
      size = 'md',
      variant = 'default',
      labelPosition = 'right',
      disabled,
      checked,
      onChange,
      ...props
    },
    ref
  ) => {
    const [isChecked, setIsChecked] = useState(checked || false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setIsChecked(e.target.checked);
      onChange?.(e);
    };

    // 尺寸配置
    const sizeConfig = {
      sm: {
        track: 'w-8 h-4',
        thumb: 'w-3 h-3',
        translate: 'translate-x-4',
        text: 'text-sm',
      },
      md: {
        track: 'w-11 h-6',
        thumb: 'w-5 h-5',
        translate: 'translate-x-5',
        text: 'text-base',
      },
      lg: {
        track: 'w-14 h-7',
        thumb: 'w-6 h-6',
        translate: 'translate-x-7',
        text: 'text-lg',
      },
    };

    // 变体样式
    const variantStyles = {
      default: {
        track: 'bg-neutral-300 checked:bg-primary',
        thumb: 'bg-white',
      },
      success: {
        track: 'bg-neutral-300 checked:bg-green-500',
        thumb: 'bg-white',
      },
      warning: {
        track: 'bg-neutral-300 checked:bg-yellow-500',
        thumb: 'bg-white',
      },
      danger: {
        track: 'bg-neutral-300 checked:bg-red-500',
        thumb: 'bg-white',
      },
    };

    const currentSize = sizeConfig[size];
    const currentVariant = variantStyles[variant];
    const currentChecked = checked !== undefined ? checked : isChecked;

    const trackStyles = cn(
      'relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out',
      'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary/50',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      'cursor-pointer',
      currentSize.track,
      currentVariant.track,
      className
    );

    const thumbStyles = cn(
      'inline-block rounded-full shadow-lg transform transition-transform duration-200 ease-in-out',
      'ring-0',
      currentSize.thumb,
      currentVariant.thumb,
      currentChecked ? currentSize.translate : 'translate-x-0.5'
    );

    const SwitchComponent = (
      <div className="relative">
        <input
          ref={ref}
          type="checkbox"
          className="sr-only"
          disabled={disabled}
          checked={currentChecked}
          onChange={handleChange}
          {...props}
        />
        <div className={trackStyles}>
          <span className={thumbStyles} />
        </div>
      </div>
    );

    const LabelComponent = (label || description) && (
      <div className="flex flex-col">
        {label && (
          <span
            className={cn(
              'font-medium transition-colors',
              currentSize.text,
              disabled ? 'text-text-muted' : 'text-text-primary'
            )}
          >
            {label}
          </span>
        )}
        {description && (
          <span className={cn('text-text-muted', size === 'sm' ? 'text-xs' : 'text-sm')}>
            {description}
          </span>
        )}
      </div>
    );

    if (!label && !description) {
      return SwitchComponent;
    }

    return (
      <div
        className={cn(
          'flex items-center gap-3',
          labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row'
        )}
      >
        {SwitchComponent}
        {LabelComponent}
      </div>
    );
  }
);

Switch.displayName = 'Switch';

export { Switch };
