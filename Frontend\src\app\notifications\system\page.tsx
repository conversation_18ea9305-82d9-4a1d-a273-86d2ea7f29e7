'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import LoadingState from '@/components/ui/LoadingState';
import ErrorAlert from '@/components/ui/ErrorAlert';

interface SystemMessage {
  id: number;
  type: 'announcement' | 'maintenance' | 'update' | 'policy' | 'security';
  title: string;
  content: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  isRead: boolean;
  createdAt: string;
  updatedAt?: string;
  imageUrl?: string;
  actionUrl?: string;
}

const MESSAGE_TYPES = {
  announcement: { icon: '📢', color: 'text-blue-500', bgColor: 'bg-blue-50', label: '公告' },
  maintenance: { icon: '🔧', color: 'text-orange-500', bgColor: 'bg-orange-50', label: '维护' },
  update: { icon: '🆕', color: 'text-green-500', bgColor: 'bg-green-50', label: '更新' },
  policy: { icon: '📋', color: 'text-purple-500', bgColor: 'bg-purple-50', label: '政策' },
  security: { icon: '🔒', color: 'text-red-500', bgColor: 'bg-red-50', label: '安全' }
};

const mockSystemMessages: SystemMessage[] = [
  {
    id: 1,
    type: 'announcement',
    title: '🎉 Newzora 2.0 正式发布！',
    content: '我们很高兴地宣布 Newzora 2.0 正式发布！新版本带来了全新的用户界面、更强大的内容编辑器、实时协作功能以及更好的性能表现。感谢所有用户的支持和反馈，让我们能够不断改进平台。立即体验新功能，开启更精彩的创作之旅！',
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - 3600000).toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=400&h=200&fit=crop',
    actionUrl: '/explore'
  },
  {
    id: 2,
    type: 'maintenance',
    title: '系统维护通知',
    content: '为了提供更好的服务体验，我们将于今晚 23:00-24:00 进行系统维护升级。维护期间可能会出现短暂的服务中断，请提前保存您的工作内容。维护完成后，系统将自动恢复正常运行。给您带来的不便，我们深表歉意。',
    priority: 'urgent',
    isRead: false,
    createdAt: new Date(Date.now() - 7200000).toISOString()
  },
  {
    id: 3,
    type: 'update',
    title: '新增 AI 内容助手功能',
    content: '我们推出了全新的 AI 内容助手功能！现在您可以使用 AI 来帮助生成创意想法、优化文章结构、检查语法错误，甚至生成吸引人的标题。这个功能将大大提升您的创作效率。在编辑器中点击 AI 助手按钮即可开始使用。',
    priority: 'normal',
    isRead: true,
    createdAt: new Date(Date.now() - 86400000).toISOString(),
    imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=200&fit=crop'
  },
  {
    id: 4,
    type: 'policy',
    title: '社区准则更新',
    content: '我们更新了社区准则，以创建更加友好和包容的创作环境。新准则包括对骚扰行为的零容忍政策、更明确的内容质量标准，以及改进的举报和申诉流程。请花几分钟时间阅读新准则，确保您的内容符合我们的社区标准。',
    priority: 'normal',
    isRead: true,
    createdAt: new Date(Date.now() - 172800000).toISOString(),
    actionUrl: '/rules'
  },
  {
    id: 5,
    type: 'security',
    title: '账户安全提醒',
    content: '我们检测到一些账户存在安全风险。为了保护您的账户安全，建议您立即更新密码，启用两步验证，并检查最近的登录活动。如果发现任何可疑活动，请立即联系我们的客服团队。您的账户安全是我们的首要任务。',
    priority: 'high',
    isRead: false,
    createdAt: new Date(Date.now() - *********).toISOString(),
    actionUrl: '/settings/account'
  }
];

export default function SystemMessagesPage() {
  const router = useRouter();
  const [messages, setMessages] = useState<SystemMessage[]>([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'announcement' | 'maintenance' | 'update'>('all');
  const [unreadCount, setUnreadCount] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const loadSystemMessages = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 500));
      setMessages(mockSystemMessages);
      const unread = mockSystemMessages.filter(m => !m.isRead).length;
      setUnreadCount(unread);
    } catch (error) {
      console.error('加载系统消息失败:', error);
      setError('加载系统消息失败');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (messageId: number) => {
    try {
      setMessages(prev => 
        prev.map(m => 
          m.id === messageId 
            ? { ...m, isRead: true }
            : m
        )
      );
      const newUnreadCount = unreadCount - 1;
      setUnreadCount(Math.max(0, newUnreadCount));
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  const handleMessageClick = (message: SystemMessage) => {
    if (!message.isRead) {
      markAsRead(message.id);
    }
    if (message.actionUrl) {
      router.push(message.actionUrl);
    }
  };

  const filterMessages = (messages: SystemMessage[]) => {
    switch (filter) {
      case 'unread':
        return messages.filter(m => !m.isRead);
      case 'announcement':
        return messages.filter(m => m.type === 'announcement');
      case 'maintenance':
        return messages.filter(m => m.type === 'maintenance');
      case 'update':
        return messages.filter(m => m.type === 'update');
      default:
        return messages;
    }
  };

  const formatTime = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  useEffect(() => {
    loadSystemMessages();
  }, [filter]);

  const filteredMessages = filterMessages(messages);

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">📢 平台消息</h1>
            <p className="text-gray-600 mt-1">
              {unreadCount > 0 ? `你有 ${unreadCount} 条未读消息` : '暂无未读消息'}
            </p>
          </div>
        </div>

        <div className="flex border-b border-gray-200 mb-6">
          {[
            { key: 'all', label: '全部' },
            { key: 'unread', label: '未读' },
            { key: 'announcement', label: '公告' },
            { key: 'maintenance', label: '维护' },
            { key: 'update', label: '更新' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`px-4 py-3 text-sm font-medium relative ${
                filter === tab.key
                  ? 'text-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
              {filter === tab.key && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-blue-600"></div>
              )}
            </button>
          ))}
        </div>

        {loading && (
          <div className="flex justify-center items-center py-12">
            <LoadingState isLoading={true} loadingText="Loading system messages..." size="md" />
          </div>
        )}

        <ErrorAlert 
          error={error}
          onDismiss={() => setError(null)}
          onRetry={() => loadSystemMessages()}
          className="mb-6"
        />

        {!loading && !error && (
          <div className="divide-y divide-gray-200">
            {filteredMessages.map((message) => {
              const messageType = MESSAGE_TYPES[message.type] || MESSAGE_TYPES.announcement;
              
              return (
                <div
                  key={message.id}
                  onClick={() => handleMessageClick(message)}
                  className={`p-6 hover:bg-gray-50 cursor-pointer transition-colors ${
                    !message.isRead ? 'bg-blue-50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-12 h-12 rounded-full ${messageType.bgColor} flex items-center justify-center`}>
                      <span className="text-lg">{messageType.icon}</span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <p className={`text-lg font-semibold ${!message.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                            {message.title}
                          </p>
                          <span className="ml-3 text-xs px-2 py-1 bg-gray-100 text-gray-600 rounded-full">
                            {messageType.label}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {!message.isRead && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                          {(message.priority === 'high' || message.priority === 'urgent') && (
                            <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                              重要
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {message.imageUrl && (
                        <div className="mb-3">
                          <img
                            src={message.imageUrl}
                            alt={message.title}
                            className="w-full max-w-md h-32 object-cover rounded-lg"
                          />
                        </div>
                      )}
                      
                      <p className="text-gray-700 leading-relaxed mb-3">{message.content}</p>
                      
                      <div className="flex items-center justify-between">
                        <p className="text-sm text-gray-500">
                          {formatTime(message.createdAt)}
                        </p>
                        {message.actionUrl && (
                          <span className="text-sm text-blue-600 font-medium">
                            点击查看详情 →
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {!loading && !error && filteredMessages.length === 0 && (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h10v-1a3 3 0 00-3-3H7a3 3 0 00-3 3v1zM6 10a3 3 0 116 0 3 3 0 01-6 0z" />
              </svg>
            </div>
            <p className="text-lg font-medium text-gray-900 mb-2">暂无系统消息</p>
            <p className="text-sm text-gray-500">这里会显示平台的重要通知和公告</p>
          </div>
        )}
      </main>
    </div>
  );
}