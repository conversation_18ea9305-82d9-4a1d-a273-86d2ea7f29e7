'use client';

import React, { forwardRef, ReactNode } from 'react';
import { cn } from '@/lib/utils';

export interface ButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  orientation?: 'horizontal' | 'vertical';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'outlined' | 'ghost';
  fullWidth?: boolean;
  children: ReactNode;
}

const ButtonGroup = forwardRef<HTMLDivElement, ButtonGroupProps>(
  (
    {
      className,
      orientation = 'horizontal',
      size = 'md',
      variant = 'default',
      fullWidth = false,
      children,
      ...props
    },
    ref
  ) => {
    // 基础样式
    const baseStyles = ['inline-flex', fullWidth && 'w-full'];

    // 方向样式
    const orientationStyles = {
      horizontal: 'flex-row',
      vertical: 'flex-col',
    };

    // 变体样式
    const variantStyles = {
      default: [
        '[&>*:not(:first-child)]:border-l-0',
        '[&>*:first-child]:rounded-r-none',
        '[&>*:last-child]:rounded-l-none',
        '[&>*:not(:first-child):not(:last-child)]:rounded-none',
      ],
      outlined: [
        '[&>*]:border-2',
        '[&>*:not(:first-child)]:border-l-0',
        '[&>*:first-child]:rounded-r-none',
        '[&>*:last-child]:rounded-l-none',
        '[&>*:not(:first-child):not(:last-child)]:rounded-none',
      ],
      ghost: ['[&>*]:bg-transparent', '[&>*:hover]:bg-surface-2'],
    };

    const buttonGroupClasses = cn(
      baseStyles,
      orientationStyles[orientation],
      variantStyles[variant],
      className
    );

    return (
      <div ref={ref} className={buttonGroupClasses} role="group" {...props}>
        {children}
      </div>
    );
  }
);

ButtonGroup.displayName = 'ButtonGroup';

export { ButtonGroup };
