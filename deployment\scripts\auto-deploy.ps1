# Newzora 自动部署脚本
# 检查环境并自动执行部署

param(
    [switch]$Force,
    [switch]$SkipChecks,
    [switch]$DockerOnly,
    [switch]$LocalOnly
)

# 颜色输出函数
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }
function Write-Header { Write-ColorOutput Magenta $args }

# 全局变量
$script:DeploymentSuccess = $false
$script:DeploymentMethod = ""
$script:ServicesStarted = @()

Write-Header "🚀 Newzora 自动部署脚本"
Write-Header "=================================="
Write-Info "开始检查部署环境..."

# 环境检查函数
function Test-NodeJS {
    try {
        $nodeVersion = node --version 2>$null
        if ($nodeVersion) {
            Write-Success "✅ Node.js: $nodeVersion"
            return $true
        }
    } catch {}
    Write-Warning "❌ Node.js 未安装或不在PATH中"
    return $false
}

function Test-NPM {
    try {
        $npmVersion = npm --version 2>$null
        if ($npmVersion) {
            Write-Success "✅ npm: v$npmVersion"
            return $true
        }
    } catch {}
    Write-Warning "❌ npm 未安装或不在PATH中"
    return $false
}

function Test-Docker {
    try {
        $dockerVersion = docker --version 2>$null
        if ($dockerVersion) {
            Write-Success "✅ Docker: $dockerVersion"
            # 检查Docker是否运行
            $dockerInfo = docker info 2>$null
            if ($dockerInfo) {
                Write-Success "✅ Docker 服务正在运行"
                return $true
            } else {
                Write-Warning "⚠️ Docker 已安装但服务未运行"
                return $false
            }
        }
    } catch {}
    Write-Warning "❌ Docker 未安装或不在PATH中"
    return $false
}

function Test-PostgreSQL {
    try {
        # 检查PostgreSQL服务
        $pgService = Get-Service -Name "postgresql*" -ErrorAction SilentlyContinue
        if ($pgService) {
            if ($pgService.Status -eq "Running") {
                Write-Success "✅ PostgreSQL 服务正在运行"
                return $true
            } else {
                Write-Warning "⚠️ PostgreSQL 服务已安装但未运行"
                # 尝试启动服务
                try {
                    Start-Service $pgService.Name
                    Write-Success "✅ PostgreSQL 服务已启动"
                    return $true
                } catch {
                    Write-Warning "❌ 无法启动PostgreSQL服务: $($_.Exception.Message)"
                    return $false
                }
            }
        }
    } catch {}
    Write-Warning "❌ PostgreSQL 服务未找到"
    return $false
}

function Test-Ports {
    $ports = @(3000, 5000, 5432)
    $portsAvailable = $true
    
    foreach ($port in $ports) {
        try {
            $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
            if ($connection.TcpTestSucceeded) {
                Write-Warning "⚠️ 端口 $port 已被占用"
                $portsAvailable = $false
            } else {
                Write-Success "✅ 端口 $port 可用"
            }
        } catch {
            Write-Success "✅ 端口 $port 可用"
        }
    }
    
    return $portsAvailable
}

# 部署方法选择
function Select-DeploymentMethod {
    Write-Info "🔍 分析最佳部署方法..."
    
    $hasDocker = Test-Docker
    $hasNode = Test-NodeJS
    $hasNPM = Test-NPM
    $hasPostgreSQL = Test-PostgreSQL
    
    if ($DockerOnly) {
        if ($hasDocker) {
            return "docker"
        } else {
            Write-Error "❌ Docker部署被指定但Docker不可用"
            return $null
        }
    }
    
    if ($LocalOnly) {
        if ($hasNode -and $hasNPM) {
            return "local"
        } else {
            Write-Error "❌ 本地部署被指定但Node.js环境不可用"
            return $null
        }
    }
    
    # 自动选择最佳方法
    if ($hasDocker) {
        Write-Success "🐳 推荐使用Docker部署 (最可靠)"
        return "docker"
    } elseif ($hasNode -and $hasNPM) {
        Write-Success "🔧 使用本地环境部署"
        return "local"
    } else {
        Write-Error "❌ 无可用的部署环境"
        Write-Info "💡 请安装以下软件之一:"
        Write-Info "   - Docker Desktop (推荐)"
        Write-Info "   - Node.js 18+ 和 npm"
        return $null
    }
}

# Docker部署
function Deploy-WithDocker {
    Write-Header "🐳 开始Docker部署..."
    
    try {
        # 检查docker-compose文件
        $composeFile = "config\docker-compose.yml"
        if (-not (Test-Path $composeFile)) {
            Write-Error "❌ Docker Compose文件不存在: $composeFile"
            return $false
        }
        
        # 停止现有容器
        Write-Info "🛑 停止现有容器..."
        docker-compose -f $composeFile down 2>$null
        
        # 构建并启动服务
        Write-Info "🔨 构建并启动服务..."
        $result = docker-compose -f $composeFile up -d --build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "✅ Docker服务启动成功"
            
            # 等待服务启动
            Write-Info "⏳ 等待服务启动..."
            Start-Sleep -Seconds 30
            
            # 检查服务状态
            $containers = docker-compose -f $composeFile ps
            Write-Info "📊 容器状态:"
            Write-Output $containers
            
            $script:ServicesStarted = @("Docker Containers")
            return $true
        } else {
            Write-Error "❌ Docker部署失败"
            return $false
        }
    } catch {
        Write-Error "❌ Docker部署异常: $($_.Exception.Message)"
        return $false
    }
}

# 本地环境部署
function Deploy-WithLocal {
    Write-Header "🔧 开始本地环境部署..."
    
    try {
        # 检查并安装依赖
        Write-Info "📦 检查并安装依赖..."
        
        # 安装根目录依赖
        if (Test-Path "package.json") {
            Write-Info "安装根目录依赖..."
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Error "❌ 根目录依赖安装失败"
                return $false
            }
        }
        
        # 安装后端依赖
        if (Test-Path "Backend\package.json") {
            Write-Info "安装后端依赖..."
            Set-Location "Backend"
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Error "❌ 后端依赖安装失败"
                Set-Location ".."
                return $false
            }
            Set-Location ".."
        }
        
        # 安装前端依赖
        if (Test-Path "Frontend\package.json") {
            Write-Info "安装前端依赖..."
            Set-Location "Frontend"
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Error "❌ 前端依赖安装失败"
                Set-Location ".."
                return $false
            }
            Set-Location ".."
        }
        
        Write-Success "✅ 依赖安装完成"
        
        # 启动数据库（如果需要）
        $hasPostgreSQL = Test-PostgreSQL
        if (-not $hasPostgreSQL) {
            Write-Warning "⚠️ PostgreSQL未运行，尝试启动..."
            # 这里可以添加启动PostgreSQL的逻辑
        }
        
        # 启动服务
        Write-Info "🚀 启动开发服务..."
        
        # 使用并发启动
        if (Test-Path "package.json") {
            $startJob = Start-Job -ScriptBlock {
                Set-Location $using:PWD
                npm run dev
            }
            
            Write-Success "✅ 开发服务启动中..."
            Write-Info "⏳ 等待服务启动..."
            Start-Sleep -Seconds 15
            
            $script:ServicesStarted = @("Frontend (3000)", "Backend (5000)")
            return $true
        } else {
            Write-Error "❌ 找不到package.json文件"
            return $false
        }
        
    } catch {
        Write-Error "❌ 本地部署异常: $($_.Exception.Message)"
        return $false
    }
}

# 验证部署
function Test-Deployment {
    Write-Header "🔍 验证部署状态..."
    
    $frontendOK = $false
    $backendOK = $false
    
    # 检查前端
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "✅ 前端服务 (http://localhost:3000) 正常"
            $frontendOK = $true
        }
    } catch {
        Write-Warning "❌ 前端服务不可访问"
    }
    
    # 检查后端
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:5000/api/health" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "✅ 后端API (http://localhost:5000) 正常"
            $backendOK = $true
        }
    } catch {
        Write-Warning "❌ 后端API不可访问"
    }
    
    return ($frontendOK -and $backendOK)
}

# 主部署流程
function Start-Deployment {
    Write-Header "🚀 开始Newzora平台部署..."
    
    # 环境检查
    if (-not $SkipChecks) {
        Write-Info "🔍 环境检查..."
        Test-NodeJS | Out-Null
        Test-NPM | Out-Null
        Test-Docker | Out-Null
        Test-PostgreSQL | Out-Null
        Test-Ports | Out-Null
    }
    
    # 选择部署方法
    $method = Select-DeploymentMethod
    if (-not $method) {
        Write-Error "❌ 无法确定部署方法"
        return $false
    }
    
    $script:DeploymentMethod = $method
    
    # 执行部署
    $success = $false
    switch ($method) {
        "docker" {
            $success = Deploy-WithDocker
        }
        "local" {
            $success = Deploy-WithLocal
        }
    }
    
    if ($success) {
        Write-Success "✅ 部署完成"
        
        # 验证部署
        Write-Info "⏳ 等待服务完全启动..."
        Start-Sleep -Seconds 10
        
        $verified = Test-Deployment
        if ($verified) {
            Write-Success "🎉 部署验证成功！"
            Write-Info "🌐 访问地址:"
            Write-Info "   前端: http://localhost:3000"
            Write-Info "   后端: http://localhost:5000"
            $script:DeploymentSuccess = $true
        } else {
            Write-Warning "⚠️ 部署完成但验证失败，请检查服务状态"
        }
    } else {
        Write-Error "❌ 部署失败"
    }
    
    return $success
}

# 显示部署结果
function Show-DeploymentResult {
    Write-Header "📊 部署结果报告"
    Write-Header "=================="
    
    if ($script:DeploymentSuccess) {
        Write-Success "🎉 Newzora平台部署成功！"
        Write-Info "部署方法: $($script:DeploymentMethod)"
        Write-Info "启动的服务: $($script:ServicesStarted -join ', ')"
        Write-Info ""
        Write-Info "🌐 访问地址:"
        Write-Info "   前端应用: http://localhost:3000"
        Write-Info "   后端API: http://localhost:5000"
        Write-Info "   API文档: http://localhost:5000/api/health"
        Write-Info ""
        Write-Info "🔧 管理命令:"
        Write-Info "   查看状态: .\manage.ps1 status"
        Write-Info "   停止服务: .\manage.ps1 stop"
        Write-Info "   重启服务: .\manage.ps1 restart"
    } else {
        Write-Error "❌ 部署失败"
        Write-Info "💡 建议:"
        Write-Info "   1. 检查错误日志"
        Write-Info "   2. 确保所需软件已安装"
        Write-Info "   3. 检查端口是否被占用"
        Write-Info "   4. 尝试手动部署步骤"
    }
}

# 执行部署
try {
    $deploymentResult = Start-Deployment
    Show-DeploymentResult
} catch {
    Write-Error "❌ 部署过程中发生异常: $($_.Exception.Message)"
    Write-Info "💡 请检查错误信息并重试"
}

Write-Info ""
Write-Info "📝 部署日志已保存"
Write-Info "🔧 如需帮助，请查看 README.md 或联系技术支持"
