'use client';

import React, { useState } from 'react';
import Link from 'next/link';

interface AuthPromptProps {
  title?: string;
  message?: string;
  showLoginButton?: boolean;
  showRegisterButton?: boolean;
}

export default function AuthPrompt({
  title = "Authentication Required",
  message = "Please sign in to access this content.",
  showLoginButton = true,
  showRegisterButton = true
}: AuthPromptProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">{title}</h2>
          <p className="text-gray-600">{message}</p>
        </div>

        <div className="space-y-3">
          {showLoginButton && (
            <Link
              href="/auth/login"
              className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium block"
            >
              Sign In
            </Link>
          )}
          
          {showRegisterButton && (
            <Link
              href="/auth/register"
              className="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-200 transition-colors font-medium block"
            >
              Create Account
            </Link>
          )}
          
          <Link
            href="/"
            className="w-full text-gray-500 py-2 px-4 rounded-lg hover:text-gray-700 transition-colors block"
          >
            Back to Home
          </Link>
        </div>
      </div>
    </div>
  );
}

// Hook for managing auth prompt state
export function useAuthPrompt() {
  const [isOpen, setIsOpen] = useState(false);
  const [action, setAction] = useState<string>('');

  const showPrompt = (actionType: string = '') => {
    setAction(actionType);
    setIsOpen(true);
  };

  const hidePrompt = () => {
    setIsOpen(false);
    setAction('');
  };

  return {
    isOpen,
    action,
    showPrompt,
    hidePrompt
  };
}
