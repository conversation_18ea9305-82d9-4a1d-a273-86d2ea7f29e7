const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const AdminLog = sequelize.define('AdminLog', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  admin_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  action: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '操作类型，如：create_user, update_article, delete_comment'
  },
  target_type: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: '操作目标类型，如：user, article, comment'
  },
  target_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '操作目标ID'
  },
  details: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: '操作详情，包含请求参数、响应等信息'
  },
  ip_address: {
    type: DataTypes.INET,
    allowNull: true
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  }
}, {
  tableName: 'admin_logs',
  timestamps: false,
  indexes: [
    {
      fields: ['admin_id']
    },
    {
      fields: ['created_at']
    },
    {
      fields: ['action']
    },
    {
      fields: ['target_type', 'target_id']
    }
  ]
});

module.exports = AdminLog;