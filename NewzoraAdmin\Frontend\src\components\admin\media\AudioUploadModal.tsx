'use client';

import React, { useState, useRef } from 'react';
import { X, Upload, Music, FileAudio, AlertCircle, CheckCircle } from 'lucide-react';

interface AudioUploadModalProps {
  onClose: () => void;
  onSuccess: (audio: any) => void;
}

interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  stage: 'uploading' | 'processing' | 'complete' | 'error';
  message: string;
}

const AudioUploadModal: React.FC<AudioUploadModalProps> = ({ onClose, onSuccess }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [audioInfo, setAudioInfo] = useState<{
    duration: number;
    size: number;
  } | null>(null);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);
  const [metadata, setMetadata] = useState({
    title: '',
    description: '',
    category: '',
    tags: '',
    isPublic: true,
    allowComments: true,
    allowDownload: false
  });
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string>('');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('audio/')) {
      setError('Please select an audio file');
      return;
    }

    // Validate file size (max 500MB)
    const maxSize = 500 * 1024 * 1024;
    if (file.size > maxSize) {
      setError('File size exceeds 500MB limit');
      return;
    }

    setSelectedFile(file);
    setError('');
    setMetadata(prev => ({ ...prev, title: file.name.replace(/\.[^/.]+$/, '') }));

    // Create preview URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);

    // Get audio metadata
    const audio = document.createElement('audio');
    audio.preload = 'metadata';
    audio.onloadedmetadata = () => {
      setAudioInfo({
        duration: audio.duration,
        size: file.size
      });
      URL.revokeObjectURL(audio.src);
    };
    audio.src = url;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    const formData = new FormData();
    formData.append('audio', selectedFile);
    formData.append('title', metadata.title);
    formData.append('description', metadata.description);
    formData.append('category', metadata.category);
    formData.append('tags', metadata.tags);
    formData.append('isPublic', metadata.isPublic.toString());
    formData.append('allowComments', metadata.allowComments.toString());
    formData.append('allowDownload', metadata.allowDownload.toString());

    try {
      setUploadProgress({
        loaded: 0,
        total: selectedFile.size,
        percentage: 0,
        stage: 'uploading',
        message: 'Uploading audio...'
      });

      const xhr = new XMLHttpRequest();
      
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentage = Math.round((event.loaded / event.total) * 100);
          setUploadProgress(prev => prev ? {
            ...prev,
            loaded: event.loaded,
            total: event.total,
            percentage,
            message: `Uploading... ${percentage}%`
          } : null);
        }
      };

      xhr.onload = () => {
        if (xhr.status === 200) {
          const response = JSON.parse(xhr.responseText);
          if (response.success) {
            setUploadProgress({
              loaded: selectedFile.size,
              total: selectedFile.size,
              percentage: 100,
              stage: 'complete',
              message: 'Upload completed successfully!'
            });
            
            setTimeout(() => {
              onSuccess(response.data);
            }, 1000);
          } else {
            throw new Error(response.message || 'Upload failed');
          }
        } else {
          throw new Error('Upload failed');
        }
      };

      xhr.onerror = () => {
        setUploadProgress({
          loaded: 0,
          total: selectedFile.size,
          percentage: 0,
          stage: 'error',
          message: 'Upload failed. Please try again.'
        });
      };

      xhr.open('POST', '/api/admin/media/upload-audio');
      xhr.setRequestHeader('Authorization', `Bearer ${localStorage.getItem('auth_token')}`);
      xhr.send(formData);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadProgress({
        loaded: 0,
        total: selectedFile.size,
        percentage: 0,
        stage: 'error',
        message: error instanceof Error ? error.message : 'Upload failed'
      });
    }
  };

  const resetForm = () => {
    setSelectedFile(null);
    setPreviewUrl('');
    setAudioInfo(null);
    setUploadProgress(null);
    setMetadata({
      title: '',
      description: '',
      category: '',
      tags: '',
      isPublic: true,
      allowComments: true,
      allowDownload: false
    });
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Upload Audio</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {!selectedFile ? (
            /* File Selection */
            <div
              className={`border-2 border-dashed rounded-lg p-12 text-center transition-colors ${
                dragActive 
                  ? 'border-blue-400 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragOver={handleDrag}
              onDragLeave={handleDrag}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="audio/*"
                onChange={handleFileInput}
                className="hidden"
              />
              <FileAudio className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Choose an audio file or drag it here
              </h3>
              <p className="text-gray-500 mb-4">
                Supports MP3, WAV, OGG, AAC, FLAC formats
              </p>
              <p className="text-sm text-gray-400">
                Maximum file size: 500MB
              </p>
              {error && (
                <div className="mt-4 flex items-center justify-center text-red-600">
                  <AlertCircle className="w-4 h-4 mr-2" />
                  {error}
                </div>
              )}
            </div>
          ) : (
            /* Upload Form */
            <div className="space-y-6">
              {/* Audio Preview and Info */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Audio Preview</h3>
                  <div className="bg-gray-100 rounded-lg p-6">
                    {previewUrl && (
                      <audio
                        ref={audioRef}
                        src={previewUrl}
                        controls
                        className="w-full"
                      />
                    )}
                    <div className="mt-4 flex items-center justify-center">
                      <Music className="w-8 h-8 text-gray-400" />
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-3">Audio Information</h3>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">File Name:</span>
                      <span className="font-medium">{selectedFile.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">File Size:</span>
                      <span className="font-medium">{formatFileSize(selectedFile.size)}</span>
                    </div>
                    {audioInfo && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Duration:</span>
                        <span className="font-medium">{formatDuration(audioInfo.duration)}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600">Format:</span>
                      <span className="font-medium">{selectedFile.type}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Upload Progress */}
              {uploadProgress && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {uploadProgress.message}
                    </span>
                    <span className="text-sm text-gray-500">
                      {uploadProgress.percentage}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        uploadProgress.stage === 'error' 
                          ? 'bg-red-500' 
                          : uploadProgress.stage === 'complete'
                          ? 'bg-green-500'
                          : 'bg-blue-500'
                      }`}
                      style={{ width: `${uploadProgress.percentage}%` }}
                    />
                  </div>
                  {uploadProgress.stage === 'complete' && (
                    <div className="flex items-center mt-2 text-green-600">
                      <CheckCircle className="w-4 h-4 mr-2" />
                      <span className="text-sm">Upload completed successfully!</span>
                    </div>
                  )}
                  {uploadProgress.stage === 'error' && (
                    <div className="flex items-center mt-2 text-red-600">
                      <AlertCircle className="w-4 h-4 mr-2" />
                      <span className="text-sm">Upload failed. Please try again.</span>
                    </div>
                  )}
                </div>
              )}

              {/* Metadata Form */}
              {!uploadProgress && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900">Audio Details</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Title *
                      </label>
                      <input
                        type="text"
                        value={metadata.title}
                        onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter audio title"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Category
                      </label>
                      <select
                        value={metadata.category}
                        onChange={(e) => setMetadata(prev => ({ ...prev, category: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select category</option>
                        <option value="podcast">Podcast</option>
                        <option value="music">Music</option>
                        <option value="education">Education</option>
                        <option value="audiobook">Audiobook</option>
                        <option value="interview">Interview</option>
                        <option value="meditation">Meditation</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={metadata.description}
                      onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows={3}
                      placeholder="Enter audio description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tags (comma separated)
                    </label>
                    <input
                      type="text"
                      value={metadata.tags}
                      onChange={(e) => setMetadata(prev => ({ ...prev, tags: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g. podcast, interview, technology"
                    />
                  </div>

                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-700">Settings</h4>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isPublic"
                        checked={metadata.isPublic}
                        onChange={(e) => setMetadata(prev => ({ ...prev, isPublic: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isPublic" className="ml-2 text-sm text-gray-700">
                        Make audio public
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="allowComments"
                        checked={metadata.allowComments}
                        onChange={(e) => setMetadata(prev => ({ ...prev, allowComments: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="allowComments" className="ml-2 text-sm text-gray-700">
                        Allow comments
                      </label>
                    </div>

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="allowDownload"
                        checked={metadata.allowDownload}
                        onChange={(e) => setMetadata(prev => ({ ...prev, allowDownload: e.target.checked }))}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="allowDownload" className="ml-2 text-sm text-gray-700">
                        Allow download
                      </label>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        {selectedFile && !uploadProgress && (
          <div className="flex items-center justify-between p-6 border-t bg-gray-50">
            <button
              onClick={resetForm}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Choose Different File
            </button>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={handleUpload}
                disabled={!metadata.title.trim()}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Audio
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AudioUploadModal;
