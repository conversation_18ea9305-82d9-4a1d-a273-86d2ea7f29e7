'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
// import { useAuth } from '@/contexts/AuthContext'; // 暂时禁用
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Logo from './Logo';
import SafeComponent from '@/components/SafeComponent';

import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
// import EnhancedNotificationCenter from '@/components/EnhancedNotificationCenter'; // 移除弹窗式通知中心
import socketService from '@/services/socketService';
import { safeRemoveEventListener } from '@/utils/domSafetyUtils';
import AdvancedSearchModal, { SearchFilters } from '@/components/AdvancedSearchModal';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearchSuggestions, setShowSearchSuggestions] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    query: '',
    type: 'all',
    category: 'all',
    author: '',
    dateRange: 'all',
    sortBy: 'relevance',
    minViews: 0,
    tags: []
  });
  // const [isNotificationCenterOpen, setIsNotificationCenterOpen] = useState(false); // 移除弹窗状态
  const [unreadCount, setUnreadCount] = useState(0);
  const isMountedRef = useRef(true);
  const router = useRouter();
  const userMenuRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated, logout } = useSimpleAuth();

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Socket.IO实时通知监听
  useEffect(() => {
    if (isAuthenticated && user) {
      // 连接Socket.IO服务
      const token = localStorage.getItem('auth_token') || 'mock-token';
      socketService.connect(token).catch(console.error);

      // 监听新通知
      const handleNewNotification = (notification: any) => {
        setUnreadCount(prev => prev + 1);
        // 显示浮动通知
        if (notification.type === 'comment') {
          // 使用toast显示新评论通知
        } else if (notification.type === 'like') {
          // 使用toast显示点赞通知
        } else if (notification.type === 'follow') {
          // 使用toast显示关注通知
        }
      };

      // 监听未读数量更新
      const handleUnreadCountUpdate = (data: any) => {
        setUnreadCount(data.count);
      };

      socketService.on('new_notification', handleNewNotification);
      socketService.on('unread_count_updated', handleUnreadCountUpdate);

      return () => {
        socketService.off('new_notification', handleNewNotification);
        socketService.off('unread_count_updated', handleUnreadCountUpdate);
      };
    }
  }, [isAuthenticated, user]);

  // 从localStorage加载搜索历史
  useEffect(() => {
    const savedHistory = localStorage.getItem('searchHistory');
    if (savedHistory) {
      setSearchHistory(JSON.parse(savedHistory));
    }
  }, []);

  // Close menus when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      try {
        if (!isMountedRef.current) return;
        
        if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
          setIsUserMenuOpen(false);
        }
        if (
          searchRef.current && 
          !searchRef.current.contains(event.target as Node) &&
          !(event.target as HTMLElement).closest('.search-suggestion')
        ) {
          setTimeout(() => {
            if (isMountedRef.current) {
              setShowSearchSuggestions(false);
            }
          }, 100);
        }
      } catch (error) {
        console.warn('Click outside handler error:', error);
      }
    }

    if (isUserMenuOpen || showSearchSuggestions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      safeRemoveEventListener(document, 'mousedown', handleClickOutside);
    };
  }, [isUserMenuOpen, showSearchSuggestions]);

  const handleSearch = (e: React.FormEvent, query?: string) => {
    e.preventDefault();
    const searchTerm = query || searchQuery.trim();

    if (searchTerm) {
      // 保存到搜索历史
      const newHistory = [searchTerm, ...searchHistory.filter((item) => item !== searchTerm)].slice(
        0,
        5
      );
      setSearchHistory(newHistory);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));

      // 构建搜索URL参数
      const params = new URLSearchParams({
        q: searchTerm,
        type: searchFilters.type,
        category: searchFilters.category,
        author: searchFilters.author,
        dateRange: searchFilters.dateRange,
        sortBy: searchFilters.sortBy,
        minViews: searchFilters.minViews.toString(),
        tags: searchFilters.tags.join(',')
      });

      // 跳转到搜索结果页
      router.push(`/search?${params.toString()}`);

      // 关闭搜索建议
      setShowSearchSuggestions(false);

      // 如果是通过建议选择的，更新搜索框
      if (query) {
        setSearchQuery(query);
      }
    }
  };

  const handleAdvancedSearch = (filters: SearchFilters) => {
    setSearchFilters(filters);
    setSearchQuery(filters.query);
    
    // 构建搜索URL参数
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== 'all' && value !== 0 && !(Array.isArray(value) && value.length === 0)) {
        if (Array.isArray(value)) {
          params.set(key, value.join(','));
        } else {
          params.set(key, value.toString());
        }
      }
    });

    router.push(`/search?${params.toString()}`);
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (!isMountedRef.current) return;
    setSearchQuery(value);

    // 显示搜索建议（当有输入或有历史记录时）
    if (value.trim() || searchHistory.length > 0) {
      setShowSearchSuggestions(true);
    } else {
      setShowSearchSuggestions(false);
    }
  };

  const handleSearchFocus = () => {
    if (!isMountedRef.current) return;
    if (searchQuery.trim() || searchHistory.length > 0) {
      setShowSearchSuggestions(true);
    }
  };

  const clearSearchHistory = () => {
    setSearchHistory([]);
    localStorage.removeItem('searchHistory');
  };

  // const handleUnreadCountChange = (count: number) => {
  //   setUnreadCount(count);
  // }; // 移除弹窗相关处理

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Navigation */}
          <div className="flex items-center space-x-8">
            <Logo size="md" href="/" />

            {/* Navigation Links */}
            <nav className="hidden md:flex items-center space-x-6">
              <Link
                href="/"
                className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
              >
                Home
              </Link>
              <Link
                href="/explore"
                className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
              >
                Explore
              </Link>
              {isAuthenticated && (
                <Link
                  href="/create"
                  className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
                >
                  Create
                </Link>
              )}
              {isAuthenticated && (
                <div className="relative group">
                  <button className="text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors flex items-center">
                    Content
                    <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                  <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                    <div className="py-2">
                      <Link
                        href="/content"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      >
                        📝 My Content
                      </Link>
                      <Link
                        href="/earnings"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      >
                        💰 Earnings
                      </Link>
                      <Link
                        href="/analytics"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      >
                        📊 Analytics
                      </Link>
                      <Link
                        href="/ads"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                      >
                        📢 Advertising
                      </Link>
                    </div>
                  </div>
                </div>
              )}
            </nav>
          </div>

          {/* Center Search Bar */}
          <div className="hidden md:flex flex-1 max-w-md mx-8">
            <div ref={searchRef} className="relative w-full">
              <form onSubmit={handleSearch} className="relative w-full">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  onFocus={handleSearchFocus}
                  placeholder="Search"
                  className="w-full px-4 py-2 pl-10 pr-10 bg-gray-50 border border-gray-200 rounded-lg text-sm text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                  <svg
                    className="h-4 w-4 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                    />
                  </svg>
                </div>
                <button
                  type="button"
                  onClick={() => setShowAdvancedSearch(true)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded transition-colors"
                  title="Advanced Search"
                >
                  <svg className="h-4 w-4 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                </button>
              </form>

              {/* Search Suggestions */}
              {showSearchSuggestions && (
                <div 
                  className="absolute top-full left-0 right-0 mt-3 bg-surface/95 backdrop-blur-xl border border-border rounded-2xl shadow-2xl z-50 max-h-64 overflow-y-auto animate-fade-in search-suggestion"
                  onMouseDown={(e) => e.preventDefault()} // 防止blur事件过早触发
                >
                  {searchHistory.length > 0 && (
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-xs font-semibold text-text-muted uppercase tracking-wider">
                          Recent Searches
                        </span>
                        <Button
                          onClick={clearSearchHistory}
                          variant="ghost"
                          size="xs"
                          className="text-xs text-primary hover:text-primary-600"
                        >
                          Clear
                        </Button>
                      </div>
                      {searchHistory.map((item, index) => (
                        <button
                          key={index}
                          onClick={(e) => handleSearch(e, item)}
                          className="w-full text-left px-4 py-3 text-sm text-text-primary hover:bg-surface-2 rounded-xl flex items-center space-x-3 transition-all duration-200 group"
                        >
                          <svg
                            className="w-4 h-4 text-text-muted group-hover:text-primary transition-colors"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <span className="font-medium">{item}</span>
                        </button>
                      ))}
                    </div>
                  )}

                  {searchQuery.trim() && !searchHistory.includes(searchQuery.trim()) && (
                    <div className="border-t border-gray-100 p-3">
                      <button
                        onClick={(e) => handleSearch(e)}
                        className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg flex items-center space-x-2"
                      >
                        <svg
                          className="w-4 h-4 text-gray-400"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                          />
                        </svg>
                        <span>
                          Search for "<strong>{searchQuery.trim()}</strong>"
                        </span>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Notifications - 只在用户登录后显示 */}
            {isAuthenticated && (
              <Link
                href="/notifications"
                className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
                </svg>

                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </span>
                )}
              </Link>
            )}

            {/* User Authentication */}
            {isAuthenticated ? (
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-50 transition-all duration-300 group"
                >
                  <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <span className="text-white text-xs font-bold">
                      {user?.username?.charAt(0).toUpperCase() || 'U'}
                    </span>
                  </div>
                </button>

                {/* Simplified User Dropdown Menu */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                    {user?.role === 'admin' && (
                      <button
                        onClick={() => {
                          router.push('/admin');
                          setIsUserMenuOpen(false);
                        }}
                        className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <svg className="w-4 h-4 mr-3 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                        </svg>
                        Admin Panel
                      </button>
                    )}
                    
                    <button
                      onClick={() => {
                        setIsUserMenuOpen(false);
                        logout();
                      }}
                      className="flex items-center w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                    >
                      <svg className="w-4 h-4 mr-3 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => router.push('/auth/login')}
                  className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 transition-colors"
                >
                  Sign In
                </button>
                <button
                  onClick={() => router.push('/auth/register')}
                  className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Sign Up
                </button>
              </div>
            )}

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="md"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2.5 text-text-muted hover:text-text-primary"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-border bg-surface/95 backdrop-blur-xl animate-fade-in">
            <div className="px-6 py-6 space-y-4">
              {/* Mobile Search */}
              <form onSubmit={handleSearch} className="relative mb-6">
                <Input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search articles, topics..."
                  variant="glass"
                  size="lg"
                  fullWidth
                  leftIcon={
                    <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  }
                />
              </form>

              {/* Navigation Links */}
              <div className="space-y-2">
                <Link
                  href="/"
                  className="flex items-center text-text-primary hover:text-primary hover:bg-surface-2 px-5 py-4 rounded-xl text-lg font-medium transition-all duration-300 group"
                >
                  <svg
                    className="w-6 h-6 mr-4 text-text-muted group-hover:text-primary transition-colors"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                    />
                  </svg>
                  Home
                </Link>
                <Link
                  href="/explore"
                  className="flex items-center text-text-primary hover:text-primary hover:bg-surface-2 px-5 py-4 rounded-xl text-lg font-medium transition-all duration-300 group"
                >
                  <svg
                    className="w-6 h-6 mr-4 text-text-muted group-hover:text-primary transition-colors"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                    />
                  </svg>
                  Explore
                </Link>
              </div>

              {/* Additional Mobile Links */}
              <div className="space-y-2 pt-4 border-t border-border">
                {isAuthenticated && (
                  <Link
                    href="/create"
                    className="flex items-center text-text-primary hover:text-primary hover:bg-surface-2 px-5 py-4 rounded-xl text-lg font-medium transition-all duration-300 group"
                  >
                    <svg
                      className="w-6 h-6 mr-4 text-text-muted group-hover:text-primary transition-colors"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M12 4v16m8-8H4"
                      />
                    </svg>
                    Create
                  </Link>
                )}
                {isAuthenticated && (
                  <Link
                    href="/content"
                    className="flex items-center text-text-primary hover:text-primary hover:bg-surface-2 px-5 py-4 rounded-xl text-lg font-medium transition-all duration-300 group"
                  >
                    <svg
                      className="w-6 h-6 mr-4 text-text-muted group-hover:text-primary transition-colors"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    Content
                  </Link>
                )}
                {isAuthenticated && (
                  <Link
                    href="/notifications"
                    className="flex items-center text-text-primary hover:text-primary hover:bg-surface-2 px-5 py-4 rounded-xl text-lg font-medium transition-all duration-300 group"
                  >
                    <svg
                      className="w-6 h-6 mr-4 text-text-muted group-hover:text-primary transition-colors"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1.5}
                        d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"
                      />
                    </svg>
                    Notifications
                  </Link>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Advanced Search Modal */}
      <AdvancedSearchModal
        isOpen={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
        initialFilters={searchFilters}
      />
    </header>
  );
}