
'use client';

import React, { useState } from 'react';
import { Search, Filter, Eye, Trash2, Flag, MessageSquare, User } from 'lucide-react';

interface Comment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  article: {
    id: string;
    title: string;
  };
  status: 'approved' | 'pending' | 'rejected';
  createdAt: string;
  likes: number;
  reports: number;
}

const CommentsPage: React.FC = () => {
  const [comments, setComments] = useState<Comment[]>([
    {
      id: '1',
      content: 'This article provides a comprehensive overview of the topic. I especially appreciate the examples provided.',
      author: {
        id: 'user1',
        name: '<PERSON>',
      },
      article: {
        id: 'article1',
        title: 'Getting Started with React Hooks'
      },
      status: 'approved',
      createdAt: '2023-06-01 10:30:00',
      likes: 24,
      reports: 0
    },
    {
      id: '2',
      content: 'I have a different perspective on this. While the article makes some good points, it overlooks some important aspects of the discussion.',
      author: {
        id: 'user2',
        name: '<PERSON>',
      },
      article: {
        id: 'article2',
        title: 'The Future of Web Development'
      },
      status: 'approved',
      createdAt: '2023-06-02 14:15:00',
      likes: 12,
      reports: 0
    },
    {
      id: '3',
      content: 'This is spam content with irrelevant links and promotional material.',
      author: {
        id: 'user3',
        name: 'Spam Bot',
      },
      article: {
        id: 'article3',
        title: 'JavaScript Best Practices'
      },
      status: 'pending',
      createdAt: '2023-06-03 09:45:00',
      likes: 0,
      reports: 3
    },
    {
      id: '4',
      content: 'Great article! Very informative and well-written. Looking forward to more content like this.',
      author: {
        id: 'user4',
        name: 'Michael Chen',
      },
      article: {
        id: 'article4',
        title: 'Building Responsive Layouts with CSS Grid'
      },
      status: 'approved',
      createdAt: '2023-06-04 16:20:00',
      likes: 18,
      reports: 0
    },
    {
      id: '5',
      content: 'I disagree with the author\'s conclusions. The evidence presented does not support the claims made in the article.',
      author: {
        id: 'user5',
        name: 'Jennifer Davis'
      },
      article: {
        id: 'article5',
        title: 'Introduction to TypeScript'
      },
      status: 'pending',
      createdAt: '2023-06-05 11:10:00',
      likes: 5,
      reports: 1
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedComments, setSelectedComments] = useState<string[]>([]);

  const filteredComments = comments.filter(comment => {
    const matchesSearch = comment.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         comment.author.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         comment.article.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter ? comment.status === statusFilter : true;

    return matchesSearch && matchesStatus;
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would trigger a new API call
  };

  const handleApproveComment = (id: string) => {
    setComments(comments.map(comment =>
      comment.id === id ? { ...comment, status: 'approved' as const } : comment
    ));
    alert('Comment has been approved');
  };

  const handleRejectComment = (id: string) => {
    setComments(comments.map(comment =>
      comment.id === id ? { ...comment, status: 'rejected' as const } : comment
    ));
    alert('Comment has been rejected');
  };

  const handleDeleteComment = (id: string) => {
    if (confirm('Are you sure you want to delete this comment?')) {
      setComments(comments.filter(comment => comment.id !== id));
      setSelectedComments(selectedComments.filter(commentId => commentId !== id));
      alert('Comment has been deleted');
    }
  };

  const handleBulkAction = (action: string) => {
    if (selectedComments.length === 0) {
      alert('Please select comments first');
      return;
    }

    if (!confirm(`Are you sure you want to ${action} ${selectedComments.length} comments?`)) {
      return;
    }

    switch (action) {
      case 'approve':
        setComments(comments.map(comment =>
          selectedComments.includes(comment.id) ? { ...comment, status: 'approved' as const } : comment
        ));
        alert('Selected comments have been approved');
        break;
      case 'reject':
        setComments(comments.map(comment =>
          selectedComments.includes(comment.id) ? { ...comment, status: 'rejected' as const } : comment
        ));
        alert('Selected comments have been rejected');
        break;
      case 'delete':
        setComments(comments.filter(comment => !selectedComments.includes(comment.id)));
        alert('Selected comments have been deleted');
        break;
    }

    setSelectedComments([]);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'approved':
        return <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Approved</span>;
      case 'pending':
        return <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>;
      case 'rejected':
        return <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>;
      default:
        return <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Unknown</span>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Comment Management</h1>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search comments, authors, or articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="approved">Approved</option>
            <option value="pending">Pending</option>
            <option value="rejected">Rejected</option>
          </select>
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* Bulk Actions */}
      {selectedComments.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedComments.length} comments
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBulkAction('approve')}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Bulk Approve
              </button>
              <button
                onClick={() => handleBulkAction('reject')}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Bulk Reject
              </button>
              <button
                onClick={() => handleBulkAction('delete')}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Bulk Delete
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Comments List */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    checked={selectedComments.length === filteredComments.length && filteredComments.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedComments(filteredComments.map(comment => comment.id));
                      } else {
                        setSelectedComments([]);
                      }
                    }}
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Comment
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Article
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stats
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredComments.map((comment) => (
                <tr key={comment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={selectedComments.includes(comment.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedComments([...selectedComments, comment.id]);
                        } else {
                          setSelectedComments(selectedComments.filter(id => id !== comment.id));
                        }
                      }}
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {comment.content}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                        {comment.author.name.charAt(0).toUpperCase()}
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{comment.author.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {comment.article.title}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(comment.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      <div className="flex items-center">
                        <MessageSquare className="w-4 h-4 mr-1 text-gray-400" />
                        {comment.likes}
                      </div>
                      {comment.reports > 0 && (
                        <div className="flex items-center mt-1">
                          <Flag className="w-4 h-4 mr-1 text-red-400" />
                          {comment.reports}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(comment.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => alert(`View comment: ${comment.id}`)}
                        className="text-blue-600 hover:text-blue-900"
                        title="View"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                      {comment.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApproveComment(comment.id)}
                            className="text-green-600 hover:text-green-900"
                            title="Approve"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleRejectComment(comment.id)}
                            className="text-yellow-600 hover:text-yellow-900"
                            title="Reject"
                          >
                            Reject
                          </button>
                        </>
                      )}
                      <button
                        onClick={() => handleDeleteComment(comment.id)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredComments.length === 0 && (
          <div className="py-12 text-center">
            <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No comments found</h3>
            <p className="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentsPage;
