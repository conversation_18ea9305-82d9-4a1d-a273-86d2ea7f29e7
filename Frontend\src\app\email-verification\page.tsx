'use client';

import React, { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { supabase } from '@/lib/supabase';
import Link from 'next/link';

export default function EmailVerificationPage() {
  const { user, isAuthenticated } = useSimpleAuth() as any;
  const [verificationStatus, setVerificationStatus] = useState<'checking' | 'verified' | 'unverified' | 'error'>('checking');
  const [resendStatus, setResendStatus] = useState<'idle' | 'sending' | 'sent' | 'error'>('idle');
  const [message, setMessage] = useState('');

  useEffect(() => {
    checkVerificationStatus();
  }, [user, isAuthenticated]);

  const checkVerificationStatus = async () => {
    try {
      if (!isAuthenticated || !user) {
        setVerificationStatus('unverified');
        setMessage('请先登录以检查邮箱验证状态');
        return;
      }

      // 检查当前用户的邮箱验证状态
      const { data: { user: currentUser }, error } = await supabase.auth.getUser();
      
      if (error) {
        setVerificationStatus('error');
        setMessage(`检查验证状态失败: ${error.message}`);
        return;
      }

      if (currentUser?.email_confirmed_at) {
        setVerificationStatus('verified');
        setMessage('您的邮箱已验证！');
      } else {
        setVerificationStatus('unverified');
        setMessage('您的邮箱尚未验证，请检查邮箱并点击验证链接');
      }
    } catch (err: any) {
      setVerificationStatus('error');
      setMessage(`检查过程中发生错误: ${err.message}`);
    }
  };

  const resendVerificationEmail = async () => {
    if (!user?.email) {
      setMessage('无法获取用户邮箱地址');
      return;
    }

    try {
      setResendStatus('sending');
      
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: user.email,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      });

      if (error) {
        setResendStatus('error');
        setMessage(`重发验证邮件失败: ${error.message}`);
      } else {
        setResendStatus('sent');
        setMessage('验证邮件已重新发送，请检查您的邮箱');
      }
    } catch (err: any) {
      setResendStatus('error');
      setMessage(`重发过程中发生错误: ${err.message}`);
    }
  };

  const refreshStatus = () => {
    setVerificationStatus('checking');
    checkVerificationStatus();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
        <div className="text-center">
          {/* 状态图标 */}
          <div className="mb-6">
            {verificationStatus === 'checking' && (
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
            )}
            {verificationStatus === 'verified' && (
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            )}
            {verificationStatus === 'unverified' && (
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            )}
            {verificationStatus === 'error' && (
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            )}
          </div>

          {/* 标题 */}
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {verificationStatus === 'checking' && '检查验证状态'}
            {verificationStatus === 'verified' && '邮箱已验证'}
            {verificationStatus === 'unverified' && '邮箱未验证'}
            {verificationStatus === 'error' && '验证检查失败'}
          </h1>

          {/* 消息 */}
          <p className={`text-sm mb-6 ${
            verificationStatus === 'verified' ? 'text-green-600' : 
            verificationStatus === 'error' ? 'text-red-600' : 
            'text-gray-600'
          }`}>
            {message}
          </p>

          {/* 用户信息 */}
          {user && (
            <div className="bg-gray-50 p-4 rounded-lg mb-6 text-left">
              <h3 className="font-medium text-gray-900 mb-2">用户信息</h3>
              <p className="text-sm text-gray-600">邮箱: {user.email}</p>
              <p className="text-sm text-gray-600">用户ID: {user.id}</p>
              {user.email_confirmed_at && (
                <p className="text-sm text-green-600">
                  验证时间: {new Date(user.email_confirmed_at).toLocaleString()}
                </p>
              )}
            </div>
          )}

          {/* 操作按钮 */}
          <div className="space-y-3">
            {verificationStatus === 'unverified' && (
              <button
                onClick={resendVerificationEmail}
                disabled={resendStatus === 'sending'}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                {resendStatus === 'sending' ? '发送中...' : '重新发送验证邮件'}
              </button>
            )}

            <button
              onClick={refreshStatus}
              disabled={verificationStatus === 'checking'}
              className="w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 disabled:opacity-50 transition-colors"
            >
              {verificationStatus === 'checking' ? '检查中...' : '刷新状态'}
            </button>

            {verificationStatus === 'verified' && (
              <Link
                href="/"
                className="block w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors text-center"
              >
                进入应用
              </Link>
            )}

            {!isAuthenticated && (
              <Link
                href="/auth/login"
                className="block w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-center"
              >
                登录账户
              </Link>
            )}
          </div>

          {/* 帮助信息 */}
          <div className="mt-6 pt-6 border-t border-gray-200 text-left">
            <h3 className="font-medium text-gray-900 mb-2">需要帮助？</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p>• 检查垃圾邮件文件夹</p>
              <p>• 确认邮箱地址正确</p>
              <p>• 验证链接可能需要几分钟才能到达</p>
              <p>• 如果仍有问题，请联系技术支持</p>
            </div>
          </div>

          {/* 快速导航 */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <h3 className="font-medium text-gray-900 mb-3">快速导航</h3>
            <div className="flex flex-wrap gap-2 justify-center">
              <Link href="/auth-status" className="px-3 py-1 bg-blue-100 text-blue-800 rounded-md hover:bg-blue-200 text-xs">
                认证状态
              </Link>
              <Link href="/api-test" className="px-3 py-1 bg-green-100 text-green-800 rounded-md hover:bg-green-200 text-xs">
                API测试
              </Link>
              <Link href="/" className="px-3 py-1 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 text-xs">
                返回首页
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
