'use client';

import React, { forwardRef } from 'react';
import { cn } from '@/lib/utils';

export interface RadioOption {
  value: string;
  label: string;
  description?: string;
  disabled?: boolean;
}

export interface RadioProps {
  name: string;
  options: RadioOption[];
  value?: string;
  defaultValue?: string;
  onChange?: (value: string) => void;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  orientation?: 'horizontal' | 'vertical';
  error?: string;
  disabled?: boolean;
}

const Radio = forwardRef<HTMLDivElement, RadioProps>(
  (
    {
      name,
      options,
      value,
      defaultValue,
      onChange,
      size = 'md',
      variant = 'default',
      orientation = 'vertical',
      error,
      disabled = false,
      ...props
    },
    ref
  ) => {
    // 尺寸样式
    const sizeStyles = {
      sm: {
        radio: 'w-4 h-4',
        text: 'text-sm',
        dot: 'w-2 h-2',
      },
      md: {
        radio: 'w-5 h-5',
        text: 'text-base',
        dot: 'w-2.5 h-2.5',
      },
      lg: {
        radio: 'w-6 h-6',
        text: 'text-lg',
        dot: 'w-3 h-3',
      },
    };

    // 变体样式
    const variantStyles = {
      default: [
        'border-2 border-border bg-surface',
        'checked:border-primary',
        'focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
        'hover:border-primary/50',
      ],
      filled: [
        'border-0 bg-surface-2',
        'checked:bg-primary/10',
        'focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
        'hover:bg-surface',
      ],
      outlined: [
        'border-2 border-primary bg-transparent',
        'checked:border-primary',
        'focus:ring-2 focus:ring-primary/20 focus:ring-offset-1',
        'hover:bg-primary/5',
      ],
    };

    const radioStyles = cn(
      'relative cursor-pointer transition-all duration-200',
      'rounded-full appearance-none',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      sizeStyles[size].radio,
      variantStyles[variant],
      error && 'border-error focus:ring-error/20'
    );

    const containerStyles = cn(
      'flex gap-4',
      orientation === 'vertical' ? 'flex-col' : 'flex-row flex-wrap'
    );

    return (
      <div ref={ref} {...props}>
        <div className={containerStyles}>
          {options.map((option) => (
            <div key={option.value} className="flex items-start gap-3">
              {/* Radio容器 */}
              <div className="relative flex-shrink-0 mt-0.5">
                <input
                  type="radio"
                  name={name}
                  value={option.value}
                  className={radioStyles}
                  disabled={disabled || option.disabled}
                  checked={value ? value === option.value : defaultValue === option.value}
                  onChange={(e) => onChange?.(e.target.value)}
                />

                {/* 自定义radio点 */}
                <div
                  className={cn(
                    'absolute inset-0 flex items-center justify-center pointer-events-none',
                    'transition-all duration-200'
                  )}
                >
                  <div
                    className={cn(
                      'rounded-full bg-primary transition-all duration-200',
                      sizeStyles[size].dot,
                      (value ? value === option.value : defaultValue === option.value)
                        ? 'opacity-100 scale-100'
                        : 'opacity-0 scale-75'
                    )}
                  />
                </div>
              </div>

              {/* 标签和描述 */}
              <div className="flex-1">
                <label
                  className={cn(
                    'font-medium cursor-pointer transition-colors block',
                    sizeStyles[size].text,
                    disabled || option.disabled ? 'text-text-muted' : 'text-text-primary',
                    error && 'text-error'
                  )}
                >
                  {option.label}
                </label>

                {option.description && (
                  <p className={cn('text-text-muted mt-1', size === 'sm' ? 'text-xs' : 'text-sm')}>
                    {option.description}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        {error && <p className="text-sm text-error mt-2 animate-fade-in">{error}</p>}
      </div>
    );
  }
);

Radio.displayName = 'Radio';

export { Radio };
