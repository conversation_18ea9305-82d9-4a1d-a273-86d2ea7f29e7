import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Advertising - Newzora',
  description: 'Learn about advertising opportunities on Newzora platform',
};

export default function AdvertisingPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Advertising on Newzora</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Why Advertise with Us?</h2>
              <p className="text-gray-600 leading-relaxed">
                Reach millions of engaged readers on Newzora, the premier platform for quality content and meaningful discussions.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Advertising Options</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Display Ads</h3>
                  <p className="text-gray-600">Banner and sidebar advertisements with high visibility</p>
                </div>
                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sponsored Content</h3>
                  <p className="text-gray-600">Native advertising that blends with our content</p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Contact Us</h2>
              <p className="text-gray-600">
                For advertising inquiries, please contact us at{' '}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
                  <EMAIL>
                </a>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}