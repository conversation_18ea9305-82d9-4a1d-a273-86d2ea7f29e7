'use client';

import React, { useState } from 'react';
import { Calendar, ChevronDown, ChevronLeft, ChevronRight, X } from 'lucide-react';

export interface DateRange {
  startDate: Date;
  endDate: Date;
  label: string;
}

interface SimpleDatePickerProps {
  value: DateRange | null;
  onChange: (dateRange: DateRange) => void;
  className?: string;
  placeholder?: string;
}

const SimpleDatePicker: React.FC<SimpleDatePickerProps> = ({
  value,
  onChange,
  className = '',
  placeholder = 'Select date range'
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [startDate, setStartDate] = useState<Date | null>(value?.startDate || null);
  const [endDate, setEndDate] = useState<Date | null>(value?.endDate || null);
  const [showYearMonthPicker, setShowYearMonthPicker] = useState(false);

  // Calculate days between dates
  const calculateDaysDiff = (start: Date, end: Date) => {
    return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const formatDisplayValue = () => {
    if (!startDate || !endDate) return placeholder;
    if (startDate.toDateString() === endDate.toDateString()) {
      return formatDate(startDate);
    }
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const isDateInRange = (date: Date) => {
    if (!startDate || !endDate) return false;
    return date >= startDate && date <= endDate;
  };

  const isDateSelected = (date: Date) => {
    if (!startDate) return false;
    if (!endDate) return date.toDateString() === startDate.toDateString();
    return date.toDateString() === startDate.toDateString() || 
           date.toDateString() === endDate.toDateString();
  };

  const handleDateClick = (date: Date) => {
    if (!startDate || (startDate && endDate)) {
      // Start new selection
      setStartDate(date);
      setEndDate(null);
    } else {
      // Complete selection
      const newStartDate = date < startDate ? date : startDate;
      const newEndDate = date < startDate ? startDate : date;
      
      setStartDate(newStartDate);
      setEndDate(newEndDate);
      
      // Apply selection
      const newRange: DateRange = {
        startDate: newStartDate,
        endDate: newEndDate,
        label: 'Custom Range'
      };
      
      onChange(newRange);
      setIsOpen(false);
      setShowYearMonthPicker(false);
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      if (direction === 'prev') {
        newMonth.setMonth(prev.getMonth() - 1);
      } else {
        newMonth.setMonth(prev.getMonth() + 1);
      }
      return newMonth;
    });
  };

  const handleYearChange = (year: number) => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setFullYear(year);
      return newMonth;
    });
    setShowYearMonthPicker(false);
  };

  const handleMonthChange = (month: number) => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev);
      newMonth.setMonth(month);
      return newMonth;
    });
    setShowYearMonthPicker(false);
  };

  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 10; i <= currentYear + 5; i++) {
      years.push(i);
    }
    return years;
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const clearSelection = () => {
    setStartDate(null);
    setEndDate(null);
    setShowYearMonthPicker(false);
  };

  const renderCalendar = () => {
    const daysInMonth = getDaysInMonth(currentMonth);
    const firstDay = getFirstDayOfMonth(currentMonth);
    const days = [];
    
    // Empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="h-8"></div>);
    }
    
    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      const isSelected = isDateSelected(date);
      const isInRange = isDateInRange(date);
      const isToday = date.toDateString() === new Date().toDateString();
      
      days.push(
        <button
          key={`day-${day}`}
          onClick={() => handleDateClick(date)}
          className={`
            h-8 w-8 text-sm rounded-md transition-colors
            ${isSelected 
              ? 'bg-blue-600 text-white' 
              : isInRange 
              ? 'bg-blue-100 text-blue-800' 
              : 'hover:bg-gray-100'
            }
            ${isToday && !isSelected ? 'border border-blue-600' : ''}
          `}
        >
          {day}
        </button>
      );
    }
    
    return days;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        <div className="flex items-center space-x-2">
          <Calendar className="w-4 h-4 text-gray-500" />
          <span className={value ? 'text-gray-900' : 'text-gray-500'}>
            {formatDisplayValue()}
          </span>
        </div>
        <ChevronDown className="w-4 h-4 text-gray-500" />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50 p-4 min-w-80">
          {/* Days Counter */}
          {startDate && endDate && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <div className="text-center">
                <p className="text-sm font-medium text-blue-900">
                  Selected: {calculateDaysDiff(startDate, endDate)} days
                </p>
                <p className="text-xs text-blue-700 mt-1">
                  {formatDate(startDate)} - {formatDate(endDate)}
                </p>
              </div>
            </div>
          )}

          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <ChevronLeft className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowYearMonthPicker(!showYearMonthPicker)}
              className="text-sm font-medium hover:bg-gray-100 px-3 py-1 rounded"
            >
              {currentMonth.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}
            </button>
            <button
              onClick={() => navigateMonth('next')}
              className="p-1 hover:bg-gray-100 rounded"
            >
              <ChevronRight className="w-4 h-4" />
            </button>
          </div>

          {/* Year/Month Picker */}
          {showYearMonthPicker && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-2">Year</label>
                  <select
                    value={currentMonth.getFullYear()}
                    onChange={(e) => handleYearChange(parseInt(e.target.value))}
                    className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    {generateYearOptions().map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-2">Month</label>
                  <select
                    value={currentMonth.getMonth()}
                    onChange={(e) => handleMonthChange(parseInt(e.target.value))}
                    className="w-full text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  >
                    {monthNames.map((month, index) => (
                      <option key={index} value={index}>{month}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}

          {/* Calendar Grid */}
          <div className="mb-4">
            {/* Week Headers */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                <div key={day} className="h-8 flex items-center justify-center text-xs font-medium text-gray-500">
                  {day}
                </div>
              ))}
            </div>
            
            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-1">
              {renderCalendar()}
            </div>
          </div>

          {/* Selection Info */}
          <div className="flex items-center justify-between pt-3 border-t">
            <div className="text-xs text-gray-600">
              {startDate && !endDate && 'Click to select end date'}
              {!startDate && 'Click to select start date'}
              {startDate && endDate && 'Range selected'}
            </div>
            {(startDate || endDate) && (
              <button
                onClick={clearSelection}
                className="text-xs text-red-600 hover:text-red-800 flex items-center"
              >
                <X className="w-3 h-3 mr-1" />
                Clear
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SimpleDatePicker;
