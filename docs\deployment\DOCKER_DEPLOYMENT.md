# OneNews Docker 部署指南

## 🐳 Docker 容器化概述

OneNews 项目已完全容器化，支持开发和生产环境的 Docker 部署。

### 架构组件
- **Frontend**: Next.js 前端应用
- **Backend**: Node.js Express 后端服务
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **Nginx**: 反向代理和负载均衡

## 📋 部署前准备

### 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB RAM
- 至少 20GB 磁盘空间

### 安装 Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
```

## 🚀 开发环境部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd OneNews
```

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp Backend/.env.example Backend/.env

# 编辑环境变量
nano Backend/.env
```

### 3. 启动开发环境
```bash
# 使用管理脚本
chmod +x scripts/docker-dev.sh
./scripts/docker-dev.sh start

# 或直接使用 docker-compose
docker-compose -f docker-compose.dev.yml up -d
```

### 4. 访问服务
- **前端**: http://localhost:3000
- **后端API**: http://localhost:5000
- **数据库管理**: http://localhost:8080 (Adminer)
- **Redis管理**: http://localhost:8081 (Redis Commander)

### 5. 开发环境管理
```bash
# 查看服务状态
./scripts/docker-dev.sh status

# 查看日志
./scripts/docker-dev.sh logs
./scripts/docker-dev.sh logs backend

# 进入容器
./scripts/docker-dev.sh shell
./scripts/docker-dev.sh db

# 停止服务
./scripts/docker-dev.sh stop

# 清理环境
./scripts/docker-dev.sh clean
```

## 🏭 生产环境部署

### 1. 配置生产环境变量
```bash
# 复制生产环境配置模板
cp Backend/.env.production .env.production

# 编辑生产环境配置
nano .env.production
```

**重要配置项**:
```bash
# 数据库配置
DB_HOST=postgres
DB_NAME=onenews
DB_USER=postgres
DB_PASSWORD=your-secure-password

# JWT 安全配置
JWT_SECRET=your-super-secure-jwt-secret
SESSION_SECRET=your-super-secure-session-secret

# 邮件服务配置
EMAIL_PROVIDER=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# OAuth 配置
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# 前端URL
FRONTEND_URL=https://your-domain.com
```

### 2. 部署生产环境
```bash
# 使用管理脚本部署
chmod +x scripts/docker-prod.sh
./scripts/docker-prod.sh deploy

# 或手动部署
docker-compose build --no-cache
docker-compose up -d
```

### 3. 生产环境管理
```bash
# 查看服务状态
./scripts/docker-prod.sh status

# 查看日志
./scripts/docker-prod.sh logs

# 健康检查
./scripts/docker-prod.sh health

# 备份数据库
./scripts/docker-prod.sh backup

# 更新应用
./scripts/docker-prod.sh update

# 扩缩容服务
./scripts/docker-prod.sh scale backend 3
```

## 🔧 高级配置

### SSL/HTTPS 配置
1. 获取 SSL 证书（Let's Encrypt 推荐）
2. 将证书文件放置在 `nginx/ssl/` 目录
3. 更新 `nginx/conf.d/default.conf` 配置

### 环境变量说明
| 变量名 | 说明 | 示例 |
|--------|------|------|
| `NODE_ENV` | 运行环境 | `production` |
| `DB_HOST` | 数据库主机 | `postgres` |
| `DB_PASSWORD` | 数据库密码 | `secure-password` |
| `JWT_SECRET` | JWT 密钥 | `random-secret-key` |
| `EMAIL_PROVIDER` | 邮件服务商 | `gmail` |
| `FRONTEND_URL` | 前端地址 | `https://example.com` |

### 数据持久化
Docker 卷用于数据持久化：
- `postgres_data`: PostgreSQL 数据
- `redis_data`: Redis 数据
- `backend_uploads`: 上传文件
- `backend_logs`: 应用日志

### 网络配置
所有服务运行在 `onenews-network` 网络中，确保服务间通信安全。

## 🔍 监控和日志

### 查看日志
```bash
# 所有服务日志
docker-compose logs -f

# 特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
```

### 监控服务状态
```bash
# 查看容器状态
docker-compose ps

# 查看资源使用
docker stats

# 健康检查
docker-compose exec backend curl -f http://localhost:5000/api/health
```

## 🛠️ 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :3000
   
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "3001:3000"  # 改为其他端口
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库容器状态
   docker-compose logs postgres
   
   # 进入数据库容器检查
   docker-compose exec postgres psql -U postgres -d onenews
   ```

3. **内存不足**
   ```bash
   # 增加 Docker 内存限制
   # 在 docker-compose.yml 中添加
   deploy:
     resources:
       limits:
         memory: 1G
   ```

4. **文件权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R $USER:$USER ./
   chmod +x scripts/*.sh
   ```

### 性能优化

1. **启用生产模式**
   ```bash
   NODE_ENV=production
   ```

2. **配置资源限制**
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '0.5'
         memory: 512M
   ```

3. **使用多阶段构建**
   - 前端 Dockerfile 已使用多阶段构建
   - 减少镜像大小和构建时间

## 🔄 CI/CD 集成

### GitHub Actions 示例
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Deploy to server
        run: |
          ssh user@server 'cd /path/to/OneNews && git pull && ./scripts/docker-prod.sh update'
```

## 📊 备份和恢复

### 自动备份脚本
```bash
#!/bin/bash
# 每日备份脚本
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# 备份数据库
docker-compose exec postgres pg_dump -U postgres onenews > "$BACKUP_DIR/db_$DATE.sql"

# 备份上传文件
tar -czf "$BACKUP_DIR/uploads_$DATE.tar.gz" Backend/uploads/

# 清理旧备份（保留7天）
find "$BACKUP_DIR" -name "*.sql" -mtime +7 -delete
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +7 -delete
```

### 恢复数据
```bash
# 恢复数据库
./scripts/docker-prod.sh restore backup_20240101_120000.sql

# 恢复上传文件
tar -xzf uploads_20240101_120000.tar.gz -C Backend/
```

---

**部署完成后，OneNews 将提供：**
- 高可用的容器化部署
- 自动化的服务管理
- 完整的监控和日志
- 便捷的备份和恢复
- 灵活的扩缩容能力
