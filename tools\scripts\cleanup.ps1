# OneNews 项目清理和优化脚本

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "        OneNews 项目清理脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 清理根目录的不必要文件
Write-Host "🧹 清理根目录..." -ForegroundColor Yellow

# 检查并删除根目录的 node_modules
if (Test-Path "node_modules") {
    Write-Host "  - 删除根目录 node_modules..." -ForegroundColor Gray
    try {
        Remove-Item -Recurse -Force "node_modules" -ErrorAction SilentlyContinue
        Write-Host "  ✅ 根目录 node_modules 已删除" -ForegroundColor Green
    } catch {
        Write-Host "  ⚠️ 部分文件可能被占用，请手动删除" -ForegroundColor Yellow
    }
}

# 检查并删除根目录的 package.json 和 package-lock.json
if (Test-Path "package.json") {
    Remove-Item "package.json" -Force
    Write-Host "  ✅ Root directory package.json deleted" -ForegroundColor Green
}

if (Test-Path "package-lock.json") {
    Remove-Item "package-lock.json" -Force
    Write-Host "  ✅ Root directory package-lock.json deleted" -ForegroundColor Green
}

# Clean temporary files
Write-Host ""
Write-Host "🧹 Cleaning temporary files..." -ForegroundColor Yellow

$tempFiles = @("*.tmp", "*.log", ".DS_Store", "Thumbs.db")
foreach ($pattern in $tempFiles) {
    Get-ChildItem -Recurse -Name $pattern -ErrorAction SilentlyContinue | ForEach-Object {
        Remove-Item $_ -Force -ErrorAction SilentlyContinue
        Write-Host "  - Deleted: $_" -ForegroundColor Gray
    }
}

# Check project structure
Write-Host ""
Write-Host "📁 Checking project structure..." -ForegroundColor Yellow

$requiredDirs = @("Backend", "Frontend")
foreach ($dir in $requiredDirs) {
    if (Test-Path $dir) {
        Write-Host "  ✅ $dir directory exists" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $dir directory does not exist" -ForegroundColor Red
    }
}

# 检查重要文件
$requiredFiles = @(
    "Backend/package.json",
    "Backend/server.js",
    "Backend/.env",
    "Frontend/package.json",
    "Frontend/next.config.js",
    "README.md"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "  ✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $file does not exist" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "✅ 项目清理完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📝 建议接下来执行：" -ForegroundColor Yellow
Write-Host "  1. cd Backend && npm install" -ForegroundColor White
Write-Host "  2. cd Frontend && npm install" -ForegroundColor White
Write-Host "  3. 运行 start.ps1 启动项目" -ForegroundColor White
Write-Host "========================================" -ForegroundColor Green

Read-Host "按回车键关闭此窗口"
