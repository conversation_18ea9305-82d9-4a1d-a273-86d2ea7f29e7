'use client';

import React from 'react';

interface StatsCardProps {
  title: string;
  value: number;
  change: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon: React.ReactNode;
  color: 'blue' | 'green' | 'yellow' | 'red';
  link?: string;
  onClick?: () => void;
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, change, icon, color, link, onClick }) => {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    red: 'bg-red-100 text-red-600'
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow p-6 ${link || onClick ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : ''}`}
      onClick={() => {
        if (onClick) {
          onClick();
        }
      }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          {icon}
        </div>
      </div>
      
      <div>
        <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
        <p className="text-2xl font-semibold text-gray-900 mb-2">{value.toLocaleString()}</p>
        <div className="flex items-center">
          <span className={`text-sm ${
            change.type === 'increase' ? 'text-green-600' : 'text-red-600'
          }`}>
            {change.type === 'increase' ? '+' : '-'}{change.value}%
          </span>
          <span className="text-sm text-gray-500 ml-2">vs yesterday</span>
        </div>
      </div>
    </div>
  );
};

export default StatsCard;