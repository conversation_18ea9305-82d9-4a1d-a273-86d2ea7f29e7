'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import BrandName from './BrandName';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  href?: string;
}

const sizeClasses = {
  sm: 'text-lg',      // ~18px
  md: 'text-2xl',     // ~24px
  lg: 'text-4xl',     // ~36px
  xl: 'text-6xl',     // ~60px
};

export default function Logo({
  size = 'md',
  className = '',
  href
}: LogoProps) {
  const [isClient, setIsClient] = useState(false);
  const sizeClass = sizeClasses[size];

  // 确保只在客户端渲染后显示内容，避免水合错误
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 防止翻译的Logo文本组件
  const LogoText = () => (
    <BrandName 
      className={`${sizeClass} font-normal text-gray-900 ${className}`}
    />
  );

  // 在服务器端渲染时显示简化版本，避免水合错误
  if (!isClient) {
    const SimpleLogoText = () => (
      <BrandName 
        className={`${sizeClass} font-normal text-gray-900 ${className}`}
      />
    );

    if (href) {
      return (
        <Link href={href} className="inline-block" suppressHydrationWarning={true}>
          <SimpleLogoText />
        </Link>
      );
    }
    return <SimpleLogoText />;
  }

  // 客户端渲染完整版本
  if (href) {
    return (
      <Link href={href} className="inline-block">
        <LogoText />
      </Link>
    );
  }

  return <LogoText />;
}


