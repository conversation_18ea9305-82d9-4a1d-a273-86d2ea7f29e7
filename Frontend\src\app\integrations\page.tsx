'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Switch } from '@/components/ui/Switch';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  status: 'connected' | 'available' | 'coming-soon';
  features: string[];
}

const integrations: Integration[] = [
  {
    id: 'wechat-pay',
    name: '微信支付',
    description: '集成微信支付，支持用户打赏和付费内容',
    icon: '💳',
    category: 'payment',
    status: 'available',
    features: ['用户打赏', '付费内容', '自动结算']
  },
  {
    id: 'alipay',
    name: '支付宝',
    description: '支付宝支付集成，提供多种支付方式',
    icon: '💰',
    category: 'payment',
    status: 'available',
    features: ['在线支付', '移动支付', '批量转账']
  },
  {
    id: 'qiniu-cloud',
    name: '七牛云',
    description: '七牛云存储服务，用于媒体文件存储和CDN加速',
    icon: '☁️',
    category: 'storage',
    status: 'available',
    features: ['文件存储', 'CDN加速', '图片处理']
  },
  {
    id: 'aliyun-oss',
    name: '阿里云OSS',
    description: '阿里云对象存储服务',
    icon: '🗄️',
    category: 'storage',
    status: 'available',
    features: ['海量存储', '高可用性', '数据备份']
  },
  {
    id: 'baidu-ai',
    name: '百度AI',
    description: '百度AI服务，提供内容审核和智能推荐',
    icon: '🤖',
    category: 'ai',
    status: 'connected',
    features: ['内容审核', '文本分析', '图像识别']
  },
  {
    id: 'tencent-sms',
    name: '腾讯云短信',
    description: '短信验证码和通知服务',
    icon: '📱',
    category: 'communication',
    status: 'available',
    features: ['验证码', '通知短信', '营销短信']
  },
  {
    id: 'google-analytics',
    name: 'Google Analytics',
    description: '网站流量分析和用户行为追踪',
    icon: '📊',
    category: 'analytics',
    status: 'coming-soon',
    features: ['流量分析', '用户画像', '转化追踪']
  },
  {
    id: 'social-login',
    name: '社交登录',
    description: '支持微信、QQ、微博等社交平台登录',
    icon: '👥',
    category: 'auth',
    status: 'available',
    features: ['微信登录', 'QQ登录', '微博登录']
  }
];

const categories = {
  payment: '支付服务',
  storage: '存储服务',
  ai: 'AI服务',
  communication: '通信服务',
  analytics: '分析服务',
  auth: '认证服务'
};

export default function IntegrationsPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [connectedIntegrations, setConnectedIntegrations] = useState<Set<string>>(
    new Set(['baidu-ai'])
  );

  const filteredIntegrations = selectedCategory === 'all' 
    ? integrations 
    : integrations.filter(integration => integration.category === selectedCategory);

  const handleToggleIntegration = (integrationId: string) => {
    const newConnected = new Set(connectedIntegrations);
    if (newConnected.has(integrationId)) {
      newConnected.delete(integrationId);
    } else {
      newConnected.add(integrationId);
    }
    setConnectedIntegrations(newConnected);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">第三方集成</h1>
        <p className="text-gray-600">
          连接您喜欢的服务和工具，扩展Newzora平台功能。
        </p>
      </div>

      {/* 分类筛选 */}
      <div className="mb-8">
        <div className="flex flex-wrap gap-2">
          <Button
            variant={selectedCategory === 'all' ? 'primary' : 'outline'}
            onClick={() => setSelectedCategory('all')}
            size="sm"
          >
            全部
          </Button>
          {Object.entries(categories).map(([key, name]) => (
            <Button
              key={key}
              variant={selectedCategory === key ? 'primary' : 'outline'}
              onClick={() => setSelectedCategory(key)}
              size="sm"
            >
              {name}
            </Button>
          ))}
        </div>
      </div>

      {/* 集成列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => (
          <Card key={integration.id} className="p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                <div className="text-2xl">{integration.icon}</div>
                <div>
                  <h3 className="font-semibold">{integration.name}</h3>
                  <span className="text-xs text-gray-500">
                    {categories[integration.category as keyof typeof categories]}
                  </span>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {integration.status === 'connected' && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">
                    已连接
                  </span>
                )}
                {integration.status === 'coming-soon' && (
                  <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                    即将推出
                  </span>
                )}
                {integration.status === 'available' && (
                  <Switch
                    checked={connectedIntegrations.has(integration.id)}
                    onChange={() => handleToggleIntegration(integration.id)}
                  />
                )}
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4">
              {integration.description}
            </p>

            <div className="mb-4">
              <h4 className="font-medium text-sm mb-2">主要功能</h4>
              <div className="flex flex-wrap gap-1">
                {integration.features.map((feature, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded"
                  >
                    {feature}
                  </span>
                ))}
              </div>
            </div>

            <div className="flex gap-2">
              {integration.status === 'available' && (
                <Button
                  size="sm"
                  variant={connectedIntegrations.has(integration.id) ? 'outline' : 'primary'}
                  onClick={() => handleToggleIntegration(integration.id)}
                  className="flex-1"
                >
                  {connectedIntegrations.has(integration.id) ? '断开连接' : '立即连接'}
                </Button>
              )}
              {integration.status === 'coming-soon' && (
                <Button size="sm" variant="outline" disabled className="flex-1">
                  即将推出
                </Button>
              )}
              <Button size="sm" variant="outline">
                了解更多
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* 自定义集成 */}
      <Card className="p-6 mt-8">
        <h2 className="text-2xl font-semibold mb-4">自定义集成</h2>
        <p className="text-gray-600 mb-6">
          没有找到您需要的集成？您可以使用我们的API创建自定义集成。
        </p>
        <div className="flex gap-4">
          <Button>查看API文档</Button>
          <Button variant="outline">联系技术支持</Button>
        </div>
      </Card>

      {/* 配置说明 */}
      <Card className="p-6 mt-8">
        <h2 className="text-2xl font-semibold mb-4">配置说明</h2>
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold mb-2">⚠️ 重要提示</h3>
            <p className="text-gray-600 text-sm">
              部分集成需要您提供相应的API密钥或配置信息。请确保从官方渠道获取这些信息，并妥善保管。
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">🔧 配置步骤</h3>
            <ol className="text-gray-600 text-sm space-y-1 list-decimal list-inside">
              <li>选择要集成的服务</li>
              <li>在对应平台注册账号并获取API密钥</li>
              <li>在Newzora后台配置相关参数</li>
              <li>测试集成是否正常工作</li>
            </ol>
          </div>
        </div>
      </Card>
    </div>
  );
}