'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useToast } from '@/components/Toast';
import { TagIcon, PlusIcon, XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface Tag {
  id: number;
  name: string;
  description?: string;
  color: string;
  usageCount: number;
  isSelected?: boolean;
}

interface UserTagsProps {
  userId?: number; // If provided, show user's tags; otherwise show current user's tags
  editable?: boolean; // Whether tags can be edited
  onTagsChange?: (tags: Tag[]) => void;
  className?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

export default function UserTags({
  userId,
  editable = true,
  onTagsChange,
  className = '',
}: UserTagsProps) {
  const { user, isAuthenticated } = useSimpleAuth();
  // 注意：SimpleAuth不提供session，这里需要根据实际需求调整
  const session = null;
  const token = session?.access_token;
  const toast = useToast();

  const [userTags, setUserTags] = useState<Tag[]>([]);
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddTags, setShowAddTags] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [addingTags, setAddingTags] = useState(false);

  const targetUserId = userId || user?.id;
  const canEdit = editable && isAuthenticated && (!userId || userId === user?.id);

  const fetchUserTags = useCallback(async () => {
    if (!targetUserId) return;

    try {
      const response = await fetch(`${API_BASE_URL}/tags/user/${targetUserId}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserTags(data.data);
          onTagsChange?.(data.data);
        }
      } else {
        console.error('Error fetching user tags');
      }
    } catch (error) {
      console.error('Error fetching user tags:', error);
    }
  }, [targetUserId, onTagsChange]);

  const fetchAvailableTags = useCallback(async () => {
    try {
      const query = searchQuery ? `?search=${encodeURIComponent(searchQuery)}` : '';
      const response = await fetch(`${API_BASE_URL}/tags${query}`);

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // Mark tags as selected if user already has them
          const tagsWithSelection = data.data.map((tag: Tag) => ({
            ...tag,
            isSelected: userTags.some((userTag) => userTag.id === tag.id),
          }));
          setAvailableTags(tagsWithSelection);
        }
      }
    } catch (error) {
      console.error('Error fetching available tags:', error);
    }
  }, [searchQuery, userTags]);

  useEffect(() => {
    fetchUserTags();
  }, [fetchUserTags]);

  useEffect(() => {
    if (showAddTags) {
      fetchAvailableTags();
    }
  }, [showAddTags, fetchAvailableTags]);

  useEffect(() => {
    setLoading(false);
  }, []);

  const handleAddTag = async (tagId: number) => {
    if (!isAuthenticated || !token || addingTags) return;

    setAddingTags(true);

    try {
      const response = await fetch(`${API_BASE_URL}/tags/user`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ tagId }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        await fetchUserTags();
        await fetchAvailableTags();
        toast.success('Tag Added', 'Successfully added interest tag');
      } else {
        toast.error('Add Failed', data.message || 'Unable to add tag');
      }
    } catch (error) {
      console.error('Error adding tag:', error);
      toast.error('Add Failed', 'Network error, please check connection');
    } finally {
      setAddingTags(false);
    }
  };

  const handleRemoveTag = async (tagId: number) => {
    if (!isAuthenticated || !token) return;

    try {
      const response = await fetch(`${API_BASE_URL}/tags/user/${tagId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (response.ok && data.success) {
        await fetchUserTags();
        if (showAddTags) {
          await fetchAvailableTags();
        }
        toast.success('Tag Removed', 'Successfully removed interest tag');
      } else {
        toast.error('Remove Failed', data.message || 'Unable to remove tag');
      }
    } catch (error) {
      console.error('Error removing tag:', error);
      toast.error('Remove Failed', 'Network error, please check connection');
    }
  };

  const getTagColorClasses = (color: string) => {
    const colorMap: { [key: string]: string } = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      green: 'bg-green-100 text-green-800 border-green-200',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      red: 'bg-red-100 text-red-800 border-red-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      pink: 'bg-pink-100 text-pink-800 border-pink-200',
      indigo: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200',
    };
    return colorMap[color] || colorMap['gray'];
  };

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <div className="h-5 w-5 bg-gray-200 rounded animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-20 animate-pulse"></div>
        </div>
        <div className="flex flex-wrap gap-2">
          {[...Array(5)].map((_, index) => (
            <div key={index} className="h-6 bg-gray-200 rounded-full w-16 animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <TagIcon className="h-5 w-5 text-gray-600" />
          <h3 className="text-sm font-medium text-gray-900">
            {userId && userId !== user?.id ? 'Interest Tags' : 'My Interest Tags'}
          </h3>
          <span className="text-xs text-gray-500">({userTags.length})</span>
        </div>
        {canEdit && (
          <button
            onClick={() => setShowAddTags(!showAddTags)}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
          >
            <PlusIcon className="h-4 w-4" />
            <span>Add Tag</span>
          </button>
        )}
      </div>

      {/* User Tags */}
      {userTags.length > 0 ? (
        <div className="flex flex-wrap gap-2">
          {userTags.map((tag) => (
            <div
              key={tag.id}
              className={`
                inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                ${getTagColorClasses(tag.color)}
              `}
            >
              <span>{tag.name}</span>
              {canEdit && (
                <button
                  onClick={() => handleRemoveTag(tag.id)}
                  className="ml-2 hover:text-red-600 transition-colors"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-6 text-gray-500">
          <TagIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm">
            {canEdit ? 'No interest tags added yet' : 'This user has not set any interest tags yet'}
          </p>
          {canEdit && (
            <button
              onClick={() => setShowAddTags(true)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800"
            >
              Add Now
            </button>
          )}
        </div>
      )}

      {/* Add Tags Panel */}
      {showAddTags && canEdit && (
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-gray-900">Add Interest Tags</h4>
            <button
              onClick={() => setShowAddTags(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>

          {/* Search */}
          <div className="relative mb-3">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>

          {/* Available Tags */}
          <div className="flex flex-wrap gap-2 max-h-40 overflow-y-auto">
            {availableTags
              .filter((tag) => !tag.isSelected)
              .map((tag) => (
                <button
                  key={tag.id}
                  onClick={() => handleAddTag(tag.id)}
                  disabled={addingTags}
                  className={`
                    inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border
                    transition-all duration-200 hover:shadow-sm
                    ${getTagColorClasses(tag.color)}
                    hover:opacity-80 disabled:opacity-50 disabled:cursor-not-allowed
                  `}
                  title={tag.description}
                >
                  <PlusIcon className="h-3 w-3 mr-1" />
                  <span>{tag.name}</span>
                  <span className="ml-1 text-xs opacity-60">({tag.usageCount})</span>
                </button>
              ))}
          </div>

          {availableTags.filter((tag) => !tag.isSelected).length === 0 && (
            <div className="text-center py-4 text-gray-500">
              <p className="text-sm">
                {searchQuery ? 'No matching tags found' : 'All tags have been added'}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
