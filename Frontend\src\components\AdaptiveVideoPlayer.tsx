'use client';

import React, { useState, useRef, useEffect } from 'react';
import { VideoResolution, VideoSubtitle } from '@/types';

interface AdaptiveVideoPlayerProps {
  src: string;
  title: string;
  resolutions?: VideoResolution[];
  subtitles?: VideoSubtitle[];
  thumbnail?: string;
  autoplay?: boolean;
  controls?: boolean;
  loop?: boolean;
  muted?: boolean;
}

export default function AdaptiveVideoPlayer({
  src,
  title,
  resolutions = [],
  subtitles = [],
  thumbnail,
  autoplay = false,
  controls = true,
  loop = false,
  muted = false
}: AdaptiveVideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(muted);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [selectedResolution, setSelectedResolution] = useState<VideoResolution | null>(null);
  const [selectedSubtitle, setSelectedSubtitle] = useState<VideoSubtitle | null>(null);
  const [showSettings, setShowSettings] = useState(false);
  const [showResolutions, setShowResolutions] = useState(false);
  const [showSubtitles, setShowSubtitles] = useState(false);
  const [buffered, setBuffered] = useState(0);
  
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 初始化默认分辨率
  useEffect(() => {
    if (resolutions.length > 0) {
      // 选择最接近720p的分辨率作为默认
      const defaultRes = resolutions.find(res => res.quality === '720p') || resolutions[0];
      setSelectedResolution(defaultRes);
    }
  }, [resolutions]);

  // 初始化默认字幕
  useEffect(() => {
    if (subtitles.length > 0) {
      const defaultSubtitle = subtitles.find(sub => sub.isDefault) || null;
      setSelectedSubtitle(defaultSubtitle);
    }
  }, [subtitles]);

  // 视频事件处理
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime);
    };

    const handleLoadedMetadata = () => {
      setDuration(video.duration);
    };

    const handleVolumeChange = () => {
      setVolume(video.volume);
      setIsMuted(video.muted);
    };

    const handlePlay = () => {
      setIsPlaying(true);
    };

    const handlePause = () => {
      setIsPlaying(false);
    };

    const handleProgress = () => {
      if (video.duration > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        setBuffered((bufferedEnd / video.duration) * 100);
      }
    };

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('volumechange', handleVolumeChange);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('progress', handleProgress);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('volumechange', handleVolumeChange);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('progress', handleProgress);
    };
  }, []);

  // 控制栏自动隐藏
  useEffect(() => {
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      
      setShowControls(true);
      
      controlsTimeoutRef.current = setTimeout(() => {
        if (isPlaying) {
          setShowControls(false);
        }
      }, 3000);
    };

    if (isPlaying) {
      resetControlsTimeout();
    } else {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
      setShowControls(true);
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [isPlaying]);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (video.paused || video.ended) {
      video.play();
    } else {
      video.pause();
    }
  };

  const handleSeek = (e: any) => {
    const video = videoRef.current;
    if (!video) return;

    const time = parseFloat(e.target.value);
    video.currentTime = time;
    setCurrentTime(time);
  };

  const handleVolumeChange = (e: any) => {
    const video = videoRef.current;
    if (!video) return;

    const vol = parseFloat(e.target.value);
    video.volume = vol;
    setVolume(vol);
    setIsMuted(vol === 0);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !video.muted;
    setIsMuted(video.muted);
  };

  const changePlaybackRate = (rate: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.playbackRate = rate;
    setPlaybackRate(rate);
    setShowSettings(false);
  };

  const selectResolution = (resolution: VideoResolution) => {
    // 在实际应用中，这里会切换视频源
    setSelectedResolution(resolution);
    setShowResolutions(false);
    setShowSettings(false);
  };

  const selectSubtitle = (subtitle: VideoSubtitle | null) => {
    setSelectedSubtitle(subtitle);
    setShowSubtitles(false);
    setShowSettings(false);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      videoRef.current?.parentElement?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  };

  return (
    <div className="relative bg-black rounded-lg overflow-hidden">
      <div 
        className="relative w-full"
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => {
          if (isPlaying) setShowControls(false);
        }}
        onClick={togglePlay}
      >
        {/* 视频元素 */}
        <video
          ref={videoRef}
          src={selectedResolution?.url || src}
          poster={thumbnail}
          autoPlay={autoplay}
          controls={false}
          loop={loop}
          muted={muted}
          className="w-full aspect-video"
        />
        
        {/* 播放按钮覆盖层 */}
        {!isPlaying && (
          <div className="absolute inset-0 flex items-center justify-center">
            <button 
              className="w-16 h-16 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-75 transition-all"
              onClick={(e) => {
                e.stopPropagation();
                togglePlay();
              }}
            >
              <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        )}
        
        {/* 控制栏 */}
        {controls && showControls && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4 transition-opacity">
            {/* 进度条 */}
            <div className="mb-2">
              <input
                type="range"
                min="0"
                max={duration || 100}
                value={currentTime}
                onChange={handleSeek}
                className="w-full h-1.5 bg-gray-600 rounded-lg appearance-none cursor-pointer"
              />
              <div 
                className="h-1.5 bg-blue-600 rounded-lg -mt-1.5 pointer-events-none"
                style={{ width: `${(buffered / 100) * 100}%` }}
              />
            </div>
            
            {/* 控制按钮 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* 播放/暂停按钮 */}
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    togglePlay();
                  }}
                  className="text-white hover:text-gray-300"
                >
                  {isPlaying ? (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
                
                {/* 音量控制 */}
                <div className="flex items-center space-x-2">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleMute();
                    }}
                    className="text-white hover:text-gray-300"
                  >
                    {isMuted ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM12.293 7.293a1 1 0 011.414 0L15 8.586l1.293-1.293a1 1 0 111.414 1.414L16.414 10l1.293 1.293a1 1 0 01-1.414 1.414L15 11.414l-1.293 1.293a1 1 0 01-1.414-1.414L13.586 10l-1.293-1.293a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.01"
                    value={volume}
                    onChange={handleVolumeChange}
                    className="w-20 h-1.5 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                  />
                </div>
                
                {/* 时间显示 */}
                <div className="text-white text-sm">
                  {formatTime(currentTime)} / {formatTime(duration)}
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* 字幕和设置按钮 */}
                <div className="relative">
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowSettings(!showSettings);
                    }}
                    className="text-white hover:text-gray-300"
                  >
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                    </svg>
                  </button>
                  
                  {/* 设置菜单 */}
                  {showSettings && (
                    <div 
                      className="absolute bottom-full right-0 mb-2 w-48 bg-gray-800 rounded-lg shadow-lg z-10"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <div className="py-1">
                        {/* 播放速度 */}
                        <div className="px-4 py-2 text-gray-300 text-sm">播放速度</div>
                        {[0.5, 0.75, 1, 1.25, 1.5, 2].map((rate) => (
                          <button
                            key={rate}
                            onClick={() => changePlaybackRate(rate)}
                            className={`block w-full text-left px-4 py-2 text-sm ${
                              playbackRate === rate 
                                ? 'text-white bg-blue-600' 
                                : 'text-gray-300 hover:bg-gray-700'
                            }`}
                          >
                            {rate}x
                          </button>
                        ))}
                        
                        <div className="border-t border-gray-700 my-1"></div>
                        
                        {/* 分辨率 */}
                        <button
                          onClick={() => {
                            setShowResolutions(!showResolutions);
                            setShowSubtitles(false);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex justify-between items-center"
                        >
                          <span>分辨率</span>
                          <span className="text-gray-400">
                            {selectedResolution?.quality || '自动'}
                          </span>
                        </button>
                        
                        {/* 字幕 */}
                        <button
                          onClick={() => {
                            setShowSubtitles(!showSubtitles);
                            setShowResolutions(false);
                          }}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-300 hover:bg-gray-700 flex justify-between items-center"
                        >
                          <span>字幕</span>
                          <span className="text-gray-400">
                            {selectedSubtitle?.label || '关闭'}
                          </span>
                        </button>
                      </div>
                      
                      {/* 分辨率子菜单 */}
                      {showResolutions && (
                        <div className="absolute right-full top-0 mr-2 w-32 bg-gray-800 rounded-lg shadow-lg">
                          <div className="py-1">
                            <button
                              onClick={() => selectResolution(null as any)}
                              className={`block w-full text-left px-4 py-2 text-sm ${
                                !selectedResolution 
                                  ? 'text-white bg-blue-600' 
                                  : 'text-gray-300 hover:bg-gray-700'
                              }`}
                            >
                              自动
                            </button>
                            {resolutions.map((res) => (
                              <button
                                key={res.quality}
                                onClick={() => selectResolution(res)}
                                className={`block w-full text-left px-4 py-2 text-sm ${
                                  selectedResolution?.quality === res.quality 
                                    ? 'text-white bg-blue-600' 
                                    : 'text-gray-300 hover:bg-gray-700'
                                }`}
                              >
                                {res.quality}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {/* 字幕子菜单 */}
                      {showSubtitles && (
                        <div className="absolute right-full top-0 mr-2 w-32 bg-gray-800 rounded-lg shadow-lg">
                          <div className="py-1">
                            <button
                              onClick={() => selectSubtitle(null)}
                              className={`block w-full text-left px-4 py-2 text-sm ${
                                !selectedSubtitle 
                                  ? 'text-white bg-blue-600' 
                                  : 'text-gray-300 hover:bg-gray-700'
                              }`}
                            >
                              关闭
                            </button>
                            {subtitles.map((subtitle) => (
                              <button
                                key={subtitle.id}
                                onClick={() => selectSubtitle(subtitle)}
                                className={`block w-full text-left px-4 py-2 text-sm ${
                                  selectedSubtitle?.id === subtitle.id 
                                    ? 'text-white bg-blue-600' 
                                    : 'text-gray-300 hover:bg-gray-700'
                                }`}
                              >
                                {subtitle.label}
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
                
                {/* 全屏按钮 */}
                <button 
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFullscreen();
                  }}
                  className="text-white hover:text-gray-300"
                >
                  {isFullscreen ? (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v2a1 1 0 01-2 0V5a4 4 0 014-4h2a1 1 0 110 2H5zm10 0a1 1 0 110-2h2a4 4 0 014 4v2a1 1 0 11-2 0V5a2 2 0 00-2-2h-2zm-7 12a1 1 0 012 0v2a2 2 0 002 2h2a1 1 0 110 2H5a4 4 0 01-4-4v-2a1 1 0 112 0v2a2 2 0 002 2h2zm8 0a1 1 0 012 0v2a2 2 0 002 2h2a1 1 0 110 2h-2a4 4 0 01-4-4v-2z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 110-2h4a1 1 0 011 1v4a1 1 0 11-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 9a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 110 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 110-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* 视频标题 */}
      <div className="p-4">
        <h3 className="text-lg font-semibold text-white">{title}</h3>
      </div>
    </div>
  );
}