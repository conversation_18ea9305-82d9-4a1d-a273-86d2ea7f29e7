const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ContentReview = sequelize.define('ContentReview', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  content_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['article', 'comment']]
    },
    comment: '内容类型：article-文章, comment-评论'
  },
  content_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: '内容ID'
  },
  reviewer_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  status: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'pending',
    validate: {
      isIn: [['pending', 'approved', 'rejected']]
    }
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: '审核理由，特别是拒绝时的原因'
  },
  auto_review: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
    comment: '是否为自动审核'
  },
  review_score: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    comment: '审核评分，用于自动审核算法'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    allowNull: false
  }
}, {
  tableName: 'content_reviews',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      fields: ['content_type', 'content_id']
    },
    {
      fields: ['status']
    },
    {
      fields: ['reviewer_id']
    },
    {
      fields: ['created_at']
    }
  ]
});

module.exports = ContentReview;