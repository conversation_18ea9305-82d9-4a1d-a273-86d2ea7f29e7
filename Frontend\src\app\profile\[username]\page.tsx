'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import WorkCard from '@/components/WorkCard';
import { Work } from '@/types';
import { mockWorks } from '@/data/mockWorks';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { UserPlusIcon, UserMinusIcon } from '@heroicons/react/24/outline';

interface UserProfile {
  id: number;
  username: string;
  name: string;
  avatar: string;
  bio: string;
  followers: number;
  following: number;
  works: number;
  joinedAt: string;
  isFollowing: boolean;
  isVerified: boolean;
}

export default function ProfilePage() {
  const params = useParams();
  const username = params.username as string;
  const { isAuthenticated } = useSimpleAuth();
  const router = useRouter();

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [userWorks, setUserWorks] = useState<Work[]>([]);
  const [activeTab, setActiveTab] = useState<'works' | 'liked' | 'bookmarked'>('works');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (username) {
      loadUserProfile();
      loadUserWorks();
    }
  }, [username]);

  const loadUserProfile = async () => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 500));

      // 根据用户名提供不同的模拟数据
      const userProfiles: { [key: string]: UserProfile } = {
        'alice_j': {
          id: 1,
          username: 'alice_j',
          name: 'Alice Johnson',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=200&h=200&fit=crop&crop=face',
          bio: 'Content creator and digital marketing specialist. Love sharing insights about social media trends! 📱✨',
          followers: 8500,
          following: 1200,
          works: 89,
          joinedAt: '2023-03-20',
          isFollowing: false,
          isVerified: false,
        },
        'bob_smith': {
          id: 2,
          username: 'bob_smith',
          name: 'Bob Smith',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face',
          bio: 'Software engineer and tech enthusiast. Building the future one line of code at a time. 💻🚀',
          followers: 15200,
          following: 650,
          works: 124,
          joinedAt: '2022-11-10',
          isFollowing: false,
          isVerified: true,
        },
        'carol_d': {
          id: 3,
          username: 'carol_d',
          name: 'Carol Davis',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=200&h=200&fit=crop&crop=face',
          bio: 'Freelance writer and storyteller. Passionate about crafting compelling narratives. 📝📚',
          followers: 6800,
          following: 890,
          works: 67,
          joinedAt: '2023-07-05',
          isFollowing: false,
          isVerified: false,
        },
        'david_w': {
          id: 4,
          username: 'david_w',
          name: 'David Wilson',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face',
          bio: 'Product designer and UX researcher. Creating user-centered experiences. 🎨👥',
          followers: 11300,
          following: 780,
          works: 95,
          joinedAt: '2023-01-28',
          isFollowing: false,
          isVerified: true,
        }
      };

      const mockProfile = userProfiles[username] || {
        id: 999,
        username: username,
        name: username.charAt(0).toUpperCase() + username.slice(1),
        avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(username)}&background=6366f1&color=fff&size=200`,
        bio: 'Welcome to my profile! 👋',
        followers: Math.floor(Math.random() * 10000),
        following: Math.floor(Math.random() * 1000),
        works: Math.floor(Math.random() * 100),
        joinedAt: '2023-06-15',
        isFollowing: false,
        isVerified: false,
      };

      setProfile(mockProfile);
    } catch (error) {
      console.error('Error loading user profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadUserWorks = async () => {
    try {
      const userWorks = mockWorks.filter(
        (work) => typeof work.author === 'object' && work.author.username === username
      );
      setUserWorks(userWorks);
    } catch (error) {
      console.error('Error loading user works:', error);
    }
  };

  const handleFollow = () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    if (profile) {
      setProfile((prev) =>
        prev
          ? {
              ...prev,
              isFollowing: !prev.isFollowing,
              followers: prev.isFollowing ? prev.followers - 1 : prev.followers + 1,
            }
          : null
      );
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="animate-pulse">
            <div className="flex items-start gap-6 mb-8">
              <div className="w-32 h-32 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="text-center py-16">
            <div className="text-6xl mb-4">👤</div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">User not found</h2>
            <p className="text-gray-600">
              The user you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Profile Header */}
        <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
          <div className="flex items-start gap-6">
            {/* Avatar */}
            <div className="relative">
              <img
                src={profile.avatar}
                alt={profile.name}
                className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
              />
              {profile.isVerified && (
                <div className="absolute -bottom-2 -right-2 bg-blue-500 rounded-full p-2">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{profile.name}</h1>
                {profile.isVerified && (
                  <span className="text-blue-500 text-sm font-medium">Verified</span>
                )}
              </div>

              <p className="text-gray-600 text-lg mb-1">@{profile.username}</p>

              <p className="text-gray-700 mb-4 leading-relaxed">{profile.bio}</p>

              <div className="flex items-center gap-6 text-sm text-gray-600 mb-4">
                <span>📅 Joined {formatDate(profile.joinedAt)}</span>
              </div>

              {/* Stats */}
              <div className="flex items-center gap-6 mb-6">
                <button 
                  className="text-center hover:bg-gray-50 p-2 rounded-lg transition-colors"
                  onClick={() => setActiveTab('works')}
                >
                  <div className="text-2xl font-bold text-gray-900">
                    {formatNumber(profile.works)}
                  </div>
                  <div className="text-sm text-gray-600">Works</div>
                </button>
                <button 
                  className="text-center hover:bg-gray-50 p-2 rounded-lg transition-colors"
                  onClick={() => router.push(`/profile/${username}/following?tab=followers`)}
                >
                  <div className="text-2xl font-bold text-gray-900">
                    {formatNumber(profile.followers)}
                  </div>
                  <div className="text-sm text-gray-600">Followers</div>
                </button>
                <button 
                  className="text-center hover:bg-gray-50 p-2 rounded-lg transition-colors"
                  onClick={() => router.push(`/profile/${username}/following?tab=following`)}
                >
                  <div className="text-2xl font-bold text-gray-900">
                    {formatNumber(profile.following)}
                  </div>
                  <div className="text-sm text-gray-600">Following</div>
                </button>
              </div>

              {/* Follow Button */}
              <button
                onClick={handleFollow}
                className={`flex items-center gap-2 px-6 py-2 rounded-lg font-medium transition-colors ${
                  profile.isFollowing
                    ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {profile.isFollowing ? (
                  <>
                    <UserMinusIcon className="w-4 h-4" />
                    Following
                  </>
                ) : (
                  <>
                    <UserPlusIcon className="w-4 h-4" />
                    Follow
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Content Tabs */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex">
              {[
                { key: 'works', label: 'Works', count: userWorks.length },
                { key: 'liked', label: 'Liked', count: 0 },
                { key: 'bookmarked', label: 'Bookmarked', count: 0 },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.key
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'works' && (
              <div className="space-y-6">
                {userWorks.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">📝</div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No works yet</h3>
                    <p className="text-gray-600">This user hasn't published any content yet.</p>
                  </div>
                ) : (
                  userWorks.map((work) => (
                    <WorkCard
                      key={work.id}
                      work={work}
                      layout="horizontal"
                      showImage={true}
                      showAuthor={false}
                      showStats={true}
                      showInteractions={isAuthenticated}
                    />
                  ))
                )}
              </div>
            )}

            {(activeTab === 'liked' || activeTab === 'bookmarked') && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">{activeTab === 'liked' ? '❤️' : '🔖'}</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {activeTab === 'liked' ? 'No liked content' : 'No bookmarked content'}
                </h3>
                <p className="text-gray-600">
                  {activeTab === 'liked'
                    ? "This user hasn't liked any content yet."
                    : "This user hasn't bookmarked any content yet."}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>


    </div>
  );
}