@echo off
echo 正在推送Newzora项目到GitHub仓库...

echo.
echo ========================================
echo 1. 推送主仓库 (newzora.git)
echo ========================================
cd /d "d:\Newzora"
git add .
git commit -m "项目完整更新 - 安全修复、配置完善、测试验证、文档整理"
git push origin main
if %errorlevel% neq 0 (
    echo 主仓库推送失败，请检查网络连接
    pause
    exit /b 1
)
echo 主仓库推送成功！

echo.
echo ========================================
echo 2. 推送私有仓库 (newzora-Private.git)
echo ========================================
cd /d "d:\Newzora\newzora-private"
git init
git add .
git commit -m "Newzora私有仓库 - 核心业务代码和配置"
git branch -M main
git remote add origin https://github.com/Jacken22/newzora-Private.git
git push -u origin main
if %errorlevel% neq 0 (
    echo 私有仓库推送失败，请检查网络连接和仓库权限
    pause
    exit /b 1
)
echo 私有仓库推送成功！

echo.
echo ========================================
echo 3. 推送公共仓库 (newzora-Public.git)
echo ========================================
cd /d "d:\Newzora\newzora-public"
git init
git add .
git commit -m "Newzora公共仓库 - 前端展示和文档"
git branch -M main
git remote add origin https://github.com/Jacken22/newzora-Public.git
git push -u origin main
if %errorlevel% neq 0 (
    echo 公共仓库推送失败，请检查网络连接和仓库权限
    pause
    exit /b 1
)
echo 公共仓库推送成功！

echo.
echo ========================================
echo 所有仓库推送完成！
echo ========================================
echo 主仓库: https://github.com/Jacken22/newzora.git
echo 私有仓库: https://github.com/Jacken22/newzora-Private.git
echo 公共仓库: https://github.com/Jacken22/newzora-Public.git
echo.
pause