'use client';

import React, { useState, useEffect } from 'react';
import { Users, FileText, MessageCircle, Activity, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import DateRangeSelector, { DateRange } from '@/components/admin/common/DateRangeSelector';
import { dashboardService } from '@/services/dashboardService';
import { DashboardStats } from '@/types/admin';
import { getPredefinedDateRanges, formatDateRange } from '@/utils/dateFilters';

interface StatCard {
  title: string;
  value: number;
  previousValue: number;
  icon: React.ComponentType<any>;
  color: string;
}

const DashboardTestPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [previousStats, setPreviousStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Array<{
    range: string;
    stats: DashboardStats;
    timestamp: string;
  }>>([]);

  const handleDateRangeChange = async (range: string, dateRange: DateRange) => {
    setTimeRange(range);
    setCurrentDateRange(dateRange);
    
    try {
      setLoading(true);
      
      // 获取当前日期范围的统计数据
      const filters: any = { timeRange: range };
      if (dateRange) {
        filters.dateRange = dateRange;
      }
      
      const currentStats = await dashboardService.getStats(filters);
      
      // 保存之前的统计数据用于比较
      if (stats) {
        setPreviousStats(stats);
      }
      
      setStats(currentStats);
      
      // 记录测试结果
      setTestResults(prev => [...prev, {
        range,
        stats: currentStats,
        timestamp: new Date().toLocaleString()
      }].slice(-10)); // 只保留最近10次测试结果
      
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    const initialRange = getPredefinedDateRanges().find(r => r.value === '7d');
    if (initialRange) {
      const now = new Date();
      const startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
      
      const dateRange: DateRange = {
        value: '7d',
        label: initialRange.label,
        startDate,
        endDate: now
      };
      
      handleDateRangeChange('7d', dateRange);
    }
  }, []);

  const calculateChange = (current: number, previous: number): { percentage: number; trend: 'up' | 'down' | 'same' } => {
    if (previous === 0) return { percentage: 0, trend: 'same' };
    
    const percentage = ((current - previous) / previous) * 100;
    
    if (percentage > 0) return { percentage, trend: 'up' };
    if (percentage < 0) return { percentage: Math.abs(percentage), trend: 'down' };
    return { percentage: 0, trend: 'same' };
  };

  const renderStatCard = ({ title, value, previousValue, icon: Icon, color }: StatCard) => {
    const change = calculateChange(value, previousValue);
    
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">{title}</p>
            <p className="text-2xl font-bold text-gray-900">{value.toLocaleString()}</p>
            {previousValue > 0 && (
              <div className="flex items-center mt-2">
                {change.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-500 mr-1" />}
                {change.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-500 mr-1" />}
                {change.trend === 'same' && <Minus className="w-4 h-4 text-gray-500 mr-1" />}
                <span className={`text-sm ${
                  change.trend === 'up' ? 'text-green-600' : 
                  change.trend === 'down' ? 'text-red-600' : 
                  'text-gray-600'
                }`}>
                  {change.percentage.toFixed(1)}% vs previous
                </span>
              </div>
            )}
          </div>
          <div className={`p-3 rounded-full bg-${color}-100`}>
            <Icon className={`w-6 h-6 text-${color}-600`} />
          </div>
        </div>
      </div>
    );
  };

  const statCards: StatCard[] = stats ? [
    {
      title: 'Total Users',
      value: stats.users.total,
      previousValue: previousStats?.users.total || 0,
      icon: Users,
      color: 'blue'
    },
    {
      title: 'Total Articles',
      value: stats.content.totalArticles,
      previousValue: previousStats?.content.totalArticles || 0,
      icon: FileText,
      color: 'green'
    },
    {
      title: 'Total Comments',
      value: stats.engagement.totalComments,
      previousValue: previousStats?.engagement.totalComments || 0,
      icon: MessageCircle,
      color: 'yellow'
    },
    {
      title: 'Active Users',
      value: stats.users.activeUsers,
      previousValue: previousStats?.users.activeUsers || 0,
      icon: Activity,
      color: 'red'
    }
  ] : [];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard Data Consistency Test</h1>
          <p className="text-gray-600 mt-2">Test how dashboard metrics respond to date range changes</p>
        </div>
        <div className="flex items-center space-x-4">
          <DateRangeSelector
            value={timeRange}
            onChange={handleDateRangeChange}
            options={getPredefinedDateRanges().filter(opt => 
              ['today', '7d', '30d', '90d', '1y'].includes(opt.value)
            )}
            showIcon={true}
          />
          {loading && (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          )}
        </div>
      </div>

      {/* Current Date Range Info */}
      {currentDateRange && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-blue-900 mb-2">Current Selection</h3>
          <p className="text-blue-700">
            <strong>Range:</strong> {currentDateRange.label} 
            ({formatDateRange(currentDateRange.startDate, currentDateRange.endDate)})
          </p>
          <p className="text-blue-600 text-sm mt-1">
            All metrics below should reflect data within this date range
          </p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => (
          <div key={index}>
            {renderStatCard(card)}
          </div>
        ))}
      </div>

      {/* Test Results History */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Test History</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Range
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Users
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Articles
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Comments
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Active Users
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {testResults.slice().reverse().map((result, index) => (
                <tr key={index} className={index === 0 ? 'bg-blue-50' : ''}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {result.timestamp}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {result.range}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {result.stats.users.total.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {result.stats.content.totalArticles.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {result.stats.engagement.totalComments.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {result.stats.users.activeUsers.toLocaleString()}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {testResults.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No test results yet. Select different date ranges to see how metrics change.
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Testing Instructions</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p>• <strong>Select different date ranges</strong> using the dropdown above</p>
          <p>• <strong>Observe how metrics change</strong> - they should reflect the selected time period</p>
          <p>• <strong>Check the trend indicators</strong> - they show changes compared to the previous selection</p>
          <p>• <strong>Review the test history</strong> - it shows how metrics varied across different ranges</p>
          <p>• <strong>Expected behavior:</strong> Shorter ranges should generally show smaller numbers</p>
        </div>
      </div>
    </div>
  );
};

export default DashboardTestPage;
