'use client';

import { useEffect } from 'react';
import { SimpleAuthProvider } from '@/contexts/SimpleAuthContext';
import { DataProvider } from '@/contexts/DataContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { NotificationProvider } from '@/contexts/NotificationContext';
import { ToastProvider } from '@/components/Toast';
import ErrorBoundary from '@/components/ErrorBoundary';
import { initializeGlobalErrorHandler } from '@/utils/globalErrorHandler';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  useEffect(() => {
    // Initialize global error handler on client side
    initializeGlobalErrorHandler();
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider>
        <SimpleAuthProvider>
          <DataProvider>
            <NotificationProvider>
              <ToastProvider>
                {children}
              </ToastProvider>
            </NotificationProvider>
          </DataProvider>
        </SimpleAuthProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}