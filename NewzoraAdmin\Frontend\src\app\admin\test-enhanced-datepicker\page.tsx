'use client';

import React, { useState } from 'react';
import { Calendar, CheckCircle, XCircle, Info } from 'lucide-react';
import EnhancedDatePicker, { DateRange } from '@/components/admin/common/EnhancedDatePicker';

const TestEnhancedDatePickerPage: React.FC = () => {
  const [selectedRange, setSelectedRange] = useState<DateRange | null>(null);
  const [testResults, setTestResults] = useState<Array<{
    name: string;
    range: DateRange;
    timestamp: string;
    daysDiff: number;
  }>>([]);

  const handleDateRangeChange = (dateRange: DateRange) => {
    setSelectedRange(dateRange);
    
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    setTestResults(prev => [...prev, {
      name: dateRange.label,
      range: dateRange,
      timestamp: new Date().toLocaleString(),
      daysDiff
    }].slice(-10)); // 只保留最近10次测试
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  const testScenarios = [
    {
      name: '今日测试',
      description: '选择今天，验证单日选择功能',
      expected: '开始和结束日期应该是同一天'
    },
    {
      name: '快速选择测试',
      description: '使用预设的快速选择按钮',
      expected: '应该正确设置对应的日期范围'
    },
    {
      name: '数字输入测试',
      description: '在数字输入框中输入天数（如：30）',
      expected: '应该自动选择最近30天的范围'
    },
    {
      name: '日历点击测试',
      description: '在日历上点击选择开始和结束日期',
      expected: '应该正确设置自定义日期范围'
    },
    {
      name: '跨月测试',
      description: '选择跨越月份边界的日期范围',
      expected: '应该正确处理月份切换'
    },
    {
      name: '边界测试',
      description: '选择同一天作为开始和结束日期',
      expected: '应该正确处理单日范围'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enhanced Date Picker Testing</h1>
          <p className="text-gray-600 mt-2">Test the enhanced date picker with calendar and quick number input</p>
        </div>
        <div className="flex items-center space-x-2">
          <Calendar className="w-6 h-6 text-blue-600" />
          <span className="text-sm text-gray-600">Interactive Testing</span>
        </div>
      </div>

      {/* Date Picker Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Date Picker Demo</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Select Date Range</h3>
            <EnhancedDatePicker
              value={selectedRange}
              onChange={handleDateRangeChange}
              placeholder="Click to select date range"
              className="w-full"
            />
            
            {selectedRange && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Selected Range</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p><strong>Label:</strong> {selectedRange.label}</p>
                  <p><strong>Start:</strong> {formatDate(selectedRange.startDate)}</p>
                  <p><strong>End:</strong> {formatDate(selectedRange.endDate)}</p>
                  <p><strong>Duration:</strong> {Math.ceil(
                    (selectedRange.endDate.getTime() - selectedRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
                  )} days</p>
                </div>
              </div>
            )}
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Features</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Quick Select Buttons</p>
                  <p className="text-sm text-gray-600">Pre-defined ranges like "Last 7 days", "Last 30 days"</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Number Input</p>
                  <p className="text-sm text-gray-600">Type a number to quickly select "Last X days"</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Calendar Interface</p>
                  <p className="text-sm text-gray-600">Visual calendar for precise date selection</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Range Selection</p>
                  <p className="text-sm text-gray-600">Click start date, then end date for custom ranges</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Test Scenarios */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Scenarios</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {testScenarios.map((scenario, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="font-medium text-gray-900">{scenario.name}</h3>
                  <p className="text-sm text-gray-600 mt-1">{scenario.description}</p>
                  <p className="text-xs text-blue-600 mt-2"><strong>Expected:</strong> {scenario.expected}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Test Results History */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results History</h2>
        {testResults.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Label
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Start Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    End Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {testResults.slice().reverse().map((result, index) => (
                  <tr key={index} className={index === 0 ? 'bg-blue-50' : ''}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {result.timestamp}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {result.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(result.range.startDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatDate(result.range.endDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {result.daysDiff} days
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No test results yet. Use the date picker above to start testing.</p>
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-3">How to Test</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p>• <strong>Quick Select:</strong> Click on preset buttons like "Last 7 days" or "Last 30 days"</p>
          <p>• <strong>Number Input:</strong> Type a number in the "Last X days" input field (e.g., 45 for last 45 days)</p>
          <p>• <strong>Calendar Selection:</strong> Click on the calendar to select start and end dates manually</p>
          <p>• <strong>Range Selection:</strong> Click start date first, then click end date to complete the range</p>
          <p>• <strong>Navigation:</strong> Use arrow buttons to navigate between months</p>
          <p>• <strong>Clear:</strong> Use the "Clear" button to reset the selection</p>
        </div>
      </div>

      {/* Integration Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Integration Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[
            { name: 'Analytics Overview', status: 'completed' },
            { name: 'User Analytics', status: 'completed' },
            { name: 'Content Analytics', status: 'completed' },
            { name: 'Monetization', status: 'completed' }
          ].map((page, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <h3 className="font-medium text-gray-900">{page.name}</h3>
                  <p className="text-sm text-green-600 capitalize">{page.status}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TestEnhancedDatePickerPage;
