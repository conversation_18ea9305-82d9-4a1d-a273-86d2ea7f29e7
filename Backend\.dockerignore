# OneNews Backend .dockerignore
# 排除不需要复制到Docker镜像中的文件和目录

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 日志文件
logs/
*.log

# 运行时文件
pids/
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
.nyc_output/

# 依赖目录
.npm
.eslintcache

# 可选的npm缓存目录
.npm

# 可选的REPL历史
.node_repl_history

# 输出的二进制文件
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env

# 上传文件（开发环境）
uploads/

# 临时文件
tmp/
temp/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 测试文件
test/
tests/
__tests__/
*.test.js
*.spec.js

# 文档
README.md
docs/
*.md

# 其他
.editorconfig
.prettierrc
.eslintrc*
