import { DateRange } from '@/components/admin/common/DateRangeSelector';

export interface DateFilterOptions {
  dateField?: string; // The field name to filter by (e.g., 'createdAt', 'publishedAt')
  includeTime?: boolean; // Whether to include time in comparison
}

/**
 * Filters an array of objects based on a date range
 */
export function filterByDateRange<T extends Record<string, any>>(
  data: T[],
  dateRange: DateRange,
  options: DateFilterOptions = {}
): T[] {
  const { dateField = 'createdAt', includeTime = false } = options;

  if (!dateRange || !dateRange.startDate || !dateRange.endDate) {
    return data;
  }

  return data.filter(item => {
    const itemDate = new Date(item[dateField]);
    
    if (isNaN(itemDate.getTime())) {
      return false; // Invalid date
    }

    let startDate = new Date(dateRange.startDate);
    let endDate = new Date(dateRange.endDate);

    if (!includeTime) {
      // Reset time to start/end of day for date-only comparison
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      
      // Also reset item date time for consistent comparison
      const itemDateOnly = new Date(itemDate);
      itemDateOnly.setHours(0, 0, 0, 0);
      
      return itemDateOnly >= startDate && itemDateOnly <= endDate;
    }

    return itemDate >= startDate && itemDate <= endDate;
  });
}

/**
 * Validates if a date falls within a specific range
 */
export function isDateInRange(
  date: Date | string,
  startDate: Date | string,
  endDate: Date | string,
  includeTime: boolean = false
): boolean {
  const targetDate = new Date(date);
  const rangeStart = new Date(startDate);
  const rangeEnd = new Date(endDate);

  if (isNaN(targetDate.getTime()) || isNaN(rangeStart.getTime()) || isNaN(rangeEnd.getTime())) {
    return false;
  }

  if (!includeTime) {
    targetDate.setHours(0, 0, 0, 0);
    rangeStart.setHours(0, 0, 0, 0);
    rangeEnd.setHours(23, 59, 59, 999);
  }

  return targetDate >= rangeStart && targetDate <= rangeEnd;
}

/**
 * Gets predefined date ranges
 */
export function getPredefinedDateRanges(): Array<{ value: string; label: string }> {
  return [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 90 days' },
    { value: '1y', label: 'Last year' },
    { value: 'custom', label: 'Custom range' }
  ];
}

/**
 * Calculates date range based on period string
 */
export function calculateDateRange(period: string): { startDate: Date; endDate: Date } {
  const now = new Date();
  const endDate = new Date(now);
  let startDate = new Date(now);

  switch (period) {
    case 'today':
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    
    case 'yesterday':
      startDate.setDate(now.getDate() - 1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setDate(now.getDate() - 1);
      endDate.setHours(23, 59, 59, 999);
      break;
    
    case 'week':
      const dayOfWeek = now.getDay();
      startDate.setDate(now.getDate() - dayOfWeek);
      startDate.setHours(0, 0, 0, 0);
      break;
    
    case 'month':
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      break;
    
    case '7d':
      startDate.setDate(now.getDate() - 7);
      break;
    
    case '30d':
      startDate.setDate(now.getDate() - 30);
      break;
    
    case '90d':
      startDate.setDate(now.getDate() - 90);
      break;
    
    case '1y':
      startDate.setFullYear(now.getFullYear() - 1);
      break;
    
    default:
      startDate.setDate(now.getDate() - 30);
  }

  return { startDate, endDate };
}

/**
 * Formats date range for display
 */
export function formatDateRange(startDate: Date, endDate: Date): string {
  const start = startDate.toLocaleDateString();
  const end = endDate.toLocaleDateString();
  
  if (start === end) {
    return start;
  }
  
  return `${start} - ${end}`;
}

/**
 * Checks if two date ranges overlap
 */
export function dateRangesOverlap(
  range1Start: Date,
  range1End: Date,
  range2Start: Date,
  range2End: Date
): boolean {
  return range1Start <= range2End && range2Start <= range1End;
}

/**
 * Gets the number of days between two dates
 */
export function getDaysBetween(startDate: Date, endDate: Date): number {
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

/**
 * Validates date range (start date should be before end date)
 */
export function validateDateRange(startDate: Date, endDate: Date): {
  isValid: boolean;
  error?: string;
} {
  if (isNaN(startDate.getTime())) {
    return { isValid: false, error: 'Invalid start date' };
  }
  
  if (isNaN(endDate.getTime())) {
    return { isValid: false, error: 'Invalid end date' };
  }
  
  if (startDate > endDate) {
    return { isValid: false, error: 'Start date cannot be after end date' };
  }
  
  const now = new Date();
  if (startDate > now) {
    return { isValid: false, error: 'Start date cannot be in the future' };
  }
  
  if (endDate > now) {
    return { isValid: false, error: 'End date cannot be in the future' };
  }
  
  return { isValid: true };
}

/**
 * Groups data by date periods (day, week, month)
 */
export function groupByDatePeriod<T extends Record<string, any>>(
  data: T[],
  dateField: string = 'createdAt',
  period: 'day' | 'week' | 'month' = 'day'
): Record<string, T[]> {
  const groups: Record<string, T[]> = {};

  data.forEach(item => {
    const date = new Date(item[dateField]);
    if (isNaN(date.getTime())) return;

    let key: string;
    
    switch (period) {
      case 'day':
        key = date.toISOString().split('T')[0]; // YYYY-MM-DD
        break;
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split('T')[0];
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      default:
        key = date.toISOString().split('T')[0];
    }

    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(item);
  });

  return groups;
}

/**
 * Gets date range boundaries for edge case testing
 */
export function getDateRangeBoundaries() {
  const now = new Date();
  
  return {
    // Same day boundaries
    startOfToday: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0),
    endOfToday: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999),
    
    // Month boundaries
    startOfMonth: new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0),
    endOfMonth: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999),
    
    // Year boundaries
    startOfYear: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
    endOfYear: new Date(now.getFullYear(), 11, 31, 23, 59, 59, 999),
    
    // Week boundaries
    startOfWeek: (() => {
      const date = new Date(now);
      date.setDate(now.getDate() - now.getDay());
      date.setHours(0, 0, 0, 0);
      return date;
    })(),
    endOfWeek: (() => {
      const date = new Date(now);
      date.setDate(now.getDate() + (6 - now.getDay()));
      date.setHours(23, 59, 59, 999);
      return date;
    })()
  };
}
