/**
 * 监控和指标收集配置
 */

const { logger, performanceLogger } = require('./logger');

/**
 * 系统监控配置
 */
const MONITORING_CONFIG = {
  // 健康检查配置
  healthCheck: {
    enabled: true,
    interval: 30000, // 30秒
    timeout: 5000,   // 5秒超时
    endpoints: [
      '/health',
      '/health/db',
      '/health/redis',
      '/health/email'
    ]
  },

  // 性能监控配置
  performance: {
    enabled: true,
    slowQueryThreshold: 1000, // 1秒
    slowRequestThreshold: 2000, // 2秒
    memoryThreshold: 0.8, // 80%内存使用率
    cpuThreshold: 0.8 // 80%CPU使用率
  },

  // 错误监控配置
  errorTracking: {
    enabled: true,
    maxErrorsPerMinute: 10,
    alertThreshold: 5
  },

  // 指标收集配置
  metrics: {
    enabled: true,
    collectInterval: 60000, // 1分钟
    retentionDays: 30
  }
};

/**
 * 系统指标收集器
 */
class SystemMetrics {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        success: 0,
        error: 0,
        responseTime: []
      },
      database: {
        queries: 0,
        slowQueries: 0,
        errors: 0,
        connectionPool: {
          active: 0,
          idle: 0,
          total: 0
        }
      },
      memory: {
        used: 0,
        free: 0,
        total: 0,
        percentage: 0
      },
      cpu: {
        usage: 0,
        loadAverage: []
      },
      errors: {
        count: 0,
        types: new Map()
      }
    };
    
    this.startCollection();
  }

  /**
   * 开始指标收集
   */
  startCollection() {
    if (!MONITORING_CONFIG.metrics.enabled) return;

    setInterval(() => {
      this.collectSystemMetrics();
    }, MONITORING_CONFIG.metrics.collectInterval);

    logger.info('System metrics collection started');
  }

  /**
   * 收集系统指标
   */
  collectSystemMetrics() {
    try {
      // 收集内存指标
      const memUsage = process.memoryUsage();
      this.metrics.memory = {
        used: memUsage.heapUsed,
        free: memUsage.heapTotal - memUsage.heapUsed,
        total: memUsage.heapTotal,
        percentage: memUsage.heapUsed / memUsage.heapTotal
      };

      // 收集CPU指标
      const cpuUsage = process.cpuUsage();
      this.metrics.cpu = {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // 转换为秒
        loadAverage: require('os').loadavg()
      };

      // 记录性能指标
      performanceLogger.info('System metrics collected', {
        memory: this.metrics.memory,
        cpu: this.metrics.cpu,
        timestamp: new Date().toISOString()
      });

      // 检查阈值并发出警报
      this.checkThresholds();
    } catch (error) {
      logger.error('Error collecting system metrics', { error: error.message });
    }
  }

  /**
   * 记录请求指标
   */
  recordRequest(success, responseTime) {
    this.metrics.requests.total++;
    if (success) {
      this.metrics.requests.success++;
    } else {
      this.metrics.requests.error++;
    }
    
    this.metrics.requests.responseTime.push(responseTime);
    
    // 保持响应时间数组大小
    if (this.metrics.requests.responseTime.length > 1000) {
      this.metrics.requests.responseTime = this.metrics.requests.responseTime.slice(-1000);
    }

    // 检查慢请求
    if (responseTime > MONITORING_CONFIG.performance.slowRequestThreshold) {
      logger.warn('Slow request detected', {
        responseTime,
        threshold: MONITORING_CONFIG.performance.slowRequestThreshold
      });
    }
  }

  /**
   * 记录数据库指标
   */
  recordDatabaseQuery(duration, error = null) {
    this.metrics.database.queries++;
    
    if (error) {
      this.metrics.database.errors++;
    }

    if (duration > MONITORING_CONFIG.performance.slowQueryThreshold) {
      this.metrics.database.slowQueries++;
      logger.warn('Slow database query detected', {
        duration,
        threshold: MONITORING_CONFIG.performance.slowQueryThreshold
      });
    }
  }

  /**
   * 记录错误
   */
  recordError(error, type = 'unknown') {
    this.metrics.errors.count++;
    
    const currentCount = this.metrics.errors.types.get(type) || 0;
    this.metrics.errors.types.set(type, currentCount + 1);

    logger.error('Error recorded in metrics', {
      error: error.message,
      type,
      totalErrors: this.metrics.errors.count
    });
  }

  /**
   * 检查阈值
   */
  checkThresholds() {
    // 检查内存使用率
    if (this.metrics.memory.percentage > MONITORING_CONFIG.performance.memoryThreshold) {
      logger.warn('High memory usage detected', {
        usage: this.metrics.memory.percentage,
        threshold: MONITORING_CONFIG.performance.memoryThreshold
      });
    }

    // 检查CPU使用率
    const avgLoad = this.metrics.cpu.loadAverage[0];
    const cpuCores = require('os').cpus().length;
    const cpuUsagePercent = avgLoad / cpuCores;

    if (cpuUsagePercent > MONITORING_CONFIG.performance.cpuThreshold) {
      logger.warn('High CPU usage detected', {
        usage: cpuUsagePercent,
        threshold: MONITORING_CONFIG.performance.cpuThreshold,
        loadAverage: this.metrics.cpu.loadAverage
      });
    }
  }

  /**
   * 获取指标摘要
   */
  getMetricsSummary() {
    const responseTimeAvg = this.metrics.requests.responseTime.length > 0
      ? this.metrics.requests.responseTime.reduce((a, b) => a + b, 0) / this.metrics.requests.responseTime.length
      : 0;

    return {
      requests: {
        total: this.metrics.requests.total,
        success: this.metrics.requests.success,
        error: this.metrics.requests.error,
        successRate: this.metrics.requests.total > 0 
          ? (this.metrics.requests.success / this.metrics.requests.total * 100).toFixed(2) + '%'
          : '0%',
        avgResponseTime: Math.round(responseTimeAvg) + 'ms'
      },
      database: {
        queries: this.metrics.database.queries,
        slowQueries: this.metrics.database.slowQueries,
        errors: this.metrics.database.errors,
        slowQueryRate: this.metrics.database.queries > 0
          ? (this.metrics.database.slowQueries / this.metrics.database.queries * 100).toFixed(2) + '%'
          : '0%'
      },
      system: {
        memoryUsage: (this.metrics.memory.percentage * 100).toFixed(2) + '%',
        cpuLoad: this.metrics.cpu.loadAverage[0].toFixed(2),
        uptime: Math.floor(process.uptime()) + 's'
      },
      errors: {
        total: this.metrics.errors.count,
        types: Object.fromEntries(this.metrics.errors.types)
      }
    };
  }

  /**
   * 重置指标
   */
  resetMetrics() {
    this.metrics = {
      requests: { total: 0, success: 0, error: 0, responseTime: [] },
      database: { queries: 0, slowQueries: 0, errors: 0, connectionPool: { active: 0, idle: 0, total: 0 } },
      memory: { used: 0, free: 0, total: 0, percentage: 0 },
      cpu: { usage: 0, loadAverage: [] },
      errors: { count: 0, types: new Map() }
    };
    
    logger.info('System metrics reset');
  }
}

/**
 * 健康检查管理器
 */
class HealthCheckManager {
  constructor() {
    this.checks = new Map();
    this.results = new Map();
    this.setupDefaultChecks();
  }

  /**
   * 设置默认健康检查
   */
  setupDefaultChecks() {
    // 数据库健康检查
    this.addCheck('database', async () => {
      try {
        const { sequelize } = require('../models');
        await sequelize.authenticate();
        return { status: 'healthy', message: 'Database connection OK' };
      } catch (error) {
        return { status: 'unhealthy', message: `Database error: ${error.message}` };
      }
    });

    // Redis健康检查
    this.addCheck('redis', async () => {
      try {
        // 这里应该添加Redis连接检查
        return { status: 'healthy', message: 'Redis connection OK' };
      } catch (error) {
        return { status: 'unhealthy', message: `Redis error: ${error.message}` };
      }
    });

    // 邮件服务健康检查
    this.addCheck('email', async () => {
      try {
        const { testEmailConfig } = require('../services/emailService');
        const isHealthy = await testEmailConfig();
        return isHealthy 
          ? { status: 'healthy', message: 'Email service OK' }
          : { status: 'unhealthy', message: 'Email service unavailable' };
      } catch (error) {
        return { status: 'unhealthy', message: `Email error: ${error.message}` };
      }
    });
  }

  /**
   * 添加健康检查
   */
  addCheck(name, checkFunction) {
    this.checks.set(name, checkFunction);
  }

  /**
   * 执行所有健康检查
   */
  async runAllChecks() {
    const results = {};
    let overallStatus = 'healthy';

    for (const [name, checkFunction] of this.checks) {
      try {
        const result = await Promise.race([
          checkFunction(),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), 
            MONITORING_CONFIG.healthCheck.timeout)
          )
        ]);

        results[name] = result;
        if (result.status !== 'healthy') {
          overallStatus = 'unhealthy';
        }
      } catch (error) {
        results[name] = {
          status: 'unhealthy',
          message: `Check failed: ${error.message}`
        };
        overallStatus = 'unhealthy';
      }
    }

    const healthReport = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      checks: results,
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0'
    };

    this.results.set('latest', healthReport);
    return healthReport;
  }

  /**
   * 获取最新健康检查结果
   */
  getLatestResults() {
    return this.results.get('latest') || {
      status: 'unknown',
      message: 'No health checks performed yet'
    };
  }
}

// 创建全局实例
const systemMetrics = new SystemMetrics();
const healthCheckManager = new HealthCheckManager();

/**
 * 请求监控中间件
 */
const requestMonitoring = (req, res, next) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    const success = res.statusCode < 400;
    
    systemMetrics.recordRequest(success, responseTime);
    
    // 记录请求日志
    logger.info('Request completed', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: responseTime + 'ms',
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
  });

  next();
};

module.exports = {
  MONITORING_CONFIG,
  SystemMetrics,
  HealthCheckManager,
  systemMetrics,
  healthCheckManager,
  requestMonitoring
};