const { Tag } = require('../models/associations');

const seedTags = async () => {
  try {
    const tags = [
      {
        name: 'Technology',
        slug: 'technology',
        description: 'Latest technology trends and innovations',
        color: '#3B82F6',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Business',
        slug: 'business',
        description: 'Business news and insights',
        color: '#10B981',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Health',
        slug: 'health',
        description: 'Health and wellness topics',
        color: '#F59E0B',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Science',
        slug: 'science',
        description: 'Scientific discoveries and research',
        color: '#8B5CF6',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Sports',
        slug: 'sports',
        description: 'Sports news and updates',
        color: '#EF4444',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Entertainment',
        slug: 'entertainment',
        description: 'Entertainment and celebrity news',
        color: '#EC4899',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Politics',
        slug: 'politics',
        description: 'Political news and analysis',
        color: '#6B7280',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Environment',
        slug: 'environment',
        description: 'Environmental issues and sustainability',
        color: '#059669',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Education',
        slug: 'education',
        description: 'Education and learning resources',
        color: '#DC2626',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Travel',
        slug: 'travel',
        description: 'Travel guides and destinations',
        color: '#0891B2',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Food',
        slug: 'food',
        description: 'Food, recipes, and culinary trends',
        color: '#EA580C',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Fashion',
        slug: 'fashion',
        description: 'Fashion trends and style guides',
        color: '#BE185D',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Finance',
        slug: 'finance',
        description: 'Financial news and investment advice',
        color: '#16A34A',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Gaming',
        slug: 'gaming',
        description: 'Video games and gaming industry news',
        color: '#7C3AED',
        isActive: true,
        usageCount: 0,
      },
      {
        name: 'Art',
        slug: 'art',
        description: 'Art, culture, and creative content',
        color: '#DB2777',
        isActive: true,
        usageCount: 0,
      },
    ];

    // Check if tags already exist
    const existingTags = await Tag.count();
    if (existingTags > 0) {
      console.log('Tags already exist, skipping seed...');
      return;
    }

    // Create tags
    await Tag.bulkCreate(tags);
    console.log('✅ Tags seeded successfully!');
    console.log(`Created ${tags.length} tags`);
  } catch (error) {
    console.error('❌ Error seeding tags:', error);
  }
};

// Run if called directly
if (require.main === module) {
  seedTags().then(() => process.exit(0));
}

module.exports = seedTags;
