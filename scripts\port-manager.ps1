# Newzora 端口管理脚本
# 用于检查和管理开发服务器端口

param(
    [string]$Action = "check",
    [int]$Port = 3000
)

Write-Host "🔧 Newzora 端口管理工具" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan
Write-Host ""

function Check-Port {
    param([int]$PortNumber)
    
    Write-Host "🔍 检查端口 $PortNumber..." -ForegroundColor Yellow
    
    $connections = netstat -ano | Select-String ":$PortNumber"
    
    if ($connections) {
        Write-Host "⚠️  端口 $PortNumber 被占用!" -ForegroundColor Red
        Write-Host ""
        Write-Host "占用详情:" -ForegroundColor White
        $connections | ForEach-Object {
            $line = $_.Line.Trim()
            $parts = $line -split '\s+'
            if ($parts.Length -ge 5) {
                $protocol = $parts[0]
                $localAddr = $parts[1]
                $state = $parts[3]
                $pid = $parts[4]
                
                # 获取进程名
                try {
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    $processName = if ($process) { $process.ProcessName } else { "Unknown" }
                    Write-Host "  📍 $protocol $localAddr [$state] - PID: $pid ($processName)" -ForegroundColor Gray
                } catch {
                    Write-Host "  📍 $protocol $localAddr [$state] - PID: $pid" -ForegroundColor Gray
                }
            }
        }
        return $false
    } else {
        Write-Host "✅ 端口 $PortNumber 可用!" -ForegroundColor Green
        return $true
    }
}

function Kill-PortProcesses {
    param([int]$PortNumber)
    
    Write-Host "🔄 正在释放端口 $PortNumber..." -ForegroundColor Yellow
    
    $connections = netstat -ano | Select-String ":$PortNumber"
    $pids = @()
    
    $connections | ForEach-Object {
        $line = $_.Line.Trim()
        $parts = $line -split '\s+'
        if ($parts.Length -ge 5) {
            $pid = $parts[4]
            if ($pid -match '^\d+$' -and $pids -notcontains $pid) {
                $pids += $pid
            }
        }
    }
    
    if ($pids.Count -eq 0) {
        Write-Host "ℹ️  没有找到占用端口 $PortNumber 的进程" -ForegroundColor Blue
        return
    }
    
    foreach ($pid in $pids) {
        try {
            $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
            if ($process) {
                Write-Host "🔫 终止进程: $($process.ProcessName) (PID: $pid)" -ForegroundColor Red
                Stop-Process -Id $pid -Force
                Write-Host "✅ 进程 $pid 已终止" -ForegroundColor Green
            }
        } catch {
            Write-Host "❌ 无法终止进程 $pid : $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    Start-Sleep -Seconds 2
    Write-Host ""
    Check-Port -PortNumber $PortNumber
}

function Start-DevServer {
    Write-Host "🚀 启动 Newzora 前端开发服务器..." -ForegroundColor Green
    Write-Host ""
    
    # 检查是否在正确的目录
    if (-not (Test-Path "Frontend/package.json")) {
        Write-Host "❌ 错误: 请在项目根目录运行此脚本" -ForegroundColor Red
        Write-Host "   当前目录: $(Get-Location)" -ForegroundColor Gray
        Write-Host "   应该包含 Frontend/package.json 文件" -ForegroundColor Gray
        return
    }
    
    Set-Location "Frontend"
    
    try {
        Write-Host "📦 检查依赖..." -ForegroundColor Yellow
        if (-not (Test-Path "node_modules")) {
            Write-Host "📥 安装依赖..." -ForegroundColor Yellow
            npm install
        }
        
        Write-Host "🎯 启动开发服务器在端口 3000..." -ForegroundColor Green
        npm run dev
    } catch {
        Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
    } finally {
        Set-Location ".."
    }
}

function Show-Help {
    Write-Host "📖 使用说明:" -ForegroundColor White
    Write-Host ""
    Write-Host "检查端口:" -ForegroundColor Yellow
    Write-Host "  .\scripts\port-manager.ps1 -Action check -Port 3000" -ForegroundColor Gray
    Write-Host ""
    Write-Host "释放端口:" -ForegroundColor Yellow
    Write-Host "  .\scripts\port-manager.ps1 -Action kill -Port 3000" -ForegroundColor Gray
    Write-Host ""
    Write-Host "启动开发服务器:" -ForegroundColor Yellow
    Write-Host "  .\scripts\port-manager.ps1 -Action start" -ForegroundColor Gray
    Write-Host ""
    Write-Host "检查常用端口:" -ForegroundColor Yellow
    Write-Host "  .\scripts\port-manager.ps1 -Action checkall" -ForegroundColor Gray
    Write-Host ""
}

# 主逻辑
switch ($Action.ToLower()) {
    "check" {
        Check-Port -PortNumber $Port
    }
    "kill" {
        Kill-PortProcesses -PortNumber $Port
    }
    "start" {
        # 先检查并释放端口3000
        if (-not (Check-Port -PortNumber 3000)) {
            $response = Read-Host "是否要释放端口 3000? (y/n)"
            if ($response -eq "y" -or $response -eq "Y") {
                Kill-PortProcesses -PortNumber 3000
            } else {
                Write-Host "❌ 取消启动" -ForegroundColor Red
                return
            }
        }
        Start-DevServer
    }
    "checkall" {
        Write-Host "🔍 检查常用开发端口..." -ForegroundColor Yellow
        Write-Host ""
        
        $ports = @(3000, 3001, 5000, 5001, 8080, 8081)
        foreach ($p in $ports) {
            Write-Host "端口 $p :" -NoNewline -ForegroundColor White
            if (Check-Port -PortNumber $p) {
                Write-Host " ✅ 可用" -ForegroundColor Green
            } else {
                Write-Host " ❌ 占用" -ForegroundColor Red
            }
        }
    }
    "help" {
        Show-Help
    }
    default {
        Write-Host "❌ 未知操作: $Action" -ForegroundColor Red
        Write-Host ""
        Show-Help
    }
}

Write-Host ""
Write-Host "🎉 操作完成!" -ForegroundColor Cyan
