# 📧 Supabase邮箱验证配置指南

## 🔍 问题说明

**现象**:
- 注册显示: "Registration successful! Please check your email for verification."
- 登录显示: "Email not confirmed"

**原因**: Supabase默认启用了邮箱验证，用户必须点击验证邮件中的链接才能登录。

---

## 🛠️ 解决方案

### 方案1: 禁用邮箱验证（开发环境推荐）

#### 步骤1: 登录Supabase控制台
1. 访问 https://supabase.com/dashboard
2. 选择您的项目: `wdpprzeflzlardkmncfk`

#### 步骤2: 修改认证设置
1. 点击左侧菜单 **Authentication**
2. 点击 **Settings** 标签
3. 找到 **User Signups** 部分
4. **关闭** "Enable email confirmations" 选项
5. 点击 **Save** 保存设置

#### 步骤3: 清理现有用户（可选）
如果已有未验证的测试用户：
1. 在 **Authentication > Users** 中删除测试用户
2. 或者手动将用户的 `email_confirmed_at` 字段设置为当前时间

---

### 方案2: 配置邮件服务（生产环境推荐）

#### Gmail SMTP配置（已完成）
✅ **邮箱账户**: <EMAIL>  
✅ **应用专用密码**: fqky qojn ysdx voqt  
✅ **SMTP服务器**: smtp.gmail.com:587  
✅ **备用服务**: Resend API (re_Fo38TZfs_M4EnWhofUUaDFWWHEwiY3E4z)

#### 步骤1: 验证SMTP配置
```bash
# 进入Backend目录
cd Backend

# 运行邮件配置测试
node scripts/testEmailConfig.js
```

#### 步骤2: Supabase SMTP设置
1. 在 **Authentication > Settings** 中
2. 找到 **SMTP Settings** 部分
3. 配置Gmail SMTP信息：
   - **Host**: smtp.gmail.com
   - **Port**: 587
   - **Username**: <EMAIL>
   - **Password**: fqky qojn ysdx voqt
   - **Sender email**: <EMAIL>
   - **Sender name**: Newzora

#### 步骤3: 自定义邮件模板
1. 在 **Authentication > Email Templates** 中
2. 自定义确认邮件模板
3. 设置正确的重定向URL

---

### 方案3: 社交登录配置

#### Google OAuth配置
- **Client ID**: 需要在Google Cloud Console配置
- **Client Secret**: 需要在Google Cloud Console配置
- **回调URL**: http://localhost:3000/auth/callback/google

#### 其他社交登录
- **Facebook**: 待配置
- **Twitter/X**: 待配置
- **Apple**: 待配置

---

## 🔧 代码修改

### 更新注册逻辑以处理邮箱验证

```typescript
// Frontend/src/contexts/SupabaseAuthContext.tsx
const register = async (email: string, password: string, metadata?: UserMetadata): Promise<boolean> => {
  try {
    setError(null);
    setIsLoading(true);

    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata || {},
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });

    if (signUpError) {
      setError(signUpError.message);
      return false;
    }

    // 检查是否需要邮箱验证
    if (signUpData.user && !signUpData.session) {
      setError('Registration successful! Please check your email for verification.');
      return false; // 返回false但显示成功消息
    }

    // 如果直接获得session，说明邮箱验证已禁用
    if (signUpData.session && signUpData.user) {
      setSession(signUpData.session);
      await fetchUserProfile(signUpData.user.id);
      setIsAuthenticated(true);
      return true;
    }

    return false;
  } catch (err: any) {
    setError(err.message);
    return false;
  } finally {
    setIsLoading(false);
  }
};
```

### 添加邮箱验证状态处理

```typescript
// 添加邮箱验证检查函数
const checkEmailVerification = async (email: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase
      .from('auth.users')
      .select('email_confirmed_at')
      .eq('email', email)
      .single();
    
    return !error && data?.email_confirmed_at !== null;
  } catch {
    return false;
  }
};
```

---

## 🧪 测试步骤

### 禁用邮箱验证后的测试
1. 清理浏览器缓存和localStorage
2. 尝试注册新用户
3. 应该能够直接登录，无需邮箱验证

### 启用邮箱验证的测试
1. 注册新用户
2. 检查邮箱收到验证邮件
3. 点击验证链接
4. 返回应用尝试登录

---

## 📱 创建邮箱验证回调页面
