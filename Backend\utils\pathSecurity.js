/**
 * 文件路径安全工具 - 防止路径遍历攻击
 */

const path = require('path');
const fs = require('fs').promises;
const { logger, logSecurityEvent } = require('../config/logger');

/**
 * 安全的基础目录列表
 */
const SAFE_BASE_DIRS = [
  path.resolve('./uploads'),
  path.resolve('./public'),
  path.resolve('./temp'),
  path.resolve('./logs')
];

/**
 * 危险的文件扩展名
 */
const DANGEROUS_EXTENSIONS = [
  '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
  '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.sh', '.bash'
];

/**
 * 允许的文件扩展名
 */
const ALLOWED_EXTENSIONS = [
  '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
  '.pdf', '.doc', '.docx', '.txt', '.md',
  '.mp4', '.avi', '.mov', '.wmv',
  '.mp3', '.wav', '.ogg'
];

/**
 * 验证文件路径是否安全
 * @param {string} filePath - 要验证的文件路径
 * @param {string} baseDir - 基础目录
 * @returns {boolean} - 路径是否安全
 */
const isPathSafe = (filePath, baseDir = null) => {
  try {
    // 规范化路径
    const normalizedPath = path.normalize(filePath);
    
    // 检查是否包含路径遍历字符
    if (normalizedPath.includes('..') || normalizedPath.includes('~')) {
      return false;
    }

    // 如果指定了基础目录，检查路径是否在基础目录内
    if (baseDir) {
      const resolvedBase = path.resolve(baseDir);
      const resolvedPath = path.resolve(baseDir, normalizedPath);
      
      if (!resolvedPath.startsWith(resolvedBase)) {
        return false;
      }
    }

    return true;
  } catch (error) {
    logger.error('Path validation error', { error: error.message, filePath });
    return false;
  }
};

/**
 * 安全地解析文件路径
 * @param {string} filePath - 文件路径
 * @param {string} baseDir - 基础目录
 * @returns {string|null} - 安全的绝对路径或null
 */
const resolveSafePath = (filePath, baseDir) => {
  try {
    if (!isPathSafe(filePath, baseDir)) {
      return null;
    }

    const resolvedBase = path.resolve(baseDir);
    const resolvedPath = path.resolve(baseDir, filePath);

    // 双重检查确保路径在基础目录内
    if (!resolvedPath.startsWith(resolvedBase)) {
      return null;
    }

    return resolvedPath;
  } catch (error) {
    logger.error('Path resolution error', { error: error.message, filePath, baseDir });
    return null;
  }
};

/**
 * 验证文件扩展名是否安全
 * @param {string} filename - 文件名
 * @returns {boolean} - 扩展名是否安全
 */
const isExtensionSafe = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  
  // 检查是否是危险扩展名
  if (DANGEROUS_EXTENSIONS.includes(ext)) {
    return false;
  }

  // 检查是否是允许的扩展名
  return ALLOWED_EXTENSIONS.includes(ext);
};

/**
 * 清理文件名
 * @param {string} filename - 原始文件名
 * @returns {string} - 清理后的文件名
 */
const sanitizeFilename = (filename) => {
  return filename
    // 移除路径分隔符
    .replace(/[/\\]/g, '')
    // 移除危险字符
    .replace(/[<>:"|?*]/g, '')
    // 移除控制字符
    .replace(/[\x00-\x1f\x7f-\x9f]/g, '')
    // 移除前后空格和点
    .replace(/^[\s.]+|[\s.]+$/g, '')
    // 限制长度
    .substring(0, 255);
};

/**
 * 安全的文件读取中间件
 */
const safeFileAccess = (baseDir) => {
  return async (req, res, next) => {
    try {
      const requestedPath = req.params.path || req.query.path;
      
      if (!requestedPath) {
        return res.status(400).json({
          success: false,
          message: 'File path is required'
        });
      }

      const safePath = resolveSafePath(requestedPath, baseDir);
      
      if (!safePath) {
        logSecurityEvent('PATH_TRAVERSAL_ATTEMPT', {
          requestedPath,
          baseDir,
          userAgent: req.get('User-Agent')
        }, req);

        return res.status(403).json({
          success: false,
          message: 'Invalid file path'
        });
      }

      // 检查文件是否存在
      try {
        await fs.access(safePath);
        req.safePath = safePath;
        next();
      } catch (error) {
        return res.status(404).json({
          success: false,
          message: 'File not found'
        });
      }
    } catch (error) {
      logger.error('Safe file access error', { error: error.message });
      return res.status(500).json({
        success: false,
        message: 'File access error'
      });
    }
  };
};

/**
 * 文件上传路径验证中间件
 */
const validateUploadPath = (req, res, next) => {
  if (!req.file) {
    return next();
  }

  const { originalname, destination, filename } = req.file;

  // 验证文件名
  const sanitizedName = sanitizeFilename(originalname);
  if (!sanitizedName || !isExtensionSafe(sanitizedName)) {
    logSecurityEvent('UNSAFE_FILE_UPLOAD', {
      originalname,
      sanitizedName,
      destination
    }, req);

    return res.status(400).json({
      success: false,
      message: 'Invalid file type or name'
    });
  }

  // 验证目标目录
  if (!SAFE_BASE_DIRS.some(dir => destination.startsWith(dir))) {
    logSecurityEvent('UNSAFE_UPLOAD_DESTINATION', {
      destination,
      allowedDirs: SAFE_BASE_DIRS
    }, req);

    return res.status(400).json({
      success: false,
      message: 'Invalid upload destination'
    });
  }

  next();
};

/**
 * 创建安全的文件路径
 * @param {string} baseDir - 基础目录
 * @param {string} filename - 文件名
 * @returns {string} - 安全的文件路径
 */
const createSafeFilePath = (baseDir, filename) => {
  const sanitizedFilename = sanitizeFilename(filename);
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  
  const ext = path.extname(sanitizedFilename);
  const name = path.basename(sanitizedFilename, ext);
  
  const safeFilename = `${name}_${timestamp}_${randomSuffix}${ext}`;
  
  return path.join(baseDir, safeFilename);
};

module.exports = {
  isPathSafe,
  resolveSafePath,
  isExtensionSafe,
  sanitizeFilename,
  safeFileAccess,
  validateUploadPath,
  createSafeFilePath,
  SAFE_BASE_DIRS,
  DANGEROUS_EXTENSIONS,
  ALLOWED_EXTENSIONS
};