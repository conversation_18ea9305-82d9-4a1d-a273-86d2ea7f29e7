{"name": "newzora-admin", "version": "1.0.0", "description": "Newzora 后台管理系统", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd Backend && npm run dev", "dev:frontend": "cd Frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd Backend && npm run build", "build:frontend": "cd Frontend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:backend": "cd Backend && npm start", "start:frontend": "cd Frontend && npm start", "install:all": "npm install && cd Backend && npm install && cd ../Frontend && npm install", "clean": "rimraf node_modules Backend/node_modules Frontend/node_modules", "clean:install": "npm run clean && npm run install:all", "db:setup": "node scripts/database-manager.js setup", "db:create": "node scripts/database-manager.js create", "db:init": "node scripts/database-manager.js init", "db:backup": "node scripts/database-manager.js backup", "db:status": "node scripts/database-manager.js status"}, "keywords": ["admin", "management", "newzora", "backend"], "author": "Newzora Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "rimraf": "^5.0.5"}, "dependencies": {"dotenv": "^17.2.1"}}