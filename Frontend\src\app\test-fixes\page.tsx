'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import MediaUploader from '@/components/MediaUploader';
import CategorySidebar from '@/components/CategorySidebar';
// import EnhancedNotificationCenter from '@/components/EnhancedNotificationCenter'; // 已移除

export default function TestFixesPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  // const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  // const [unreadCount, setUnreadCount] = useState(3); // 已移除弹窗通知

  const handleFileSelect = (file: File) => {
    console.log('Selected file:', file.name);
  };

  const handleVideoInfo = (info: any) => {
    console.log('Video info:', info);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="flex">
        {/* 左侧栏测试 */}
        <CategorySidebar 
          selectedCategory={selectedCategory}
          onCategoryChange={setSelectedCategory}
        />
        
        <div className="flex-1 p-8">
          <div className="max-w-4xl mx-auto space-y-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-8">修复测试页面</h1>
            
            {/* 测试1: 简化的音视频上传器 */}
            <section className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4">1. 简化的音视频上传器</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">视频上传</h3>
                  <MediaUploader
                    type="video"
                    onFileSelect={handleFileSelect}
                    onVideoInfo={handleVideoInfo}
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">音频上传</h3>
                  <MediaUploader
                    type="audio"
                    onFileSelect={handleFileSelect}
                  />
                </div>
              </div>
            </section>

            {/* 测试2: 通知系统 */}
            <section className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4">2. 简化的通知系统</h2>
              <p className="text-gray-600 mb-4">
                现在点击 Header 中的铃铛按钮将直接跳转到通知页面，不再显示弹窗。
              </p>
              <a
                href="/notifications"
                className="inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                查看通知页面
              </a>
            </section>

            {/* 测试3: 导航链接 */}
            <section className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4">3. 修复的页面链接</h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a
                  href="/profile/alice_j"
                  className="block p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">👤</div>
                  <div className="text-sm font-medium">用户资料</div>
                </a>
                <a
                  href="/profile/alice_j/following"
                  className="block p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">👥</div>
                  <div className="text-sm font-medium">关注列表</div>
                </a>
                <a
                  href="/earnings"
                  className="block p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">💰</div>
                  <div className="text-sm font-medium">收益页面</div>
                </a>
                <a
                  href="/notifications"
                  className="block p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors text-center"
                >
                  <div className="text-2xl mb-2">🔔</div>
                  <div className="text-sm font-medium">通知页面</div>
                </a>
              </div>
            </section>

            {/* 修复总结 */}
            <section className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4">修复总结</h2>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <span className="text-green-500">✅</span>
                  <span>简化音视频上传器界面，移除复杂的质量和比特率显示</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-green-500">✅</span>
                  <span>左侧栏profile图标改为人头图标，风格统一</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-green-500">✅</span>
                  <span>修复关注页面404问题，添加作品数量交互功能</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-green-500">✅</span>
                  <span>修复earnings页面认证超时问题，简化加载逻辑</span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-green-500">✅</span>
                  <span>简化通知系统，移除弹窗，直接跳转到通知页面</span>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}