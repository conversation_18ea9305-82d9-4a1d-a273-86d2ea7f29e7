// 收益系统服务

export interface EarningsTransaction {
  id: string;
  userId: string;
  type: 'content_view' | 'premium_subscription' | 'donation' | 'sponsorship' | 'merchandise' | 'referral' | 'bonus';
  amount: number;
  currency: 'USD' | 'EUR' | 'GBP' | 'CNY';
  contentId?: number;
  description: string;
  timestamp: Date;
  status: 'pending' | 'confirmed' | 'cancelled';
  metadata?: Record<string, any>;
}

export interface WithdrawalRequest {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  method: 'paypal' | 'bank_transfer' | 'crypto' | 'stripe' | 'wise';
  destination: {
    paypal?: { email: string };
    bank?: { 
      accountNumber: string; 
      routingNumber: string; 
      bankName: string;
      accountHolderName: string;
    };
    crypto?: { 
      address: string; 
      network: 'bitcoin' | 'ethereum' | 'usdt';
    };
    stripe?: { accountId: string };
    wise?: { email: string; currency: string };
  };
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  requestedAt: Date;
  processedAt?: Date;
  fees: number;
  netAmount: number;
  notes?: string;
}

export interface EarningsSettings {
  userId: string;
  minimumWithdrawal: number;
  preferredCurrency: string;
  autoWithdraw: boolean;
  autoWithdrawThreshold: number;
  taxSettings: {
    country: string;
    taxId?: string;
    taxRate: number;
  };
  paymentMethods: Array<{
    id: string;
    type: string;
    isDefault: boolean;
    details: Record<string, any>;
  }>;
}

export interface EarningsAnalytics {
  totalEarnings: number;
  monthlyEarnings: number;
  yearlyEarnings: number;
  averageMonthlyGrowth: number;
  topEarningContent: Array<{
    contentId: number;
    title: string;
    earnings: number;
    views: number;
    rpm: number; // Revenue per mille (per 1000 views)
  }>;
  earningsByCategory: Record<string, number>;
  earningsByType: Record<string, number>;
  projectedEarnings: {
    nextMonth: number;
    nextQuarter: number;
    nextYear: number;
  };
}

export class EarningsService {
  private transactions: Map<string, EarningsTransaction[]> = new Map();
  private withdrawals: Map<string, WithdrawalRequest[]> = new Map();
  private settings: Map<string, EarningsSettings> = new Map();

  // 记录收益交易
  recordEarning(transaction: Omit<EarningsTransaction, 'id' | 'timestamp' | 'status'>) {
    const fullTransaction: EarningsTransaction = {
      ...transaction,
      id: this.generateId(),
      timestamp: new Date(),
      status: 'confirmed'
    };

    if (!this.transactions.has(transaction.userId)) {
      this.transactions.set(transaction.userId, []);
    }

    this.transactions.get(transaction.userId)!.push(fullTransaction);
    return fullTransaction;
  }

  // 计算用户总收益
  calculateTotalEarnings(userId: string): number {
    const userTransactions = this.transactions.get(userId) || [];
    return userTransactions
      .filter(t => t.status === 'confirmed')
      .reduce((total, t) => total + t.amount, 0);
  }

  // 计算可提现金额
  calculateAvailableBalance(userId: string): number {
    const totalEarnings = this.calculateTotalEarnings(userId);
    const totalWithdrawn = this.calculateTotalWithdrawn(userId);
    const pendingWithdrawals = this.calculatePendingWithdrawals(userId);
    
    return totalEarnings - totalWithdrawn - pendingWithdrawals;
  }

  // 计算已提现金额
  private calculateTotalWithdrawn(userId: string): number {
    const userWithdrawals = this.withdrawals.get(userId) || [];
    return userWithdrawals
      .filter(w => w.status === 'completed')
      .reduce((total, w) => total + w.amount, 0);
  }

  // 计算待处理提现金额
  private calculatePendingWithdrawals(userId: string): number {
    const userWithdrawals = this.withdrawals.get(userId) || [];
    return userWithdrawals
      .filter(w => w.status === 'pending' || w.status === 'processing')
      .reduce((total, w) => total + w.amount, 0);
  }

  // 创建提现请求
  createWithdrawalRequest(
    userId: string,
    amount: number,
    method: WithdrawalRequest['method'],
    destination: WithdrawalRequest['destination']
  ): { success: boolean; request?: WithdrawalRequest; error?: string } {
    const availableBalance = this.calculateAvailableBalance(userId);
    const settings = this.getEarningsSettings(userId);

    // 验证提现金额
    if (amount > availableBalance) {
      return { success: false, error: 'Insufficient balance' };
    }

    if (amount < settings.minimumWithdrawal) {
      return { success: false, error: `Minimum withdrawal amount is $${settings.minimumWithdrawal}` };
    }

    // 计算手续费
    const fees = this.calculateWithdrawalFees(amount, method);
    const netAmount = amount - fees;

    const withdrawalRequest: WithdrawalRequest = {
      id: this.generateId(),
      userId,
      amount,
      currency: settings.preferredCurrency,
      method,
      destination,
      status: 'pending',
      requestedAt: new Date(),
      fees,
      netAmount
    };

    if (!this.withdrawals.has(userId)) {
      this.withdrawals.set(userId, []);
    }

    this.withdrawals.get(userId)!.push(withdrawalRequest);

    return { success: true, request: withdrawalRequest };
  }

  // 计算提现手续费
  private calculateWithdrawalFees(amount: number, method: WithdrawalRequest['method']): number {
    const feeRates = {
      paypal: 0.029, // 2.9%
      bank_transfer: 0.01, // 1%
      crypto: 0.005, // 0.5%
      stripe: 0.025, // 2.5%
      wise: 0.015 // 1.5%
    };

    const minimumFees = {
      paypal: 0.30,
      bank_transfer: 1.00,
      crypto: 0.10,
      stripe: 0.30,
      wise: 0.50
    };

    const calculatedFee = amount * feeRates[method];
    return Math.max(calculatedFee, minimumFees[method]);
  }

  // 获取用户收益设置
  getEarningsSettings(userId: string): EarningsSettings {
    if (!this.settings.has(userId)) {
      // 默认设置
      const defaultSettings: EarningsSettings = {
        userId,
        minimumWithdrawal: 10.00,
        preferredCurrency: 'USD',
        autoWithdraw: false,
        autoWithdrawThreshold: 100.00,
        taxSettings: {
          country: 'US',
          taxRate: 0.15
        },
        paymentMethods: []
      };
      this.settings.set(userId, defaultSettings);
    }

    return this.settings.get(userId)!;
  }

  // 更新收益设置
  updateEarningsSettings(userId: string, updates: Partial<EarningsSettings>): EarningsSettings {
    const currentSettings = this.getEarningsSettings(userId);
    const updatedSettings = { ...currentSettings, ...updates };
    this.settings.set(userId, updatedSettings);
    return updatedSettings;
  }

  // 获取收益分析
  getEarningsAnalytics(userId: string): EarningsAnalytics {
    const transactions = this.transactions.get(userId) || [];
    const confirmedTransactions = transactions.filter(t => t.status === 'confirmed');

    const totalEarnings = confirmedTransactions.reduce((sum, t) => sum + t.amount, 0);

    // 计算月度收益
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();
    
    const monthlyEarnings = confirmedTransactions
      .filter(t => {
        const transactionDate = new Date(t.timestamp);
        return transactionDate.getMonth() === currentMonth && 
               transactionDate.getFullYear() === currentYear;
      })
      .reduce((sum, t) => sum + t.amount, 0);

    // 计算年度收益
    const yearlyEarnings = confirmedTransactions
      .filter(t => new Date(t.timestamp).getFullYear() === currentYear)
      .reduce((sum, t) => sum + t.amount, 0);

    // 按类型分组收益
    const earningsByType: Record<string, number> = {};
    confirmedTransactions.forEach(t => {
      earningsByType[t.type] = (earningsByType[t.type] || 0) + t.amount;
    });

    // 模拟预测收益
    const averageMonthlyGrowth = 0.15; // 15% 月增长率
    const projectedEarnings = {
      nextMonth: monthlyEarnings * (1 + averageMonthlyGrowth),
      nextQuarter: monthlyEarnings * 3 * (1 + averageMonthlyGrowth),
      nextYear: yearlyEarnings * (1 + averageMonthlyGrowth * 12)
    };

    return {
      totalEarnings,
      monthlyEarnings,
      yearlyEarnings,
      averageMonthlyGrowth,
      topEarningContent: [], // 需要与内容数据关联
      earningsByCategory: {},
      earningsByType,
      projectedEarnings
    };
  }

  // 获取提现历史
  getWithdrawalHistory(userId: string): WithdrawalRequest[] {
    return this.withdrawals.get(userId) || [];
  }

  // 获取收益历史
  getEarningsHistory(userId: string, limit?: number): EarningsTransaction[] {
    const transactions = this.transactions.get(userId) || [];
    const sorted = transactions.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
    
    return limit ? sorted.slice(0, limit) : sorted;
  }

  // 处理提现请求（管理员功能）
  processWithdrawal(withdrawalId: string, status: 'completed' | 'failed', notes?: string): boolean {
    for (const [userId, withdrawals] of this.withdrawals.entries()) {
      const withdrawal = withdrawals.find(w => w.id === withdrawalId);
      if (withdrawal) {
        withdrawal.status = status;
        withdrawal.processedAt = new Date();
        if (notes) withdrawal.notes = notes;
        return true;
      }
    }
    return false;
  }

  // 生成唯一ID
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // 模拟收益数据
  simulateEarnings(userId: string) {
    const earningTypes: EarningsTransaction['type'][] = [
      'content_view', 'premium_subscription', 'donation', 'sponsorship', 'merchandise', 'referral', 'bonus'
    ];

    // 生成过去30天的收益记录
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);

      // 每天生成1-5个收益记录
      const dailyTransactions = Math.floor(Math.random() * 5) + 1;
      
      for (let j = 0; j < dailyTransactions; j++) {
        const type = earningTypes[Math.floor(Math.random() * earningTypes.length)];
        const amount = this.generateRandomAmount(type);
        
        this.recordEarning({
          userId,
          type,
          amount,
          currency: 'USD',
          contentId: Math.floor(Math.random() * 16) + 1,
          description: this.generateDescription(type, amount),
          metadata: { simulatedData: true }
        });
      }
    }
  }

  private generateRandomAmount(type: EarningsTransaction['type']): number {
    const ranges = {
      content_view: [0.01, 0.05],
      premium_subscription: [5.00, 15.00],
      donation: [1.00, 50.00],
      sponsorship: [50.00, 500.00],
      merchandise: [10.00, 100.00],
      referral: [5.00, 25.00],
      bonus: [10.00, 100.00]
    };

    const [min, max] = ranges[type];
    return Math.random() * (max - min) + min;
  }

  private generateDescription(type: EarningsTransaction['type'], amount: number): string {
    const descriptions = {
      content_view: `Content view revenue: $${amount.toFixed(2)}`,
      premium_subscription: `Premium subscription revenue: $${amount.toFixed(2)}`,
      donation: `Fan donation: $${amount.toFixed(2)}`,
      sponsorship: `Sponsorship payment: $${amount.toFixed(2)}`,
      merchandise: `Merchandise sale: $${amount.toFixed(2)}`,
      referral: `Referral bonus: $${amount.toFixed(2)}`,
      bonus: `Platform bonus: $${amount.toFixed(2)}`
    };

    return descriptions[type];
  }
}

// 创建全局收益服务实例
export const earningsService = new EarningsService();
