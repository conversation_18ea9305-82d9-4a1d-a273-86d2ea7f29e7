'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add authorId to articles table
    await queryInterface.addColumn('articles', 'authorId', {
      type: Sequelize.INTEGER,
      allowNull: true, // Allow null for legacy articles
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    // Add authorId to comments table
    await queryInterface.addColumn('comments', 'authorId', {
      type: Sequelize.INTEGER,
      allowNull: true, // Allow null for legacy comments
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    // Add indexes for better performance
    await queryInterface.addIndex('articles', ['authorId']);
    await queryInterface.addIndex('comments', ['authorId']);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex('articles', ['authorId']);
    await queryInterface.removeIndex('comments', ['authorId']);

    // Remove columns
    await queryInterface.removeColumn('articles', 'authorId');
    await queryInterface.removeColumn('comments', 'authorId');
  },
};
