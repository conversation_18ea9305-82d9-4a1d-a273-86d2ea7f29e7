'use client';

import React, { useState } from 'react';
import { Save, Shield, Lock, AlertTriangle, Key, Eye, EyeOff } from 'lucide-react';

interface SecuritySettings {
  maxLoginAttempts: number;
  lockoutDuration: number;
  sessionTimeout: number;
  passwordMinLength: number;
  passwordRequireUppercase: boolean;
  passwordRequireLowercase: boolean;
  passwordRequireNumbers: boolean;
  passwordRequireSymbols: boolean;
  twoFactorAuth: boolean;
  forceHttps: boolean;
  ipWhitelist: string[];
  allowedFileTypes: string[];
  maxFileSize: number;
  enableCaptcha: boolean;
  captchaProvider: string;
  enableRateLimit: boolean;
  rateLimitRequests: number;
  rateLimitWindow: number;
}

const SecuritySettingsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [newIpAddress, setNewIpAddress] = useState('');
  const [settings, setSettings] = useState<SecuritySettings>({
    maxLoginAttempts: 5,
    lockoutDuration: 15,
    sessionTimeout: 60,
    passwordMinLength: 8,
    passwordRequireUppercase: true,
    passwordRequireLowercase: true,
    passwordRequireNumbers: true,
    passwordRequireSymbols: false,
    twoFactorAuth: false,
    forceHttps: true,
    ipWhitelist: ['***********/24', '10.0.0.0/8'],
    allowedFileTypes: ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
    maxFileSize: 10,
    enableCaptcha: true,
    captchaProvider: 'recaptcha',
    enableRateLimit: true,
    rateLimitRequests: 100,
    rateLimitWindow: 15
  });

  const handleInputChange = (field: keyof SecuritySettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // Mock API call - in real app, this would save to backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Security settings saved successfully!');
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const addIpAddress = () => {
    if (newIpAddress.trim() && !settings.ipWhitelist.includes(newIpAddress.trim())) {
      handleInputChange('ipWhitelist', [...settings.ipWhitelist, newIpAddress.trim()]);
      setNewIpAddress('');
    }
  };

  const removeIpAddress = (ip: string) => {
    handleInputChange('ipWhitelist', settings.ipWhitelist.filter(item => item !== ip));
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Security Settings</h1>
          <p className="text-gray-600 mt-1">Configure security policies and authentication settings</p>
        </div>
        <button
          onClick={handleSave}
          disabled={loading}
          className="btn-primary flex items-center"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <Save className="w-4 h-4 mr-2" />
          )}
          Save Changes
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Authentication Settings */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Lock className="w-5 h-5 mr-2" />
            Authentication
          </h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum Login Attempts
              </label>
              <input
                type="number"
                value={settings.maxLoginAttempts}
                onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value))}
                className="form-input"
                min="1"
                max="10"
              />
              <p className="text-xs text-gray-500 mt-1">
                Number of failed login attempts before account lockout
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Lockout Duration (minutes)
              </label>
              <input
                type="number"
                value={settings.lockoutDuration}
                onChange={(e) => handleInputChange('lockoutDuration', parseInt(e.target.value))}
                className="form-input"
                min="1"
                max="1440"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Session Timeout (minutes)
              </label>
              <input
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value))}
                className="form-input"
                min="5"
                max="1440"
              />
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="twoFactorAuth"
                checked={settings.twoFactorAuth}
                onChange={(e) => handleInputChange('twoFactorAuth', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="twoFactorAuth" className="ml-2 text-sm text-gray-700">
                Enable Two-Factor Authentication
              </label>
            </div>
          </div>
        </div>

        {/* Password Policy */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Key className="w-5 h-5 mr-2" />
            Password Policy
          </h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Password Length
              </label>
              <input
                type="number"
                value={settings.passwordMinLength}
                onChange={(e) => handleInputChange('passwordMinLength', parseInt(e.target.value))}
                className="form-input"
                min="6"
                max="20"
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireUppercase"
                  checked={settings.passwordRequireUppercase}
                  onChange={(e) => handleInputChange('passwordRequireUppercase', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="requireUppercase" className="ml-2 text-sm text-gray-700">
                  Require uppercase letters
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireLowercase"
                  checked={settings.passwordRequireLowercase}
                  onChange={(e) => handleInputChange('passwordRequireLowercase', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="requireLowercase" className="ml-2 text-sm text-gray-700">
                  Require lowercase letters
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireNumbers"
                  checked={settings.passwordRequireNumbers}
                  onChange={(e) => handleInputChange('passwordRequireNumbers', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="requireNumbers" className="ml-2 text-sm text-gray-700">
                  Require numbers
                </label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="requireSymbols"
                  checked={settings.passwordRequireSymbols}
                  onChange={(e) => handleInputChange('passwordRequireSymbols', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="requireSymbols" className="ml-2 text-sm text-gray-700">
                  Require special characters
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* System Security */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Shield className="w-5 h-5 mr-2" />
            System Security
          </h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="forceHttps"
                checked={settings.forceHttps}
                onChange={(e) => handleInputChange('forceHttps', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="forceHttps" className="ml-2 text-sm text-gray-700">
                Force HTTPS connections
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableCaptcha"
                checked={settings.enableCaptcha}
                onChange={(e) => handleInputChange('enableCaptcha', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="enableCaptcha" className="ml-2 text-sm text-gray-700">
                Enable CAPTCHA verification
              </label>
            </div>
            
            {settings.enableCaptcha && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CAPTCHA Provider
                </label>
                <select
                  value={settings.captchaProvider}
                  onChange={(e) => handleInputChange('captchaProvider', e.target.value)}
                  className="form-select"
                >
                  <option value="recaptcha">Google reCAPTCHA</option>
                  <option value="hcaptcha">hCaptcha</option>
                  <option value="turnstile">Cloudflare Turnstile</option>
                </select>
              </div>
            )}
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableRateLimit"
                checked={settings.enableRateLimit}
                onChange={(e) => handleInputChange('enableRateLimit', e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="enableRateLimit" className="ml-2 text-sm text-gray-700">
                Enable rate limiting
              </label>
            </div>
            
            {settings.enableRateLimit && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Requests
                  </label>
                  <input
                    type="number"
                    value={settings.rateLimitRequests}
                    onChange={(e) => handleInputChange('rateLimitRequests', parseInt(e.target.value))}
                    className="form-input"
                    min="1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Window (minutes)
                  </label>
                  <input
                    type="number"
                    value={settings.rateLimitWindow}
                    onChange={(e) => handleInputChange('rateLimitWindow', parseInt(e.target.value))}
                    className="form-input"
                    min="1"
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* File Upload Security */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            File Upload Security
          </h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Maximum File Size (MB)
              </label>
              <input
                type="number"
                value={settings.maxFileSize}
                onChange={(e) => handleInputChange('maxFileSize', parseInt(e.target.value))}
                className="form-input"
                min="1"
                max="100"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Allowed File Types
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {settings.allowedFileTypes.map((type, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    .{type}
                    <button
                      onClick={() => {
                        const newTypes = settings.allowedFileTypes.filter((_, i) => i !== index);
                        handleInputChange('allowedFileTypes', newTypes);
                      }}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                placeholder="Add file extension (e.g., txt)"
                className="form-input"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    const value = (e.target as HTMLInputElement).value.trim().toLowerCase();
                    if (value && !settings.allowedFileTypes.includes(value)) {
                      handleInputChange('allowedFileTypes', [...settings.allowedFileTypes, value]);
                      (e.target as HTMLInputElement).value = '';
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Settings */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Advanced Security Settings</h2>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-sm text-blue-600 hover:text-blue-800"
          >
            {showAdvanced ? <EyeOff className="w-4 h-4 mr-1" /> : <Eye className="w-4 h-4 mr-1" />}
            {showAdvanced ? 'Hide' : 'Show'} Advanced
          </button>
        </div>
        
        {showAdvanced && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                IP Whitelist
              </label>
              <div className="space-y-2">
                {settings.ipWhitelist.map((ip, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                    <span className="text-sm font-mono">{ip}</span>
                    <button
                      onClick={() => removeIpAddress(ip)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newIpAddress}
                    onChange={(e) => setNewIpAddress(e.target.value)}
                    placeholder="***********/24"
                    className="form-input flex-1"
                  />
                  <button
                    onClick={addIpAddress}
                    className="btn-secondary"
                  >
                    Add
                  </button>
                </div>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Only allow access from these IP addresses/ranges
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SecuritySettingsPage;
