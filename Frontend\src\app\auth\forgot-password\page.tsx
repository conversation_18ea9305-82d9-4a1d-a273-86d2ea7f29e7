'use client';

import React, { useState, useEffect, ChangeEvent, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Logo from '@/components/Logo';
import { Mail, ArrowLeft } from 'lucide-react';

/**
 * Password reset page component
 * Following code generation rules: complete TypeScript, no any types, includes error handling
 */
export default function ForgotPasswordPage() {
  const router = useRouter();

  // 使用真实的认证上下文
  const { isAuthenticated, isLoading, resetPassword } = useSimpleAuth();

  // 表单状态管理
  const [email, setEmail] = useState<string>('');
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [message, setMessage] = useState<string>('');
  const [showLoadingTimeout, setShowLoadingTimeout] = useState<boolean>(false);

  // 添加调试信息
  console.log('ForgotPasswordPage render:', { isLoading, isAuthenticated, showLoadingTimeout });

  // 添加超时处理，防止无限加载
  useEffect(() => {
    console.log('Setting timeout for loading...');
    const timer = setTimeout(() => {
      console.log('Timeout reached, showing page');
      setShowLoadingTimeout(true);
    }, 2000); // 减少到2秒

    return () => clearTimeout(timer);
  }, []);

  // 紧急fallback - 如果认证系统完全不工作，5秒后强制显示页面
  useEffect(() => {
    const emergencyTimer = setTimeout(() => {
      console.log('Emergency timeout - forcing page display');
      setShowLoadingTimeout(true);
    }, 5000);

    return () => clearTimeout(emergencyTimer);
  }, []);

  // 如果已经登录，重定向到首页（但不在加载状态时执行）
  useEffect(() => {
    // 只有在明确已登录且不在加载状态时才重定向
    if (!isLoading && isAuthenticated) {
      console.log('Redirecting to home...');
      router.push('/');
    }
  }, [isAuthenticated, isLoading, router]);

  // 只在前2秒且正在加载时显示加载界面
  if (isLoading && !showLoadingTimeout) {
    console.log('Showing loading screen');
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-2">Loading... ({showLoadingTimeout ? 'timeout' : 'waiting'})</p>
        </div>
      </div>
    );
  }

  console.log('Rendering main page content');

  // 表单验证函数
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    // 邮箱验证
    if (!email.trim()) {
      errors.email = 'Email address is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 处理输入变化
  const handleChange = (e: ChangeEvent<HTMLInputElement>): void => {
    const { value } = e.target;
    setEmail(value);

    // 清除相关错误
    if (validationErrors.email) {
      setValidationErrors(prev => ({ ...prev, email: '' }));
    }
    if (message) {
      setMessage('');
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setValidationErrors({});
    setMessage('');

    try {
      const result = await resetPassword(email);

      if (result) {
        setIsSuccess(true);
        setMessage('Password reset email has been sent! Please check your email and click the reset link.');
      } else {
        setMessage('Failed to send reset email. Please try again.');
        setValidationErrors({ form: 'Failed to send reset email. Please try again.' });
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      const errorMessage = error?.message || 'Network error. Please check your connection and try again.';
      setMessage(errorMessage);
      setValidationErrors({ form: errorMessage });
    } finally {
      setIsSubmitting(false);
    }
  };



  // 成功状态页面
  if (isSuccess) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <Logo size="lg" />
            </div>
            <h2 className="text-3xl font-bold text-gray-800">Email Sent Successfully</h2>
            <p className="mt-2 text-gray-600">Please check your email for reset instructions</p>
          </div>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow-lg sm:rounded-xl sm:px-10 border border-gray-100">
            <div className="text-center space-y-6">
              <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100">
                <svg className="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>

              <div className="space-y-3">
                <p className="text-sm text-gray-600">
                  We have sent a password reset email to{' '}
                  <span className="font-medium text-gray-900">{email}</span>
                </p>
                <p className="text-sm text-gray-600">
                  Please check your email (including spam folder) and click the reset link to set a new password.
                </p>
              </div>

              <div className="space-y-3">
                <Link
                  href="/auth/login"
                  className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Login
                </Link>

                <button
                  onClick={() => {
                    setIsSuccess(false);
                    setMessage('');
                    setEmail('');
                  }}
                  className="w-full flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  Send Another Email
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* 页面头部 */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <Logo size="lg" />
          </div>
          <h2 className="text-3xl font-bold text-gray-800">Reset Password</h2>
          <p className="mt-2 text-gray-600">Enter your email address and we will send you a reset link</p>
        </div>
      </div>

      {/* 密码重置表单 */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">

        <div className="bg-white py-8 px-4 shadow-lg sm:rounded-xl sm:px-10 border border-gray-100">
          {/* 错误提示 */}
          {(validationErrors.form || message) && !isSuccess && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 animate-fadeIn">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-red-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-red-800">
                    {validationErrors.form || message}
                  </h3>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 邮箱输入 */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <div className="mt-1 relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={handleChange}
                  className={`appearance-none block w-full pl-10 px-3 py-3 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all ${
                    validationErrors.email ? 'border-red-300' : 'border-gray-300'
                  }`}
                  placeholder="Enter your email address"
                />
              </div>
              {validationErrors.email && (
                <p className="mt-1 text-sm text-red-600">{validationErrors.email}</p>
              )}
            </div>

            {/* 提交按钮 */}
            <div>
              <button
                type="submit"
                disabled={isSubmitting}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sending Reset Email...
                  </>
                ) : (
                  'Send Reset Email'
                )}
              </button>
            </div>

            {/* 返回登录链接 */}
            <div className="text-center">
              <p className="text-sm text-gray-600">
                Remember your password?{' '}
                <Link href="/auth/login" className="font-medium text-blue-600 hover:text-blue-500 transition-colors">
                  Back to Login
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
