# 🧹 Newzora 项目代码清理规则（rule-cleanup.md）

---

## 🗃️ 文件结构清理

- 删除空文件（空组件、空对象、无任何逻辑）。
- 删除所有测试页、临时页，如：`test.tsx`、`playground.tsx`。
- 清除未引用的组件、页面、模块（使用 ts-prune、depcheck）。
- 合并或删除命名重复的文件（如：`Login.tsx` vs `LoginPage.tsx`）。
- 删除示例组件、故事文件（如 `*.demo.tsx`、`*.story.tsx`）。

## 🧪 测试调试残留清理

- 删除所有 `console.log/debug/warn/error`。
- 删除 mock 文件（如 `mockUser.ts`, `fakeApi.ts`）。
- 清除开发注释：`// TODO`, `// 临时代码`。
- 删除占位组件 `<Placeholder />`, `<DebugBox />`。

## 📦 依赖与配置清理

- 删除未使用依赖（用 `depcheck`）。
- 清除重复功能库（如 `axios` 与 `fetch`）。
- 删除开发阶段辅助工具（如 `faker`）。
- 清理无效配置文件，如 `.eslintrc.old.json`。

## ⚙️ 自动化工具建议

- `ts-prune`: 查找未使用导出函数/类型。
- `depcheck`: 检查未使用的包。
- `eslint-plugin-unused-imports`: 自动移除无用 import。
- `pre-commit + lint-staged`: 清理调试代码、格式化代码。
- `CI`: 设置清理检测规则，阻止污染提交。

---

## ✅ 提交前自检 Checklist

```markdown
- [ ] 所有空文件、临时测试页已删除
- [ ] 无任何 `console.log` 或调试代码
- [ ] package.json 中无未使用依赖
- [ ] 所有组件、页面均被引用使用
- [ ] 所有 mock 数据文件已删除
- [ ] 所有 `.demo.tsx` / `.example.tsx` 文件已移除
