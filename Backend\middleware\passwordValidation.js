/**
 * 密码验证中间件
 * 用于验证密码强度和安全性
 */

const { validatePasswordStrength } = require('../config/security');

/**
 * 密码强度验证中间件
 * 用于注册和密码重置时验证密码强度
 */
const validatePasswordMiddleware = (req, res, next) => {
  const { password } = req.body;

  if (!password) {
    return res.status(400).json({
      success: false,
      message: '密码不能为空',
    });
  }

  const validation = validatePasswordStrength(password);

  if (!validation.isValid) {
    return res.status(400).json({
      success: false,
      message: '密码强度不符合要求',
      errors: validation.errors,
      passwordStrength: validation.strength,
    });
  }

  // 将验证结果附加到请求对象，供后续使用
  req.passwordValidation = validation;
  next();
};

/**
 * 密码确认验证中间件
 * 验证密码和确认密码是否一致
 */
const validatePasswordConfirmation = (req, res, next) => {
  const { password, confirmPassword } = req.body;

  if (password !== confirmPassword) {
    return res.status(400).json({
      success: false,
      message: '密码和确认密码不一致',
    });
  }

  next();
};

/**
 * 旧密码验证中间件
 * 用于修改密码时验证旧密码
 */
const validateOldPassword = async (req, res, next) => {
  try {
    const { oldPassword } = req.body;
    const user = req.user; // 假设已通过认证中间件

    if (!oldPassword) {
      return res.status(400).json({
        success: false,
        message: '请输入当前密码',
      });
    }

    const isMatch = await user.comparePassword(oldPassword);
    if (!isMatch) {
      return res.status(400).json({
        success: false,
        message: '当前密码不正确',
      });
    }

    next();
  } catch (error) {
    console.error('Old password validation error:', error);
    res.status(500).json({
      success: false,
      message: '密码验证失败',
    });
  }
};

/**
 * 密码历史检查中间件
 * 防止用户重复使用最近使用过的密码
 */
const checkPasswordHistory = async (req, res, next) => {
  try {
    const { password } = req.body;
    const user = req.user;

    // 这里可以实现密码历史检查逻辑
    // 由于当前模型中没有密码历史字段，这里只是示例
    // 在实际实现中，需要在User模型中添加passwordHistory字段

    // 示例：检查新密码是否与当前密码相同
    if (user.password) {
      const isSameAsCurrent = await user.comparePassword(password);
      if (isSameAsCurrent) {
        return res.status(400).json({
          success: false,
          message: '新密码不能与当前密码相同',
        });
      }
    }

    next();
  } catch (error) {
    console.error('Password history check error:', error);
    res.status(500).json({
      success: false,
      message: '密码历史检查失败',
    });
  }
};

/**
 * 组合密码验证中间件
 * 用于注册时的完整密码验证
 */
const fullPasswordValidation = [validatePasswordMiddleware, validatePasswordConfirmation];

/**
 * 组合密码修改验证中间件
 * 用于修改密码时的完整验证
 */
const changePasswordValidation = [
  validateOldPassword,
  validatePasswordMiddleware,
  validatePasswordConfirmation,
  checkPasswordHistory,
];

module.exports = {
  validatePasswordMiddleware,
  validatePasswordConfirmation,
  validateOldPassword,
  checkPasswordHistory,
  fullPasswordValidation,
  changePasswordValidation,
};
