const nodemailer = require('nodemailer');

// 邮件服务提供商配置
const EMAIL_PROVIDERS = {
  gmail: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'Gmail SMTP (推荐用于开发)',
  },
  sendgrid: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'SendGrid (推荐用于生产)',
  },
  aws_ses: {
    host: process.env.AWS_SES_HOST || 'email-smtp.us-east-1.amazonaws.com',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'Amazon SES',
  },
  outlook: {
    host: 'smtp-mail.outlook.com',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'Microsoft Outlook',
  },
  custom: {
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    requireTLS: process.env.EMAIL_REQUIRE_TLS !== 'false',
    description: '自定义SMTP服务器',
  },
};

// 获取邮件服务提供商配置
const getEmailProviderConfig = () => {
  const provider = process.env.EMAIL_PROVIDER || 'gmail';
  const config = EMAIL_PROVIDERS[provider];

  if (!config) {
    console.warn(`未知的邮件提供商: ${provider}，使用Gmail配置`);
    return EMAIL_PROVIDERS.gmail;
  }

  return config;
};

// Create transporter with enhanced configuration
const createTransporter = () => {
  const providerConfig = getEmailProviderConfig();

  const transporterConfig = {
    host: providerConfig.host,
    port: providerConfig.port,
    secure: providerConfig.secure,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
    tls: {
      rejectUnauthorized: process.env.NODE_ENV === 'production',
    },
  };

  // 添加连接池配置以提高性能
  if (process.env.NODE_ENV === 'production') {
    transporterConfig.pool = true;
    transporterConfig.maxConnections = 5;
    transporterConfig.maxMessages = 100;
  }

  // 添加重试配置
  transporterConfig.retryDelay = 1000;
  transporterConfig.maxRetries = 3;

  console.log(
    `使用邮件服务: ${providerConfig.description} (${providerConfig.host}:${providerConfig.port})`
  );

  return nodemailer.createTransport(transporterConfig);
};

// Send email verification
const sendEmailVerification = async (user, token) => {
  const transporter = createTransporter();

  const verificationUrl = `${process.env.FRONTEND_URL}/verify-email/${token}`;

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: user.email,
    subject: 'Newzora - Verify Your Email Address',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 30px; background: #2563eb; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>OneNews</h1>
          </div>
          <div class="content">
            <h2>Welcome to OneNews!</h2>
            <p>Hi ${user.username},</p>
            <p>Thank you for signing up for OneNews. To complete your registration, please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email Address</a>
            </p>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #2563eb;">${verificationUrl}</p>
            <p>This verification link will expire in 24 hours.</p>
            <p>If you didn't create an account with OneNews, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 OneNews. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('Email verification sent to:', user.email);
    return true;
  } catch (error) {
    console.error('Error sending email verification:', error);
    throw new Error('Failed to send verification email');
  }
};

// Send password reset email
const sendPasswordReset = async (user, token) => {
  const transporter = createTransporter();

  const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${token}`;

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: user.email,
    subject: 'Newzora - Password Reset Request',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 30px; background: #dc2626; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .warning { background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>OneNews</h1>
          </div>
          <div class="content">
            <h2>Password Reset Request</h2>
            <p>Hi ${user.username},</p>
            <p>We received a request to reset your password for your OneNews account. Click the button below to reset your password:</p>
            <p style="text-align: center;">
              <a href="${resetUrl}" class="button">Reset Password</a>
            </p>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all; color: #dc2626;">${resetUrl}</p>
            <div class="warning">
              <strong>Important:</strong>
              <ul>
                <li>This password reset link will expire in 1 hour</li>
                <li>If you didn't request this password reset, please ignore this email</li>
                <li>Your password will remain unchanged until you create a new one</li>
              </ul>
            </div>
          </div>
          <div class="footer">
            <p>&copy; 2024 OneNews. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('Password reset email sent to:', user.email);
    return true;
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw new Error('Failed to send password reset email');
  }
};

// Send welcome email after successful verification
const sendWelcomeEmail = async (user) => {
  const transporter = createTransporter();

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: user.email,
    subject: 'Welcome to Newzora!',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to OneNews</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #16a34a; color: white; padding: 20px; text-align: center; }
          .content { padding: 30px 20px; background: #f9f9f9; }
          .button { display: inline-block; padding: 12px 30px; background: #16a34a; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
          .features { background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Welcome to OneNews!</h1>
          </div>
          <div class="content">
            <h2>Your account is now verified!</h2>
            <p>Hi ${user.username},</p>
            <p>Congratulations! Your email has been successfully verified and your OneNews account is now active.</p>
            
            <div class="features">
              <h3>What you can do now:</h3>
              <ul>
                <li>📖 Read and discover amazing articles</li>
                <li>✍️ Create and publish your own content</li>
                <li>💬 Comment and engage with the community</li>
                <li>🔖 Bookmark your favorite articles</li>
                <li>👥 Follow other writers and readers</li>
              </ul>
            </div>
            
            <p style="text-align: center;">
              <a href="${process.env.FRONTEND_URL}" class="button">Start Exploring</a>
            </p>
            
            <p>Thank you for joining our community. We're excited to see what you'll discover and create!</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 OneNews. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('Welcome email sent to:', user.email);
    return true;
  } catch (error) {
    console.error('Error sending welcome email:', error);
    // Don't throw error for welcome email as it's not critical
    return false;
  }
};

// Test email configuration
const testEmailConfig = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('✅ 邮件配置验证成功');
    return true;
  } catch (error) {
    console.error('❌ 邮件配置验证失败:', error.message);
    return false;
  }
};

// 发送测试邮件
const sendTestEmail = async (toEmail) => {
  const transporter = createTransporter();

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: toEmail,
    subject: 'Newzora - 邮件服务测试',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>邮件服务测试</title>
        <style>
          body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #16a34a; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { padding: 30px 20px; background: #f9f9f9; border-radius: 0 0 8px 8px; }
          .success-icon { font-size: 48px; margin-bottom: 20px; }
          .info-box { background: white; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #16a34a; }
          .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <div class="success-icon">✅</div>
            <h1>邮件服务测试成功！</h1>
          </div>
          <div class="content">
            <p>恭喜！您的OneNews邮件服务配置正常工作。</p>

            <div class="info-box">
              <h3>测试信息：</h3>
              <ul>
                <li><strong>发送时间：</strong>${new Date().toLocaleString('zh-CN')}</li>
                <li><strong>邮件服务：</strong>${getEmailProviderConfig().description}</li>
                <li><strong>SMTP服务器：</strong>${getEmailProviderConfig().host}:${getEmailProviderConfig().port}</li>
                <li><strong>收件人：</strong>${toEmail}</li>
              </ul>
            </div>

            <p>如果您收到了这封邮件，说明：</p>
            <ul>
              <li>✅ SMTP服务器连接正常</li>
              <li>✅ 认证凭据有效</li>
              <li>✅ 邮件发送功能正常</li>
              <li>✅ HTML邮件模板渲染正常</li>
            </ul>

            <p>您现在可以正常使用OneNews的所有邮件功能，包括：</p>
            <ul>
              <li>用户注册邮箱验证</li>
              <li>密码重置邮件</li>
              <li>欢迎邮件</li>
              <li>通知邮件</li>
              <li>每日摘要邮件</li>
            </ul>
          </div>
          <div class="footer">
            <p>&copy; 2024 OneNews. 保留所有权利。</p>
          </div>
        </div>
      </body>
      </html>
    `,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ 测试邮件发送成功:', info.messageId);
    return {
      success: true,
      messageId: info.messageId,
      provider: getEmailProviderConfig().description,
    };
  } catch (error) {
    console.error('❌ 测试邮件发送失败:', error.message);
    throw new Error(`测试邮件发送失败: ${error.message}`);
  }
};

// 发送通知邮件
const sendNotificationEmail = async (user, notification) => {
  const transporter = createTransporter();

  const subject = getNotificationEmailSubject(notification.type);
  const htmlContent = getNotificationEmailTemplate(user, notification);

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: user.email,
    subject: `OneNews - ${subject}`,
    html: htmlContent,
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Notification email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending notification email:', error);
    return false;
  }
};

// 批量发送通知邮件
const sendBulkNotificationEmails = async (notifications) => {
  const results = [];

  for (const notification of notifications) {
    try {
      const result = await sendNotificationEmail(notification.user, notification);
      results.push({ notificationId: notification.id, success: result });
    } catch (error) {
      console.error(`Error sending notification email for notification ${notification.id}:`, error);
      results.push({ notificationId: notification.id, success: false, error: error.message });
    }
  }

  return results;
};

// Get notification email subject
const getNotificationEmailSubject = (type) => {
  const subjects = {
    like: 'Someone liked your content',
    comment: 'Someone commented on your content',
    follow: 'You have a new follower',
    message: 'You have a new message',
    article_published: 'Your article has been published',
    article_approved: 'Your article has been approved',
    article_rejected: 'Your article needs revision',
    system: 'System notification',
    promotion: 'Promotional information',
    reminder: 'Reminder notification',
    security: 'Security alert',
    newsletter: 'Newsletter',
  };

  return subjects[type] || 'New notification';
};

// Get notification email template
const getNotificationEmailTemplate = (user, notification) => {
  const baseTemplate = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Newzora Notification</title>
      <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; background: #ffffff; }
        .header { background: #2563eb; color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
        .content { padding: 40px 30px; background: #f8fafc; }
        .notification-card { background: white; border-radius: 8px; padding: 25px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .notification-title { font-size: 18px; font-weight: bold; color: #1e40af; margin-bottom: 10px; }
        .notification-content { font-size: 16px; color: #374151; margin-bottom: 15px; }
        .notification-meta { font-size: 14px; color: #6b7280; }
        .button { display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 6px; margin: 20px 0; font-weight: bold; }
        .button:hover { background: #1d4ed8; }
        .footer { padding: 30px 20px; text-align: center; color: #6b7280; font-size: 14px; background: #f1f5f9; }
        .footer a { color: #2563eb; text-decoration: none; }
        .divider { height: 1px; background: #e5e7eb; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Newzora</h1>
        </div>
        <div class="content">
          <div class="notification-card">
            <div class="notification-title">${getNotificationEmailSubject(notification.type)}</div>
            <div class="notification-content">${notification.content}</div>
            <div class="notification-meta">
              Sent: ${new Date(notification.createdAt).toLocaleString('en-US')}
            </div>
            ${
              notification.actionUrl
                ? `
              <a href="${notification.actionUrl}" class="button">View Details</a>
            `
                : ''
            }
          </div>
          <div class="divider"></div>
          <p style="color: #6b7280; font-size: 14px;">
            You received this email because you enabled email notifications on Newzora.
            To modify notification settings, please visit <a href="${process.env.FRONTEND_URL}/settings/notifications">Notification Settings</a>.
          </p>
        </div>
        <div class="footer">
          <p>&copy; 2024 OneNews. 保留所有权利。</p>
          <p>
            <a href="${process.env.FRONTEND_URL}">访问网站</a> |
            <a href="${process.env.FRONTEND_URL}/settings/notifications">通知设置</a> |
            <a href="${process.env.FRONTEND_URL}/unsubscribe">取消订阅</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;

  return baseTemplate;
};

// 发送每日摘要邮件
const sendDailySummaryEmail = async (user, summaryData) => {
  const transporter = createTransporter();

  const mailOptions = {
    from: process.env.EMAIL_FROM,
    to: user.email,
    subject: 'Newzora - 每日新闻摘要',
    html: getDailySummaryTemplate(user, summaryData),
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    console.log('Daily summary email sent:', info.messageId);
    return true;
  } catch (error) {
    console.error('Error sending daily summary email:', error);
    return false;
  }
};

// 每日摘要邮件模板
const getDailySummaryTemplate = (user, summaryData) => {
  const articlesHtml = summaryData.articles
    .map(
      (article) => `
    <div style="border-bottom: 1px solid #e5e7eb; padding: 15px 0;">
      <h3 style="margin: 0 0 8px 0; font-size: 16px;">
        <a href="${process.env.FRONTEND_URL}/articles/${article.id}" style="color: #1e40af; text-decoration: none;">
          ${article.title}
        </a>
      </h3>
      <p style="margin: 0 0 8px 0; color: #6b7280; font-size: 14px;">
        ${article.excerpt || article.content.substring(0, 100)}...
      </p>
      <div style="font-size: 12px; color: #9ca3af;">
        ${article.author} • ${new Date(article.createdAt).toLocaleDateString('zh-CN')}
      </div>
    </div>
  `
    )
    .join('');

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>OneNews 每日摘要</title>
      <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; background: #ffffff; }
        .header { background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 30px 20px; text-align: center; }
        .header h1 { margin: 0; font-size: 28px; font-weight: bold; }
        .content { padding: 30px; }
        .summary-stats { display: flex; justify-content: space-around; margin: 20px 0; }
        .stat-item { text-align: center; }
        .stat-number { font-size: 24px; font-weight: bold; color: #2563eb; }
        .stat-label { font-size: 14px; color: #6b7280; }
        .articles-section { margin: 30px 0; }
        .section-title { font-size: 20px; font-weight: bold; color: #1f2937; margin-bottom: 20px; }
        .footer { padding: 30px 20px; text-align: center; color: #6b7280; font-size: 14px; background: #f1f5f9; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>OneNews</h1>
          <p style="margin: 10px 0 0 0; font-size: 16px;">每日新闻摘要 - ${new Date().toLocaleDateString('zh-CN')}</p>
        </div>
        <div class="content">
          <p>您好 ${user.username}，</p>
          <p>以下是今日为您精选的新闻内容：</p>

          <div class="summary-stats">
            <div class="stat-item">
              <div class="stat-number">${summaryData.totalArticles}</div>
              <div class="stat-label">今日文章</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">${summaryData.totalReads}</div>
              <div class="stat-label">总阅读量</div>
            </div>
            <div class="stat-item">
              <div class="stat-number">${summaryData.newFollowers}</div>
              <div class="stat-label">新关注者</div>
            </div>
          </div>

          <div class="articles-section">
            <div class="section-title">📰 热门文章</div>
            ${articlesHtml}
          </div>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}" style="display: inline-block; padding: 12px 24px; background: #2563eb; color: white; text-decoration: none; border-radius: 6px; font-weight: bold;">
              查看更多内容
            </a>
          </div>
        </div>
        <div class="footer">
          <p>&copy; 2024 OneNews. 保留所有权利。</p>
          <p>
            <a href="${process.env.FRONTEND_URL}/settings/notifications">通知设置</a> |
            <a href="${process.env.FRONTEND_URL}/unsubscribe">取消订阅</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};

module.exports = {
  sendEmailVerification,
  sendPasswordReset,
  sendWelcomeEmail,
  testEmailConfig,
  sendTestEmail,
  sendNotificationEmail,
  sendBulkNotificationEmails,
  sendDailySummaryEmail,
  getEmailProviderConfig,
};
