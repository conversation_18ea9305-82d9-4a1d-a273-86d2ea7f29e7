'use client';

import { useState, useEffect } from 'react';
import { useData } from '@/contexts/DataContext';

interface InteractionStatsProps {
  workId: number;
  workType: 'article' | 'video' | 'audio';
  onComment?: () => void;
}

export default function InteractionStats({
  workId,
  workType,
  onComment,
}: InteractionStatsProps) {
  const { getWorkInteraction, toggleLike, toggleSave, incrementShares } = useData();
  const [interaction, setInteraction] = useState(getWorkInteraction(workId, workType));

  useEffect(() => {
    setInteraction(getWorkInteraction(workId, workType));
  }, [workId, workType, getWorkInteraction]);

  const handleLike = () => {
    toggleLike(workId, workType);
    setInteraction(getWorkInteraction(workId, workType));
  };

  const handleSave = () => {
    toggleSave(workId, workType);
    setInteraction(getWorkInteraction(workId, workType));
  };

  const handleShare = () => {
    incrementShares(workId, workType);
    setInteraction(getWorkInteraction(workId, workType));
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'k';
    }
    return num.toString();
  };

  return (
    <div className="flex items-center space-x-6 py-4 border-b border-gray-200">
      {/* Like Button */}
      <button
        onClick={handleLike}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 ${
          interaction.userLiked
            ? 'text-red-500 bg-red-50 hover:bg-red-100'
            : 'text-gray-500 hover:text-red-500 hover:bg-red-50'
        }`}
      >
        <svg
          className="w-5 h-5"
          fill={interaction.userLiked ? 'currentColor' : 'none'}
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
          />
        </svg>
        <span className="font-medium">{formatNumber(interaction.likes)}</span>
      </button>

      {/* Comment Button */}
      <button
        onClick={onComment}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-500 hover:text-blue-500 hover:bg-blue-50 transition-all duration-200 transform hover:scale-105"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-4.126-.98L3 21l1.98-5.874A8.955 8.955 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"
          />
        </svg>
        <span className="font-medium">{formatNumber(interaction.comments)}</span>
      </button>

      {/* Save Button */}
      <button
        onClick={handleSave}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 ${
          interaction.userSaved
            ? 'text-blue-500 bg-blue-50 hover:bg-blue-100'
            : 'text-gray-500 hover:text-blue-500 hover:bg-blue-50'
        }`}
      >
        <svg
          className="w-5 h-5"
          fill={interaction.userSaved ? 'currentColor' : 'none'}
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
          />
        </svg>
        <span className="font-medium">{formatNumber(interaction.saves)}</span>
      </button>

      {/* Share Button */}
      <button
        onClick={handleShare}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-500 hover:text-blue-500 hover:bg-blue-50 transition-all duration-200 transform hover:scale-105"
      >
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
          />
        </svg>
        <span className="font-medium">{formatNumber(interaction.shares)}</span>
      </button>

      {/* Views (Read-only) */}
      <div className="flex items-center space-x-2 text-gray-400 ml-auto">
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
          />
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
          />
        </svg>
        <span className="text-sm">{formatNumber(interaction.views)} views</span>
      </div>
    </div>
  );
}
