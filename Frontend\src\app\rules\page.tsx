import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Community Rules - Newzora',
  description: 'Community guidelines and rules for Newzora platform',
};

export default function RulesPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-sm p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Community Rules</h1>
          
          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Our Community Standards</h2>
              <p className="text-gray-600 leading-relaxed mb-6">
                Newzora is committed to fostering a respectful and inclusive community. Please follow these guidelines:
              </p>
              
              <div className="space-y-4">
                <div className="border-l-4 border-blue-500 pl-4">
                  <h3 className="font-medium text-gray-900">Be Respectful</h3>
                  <p className="text-gray-600">Treat all community members with respect and kindness.</p>
                </div>
                
                <div className="border-l-4 border-green-500 pl-4">
                  <h3 className="font-medium text-gray-900">Share Quality Content</h3>
                  <p className="text-gray-600">Post original, well-researched, and valuable content.</p>
                </div>
                
                <div className="border-l-4 border-yellow-500 pl-4">
                  <h3 className="font-medium text-gray-900">No Spam or Self-Promotion</h3>
                  <p className="text-gray-600">Avoid excessive self-promotion and spam content.</p>
                </div>
                
                <div className="border-l-4 border-red-500 pl-4">
                  <h3 className="font-medium text-gray-900">Prohibited Content</h3>
                  <p className="text-gray-600">No hate speech, harassment, illegal content, or misinformation.</p>
                </div>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Enforcement</h2>
              <p className="text-gray-600 leading-relaxed">
                Violations of these rules may result in content removal, account suspension, or permanent ban. 
                We review all reports and take appropriate action to maintain a safe community.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-gray-800 mb-4">Report Violations</h2>
              <p className="text-gray-600">
                If you encounter content that violates our rules, please report it to{' '}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800">
                  <EMAIL>
                </a>
              </p>
            </section>
          </div>
        </div>
      </div>
    </div>
  );
}