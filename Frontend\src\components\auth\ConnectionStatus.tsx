'use client';

import React from 'react';
import { AlertTriangle, Wifi, WifiOff, RefreshCw } from 'lucide-react';

// 连接状态类型定义
type ConnectionStatusType = 'connecting' | 'connected' | 'disconnected' | 'timeout';

// 组件Props类型定义
interface ConnectionStatusProps {
  status: ConnectionStatusType;
  error?: string | null;
  onRetry?: () => void;
  className?: string;
}

// 连接状态显示组件
export default function ConnectionStatus({ 
  status, 
  error, 
  onRetry, 
  className = '' 
}: ConnectionStatusProps) {
  
  // 根据状态返回不同的UI
  const renderStatusContent = () => {
    switch (status) {
      case 'connecting':
        return (
          <div className="flex items-center space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex-shrink-0">
              <RefreshCw className="h-5 w-5 text-blue-600 animate-spin" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-800">
                正在连接认证服务...
              </p>
              <p className="text-xs text-blue-600 mt-1">
                请稍候，正在建立安全连接
              </p>
            </div>
          </div>
        );

      case 'connected':
        return (
          <div className="flex items-center space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex-shrink-0">
              <Wifi className="h-4 w-4 text-green-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-green-800">
                认证服务连接正常
              </p>
            </div>
          </div>
        );

      case 'disconnected':
        return (
          <div className="flex items-center space-x-3 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex-shrink-0">
              <WifiOff className="h-5 w-5 text-red-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-red-800">
                认证服务连接失败
              </p>
              <p className="text-xs text-red-600 mt-1">
                {error || '无法连接到认证服务，请检查网络连接'}
              </p>
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="mt-2 text-xs text-red-700 hover:text-red-900 underline focus:outline-none"
                >
                  点击重试
                </button>
              )}
            </div>
          </div>
        );

      case 'timeout':
        return (
          <div className="flex items-center space-x-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-yellow-800">
                认证服务连接超时
              </p>
              <p className="text-xs text-yellow-600 mt-1">
                {error || '连接超时，可能是网络较慢或服务暂时不可用'}
              </p>
              <div className="mt-2 space-y-1">
                <p className="text-xs text-yellow-700">
                  建议解决方案：
                </p>
                <ul className="text-xs text-yellow-600 ml-4 space-y-1">
                  <li>• 检查网络连接是否正常</li>
                  <li>• 尝试刷新页面</li>
                  <li>• 稍后再试</li>
                </ul>
                {onRetry && (
                  <button
                    onClick={onRetry}
                    className="mt-2 inline-flex items-center space-x-1 text-xs text-yellow-700 hover:text-yellow-900 underline focus:outline-none"
                  >
                    <RefreshCw className="h-3 w-3" />
                    <span>重新连接</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // 如果状态是connected且没有错误，不显示任何内容
  if (status === 'connected' && !error) {
    return null;
  }

  return (
    <div className={`w-full ${className}`}>
      {renderStatusContent()}
    </div>
  );
}

// 简化版本的连接状态指示器
export function ConnectionIndicator({ status }: { status: ConnectionStatusType }) {
  const getIndicatorColor = () => {
    switch (status) {
      case 'connecting':
        return 'bg-blue-500 animate-pulse';
      case 'connected':
        return 'bg-green-500';
      case 'disconnected':
        return 'bg-red-500';
      case 'timeout':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connecting':
        return '连接中';
      case 'connected':
        return '已连接';
      case 'disconnected':
        return '连接失败';
      case 'timeout':
        return '连接超时';
      default:
        return '未知状态';
    }
  };

  return (
    <div className="flex items-center space-x-2">
      <div className={`w-2 h-2 rounded-full ${getIndicatorColor()}`} />
      <span className="text-xs text-gray-600">{getStatusText()}</span>
    </div>
  );
}
