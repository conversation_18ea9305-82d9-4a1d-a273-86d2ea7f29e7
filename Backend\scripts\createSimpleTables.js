const { sequelize } = require('../config/database');
const SimpleUser = require('../models/SimpleUser');

/**
 * 创建简化的数据库表
 */

async function createSimpleTables() {
  try {
    console.log('🏗️  开始创建简化数据库表...');
    console.log('==========================================');

    // 同步数据库表
    await SimpleUser.sync({ force: true });
    console.log('✅ 已创建 simple_users 表');

    console.log('\n🎉 数据库表创建完成！');
    console.log('==========================================');

    // 显示表结构
    const tableInfo = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'simple_users' 
      ORDER BY ordinal_position
    `);

    console.log('\n📋 simple_users 表结构:');
    console.log('==========================================');
    tableInfo[0].forEach(column => {
      console.log(`${column.column_name}: ${column.data_type} ${column.is_nullable === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });

  } catch (error) {
    console.error('❌ 创建数据库表失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createSimpleTables()
    .then(() => {
      console.log('✅ 表创建脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 表创建脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { createSimpleTables };
