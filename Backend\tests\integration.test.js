/**
 * 集成测试套件
 */

const request = require('supertest');
const { sequelize } = require('../models');

// 模拟应用实例
const createTestApp = () => {
  const express = require('express');
  const app = express();
  
  app.use(express.json());
  
  // 基本路由用于测试
  app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
  });
  
  app.post('/api/test', (req, res) => {
    res.json({ success: true, data: req.body });
  });
  
  return app;
};

describe('Integration Tests', () => {
  let app;

  beforeAll(async () => {
    app = createTestApp();
    
    // 设置测试数据库连接
    if (sequelize) {
      try {
        await sequelize.authenticate();
        console.log('Test database connection established');
      } catch (error) {
        console.warn('Test database not available:', error.message);
      }
    }
  });

  afterAll(async () => {
    if (sequelize) {
      await sequelize.close();
    }
  });

  describe('Health Check', () => {
    test('should return health status', async () => {
      const response = await request(app).get('/health');
      
      expect(response.status).toBe(200);
      expect(response.body.status).toBe('ok');
      expect(response.body.timestamp).toBeDefined();
    });
  });

  describe('API Endpoints', () => {
    test('should handle POST requests', async () => {
      const testData = { message: 'test data' };
      
      const response = await request(app)
        .post('/api/test')
        .send(testData);
      
      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(testData);
    });

    test('should handle malformed JSON', async () => {
      const response = await request(app)
        .post('/api/test')
        .set('Content-Type', 'application/json')
        .send('invalid json');
      
      expect(response.status).toBe(400);
    });
  });

  describe('Database Integration', () => {
    test('should connect to database', async () => {
      if (!sequelize) {
        console.log('Skipping database test - sequelize not available');
        return;
      }

      try {
        await sequelize.authenticate();
        expect(true).toBe(true); // 连接成功
      } catch (error) {
        console.warn('Database connection failed:', error.message);
        expect(error).toBeUndefined(); // 如果连接失败，测试失败
      }
    });
  });

  describe('Error Handling', () => {
    test('should handle 404 errors', async () => {
      const response = await request(app).get('/nonexistent');
      
      expect(response.status).toBe(404);
    });

    test('should handle server errors gracefully', async () => {
      // 创建一个会抛出错误的路由
      app.get('/error-test', (req, res) => {
        throw new Error('Test error');
      });

      const response = await request(app).get('/error-test');
      
      // 应该返回500错误而不是崩溃
      expect(response.status).toBe(500);
    });
  });
});