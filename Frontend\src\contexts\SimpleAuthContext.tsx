'use client';

import { createClient } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import type { Database } from '@/lib/supabase/database.types';
import React, { useEffect, useCallback, createContext, useContext } from 'react';

// 用户元数据类型定义
type UserMetadata = {
  name?: string;
  avatar_url?: string;
  username?: string;
};

// 连接状态类型定义
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'timeout';

// 认证上下文类型定义
export type AuthContextType = {
  user: Database['public']['Tables']['profiles']['Row'] | null;
  session: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  connectionStatus: ConnectionStatus;
  login: (email: string, password: string) => Promise<boolean>;
  loginWithProvider: (provider: 'google' | 'facebook' | 'twitter') => Promise<boolean>;
  register: (email: string, password: string, metadata?: UserMetadata) => Promise<{data: any, error: any}>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (newPassword: string) => Promise<boolean>;
  logout: () => Promise<void>;
  clearError: () => void;
};

// 创建React Context
const AuthContext = createContext<AuthContextType | null>(null);

// Supabase 配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// 创建 Supabase 客户端
const supabase = createClient<Database>(
  supabaseUrl,
  supabaseKey,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: 'pkce'
    }
  }
);

export const SimpleAuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = React.useState<Database['public']['Tables']['profiles']['Row'] | null>(null);
  const [session, setSession] = React.useState<any>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  const [connectionStatus, setConnectionStatus] = React.useState<ConnectionStatus>('disconnected');

  const router = useRouter();

  // 获取用户资料
  const fetchUserProfile = useCallback(async (userId: string): Promise<void> => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('获取用户资料失败:', error);
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          const basicUser = {
            id: user.id,
            email: user.email,
            username: user.user_metadata?.username || user.email?.split('@')[0] || 'User',
            role: 'user',
            created_at: user.created_at
          };
          setUser(basicUser as any);
        }
        return;
      }

      if (data) {
        setUser(data);
      }
    } catch (err) {
      console.error('获取用户资料异常:', err);
    }
  }, []);

  // 初始化认证状态
  useEffect(() => {
    let mounted = true;

    const initAuth = async () => {
      try {
        setConnectionStatus('connecting');
        setIsLoading(true);

        const { data: { session }, error } = await supabase.auth.getSession();

        if (!mounted) return;

        setConnectionStatus('connected');

        if (error) {
          console.error('获取会话失败:', error);
          setUser(null);
          setSession(null);
          setIsAuthenticated(false);
          return;
        }

        if (session?.user) {
          setSession(session);
          setIsAuthenticated(true);
          setError(null);
          
          if (session.user.id) {
            await fetchUserProfile(session.user.id);
          }
        } else {
          setUser(null);
          setSession(null);
          setIsAuthenticated(false);
        }
      } catch (err) {
        console.error('认证初始化失败:', err);
        if (mounted) {
          setConnectionStatus('disconnected');
          setUser(null);
          setSession(null);
          setIsAuthenticated(false);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    initAuth();

    return () => {
      mounted = false;
    };
  }, [fetchUserProfile]);

  // 监听认证状态变化
  useEffect(() => {
    let mounted = true;
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      if (!mounted) return;

      if (event === 'SIGNED_IN' && session?.user) {
        if (mounted) {
          setSession(session);
          setIsAuthenticated(true);
          setError(null);
          setConnectionStatus('connected');
          
          if (session.user.id) {
            await fetchUserProfile(session.user.id);
          }
        }
      } else if (event === 'SIGNED_OUT' || !session) {
        if (mounted) {
          setSession(null);
          setUser(null);
          setIsAuthenticated(false);
          setError(null);
        }
      } else if (event === 'TOKEN_REFRESHED' && session?.user) {
        if (mounted) {
          setSession(session);
          if (!isAuthenticated && session.user.id) {
            setIsAuthenticated(true);
            await fetchUserProfile(session.user.id);
          }
        }
      }
    });

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, [fetchUserProfile, isAuthenticated]);

  // 登录函数
  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      if (!email || !password) {
        throw new Error('Email and password are required');
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.toLowerCase().trim(),
        password,
      });

      if (error) {
        let errorMessage = 'Login failed. Please try again.';
        if (error.message.includes('Invalid login credentials')) {
          errorMessage = 'Invalid email or password.';
        }
        setError(errorMessage);
        return false;
      }

      if (data.user && data.session) {
        return true;
      }

      throw new Error('Login failed: No user session created');
    } catch (err: any) {
      setError(err.message || 'Login failed. Please try again.');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 社交登录
  const loginWithProvider = useCallback(async (provider: 'google' | 'facebook' | 'twitter'): Promise<boolean> => {
    try {
      setError(null);

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback/${provider}`,
        }
      });

      if (error) {
        setError(error.message);
        return false;
      }

      return true;
    } catch (err: any) {
      setError(err.message || 'Social login failed');
      return false;
    }
  }, []);

  // 注册函数
  const register = useCallback(async (email: string, password: string, metadata?: UserMetadata): Promise<{data: any, error: any}> => {
    try {
      setIsLoading(true);
      setError(null);

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: metadata || {}
        }
      });

      if (error) {
        setError(error.message);
        return { data: null, error };
      }

      return { data, error: null };
    } catch (err: any) {
      const errorMsg = err.message || 'Registration failed';
      setError(errorMsg);
      return { data: null, error: { message: errorMsg } };
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 重置密码
  const resetPassword = useCallback(async (email: string): Promise<boolean> => {
    try {
      setError(null);
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) {
        setError(error.message);
        return false;
      }
      return true;
    } catch (err: any) {
      setError(err.message || 'Password reset failed');
      return false;
    }
  }, []);

  // 更新密码
  const updatePassword = useCallback(async (newPassword: string): Promise<boolean> => {
    try {
      setError(null);
      const { error } = await supabase.auth.updateUser({ password: newPassword });
      if (error) {
        setError(error.message);
        return false;
      }
      return true;
    } catch (err: any) {
      setError(err.message || 'Password update failed');
      return false;
    }
  }, []);

  // 登出函数
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setUser(null);
      setSession(null);
      setIsAuthenticated(false);
      setError(null);
      
      await supabase.auth.signOut();
    } catch (err: any) {
      console.error('Logout error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 清除错误
  const clearError = useCallback(() => setError(null), []);

  const contextValue: AuthContextType = {
    user,
    session,
    isAuthenticated,
    isLoading,
    error,
    connectionStatus,
    login,
    loginWithProvider,
    register,
    resetPassword,
    updatePassword,
    logout,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useSimpleAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useSimpleAuth must be used within a SimpleAuthProvider');
  }
  return context;
};