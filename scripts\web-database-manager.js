#!/usr/bin/env node

/**
 * Newzora Web 数据库管理工具
 * 专门用于管理主站前台的数据库
 */

const { execSync } = require('child_process');
const path = require('path');
require('dotenv').config();

// 主站前台数据库配置
const webDatabase = {
  name: 'PostgreSQL-newzora_web',
  password: 'wasd080980!',
  port: 5432,
  description: '主站前台数据库'
};

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description) {
  try {
    log(`🔄 ${description}...`, 'cyan');
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    log(`✅ ${description} 完成`, 'green');
    return result;
  } catch (error) {
    log(`❌ ${description} 失败: ${error.message}`, 'red');
    throw error;
  }
}

// 创建数据库
function createDatabase() {
  log(`\n📋 创建${webDatabase.description}`, 'bright');
  
  const createDbCommand = `psql -U postgres -h localhost -c "CREATE DATABASE \\"${webDatabase.name}\\" WITH ENCODING='UTF8';"`;
  
  try {
    executeCommand(createDbCommand, `创建数据库 ${webDatabase.name}`);
  } catch (error) {
    if (error.message.includes('already exists')) {
      log(`⚠️  数据库 ${webDatabase.name} 已存在`, 'yellow');
    } else {
      throw error;
    }
  }
}

// 初始化数据库
function initDatabase() {
  log(`\n🔧 初始化${webDatabase.description}`, 'bright');
  
  const scriptPath = path.join(__dirname, '../Backend/scripts/init-db.sql');
  const workingDir = path.join(__dirname, '../Backend');

  // 执行SQL初始化脚本
  const initCommand = `psql -U postgres -h localhost -d "${webDatabase.name}" -f "${scriptPath}"`;
  executeCommand(initCommand, `执行初始化脚本`);
}

// 备份数据库
function backupDatabase() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFile = path.join(__dirname, `../backups/${webDatabase.name}_${timestamp}.sql`);
  
  log(`\n💾 备份${webDatabase.description}`, 'bright');
  
  // 确保备份目录存在
  const mkdirCommand = `mkdir -p "${path.dirname(backupFile)}"`;
  executeCommand(mkdirCommand, '创建备份目录');
  
  const backupCommand = `pg_dump -U postgres -h localhost "${webDatabase.name}" > "${backupFile}"`;
  executeCommand(backupCommand, `备份数据库到 ${backupFile}`);
  
  return backupFile;
}

// 检查数据库状态
function checkDatabaseStatus() {
  log(`\n📊 检查${webDatabase.description}状态`, 'bright');
  
  try {
    const checkCommand = `psql -U postgres -h localhost -d "${webDatabase.name}" -c "SELECT current_database(), current_user, version();"`;
    const result = executeCommand(checkCommand, '检查数据库连接');
    log(result, 'cyan');
  } catch (error) {
    log(`❌ 数据库 ${webDatabase.name} 不可访问`, 'red');
  }
}

// 完整设置
function fullSetup() {
  log('\n🚀 开始主站前台数据库设置', 'bright');
  
  try {
    createDatabase();
    initDatabase();
    
    log('\n🎉 主站前台数据库设置完成！', 'green');
    log(`\n📋 数据库信息: ${webDatabase.name} (端口: ${webDatabase.port})`, 'cyan');
    
  } catch (error) {
    log(`\n❌ 设置失败: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 显示帮助信息
function showHelp() {
  log('\n🚀 Newzora Web 数据库管理工具', 'bright');
  log('\n用法: node web-database-manager.js <命令>', 'cyan');
  log('\n可用命令:', 'yellow');
  log('  create     - 创建数据库', 'green');
  log('  init       - 初始化数据库', 'blue');
  log('  backup     - 备份数据库', 'magenta');
  log('  status     - 检查数据库状态', 'yellow');
  log('  setup      - 完整设置（创建+初始化）', 'bright');
  log('  help       - 显示此帮助信息', 'reset');
  log(`\n数据库信息: ${webDatabase.description} (${webDatabase.name})`, 'cyan');
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  try {
    switch (command) {
      case 'create':
        createDatabase();
        break;
      case 'init':
        initDatabase();
        break;
      case 'backup':
        const backupFile = backupDatabase();
        log(`\n✅ 备份完成: ${backupFile}`, 'green');
        break;
      case 'status':
        checkDatabaseStatus();
        break;
      case 'setup':
        fullSetup();
        break;
      case 'help':
      default:
        showHelp();
        break;
    }
  } catch (error) {
    log(`\n❌ 错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createDatabase, initDatabase, backupDatabase, checkDatabaseStatus, fullSetup };