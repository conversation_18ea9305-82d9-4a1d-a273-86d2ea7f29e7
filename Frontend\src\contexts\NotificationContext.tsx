'use client';

import React, { useState, useContext, createContext, useEffect } from 'react';

// 通知类型
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  userId?: number;
}

// 通知状态
interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  isLoading: boolean;
}

// 通知操作
interface NotificationActions {
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  clearNotification: (id: string) => void;
  clearAllNotifications: () => void;
  fetchNotifications: () => Promise<void>;
}

// Context类型
type NotificationContextType = NotificationState & NotificationActions;

// 创建Context
const NotificationContext = createContext<NotificationContextType | null>(null);

// NotificationProvider组件
export function NotificationProvider({ children }: { children: React.ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isConnected, setIsConnected] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [mounted, setMounted] = useState(false);

  // 确保组件已挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 计算未读数量
  const unreadCount = notifications.filter((n) => !n.read).length;

  // 添加通知
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      timestamp: new Date(),
      read: false,
    };

    setNotifications((prev) => [newNotification, ...prev]);
  };

  // 标记为已读
  const markAsRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  // 标记全部为已读
  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notification) => ({ ...notification, read: true })));
  };

  // 移除通知
  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
  };

  // 清除所有通知
  const clearAll = () => {
    setNotifications([]);
  };

  // 别名方法
  const clearNotification = removeNotification;
  const clearAllNotifications = clearAll;

  // 获取通知（模拟数据）
  const fetchNotifications = async () => {
    if (!mounted) return;
    
    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const sampleNotifications: Notification[] = [
        {
          id: '1',
          type: 'info',
          title: 'Welcome to ' + ['N','e','w','z','o','r','a'].join(''), // Newzora - 防翻译
          message: 'Thank you for using our platform!',
          timestamp: new Date(Date.now() - 1000 * 60 * 30),
          read: false,
        },
        {
          id: '2',
          type: 'success',
          title: 'Login Successful',
          message: 'You have successfully logged in',
          timestamp: new Date(Date.now() - 1000 * 60 * 10),
          read: false,
        },
      ];
      
      setNotifications(sampleNotifications);
      setIsConnected(true);
    } catch (error) {
      console.error('Error fetching notifications:', error);
      setIsConnected(false);
    } finally {
      setIsLoading(false);
    }
  };

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    isConnected,
    isLoading,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    clearNotification,
    clearAllNotifications,
    fetchNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}

// useNotifications hook
export function useNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}