// 第三方集成配置文件
// 注意：实际密钥应通过环境变量配置，此文件仅作为配置模板

const integrationConfig = {
  // 支付服务配置
  payment: {
    wechatPay: {
      enabled: process.env.WECHAT_PAY_ENABLED === 'true',
      appId: process.env.WECHAT_PAY_APP_ID || '',
      mchId: process.env.WECHAT_PAY_MCH_ID || '',
      apiKey: process.env.WECHAT_PAY_API_KEY || '',
      certPath: process.env.WECHAT_PAY_CERT_PATH || '',
      keyPath: process.env.WECHAT_PAY_KEY_PATH || '',
      notifyUrl: process.env.WECHAT_PAY_NOTIFY_URL || '/api/payment/wechat/notify',
      sandbox: process.env.NODE_ENV !== 'production'
    },
    alipay: {
      enabled: process.env.ALIPAY_ENABLED === 'true',
      appId: process.env.ALIPAY_APP_ID || '',
      privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
      publicKey: process.env.ALIPAY_PUBLIC_KEY || '',
      gateway: process.env.ALIPAY_GATEWAY || 'https://openapi.alipay.com/gateway.do',
      notifyUrl: process.env.ALIPAY_NOTIFY_URL || '/api/payment/alipay/notify',
      returnUrl: process.env.ALIPAY_RETURN_URL || '/payment/success',
      sandbox: process.env.NODE_ENV !== 'production'
    }
  },

  // 云存储服务配置
  storage: {
    qiniuCloud: {
      enabled: process.env.QINIU_ENABLED === 'true',
      accessKey: process.env.QINIU_ACCESS_KEY || '',
      secretKey: process.env.QINIU_SECRET_KEY || '',
      bucket: process.env.QINIU_BUCKET || '',
      domain: process.env.QINIU_DOMAIN || '',
      region: process.env.QINIU_REGION || 'Zone_z0'
    },
    aliyunOSS: {
      enabled: process.env.ALIYUN_OSS_ENABLED === 'true',
      accessKeyId: process.env.ALIYUN_OSS_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.ALIYUN_OSS_ACCESS_KEY_SECRET || '',
      bucket: process.env.ALIYUN_OSS_BUCKET || '',
      region: process.env.ALIYUN_OSS_REGION || 'oss-cn-hangzhou',
      endpoint: process.env.ALIYUN_OSS_ENDPOINT || ''
    }
  },

  // AI服务配置
  ai: {
    baiduAI: {
      enabled: process.env.BAIDU_AI_ENABLED === 'true',
      apiKey: process.env.BAIDU_AI_API_KEY || '',
      secretKey: process.env.BAIDU_AI_SECRET_KEY || '',
      services: {
        contentCensor: process.env.BAIDU_AI_CONTENT_CENSOR === 'true',
        textAnalysis: process.env.BAIDU_AI_TEXT_ANALYSIS === 'true',
        imageRecognition: process.env.BAIDU_AI_IMAGE_RECOGNITION === 'true'
      }
    },
    tencentAI: {
      enabled: process.env.TENCENT_AI_ENABLED === 'true',
      secretId: process.env.TENCENT_AI_SECRET_ID || '',
      secretKey: process.env.TENCENT_AI_SECRET_KEY || '',
      region: process.env.TENCENT_AI_REGION || 'ap-beijing'
    }
  },

  // 通信服务配置
  communication: {
    tencentSMS: {
      enabled: process.env.TENCENT_SMS_ENABLED === 'true',
      secretId: process.env.TENCENT_SMS_SECRET_ID || '',
      secretKey: process.env.TENCENT_SMS_SECRET_KEY || '',
      sdkAppId: process.env.TENCENT_SMS_SDK_APP_ID || '',
      signName: process.env.TENCENT_SMS_SIGN_NAME || '',
      templateId: {
        verification: process.env.TENCENT_SMS_TEMPLATE_VERIFICATION || '',
        notification: process.env.TENCENT_SMS_TEMPLATE_NOTIFICATION || ''
      }
    },
    aliyunSMS: {
      enabled: process.env.ALIYUN_SMS_ENABLED === 'true',
      accessKeyId: process.env.ALIYUN_SMS_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.ALIYUN_SMS_ACCESS_KEY_SECRET || '',
      signName: process.env.ALIYUN_SMS_SIGN_NAME || '',
      templateCode: {
        verification: process.env.ALIYUN_SMS_TEMPLATE_VERIFICATION || '',
        notification: process.env.ALIYUN_SMS_TEMPLATE_NOTIFICATION || ''
      }
    }
  },

  // 分析服务配置
  analytics: {
    googleAnalytics: {
      enabled: process.env.GOOGLE_ANALYTICS_ENABLED === 'true',
      trackingId: process.env.GOOGLE_ANALYTICS_TRACKING_ID || '',
      measurementId: process.env.GOOGLE_ANALYTICS_MEASUREMENT_ID || ''
    },
    baiduTongji: {
      enabled: process.env.BAIDU_TONGJI_ENABLED === 'true',
      siteId: process.env.BAIDU_TONGJI_SITE_ID || '',
      token: process.env.BAIDU_TONGJI_TOKEN || ''
    }
  },

  // 社交登录配置
  socialAuth: {
    wechat: {
      enabled: process.env.WECHAT_LOGIN_ENABLED === 'true',
      appId: process.env.WECHAT_APP_ID || '',
      appSecret: process.env.WECHAT_APP_SECRET || '',
      scope: 'snsapi_userinfo',
      redirectUri: process.env.WECHAT_REDIRECT_URI || '/auth/wechat/callback'
    },
    qq: {
      enabled: process.env.QQ_LOGIN_ENABLED === 'true',
      appId: process.env.QQ_APP_ID || '',
      appKey: process.env.QQ_APP_KEY || '',
      redirectUri: process.env.QQ_REDIRECT_URI || '/auth/qq/callback'
    },
    weibo: {
      enabled: process.env.WEIBO_LOGIN_ENABLED === 'true',
      appKey: process.env.WEIBO_APP_KEY || '',
      appSecret: process.env.WEIBO_APP_SECRET || '',
      redirectUri: process.env.WEIBO_REDIRECT_URI || '/auth/weibo/callback'
    }
  },

  // CDN配置
  cdn: {
    aliyunCDN: {
      enabled: process.env.ALIYUN_CDN_ENABLED === 'true',
      accessKeyId: process.env.ALIYUN_CDN_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.ALIYUN_CDN_ACCESS_KEY_SECRET || '',
      domain: process.env.ALIYUN_CDN_DOMAIN || ''
    },
    tencentCDN: {
      enabled: process.env.TENCENT_CDN_ENABLED === 'true',
      secretId: process.env.TENCENT_CDN_SECRET_ID || '',
      secretKey: process.env.TENCENT_CDN_SECRET_KEY || '',
      domain: process.env.TENCENT_CDN_DOMAIN || ''
    }
  }
};

// 验证配置完整性
function validateIntegrationConfig() {
  const warnings = [];
  
  // 检查支付配置
  if (integrationConfig.payment.wechatPay.enabled && !integrationConfig.payment.wechatPay.appId) {
    warnings.push('微信支付已启用但缺少AppId配置');
  }
  
  if (integrationConfig.payment.alipay.enabled && !integrationConfig.payment.alipay.appId) {
    warnings.push('支付宝已启用但缺少AppId配置');
  }
  
  // 检查存储配置
  if (integrationConfig.storage.qiniuCloud.enabled && !integrationConfig.storage.qiniuCloud.accessKey) {
    warnings.push('七牛云存储已启用但缺少AccessKey配置');
  }
  
  if (integrationConfig.storage.aliyunOSS.enabled && !integrationConfig.storage.aliyunOSS.accessKeyId) {
    warnings.push('阿里云OSS已启用但缺少AccessKeyId配置');
  }
  
  // 检查AI服务配置
  if (integrationConfig.ai.baiduAI.enabled && !integrationConfig.ai.baiduAI.apiKey) {
    warnings.push('百度AI已启用但缺少ApiKey配置');
  }
  
  if (warnings.length > 0) {
    console.warn('集成配置警告:', warnings);
  }
  
  return warnings;
}

// 获取已启用的集成服务
function getEnabledIntegrations() {
  const enabled = {};
  
  Object.keys(integrationConfig).forEach(category => {
    enabled[category] = {};
    Object.keys(integrationConfig[category]).forEach(service => {
      if (integrationConfig[category][service].enabled) {
        enabled[category][service] = true;
      }
    });
  });
  
  return enabled;
}

module.exports = {
  integrationConfig,
  validateIntegrationConfig,
  getEnabledIntegrations
};