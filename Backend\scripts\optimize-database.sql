-- OneNews 数据库生产环境优化脚本
-- PostgreSQL 性能优化、索引创建和维护

-- =============================================
-- 1. 基础性能配置
-- =============================================

-- 设置工作内存 (每个查询操作可用内存)
SET work_mem = '256MB';

-- 设置维护工作内存 (维护操作如 VACUUM, CREATE INDEX)
SET maintenance_work_mem = '512MB';

-- 设置有效缓存大小 (PostgreSQL 可用的系统缓存)
SET effective_cache_size = '2GB';

-- 设置共享缓冲区 (PostgreSQL 内部缓存)
-- 注意：这个设置需要在 postgresql.conf 中配置
-- shared_buffers = '512MB'

-- 启用并行查询
SET max_parallel_workers_per_gather = 2;
SET max_parallel_workers = 4;

-- 设置随机页面成本 (SSD 存储建议设置为 1.1)
SET random_page_cost = 1.1;

-- 启用 JIT 编译 (PostgreSQL 11+)
SET jit = on;
SET jit_above_cost = 100000;

-- =============================================
-- 2. 用户表索引优化
-- =============================================

-- 用户表基础索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email 
ON users(email) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username 
ON users(username) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status 
ON users(status) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_role 
ON users(role) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at 
ON users(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_last_login 
ON users(last_login_at DESC) WHERE last_login_at IS NOT NULL;

-- 用户复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_role 
ON users(status, role) WHERE deleted_at IS NULL;

-- =============================================
-- 3. 文章表索引优化
-- =============================================

-- 文章表基础索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_author_id 
ON articles(author_id) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_status 
ON articles(status) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_category_id 
ON articles(category_id) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_published_at 
ON articles(published_at DESC) WHERE published_at IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_created_at 
ON articles(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_updated_at 
ON articles(updated_at DESC);

-- 文章全文搜索索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_title_search 
ON articles USING gin(to_tsvector('english', title)) 
WHERE deleted_at IS NULL AND status = 'published';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_content_search 
ON articles USING gin(to_tsvector('english', content)) 
WHERE deleted_at IS NULL AND status = 'published';

-- 文章复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_status_published 
ON articles(status, published_at DESC) 
WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_author_status 
ON articles(author_id, status, published_at DESC) 
WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_category_status 
ON articles(category_id, status, published_at DESC) 
WHERE deleted_at IS NULL;

-- =============================================
-- 4. 评论表索引优化
-- =============================================

-- 评论表基础索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_article_id 
ON comments(article_id) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_user_id 
ON comments(user_id) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_parent_id 
ON comments(parent_id) WHERE parent_id IS NOT NULL AND deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_created_at 
ON comments(created_at DESC);

-- 评论复合索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_article_created 
ON comments(article_id, created_at DESC) WHERE deleted_at IS NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_user_created 
ON comments(user_id, created_at DESC) WHERE deleted_at IS NULL;

-- =============================================
-- 5. 社交功能索引优化
-- =============================================

-- 关注关系索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_follower_id 
ON follows(follower_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_following_id 
ON follows(following_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_created_at 
ON follows(created_at DESC);

-- 点赞索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_likes_user_id 
ON likes(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_likes_article_id 
ON likes(article_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_likes_user_article 
ON likes(user_id, article_id);

-- 私信索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_sender_id 
ON messages(sender_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_receiver_id 
ON messages(receiver_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_created_at 
ON messages(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_conversation 
ON messages(sender_id, receiver_id, created_at DESC);

-- =============================================
-- 6. 通知系统索引优化
-- =============================================

-- 通知索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_id 
ON notifications(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_read_status 
ON notifications(is_read, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user_unread 
ON notifications(user_id, is_read, created_at DESC) 
WHERE is_read = false;

-- =============================================
-- 7. 分析统计索引优化
-- =============================================

-- 用户行为分析索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_user_id 
ON user_activities(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_action 
ON user_activities(action);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_created_at 
ON user_activities(created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_activities_user_action_date 
ON user_activities(user_id, action, created_at DESC);

-- 文章统计索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_article_stats_article_id 
ON article_stats(article_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_article_stats_views 
ON article_stats(view_count DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_article_stats_likes 
ON article_stats(like_count DESC);

-- =============================================
-- 8. 内容管理索引优化
-- =============================================

-- 媒体文件索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_files_user_id 
ON media_files(user_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_files_type 
ON media_files(file_type);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_files_status 
ON media_files(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_media_files_created_at 
ON media_files(created_at DESC);

-- 内容审核索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_content_reviews_status 
ON content_reviews(status);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_content_reviews_content_type 
ON content_reviews(content_type, content_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_content_reviews_created_at 
ON content_reviews(created_at DESC);

-- =============================================
-- 9. 性能监控视图
-- =============================================

-- 创建慢查询监控视图
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 1000  -- 平均执行时间超过1秒
ORDER BY mean_time DESC;

-- 创建索引使用情况监控视图
CREATE OR REPLACE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE 
        WHEN idx_scan = 0 THEN 'Unused'
        WHEN idx_scan < 100 THEN 'Low Usage'
        ELSE 'Active'
    END as usage_status
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- 创建表大小监控视图
CREATE OR REPLACE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- =============================================
-- 10. 维护任务
-- =============================================

-- 更新表统计信息
ANALYZE;

-- 重建索引统计信息
REINDEX DATABASE onenews;

-- 清理死元组
VACUUM ANALYZE;

-- =============================================
-- 11. 性能优化建议查询
-- =============================================

-- 查找未使用的索引
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE idx_scan = 0 
    AND schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- 查找缺少索引的外键
SELECT 
    c.conname AS constraint_name,
    t.relname AS table_name,
    ARRAY_AGG(a.attname ORDER BY t2.seq) AS columns,
    pg_size_pretty(pg_total_relation_size(t.oid)) AS table_size
FROM pg_constraint c
JOIN pg_class t ON c.conrelid = t.oid
JOIN pg_namespace n ON t.relnamespace = n.oid
JOIN (
    SELECT 
        conrelid,
        UNNEST(conkey) AS attnum,
        ROW_NUMBER() OVER() AS seq
    FROM pg_constraint
    WHERE contype = 'f'
) t2 ON c.conrelid = t2.conrelid
JOIN pg_attribute a ON t.oid = a.attrelid AND a.attnum = t2.attnum
WHERE c.contype = 'f'
    AND n.nspname = 'public'
    AND NOT EXISTS (
        SELECT 1 
        FROM pg_index i 
        WHERE i.indrelid = c.conrelid 
            AND i.indkey::text LIKE '%' || t2.attnum || '%'
    )
GROUP BY c.conname, t.relname, t.oid
ORDER BY pg_total_relation_size(t.oid) DESC;
