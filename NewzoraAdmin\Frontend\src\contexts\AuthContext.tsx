'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { AdminRole } from '@/types/admin';
import api from '@/lib/api';

interface User {
  id: string;
  username: string;
  email: string;
  display_name: string;
  role: AdminRole;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
  initializing: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(false);
  const [initializing, setInitializing] = useState(true);

  // 添加未授权事件监听
  useEffect(() => {
    const handleUnauthorized = () => {
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      setUser(null);
    };

    window.addEventListener('unauthorized', handleUnauthorized);
    return () => {
      window.removeEventListener('unauthorized', handleUnauthorized);
    };
  }, []);

  // 初始化时检查localStorage中是否有token并验证
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('admin_token');
      if (token) {
        try {
          // 验证token有效性
          const response = await api.get('/api/auth/verify');
          
          if (response.data && response.data.valid) {
            const savedUser = localStorage.getItem('admin_user');
            if (savedUser) {
              try {
                setUser(JSON.parse(savedUser));
              } catch (error) {
                console.error('Failed to parse user information:', error);
                localStorage.removeItem('admin_token');
                localStorage.removeItem('admin_user');
              }
            }
          } else {
            // Token无效，清除存储
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_user');
          }
        } catch (error) {
          // 验证失败，清除存储
          console.error('Token verification failed:', error);
          localStorage.removeItem('admin_token');
          localStorage.removeItem('admin_user');
        }
      }
      setInitializing(false);
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      console.log('Attempting login with email:', email);
      const response = await api.post('/api/auth/login', { email, password });
      console.log('Login response:', response.data);

      const { success, token, user, message } = response.data;

      if (!success || !token || !user) {
        throw new Error(message || 'Login response data is incomplete');
      }

      // Map backend user data to frontend User interface
      const mappedUser: User = {
        id: user.id,
        username: user.username || user.email.split('@')[0],
        email: user.email,
        display_name: user.display_name || user.username || 'Admin User',
        role: user.role as AdminRole
      };

      // Save token and user data to localStorage
      localStorage.setItem('admin_token', token);
      localStorage.setItem('admin_user', JSON.stringify(mappedUser));

      // Set user state
      setUser(mappedUser);
      console.log('Login successful, user information saved:', mappedUser);
    } catch (error: any) {
      console.error('Login failed:', error);

      // Clear any existing auth data on login failure
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');

      if (error.response) {
        console.error('Error response:', error.response.data);
        throw new Error(error.response.data?.message || 'Login failed');
      }

      throw new Error(error.message || 'Login failed, please check your credentials');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // 可以调用后端的logout API（如果有的话）
      // await api.post('/auth/logout');
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      // 清除本地存储的token和用户信息
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_user');
      setUser(null);
    }
  };

  const value = {
    user,
    isAuthenticated: !!user && !initializing,
    login,
    logout,
    loading,
    initializing
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};