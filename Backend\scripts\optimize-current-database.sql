-- OneNews 数据库优化脚本 (适配当前表结构)
-- 为现有的数据库表创建性能优化索引

-- 基础性能配置
SET work_mem = '256MB';
SET maintenance_work_mem = '512MB';
SET effective_cache_size = '2GB';
SET random_page_cost = 1.1;
SET seq_page_cost = 1.0;
SET cpu_tuple_cost = 0.01;
SET cpu_index_tuple_cost = 0.005;
SET cpu_operator_cost = 0.0025;

-- ============================================================================
-- ARTICLES 表索引优化
-- ============================================================================

-- 复合索引：分类 + 发布状态 + 创建时间 (用于分类页面排序)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_category_published_created 
ON articles(category, published, "createdAt" DESC) 
WHERE published = true;

-- 复合索引：特色文章 + 发布状态 + 创建时间 (用于首页特色内容)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_featured_published_created 
ON articles(featured, published, "createdAt" DESC) 
WHERE featured = true AND published = true;

-- 全文搜索索引：标题
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_title_search 
ON articles USING gin(to_tsvector('english', title)) 
WHERE published = true;

-- 全文搜索索引：内容
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_content_search 
ON articles USING gin(to_tsvector('english', content)) 
WHERE published = true;

-- 全文搜索索引：描述
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_description_search 
ON articles USING gin(to_tsvector('english', description)) 
WHERE published = true;

-- 标签搜索索引 (GIN 索引用于数组搜索)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_tags_search 
ON articles USING gin(tags) 
WHERE published = true;

-- 热门文章索引：浏览量 + 发布状态
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_views_published 
ON articles(views DESC, published) 
WHERE published = true AND views > 0;

-- 热门文章索引：点赞数 + 发布状态
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_likes_published 
ON articles(likes DESC, published) 
WHERE published = true AND likes > 0;

-- 作者文章索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_articles_author_created 
ON articles(author, "createdAt" DESC) 
WHERE published = true;

-- ============================================================================
-- 其他现有表的基础索引优化
-- ============================================================================

-- 检查并创建其他表的索引（如果表存在）
DO $$
BEGIN
    -- Users 表索引 (如果存在)
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'users') THEN
        -- 邮箱唯一索引 (如果字段存在)
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'email') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);
        END IF;
        
        -- 用户名索引 (如果字段存在)
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'username') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_username ON users(username);
        END IF;
        
        -- 创建时间索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'createdAt') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created ON users("createdAt" DESC);
        END IF;
    END IF;

    -- Comments 表索引 (如果存在)
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'comments') THEN
        -- 文章评论索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'articleId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_article ON comments("articleId", "createdAt" DESC);
        END IF;
        
        -- 用户评论索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'userId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_user ON comments("userId", "createdAt" DESC);
        END IF;
        
        -- 父评论索引 (回复功能)
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'comments' AND column_name = 'parentId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_comments_parent ON comments("parentId") WHERE "parentId" IS NOT NULL;
        END IF;
    END IF;

    -- Notifications 表索引 (如果存在)
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'notifications') THEN
        -- 用户通知索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'userId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_user ON notifications("userId", "createdAt" DESC);
        END IF;
        
        -- 未读通知索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'notifications' AND column_name = 'isRead') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_notifications_unread ON notifications("userId", "isRead") WHERE "isRead" = false;
        END IF;
    END IF;

    -- Messages 表索引 (如果存在)
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'messages') THEN
        -- 发送者消息索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'senderId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_sender ON messages("senderId", "createdAt" DESC);
        END IF;
        
        -- 接收者消息索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'receiverId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_receiver ON messages("receiverId", "createdAt" DESC);
        END IF;
        
        -- 对话索引 (发送者-接收者对)
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'senderId') 
           AND EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'receiverId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_messages_conversation ON messages("senderId", "receiverId", "createdAt" DESC);
        END IF;
    END IF;

    -- Follows 表索引 (如果存在)
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'follows') THEN
        -- 关注者索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'follows' AND column_name = 'followerId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_follower ON follows("followerId");
        END IF;
        
        -- 被关注者索引
        IF EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'follows' AND column_name = 'followingId') THEN
            CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_follows_following ON follows("followingId");
        END IF;
    END IF;

END $$;

-- ============================================================================
-- 性能监控视图
-- ============================================================================

-- 创建性能监控视图
CREATE OR REPLACE VIEW performance_overview AS
SELECT 
    'Database Size' as metric,
    pg_size_pretty(pg_database_size(current_database())) as value,
    'Total database size' as description
UNION ALL
SELECT 
    'Active Connections' as metric,
    count(*)::text as value,
    'Currently active database connections' as description
FROM pg_stat_activity 
WHERE state = 'active'
UNION ALL
SELECT 
    'Total Tables' as metric,
    count(*)::text as value,
    'Number of user tables' as description
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
UNION ALL
SELECT 
    'Total Indexes' as metric,
    count(*)::text as value,
    'Number of indexes on user tables' as description
FROM pg_indexes 
WHERE schemaname = 'public';

-- 创建表大小监控视图
CREATE OR REPLACE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ============================================================================
-- 数据库维护
-- ============================================================================

-- 更新表统计信息
ANALYZE;

-- 清理和优化
VACUUM ANALYZE;

-- ============================================================================
-- 优化完成报告
-- ============================================================================

-- 显示优化结果
SELECT 'Database Optimization Completed' as status;

-- 显示当前索引状态
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
ORDER BY tablename, indexname;

-- 显示性能概览
SELECT * FROM performance_overview;

-- 显示表大小
SELECT * FROM table_sizes;
