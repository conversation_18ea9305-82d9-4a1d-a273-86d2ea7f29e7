/**
 * 安全功能测试套件
 */

const request = require('supertest');
const { sanitizeLogInput, sanitizeLogObject } = require('../utils/logSanitizer');
const { isPathSafe, sanitizeFilename } = require('../utils/pathSecurity');
const { validateJWTSecret, checkForHardcodedCredentials } = require('../utils/credentialValidator');

describe('Security Tests', () => {
  
  describe('Log Sanitization', () => {
    test('should sanitize log injection attempts', () => {
      const maliciousInput = 'user input\r\nFAKE LOG ENTRY: Admin login successful';
      const sanitized = sanitizeLogInput(maliciousInput);
      
      expect(sanitized).not.toContain('\r');
      expect(sanitized).not.toContain('\n');
      expect(sanitized).toContain('user input FAKE LOG ENTRY: Admin login successful');
    });

    test('should remove control characters', () => {
      const input = 'test\x00\x1f\x7f\x9finput';
      const sanitized = sanitizeLogInput(input);
      
      expect(sanitized).toBe('testinput');
    });

    test('should limit input length', () => {
      const longInput = 'a'.repeat(2000);
      const sanitized = sanitizeLogInput(longInput);
      
      expect(sanitized.length).toBeLessThanOrEqual(1000);
    });

    test('should sanitize objects recursively', () => {
      const maliciousObject = {
        user: 'test\r\nFAKE: admin',
        data: {
          nested: 'value\nmalicious'
        }
      };
      
      const sanitized = sanitizeLogObject(maliciousObject);
      
      expect(sanitized.user).not.toContain('\r');
      expect(sanitized.user).not.toContain('\n');
      expect(sanitized.data.nested).not.toContain('\n');
    });
  });

  describe('Path Security', () => {
    test('should detect path traversal attempts', () => {
      const maliciousPaths = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32',
        '/var/log/../../../etc/passwd',
        'normal/../../sensitive'
      ];

      maliciousPaths.forEach(path => {
        expect(isPathSafe(path)).toBe(false);
      });
    });

    test('should allow safe paths', () => {
      const safePaths = [
        'uploads/image.jpg',
        'documents/file.pdf',
        'media/video.mp4'
      ];

      safePaths.forEach(path => {
        expect(isPathSafe(path)).toBe(true);
      });
    });

    test('should sanitize filenames', () => {
      const dangerousFilenames = [
        '../../../malicious.exe',
        'file<script>alert(1)</script>.jpg',
        'file|rm -rf /.jpg',
        'file:with:colons.txt'
      ];

      dangerousFilenames.forEach(filename => {
        const sanitized = sanitizeFilename(filename);
        expect(sanitized).not.toContain('../');
        expect(sanitized).not.toContain('<');
        expect(sanitized).not.toContain('|');
        expect(sanitized).not.toContain(':');
      });
    });
  });

  describe('Credential Validation', () => {
    test('should validate JWT secret strength', () => {
      const originalSecret = process.env.JWT_SECRET;
      
      // 测试强密钥
      process.env.JWT_SECRET = 'a'.repeat(32);
      expect(() => validateJWTSecret()).not.toThrow();

      // 测试弱密钥
      process.env.JWT_SECRET = 'weak';
      expect(() => validateJWTSecret()).toThrow();

      // 测试默认密钥
      process.env.JWT_SECRET = 'your-super-secure-jwt-secret-key-here';
      expect(() => validateJWTSecret()).toThrow();
      
      // 恢复原始值
      process.env.JWT_SECRET = originalSecret;
    });

    test('should detect hardcoded credentials', () => {
      const codeWithCredentials = `
        const password = "hardcoded123456";
        const apiKey = "sk-1234567890abcdef";
        const secret = "my-secret-key-here";
      `;

      const found = checkForHardcodedCredentials(codeWithCredentials);
      expect(found.length).toBeGreaterThan(0);
    });
  });
});