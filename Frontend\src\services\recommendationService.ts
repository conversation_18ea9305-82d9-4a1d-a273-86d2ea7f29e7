import { Work } from '@/types';

// 用户行为数据接口
export interface UserBehavior {
  userId: string;
  workId: number;
  action: 'view' | 'like' | 'share' | 'comment' | 'bookmark' | 'download';
  timestamp: Date;
  duration?: number; // 观看时长（秒）
  category: string;
  tags: string[];
}

// 用户偏好数据接口
export interface UserPreferences {
  userId: string;
  categories: Record<string, number>; // 分类偏好权重
  tags: Record<string, number>; // 标签偏好权重
  contentTypes: Record<string, number>; // 内容类型偏好权重
  authors: Record<string, number>; // 作者偏好权重
  timePreferences: {
    preferredDuration: number; // 偏好的内容时长
    activeHours: number[]; // 活跃时间段
  };
}

// 推荐算法类
export class RecommendationEngine {
  private userBehaviors: Map<string, UserBehavior[]> = new Map();
  private userPreferences: Map<string, UserPreferences> = new Map();
  private contentSimilarity: Map<number, Map<number, number>> = new Map();

  // 记录用户行为
  recordUserBehavior(behavior: UserBehavior) {
    const userId = behavior.userId;
    if (!this.userBehaviors.has(userId)) {
      this.userBehaviors.set(userId, []);
    }
    this.userBehaviors.get(userId)!.push(behavior);
    
    // 更新用户偏好
    this.updateUserPreferences(userId);
  }

  // 更新用户偏好
  private updateUserPreferences(userId: string) {
    const behaviors = this.userBehaviors.get(userId) || [];
    const preferences: UserPreferences = {
      userId,
      categories: {},
      tags: {},
      contentTypes: {},
      authors: {},
      timePreferences: {
        preferredDuration: 0,
        activeHours: []
      }
    };

    // 计算分类偏好
    behaviors.forEach(behavior => {
      const weight = this.getActionWeight(behavior.action);
      
      // 分类偏好
      preferences.categories[behavior.category] = 
        (preferences.categories[behavior.category] || 0) + weight;
      
      // 标签偏好
      behavior.tags.forEach(tag => {
        preferences.tags[tag] = (preferences.tags[tag] || 0) + weight;
      });
    });

    // 归一化权重
    this.normalizeWeights(preferences.categories);
    this.normalizeWeights(preferences.tags);

    this.userPreferences.set(userId, preferences);
  }

  // 获取行为权重
  private getActionWeight(action: UserBehavior['action']): number {
    const weights = {
      view: 1,
      like: 3,
      share: 5,
      comment: 4,
      bookmark: 6,
      download: 7
    };
    return weights[action] || 1;
  }

  // 归一化权重
  private normalizeWeights(weights: Record<string, number>) {
    const total = Object.values(weights).reduce((sum, weight) => sum + weight, 0);
    if (total > 0) {
      Object.keys(weights).forEach(key => {
        weights[key] = weights[key] / total;
      });
    }
  }

  // 计算内容相似度
  calculateContentSimilarity(work1: Work, work2: Work): number {
    let similarity = 0;

    // 分类相似度
    if (work1.category === work2.category) {
      similarity += 0.3;
    }

    // 标签相似度
    const tags1 = work1.tags || [];
    const tags2 = work2.tags || [];
    const commonTags = tags1.filter(tag => tags2.includes(tag));
    const tagSimilarity = commonTags.length / Math.max(tags1.length, tags2.length, 1);
    similarity += tagSimilarity * 0.4;

    // 内容类型相似度
    if (work1.type === work2.type) {
      similarity += 0.2;
    }

    // 作者相似度
    if (work1.author.id === work2.author.id) {
      similarity += 0.1;
    }

    return Math.min(similarity, 1);
  }

  // 基于协同过滤的推荐
  getCollaborativeRecommendations(userId: string, allWorks: Work[], limit: number = 10): Work[] {
    const userPrefs = this.userPreferences.get(userId);
    if (!userPrefs) return [];

    const scores = new Map<number, number>();

    allWorks.forEach(work => {
      let score = 0;

      // 基于分类偏好
      const categoryScore = userPrefs.categories[work.category] || 0;
      score += categoryScore * 0.4;

      // 基于标签偏好
      const workTags = work.tags || [];
      const tagScore = workTags.reduce((sum, tag) => {
        return sum + (userPrefs.tags[tag] || 0);
      }, 0) / Math.max(workTags.length, 1);
      score += tagScore * 0.3;

      // 基于热度
      const popularityScore = Math.log(work.views + 1) / Math.log(10000);
      score += popularityScore * 0.2;

      // 基于新鲜度
      const publishDate = new Date(work.publishedAt || work.createdAt);
      const daysSincePublish = (Date.now() - publishDate.getTime()) / (1000 * 60 * 60 * 24);
      const freshnessScore = Math.max(0, 1 - daysSincePublish / 30); // 30天内的内容有新鲜度加分
      score += freshnessScore * 0.1;

      scores.set(work.id, score);
    });

    // 排序并返回推荐结果
    return allWorks
      .sort((a, b) => (scores.get(b.id) || 0) - (scores.get(a.id) || 0))
      .slice(0, limit);
  }

  // 基于内容的推荐
  getContentBasedRecommendations(userId: string, allWorks: Work[], limit: number = 10): Work[] {
    const userBehaviors = this.userBehaviors.get(userId) || [];
    if (userBehaviors.length === 0) return [];

    // 获取用户最近交互的内容
    const recentInteractions = userBehaviors
      .filter(b => b.action !== 'view')
      .slice(-10);

    const scores = new Map<number, number>();

    allWorks.forEach(work => {
      let score = 0;

      recentInteractions.forEach(interaction => {
        const interactedWork = allWorks.find(w => w.id === interaction.workId);
        if (interactedWork) {
          const similarity = this.calculateContentSimilarity(work, interactedWork);
          const actionWeight = this.getActionWeight(interaction.action);
          score += similarity * actionWeight;
        }
      });

      scores.set(work.id, score);
    });

    return allWorks
      .sort((a, b) => (scores.get(b.id) || 0) - (scores.get(a.id) || 0))
      .slice(0, limit);
  }

  // 混合推荐算法
  getHybridRecommendations(userId: string, allWorks: Work[], limit: number = 10): Work[] {
    const collaborativeRecs = this.getCollaborativeRecommendations(userId, allWorks, limit * 2);
    const contentBasedRecs = this.getContentBasedRecommendations(userId, allWorks, limit * 2);

    // 合并并去重
    const combinedRecs = new Map<number, { work: Work; score: number }>();

    collaborativeRecs.forEach((work, index) => {
      const score = (collaborativeRecs.length - index) * 0.6; // 协同过滤权重60%
      combinedRecs.set(work.id, { work, score });
    });

    contentBasedRecs.forEach((work, index) => {
      const score = (contentBasedRecs.length - index) * 0.4; // 基于内容权重40%
      const existing = combinedRecs.get(work.id);
      if (existing) {
        existing.score += score;
      } else {
        combinedRecs.set(work.id, { work, score });
      }
    });

    // 排序并返回结果
    return Array.from(combinedRecs.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.work);
  }

  // 获取热门推荐
  getTrendingRecommendations(allWorks: Work[], limit: number = 10): Work[] {
    const now = Date.now();
    const oneDayMs = 24 * 60 * 60 * 1000;

    return allWorks
      .map(work => {
        const publishDate = new Date(work.publishedAt || work.createdAt).getTime();
        const daysSincePublish = (now - publishDate) / oneDayMs;
        
        // 计算趋势分数：结合观看量、点赞数和时间衰减
        const timeDecay = Math.exp(-daysSincePublish / 7); // 7天衰减
        const engagementScore = (work.likes + work.comments * 2 + work.shares * 3) / Math.max(work.views, 1);
        const trendingScore = work.views * engagementScore * timeDecay;
        
        return { work, score: trendingScore };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => item.work);
  }

  // 获取个性化推荐
  getPersonalizedRecommendations(userId: string, allWorks: Work[], limit: number = 10): Work[] {
    const userBehaviors = this.userBehaviors.get(userId) || [];
    
    // 如果用户行为数据不足，返回热门推荐
    if (userBehaviors.length < 5) {
      return this.getTrendingRecommendations(allWorks, limit);
    }

    // 否则使用混合推荐算法
    return this.getHybridRecommendations(userId, allWorks, limit);
  }
}

// 创建全局推荐引擎实例
export const recommendationEngine = new RecommendationEngine();

// 模拟用户行为数据
export const simulateUserBehaviors = () => {
  const users = ['user1', 'user2', 'user3', 'user4', 'user5'];
  const actions: UserBehavior['action'][] = ['view', 'like', 'share', 'comment', 'bookmark'];
  
  users.forEach(userId => {
    // 为每个用户生成一些随机行为
    for (let i = 0; i < 20; i++) {
      const behavior: UserBehavior = {
        userId,
        workId: Math.floor(Math.random() * 16) + 1, // 假设有16个作品
        action: actions[Math.floor(Math.random() * actions.length)],
        timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 过去30天内
        category: ['technology', 'lifestyle', 'education', 'entertainment'][Math.floor(Math.random() * 4)],
        tags: ['AI', 'Web Development', 'Health', 'Travel', 'Food'].slice(0, Math.floor(Math.random() * 3) + 1)
      };
      
      recommendationEngine.recordUserBehavior(behavior);
    }
  });
};
