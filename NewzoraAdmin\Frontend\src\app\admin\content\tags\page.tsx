'use client';

import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Tag,
  Eye,
  EyeOff,
  Save,
  X,
  TrendingUp,
  Hash
} from 'lucide-react';

interface TagItem {
  id: string;
  name: string;
  slug: string;
  description: string;
  color: string;
  isActive: boolean;
  articleCount: number;
  usageCount: number;
  trending: boolean;
  createdAt: string;
  updatedAt: string;
}

const TagManagementPage: React.FC = () => {
  const [tags, setTags] = useState<TagItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingTag, setEditingTag] = useState<TagItem | null>(null);
  const [sortBy, setSortBy] = useState<'name' | 'usage' | 'articles'>('usage');

  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#3B82F6',
    isActive: true
  });

  useEffect(() => {
    fetchTags();
  }, []);

  const fetchTags = async () => {
    try {
      // Mock data - in real app, this would fetch from API
      const mockTags: TagItem[] = [
        {
          id: '1',
          name: 'React',
          slug: 'react',
          description: 'React.js library and ecosystem',
          color: '#61DAFB',
          isActive: true,
          articleCount: 45,
          usageCount: 156,
          trending: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '2',
          name: 'JavaScript',
          slug: 'javascript',
          description: 'JavaScript programming language',
          color: '#F7DF1E',
          isActive: true,
          articleCount: 67,
          usageCount: 234,
          trending: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '3',
          name: 'TypeScript',
          slug: 'typescript',
          description: 'TypeScript programming language',
          color: '#3178C6',
          isActive: true,
          articleCount: 32,
          usageCount: 89,
          trending: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '4',
          name: 'Node.js',
          slug: 'nodejs',
          description: 'Node.js runtime environment',
          color: '#339933',
          isActive: true,
          articleCount: 28,
          usageCount: 76,
          trending: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '5',
          name: 'CSS',
          slug: 'css',
          description: 'Cascading Style Sheets',
          color: '#1572B6',
          isActive: true,
          articleCount: 23,
          usageCount: 65,
          trending: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        },
        {
          id: '6',
          name: 'Deprecated Tag',
          slug: 'deprecated-tag',
          description: 'This tag is no longer in use',
          color: '#6B7280',
          isActive: false,
          articleCount: 5,
          usageCount: 12,
          trending: false,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z'
        }
      ];
      
      setTags(mockTags);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch tags:', error);
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (editingTag) {
        // Update existing tag
        const updatedTag: TagItem = {
          ...editingTag,
          ...formData,
          slug: formData.slug || formData.name.toLowerCase().replace(/\s+/g, '-'),
          updatedAt: new Date().toISOString()
        };
        
        setTags(prev => prev.map(tag => 
          tag.id === editingTag.id ? updatedTag : tag
        ));
      } else {
        // Create new tag
        const newTag: TagItem = {
          id: Date.now().toString(),
          ...formData,
          slug: formData.slug || formData.name.toLowerCase().replace(/\s+/g, '-'),
          articleCount: 0,
          usageCount: 0,
          trending: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        setTags(prev => [...prev, newTag]);
      }
      
      resetForm();
      setShowModal(false);
    } catch (error) {
      console.error('Failed to save tag:', error);
    }
  };

  const handleEdit = (tag: TagItem) => {
    setEditingTag(tag);
    setFormData({
      name: tag.name,
      slug: tag.slug,
      description: tag.description,
      color: tag.color,
      isActive: tag.isActive
    });
    setShowModal(true);
  };

  const handleDelete = async (tagId: string) => {
    const tag = tags.find(t => t.id === tagId);
    if (tag && tag.articleCount > 0) {
      if (!confirm(`This tag is used in ${tag.articleCount} articles. Are you sure you want to delete it? This will remove the tag from all articles.`)) {
        return;
      }
    } else if (!confirm('Are you sure you want to delete this tag? This action cannot be undone.')) {
      return;
    }

    try {
      setTags(prev => prev.filter(tag => tag.id !== tagId));
      // In real app, this would call API
      console.log(`Tag ${tagId} deleted`);
    } catch (error) {
      console.error('Failed to delete tag:', error);
    }
  };

  const handleToggleActive = async (tagId: string) => {
    try {
      setTags(prev => prev.map(tag => 
        tag.id === tagId ? { ...tag, isActive: !tag.isActive } : tag
      ));
      // In real app, this would call API
      console.log(`Tag ${tagId} status toggled`);
    } catch (error) {
      console.error('Failed to toggle tag status:', error);
    }
  };

  const handleMergeTags = async (sourceTagId: string, targetTagId: string) => {
    // This would merge two tags together
    console.log(`Merge tag ${sourceTagId} into ${targetTagId}`);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '#3B82F6',
      isActive: true
    });
    setEditingTag(null);
  };

  const filteredTags = tags
    .filter(tag =>
      tag.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tag.description.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'usage':
          return b.usageCount - a.usageCount;
        case 'articles':
          return b.articleCount - a.articleCount;
        default:
          return 0;
      }
    });

  const totalTags = tags.length;
  const activeTags = tags.filter(tag => tag.isActive).length;
  const trendingTags = tags.filter(tag => tag.trending).length;
  const totalUsage = tags.reduce((sum, tag) => sum + tag.usageCount, 0);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Tag Management</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Tag Management</h1>
          <p className="text-gray-600 mt-1">Organize and manage content tags</p>
        </div>
        <button
          onClick={() => {
            resetForm();
            setShowModal(true);
          }}
          className="btn-primary flex items-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          New Tag
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Hash className="w-8 h-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Tags</p>
              <p className="text-2xl font-semibold text-gray-900">{totalTags}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Eye className="w-8 h-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Tags</p>
              <p className="text-2xl font-semibold text-gray-900">{activeTags}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="w-8 h-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Trending</p>
              <p className="text-2xl font-semibold text-gray-900">{trendingTags}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Tag className="w-8 h-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Usage</p>
              <p className="text-2xl font-semibold text-gray-900">{totalUsage.toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="form-input pl-10"
            />
          </div>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'name' | 'usage' | 'articles')}
            className="form-select"
          >
            <option value="usage">Sort by Usage</option>
            <option value="articles">Sort by Articles</option>
            <option value="name">Sort by Name</option>
          </select>
          
          <button className="btn-secondary">
            Export Tags
          </button>
        </div>
      </div>

      {/* Tags Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTags.map((tag) => (
          <div key={tag.id} className="bg-white rounded-lg shadow p-6 hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                <div 
                  className="w-4 h-4 rounded mr-3"
                  style={{ backgroundColor: tag.color }}
                />
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    {tag.name}
                    {tag.trending && (
                      <TrendingUp className="w-4 h-4 ml-2 text-orange-500" />
                    )}
                  </h3>
                  <p className="text-sm text-gray-500">#{tag.slug}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleToggleActive(tag.id)}
                  className="text-gray-400 hover:text-gray-600"
                  title={tag.isActive ? 'Deactivate' : 'Activate'}
                >
                  {tag.isActive ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => handleEdit(tag)}
                  className="text-blue-600 hover:text-blue-900"
                >
                  <Edit className="w-4 h-4" />
                </button>
                <button
                  onClick={() => handleDelete(tag.id)}
                  className="text-red-600 hover:text-red-900"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <p className="text-sm text-gray-600 mb-4">{tag.description}</p>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Articles:</span>
                <span className="ml-2 text-gray-900">{tag.articleCount}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700">Usage:</span>
                <span className="ml-2 text-gray-900">{tag.usageCount}</span>
              </div>
            </div>
            
            <div className="mt-4">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                tag.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {tag.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        ))}
      </div>

      {filteredTags.length === 0 && (
        <div className="text-center py-12">
          <Tag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <div className="text-gray-500">No tags found</div>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {editingTag ? 'Edit Tag' : 'New Tag'}
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="form-input"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Slug
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                  className="form-input"
                  placeholder="auto-generated from name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="form-input"
                  rows={3}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Color
                </label>
                <input
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData(prev => ({ ...prev, color: e.target.value }))}
                  className="form-input h-10"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
                  Active
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn-primary flex items-center"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {editingTag ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default TagManagementPage;
