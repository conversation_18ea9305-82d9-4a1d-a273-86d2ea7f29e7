-- Newzora Admin 数据库初始化脚本
-- 创建数据库和基础配置

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE "PostgreSQL-newzora_admin"'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'PostgreSQL-newzora_admin');

-- 连接到 PostgreSQL-newzora_admin 数据库
\c "PostgreSQL-newzora_admin";

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建全文搜索配置
CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS chinese (COPY = simple);

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建用户角色（如果需要）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'newzora_admin_app') THEN
        CREATE ROLE newzora_admin_app WITH LOGIN PASSWORD 'QWasd080980!';
    END IF;
END
$$;

-- 授权
GRANT CONNECT ON DATABASE "PostgreSQL-newzora_admin" TO newzora_admin_app;
GRANT USAGE ON SCHEMA public TO newzora_admin_app;
GRANT CREATE ON SCHEMA public TO newzora_admin_app;

-- 输出初始化完成信息
SELECT 'Newzora Admin database initialized successfully' as status;