const express = require('express');
const router = express.Router();
const { Op, sequelize } = require('sequelize');
const auth = require('../middleware/auth');

// 广告概览数据
router.get('/overview', auth, async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    const dateRange = getDateRange(period);
    
    const overview = {
      totalImpressions: Math.floor(Math.random() * 100000) + 50000,
      totalClicks: Math.floor(Math.random() * 5000) + 2000,
      totalRevenue: Math.random() * 10000 + 5000,
      ctr: 0, // 点击率
      cpm: 0, // 千次展示成本
      cpc: 0  // 单次点击成本
    };
    
    overview.ctr = (overview.totalClicks / overview.totalImpressions * 100).toFixed(2);
    overview.cpm = (overview.totalRevenue / overview.totalImpressions * 1000).toFixed(2);
    overview.cpc = (overview.totalRevenue / overview.totalClicks).toFixed(2);
    
    res.json({ success: true, data: overview });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// 广告位表现分析
router.get('/slots-performance', auth, async (req, res) => {
  try {
    const { period = '30d' } = req.query;
    
    const slotsData = [
      {
        slotId: 'header-banner',
        name: '顶部横幅',
        impressions: Math.floor(Math.random() * 20000) + 10000,
        clicks: Math.floor(Math.random() * 1000) + 500,
        revenue: Math.random() * 2000 + 1000
      },
      {
        slotId: 'sidebar-ad',
        name: '侧边栏广告',
        impressions: Math.floor(Math.random() * 15000) + 8000,
        clicks: Math.floor(Math.random() * 800) + 400,
        revenue: Math.random() * 1500 + 800
      },
      {
        slotId: 'content-inline',
        name: '内容内嵌',
        impressions: Math.floor(Math.random() * 25000) + 15000,
        clicks: Math.floor(Math.random() * 1200) + 600,
        revenue: Math.random() * 2500 + 1500
      }
    ];
    
    slotsData.forEach(slot => {
      slot.ctr = (slot.clicks / slot.impressions * 100).toFixed(2);
      slot.cpm = (slot.revenue / slot.impressions * 1000).toFixed(2);
    });
    
    res.json({ success: true, data: slotsData });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// 广告趋势分析
router.get('/trends', auth, async (req, res) => {
  try {
    const { period = '30d', metric = 'revenue' } = req.query;
    const dateRange = getDateRange(period);
    
    const trends = [];
    const current = new Date(dateRange.start);
    
    while (current <= dateRange.end) {
      let value;
      switch (metric) {
        case 'impressions':
          value = Math.floor(Math.random() * 5000) + 2000;
          break;
        case 'clicks':
          value = Math.floor(Math.random() * 200) + 100;
          break;
        case 'revenue':
          value = Math.random() * 500 + 200;
          break;
        default:
          value = Math.random() * 500 + 200;
      }
      
      trends.push({
        date: current.toISOString().split('T')[0],
        value: value
      });
      
      current.setDate(current.getDate() + 1);
    }
    
    res.json({ success: true, data: trends });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// 受众分析
router.get('/audience', auth, async (req, res) => {
  try {
    const audienceData = {
      demographics: {
        age: [
          { range: '18-24', percentage: 25 },
          { range: '25-34', percentage: 35 },
          { range: '35-44', percentage: 25 },
          { range: '45+', percentage: 15 }
        ],
        gender: [
          { type: '男性', percentage: 60 },
          { type: '女性', percentage: 40 }
        ]
      },
      geography: [
        { region: '北京', percentage: 20 },
        { region: '上海', percentage: 18 },
        { region: '广州', percentage: 15 },
        { region: '深圳', percentage: 12 },
        { region: '其他', percentage: 35 }
      ],
      interests: [
        { category: '科技', percentage: 30 },
        { category: '娱乐', percentage: 25 },
        { category: '体育', percentage: 20 },
        { category: '财经', percentage: 15 },
        { category: '其他', percentage: 10 }
      ]
    };
    
    res.json({ success: true, data: audienceData });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

function getDateRange(period) {
  const end = new Date();
  const start = new Date();
  
  switch (period) {
    case '7d': start.setDate(end.getDate() - 7); break;
    case '30d': start.setDate(end.getDate() - 30); break;
    case '90d': start.setDate(end.getDate() - 90); break;
    default: start.setDate(end.getDate() - 30);
  }
  
  return { start, end };
}

module.exports = router;