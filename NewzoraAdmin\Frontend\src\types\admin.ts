// 管理员角色枚举
export enum AdminRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

// 权限枚举
export enum Permission {
  USER_VIEW = 'user:view',
  USER_EDIT = 'user:edit',
  USER_DELETE = 'user:delete',
  USER_ROLE_CHANGE = 'user:role_change',
  CONTENT_VIEW = 'content:view',
  CONTENT_EDIT = 'content:edit',
  CONTENT_DELETE = 'content:delete',
  CONTENT_PUBLISH = 'content:publish',
  CONTENT_REVIEW = 'content:review',
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export',
  SYSTEM_SETTINGS = 'system:settings',
  SYSTEM_LOGS = 'system:logs'
}

// 仪表板统计数据
export interface DashboardStats {
  users: {
    total: number;
    todayNew: number;
    activeUsers: number;
  };
  content: {
    totalArticles: number;
    todayPublished: number;
    pendingReview: number;
  };
  engagement: {
    totalComments: number;
    todayComments: number;
    averageReadTime: number;
  };
}

// 用户管理数据
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  display_name: string;
  role: AdminRole;
  is_active: boolean;
  email_verified: boolean;
  created_at: string;
  last_login_at: string;
  // Enhanced verification fields
  is_verified?: boolean;
  is_premium?: boolean;
  membership_level?: number;
  is_expert?: boolean;
  expert_verified?: boolean;
  phone_verified?: boolean;
  identity_verified?: boolean;
  // Additional user fields
  avatar?: string;
  bio?: string;
  location?: string;
  website?: string;
  social_links?: {
    twitter?: string;
    linkedin?: string;
    github?: string;
  };
  stats?: {
    articles: number;
    comments: number;
    followers: number;
    following: number;
    views?: number;
    likes?: number;
  };
  // Moderation fields
  warning_count?: number;
  suspension_count?: number;
  last_warning_at?: string;
  last_suspension_at?: string;
}

// 内容管理数据
export interface AdminArticle {
  id: number;
  title: string;
  content: string;
  authorId: number;
  author: {
    username: string;
    display_name: string;
  };
  category: string;
  published: boolean;
  views: number;
  likes: number;
  created_at: string;
  updated_at: string;
}

// 数据表格属性
export interface DataTableColumn<T> {
  key: keyof T;
  title: string;
  render?: (value: any, record: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

export interface PaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number) => void;
}

// API响应格式
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    totalPages: number;
  };
}