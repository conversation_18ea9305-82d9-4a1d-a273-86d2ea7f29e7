/**
 * Newzora 数据库生产环境优化配置
 * PostgreSQL 生产级别性能和安全配置
 */

const { Pool } = require('pg');

// 生产环境数据库配置
const productionConfig = {
  // 基础连接配置
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'onenews',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD,

  // 连接池配置 - 生产环境优化
  min: parseInt(process.env.DB_POOL_MIN) || 5, // 最小连接数
  max: parseInt(process.env.DB_POOL_MAX) || 20, // 最大连接数
  acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 60000, // 获取连接超时 (60s)
  idle: parseInt(process.env.DB_POOL_IDLE) || 10000, // 空闲连接超时 (10s)
  evict: parseInt(process.env.DB_POOL_EVICT) || 1000, // 连接回收检查间隔 (1s)

  // SSL 配置 - 生产环境必须启用
  ssl:
    process.env.NODE_ENV === 'production'
      ? {
          require: true,
          rejectUnauthorized: false, // 如果使用自签名证书，设置为 false
        }
      : false,

  // 连接超时配置
  connectionTimeoutMillis: 30000, // 连接超时 30s
  idleTimeoutMillis: 30000, // 空闲超时 30s
  query_timeout: 60000, // 查询超时 60s
  statement_timeout: 60000, // 语句超时 60s

  // 性能优化配置
  application_name: 'OneNews', // 应用名称，便于监控

  // 日志配置
  logging:
    process.env.NODE_ENV === 'production'
      ? (sql, timing) => {
          if (timing > 1000) {
            // 只记录慢查询 (>1s)
            console.warn(`[SLOW QUERY] ${timing}ms: ${sql}`);
          }
        }
      : console.log,

  // 重试配置
  retry: {
    max: 3,
    match: [
      /ConnectionError/,
      /ConnectionTimedOutError/,
      /TimeoutError/,
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
    ],
  },

  // 数据库方言特定配置
  dialectOptions: {
    // PostgreSQL 特定配置
    supportBigNumbers: true,
    bigNumberStrings: true,

    // 时区配置
    timezone: process.env.TZ || 'UTC',

    // 字符集配置
    charset: 'utf8mb4',

    // SSL 配置
    ssl:
      process.env.NODE_ENV === 'production'
        ? {
            require: true,
            rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
          }
        : false,

    // 连接配置
    connectTimeout: 30000,
    acquireTimeout: 30000,
    timeout: 60000,

    // PostgreSQL 特定参数
    options: {
      // 启用预编译语句缓存
      statement_cache_size: 100,

      // 设置工作内存
      work_mem: '256MB',

      // 设置维护工作内存
      maintenance_work_mem: '512MB',

      // 启用并行查询
      max_parallel_workers_per_gather: 2,

      // 设置有效缓存大小
      effective_cache_size: '2GB',

      // 设置随机页面成本
      random_page_cost: 1.1,

      // 启用 JIT 编译
      jit: 'on',
    },
  },

  // 事务配置
  transactionType: 'IMMEDIATE',
  isolationLevel: 'READ_COMMITTED',

  // 基准测试和性能监控
  benchmark: process.env.NODE_ENV === 'development',

  // 查询优化
  define: {
    // 时间戳
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',

    // 软删除
    paranoid: true,
    deletedAt: 'deleted_at',

    // 下划线命名
    underscored: true,

    // 冻结表名
    freezeTableName: true,

    // 版本控制
    version: true,
  },

  // 钩子配置
  hooks: {
    beforeConnect: (config) => {
      console.log(`[DB] Connecting to ${config.host}:${config.port}/${config.database}`);
    },
    afterConnect: (connection, config) => {
      console.log(
        `[DB] Connected successfully to ${config.host}:${config.port}/${config.database}`
      );
    },
    beforeDisconnect: (connection) => {
      console.log('[DB] Disconnecting from database...');
    },
    afterDisconnect: (connection) => {
      console.log('[DB] Disconnected from database');
    },
  },
};

// 开发环境配置
const developmentConfig = {
  ...productionConfig,

  // 开发环境连接池较小
  min: 2,
  max: 10,

  // 开发环境不需要 SSL
  ssl: false,
  dialectOptions: {
    ...productionConfig.dialectOptions,
    ssl: false,
  },

  // 开发环境启用详细日志
  logging: console.log,
  benchmark: true,

  // 开发环境查询超时较短
  query_timeout: 30000,
  statement_timeout: 30000,
};

// 测试环境配置
const testConfig = {
  ...developmentConfig,

  // 测试环境使用内存数据库或独立测试库
  database: process.env.DB_TEST_NAME || 'onenews_test',

  // 测试环境连接池最小
  min: 1,
  max: 5,

  // 测试环境禁用日志
  logging: false,
  benchmark: false,

  // 测试环境快速超时
  query_timeout: 10000,
  statement_timeout: 10000,
  connectionTimeoutMillis: 10000,
  idleTimeoutMillis: 5000,
};

// 根据环境选择配置
const getConfig = () => {
  switch (process.env.NODE_ENV) {
    case 'production':
      return productionConfig;
    case 'test':
      return testConfig;
    case 'development':
    default:
      return developmentConfig;
  }
};

// 创建连接池
const createPool = () => {
  const config = getConfig();

  const pool = new Pool({
    host: config.host,
    port: config.port,
    database: config.database,
    user: config.user,
    password: config.password,
    min: config.min,
    max: config.max,
    connectionTimeoutMillis: config.connectionTimeoutMillis,
    idleTimeoutMillis: config.idleTimeoutMillis,
    ssl: config.ssl,
    application_name: config.application_name,
  });

  // 连接池事件监听
  pool.on('connect', (client) => {
    console.log(`[DB Pool] New client connected (total: ${pool.totalCount})`);
  });

  pool.on('remove', (client) => {
    console.log(`[DB Pool] Client removed (total: ${pool.totalCount})`);
  });

  pool.on('error', (err, client) => {
    console.error('[DB Pool] Unexpected error on idle client', err);
  });

  return pool;
};

// 健康检查函数
const healthCheck = async (pool) => {
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as version');
    client.release();

    return {
      status: 'healthy',
      timestamp: result.rows[0].current_time,
      version: result.rows[0].version,
      pool: {
        total: pool.totalCount,
        idle: pool.idleCount,
        waiting: pool.waitingCount,
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

// 性能监控函数
const getPoolStats = (pool) => {
  return {
    totalCount: pool.totalCount,
    idleCount: pool.idleCount,
    waitingCount: pool.waitingCount,
    maxSize: pool.options.max,
    minSize: pool.options.min,
  };
};

module.exports = {
  getConfig,
  createPool,
  healthCheck,
  getPoolStats,
  productionConfig,
  developmentConfig,
  testConfig,
};
