import { AdCampaign, AdSlot, UserAdProfile } from '@/types';

class AdService {
  // 基于用户画像和广告信息计算匹配度
  calculateAdRelevance(userProfile: UserAdProfile, campaign: AdCampaign): number {
    let relevanceScore = 0;
    
    // 年龄匹配度 (0-30分)
    if (campaign.targeting.minAge || campaign.targeting.maxAge) {
      const userAge = userProfile.demographics.age;
      const minAge = campaign.targeting.minAge || 0;
      const maxAge = campaign.targeting.maxAge || 100;
      
      if (userAge >= minAge && userAge <= maxAge) {
        relevanceScore += 30;
      }
    }
    
    // 性别匹配度 (0-20分)
    if (campaign.targeting.genders && campaign.targeting.genders.length > 0) {
      if (campaign.targeting.genders.includes(userProfile.demographics.gender)) {
        relevanceScore += 20;
      }
    }
    
    // 地域匹配度 (0-20分)
    if (campaign.targeting.locations && campaign.targeting.locations.length > 0) {
      if (campaign.targeting.locations.includes(userProfile.demographics.location)) {
        relevanceScore += 20;
      }
    }
    
    // 兴趣匹配度 (0-30分)
    if (campaign.targeting.categories && campaign.targeting.categories.length > 0) {
      const interestMatches = userProfile.interests.filter(interest => 
        campaign.targeting.categories?.includes(interest.category)
      );
      
      const interestScore = interestMatches.reduce((sum, interest) => sum + interest.score, 0);
      relevanceScore += Math.min(30, interestScore * 10); // 最高30分
    }
    
    return relevanceScore;
  }
  
  // 选择最适合的广告
  selectAdForSlot(
    adSlot: AdSlot, 
    campaigns: AdCampaign[], 
    userProfile: UserAdProfile
  ): AdCampaign | null {
    if (campaigns.length === 0) return null;
    
    // 过滤出活跃的、在投放时间内的广告活动
    const activeCampaigns = campaigns.filter(campaign => {
      const now = new Date();
      const startDate = new Date(campaign.startDate);
      const endDate = new Date(campaign.endDate);
      
      return (
        campaign.status === 'active' &&
        now >= startDate &&
        now <= endDate &&
        campaign.budget > campaign.spent
      );
    });
    
    if (activeCampaigns.length === 0) return null;
    
    // 计算每个广告活动的相关性得分
    const campaignScores = activeCampaigns.map(campaign => ({
      campaign,
      score: this.calculateAdRelevance(userProfile, campaign)
    }));
    
    // 按得分排序，返回得分最高的广告活动
    campaignScores.sort((a, b) => b.score - a.score);
    
    return campaignScores[0]?.campaign || null;
  }
  
  // 记录广告展示
  recordAdImpression(
    adSlotId: number,
    campaignId: number,
    userId?: number
  ): void {
    // 在实际应用中，这里会发送请求到后端记录广告展示
    console.log(`Ad impression recorded: Slot ${adSlotId}, Campaign ${campaignId}, User ${userId || 'Anonymous'}`);
  }
  
  // 计算广告点击价值
  calculateAdValue(campaign: AdCampaign): number {
    // 简单的计算方式：剩余预算 / 预计展示次数
    const remainingBudget = campaign.budget - campaign.spent;
    const estimatedImpressions = campaign.budget * 20; // 假设每元预算可获得20次展示
    return estimatedImpressions > 0 ? remainingBudget / estimatedImpressions : 0;
  }
}

export const adService = new AdService();