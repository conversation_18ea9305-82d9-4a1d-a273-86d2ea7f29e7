# OneNews Prometheus 告警规则

groups:
  # 应用服务告警
  - name: onenews-application
    rules:
      # 服务下线告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "OneNews service is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} has been down for more than 1 minute."

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second on {{ $labels.instance }}."

      # API响应时间过长
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time"
          description: "95th percentile response time is {{ $value }}s on {{ $labels.instance }}."

      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}."

      # CPU使用率过高
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}."

      # 磁盘空间不足
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Disk space low"
          description: "Disk space is {{ $value | humanizePercentage }} available on {{ $labels.instance }}."

  # 数据库告警
  - name: onenews-database
    rules:
      # PostgreSQL 连接数过多
      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL too many connections"
          description: "PostgreSQL has {{ $value | humanizePercentage }} connections used."

      # PostgreSQL 死锁检测
      - alert: PostgreSQLDeadlocks
        expr: rate(pg_stat_database_deadlocks[5m]) > 0
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL deadlocks detected"
          description: "PostgreSQL has {{ $value }} deadlocks per second."

      # 数据库查询时间过长
      - alert: PostgreSQLSlowQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "PostgreSQL slow queries"
          description: "PostgreSQL has queries running for more than 5 minutes."

  # Redis 告警
  - name: onenews-redis
    rules:
      # Redis 内存使用率过高
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis high memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}."

      # Redis 连接数过多
      - alert: RedisTooManyConnections
        expr: redis_connected_clients > 100
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis too many connections"
          description: "Redis has {{ $value }} connected clients."

      # Redis 主从同步延迟
      - alert: RedisMasterSlaveReplicationLag
        expr: redis_master_repl_offset - redis_slave_repl_offset > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Redis replication lag"
          description: "Redis replication lag is {{ $value }} bytes."

  # 网络和负载均衡告警
  - name: onenews-network
    rules:
      # SSL证书即将过期
      - alert: SSLCertificateExpiringSoon
        expr: ssl_cert_not_after - time() < 7 * 24 * 3600
        for: 1h
        labels:
          severity: warning
        annotations:
          summary: "SSL certificate expiring soon"
          description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}."

      # 网站无法访问
      - alert: WebsiteDown
        expr: probe_success == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Website is down"
          description: "Website {{ $labels.instance }} is not responding."

      # 网站响应时间过长
      - alert: WebsiteSlowResponse
        expr: probe_duration_seconds > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Website slow response"
          description: "Website {{ $labels.instance }} response time is {{ $value }}s."

  # 业务指标告警
  - name: onenews-business
    rules:
      # 用户注册率异常低
      - alert: LowRegistrationRate
        expr: rate(user_registrations_total[1h]) < 0.1
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Low user registration rate"
          description: "User registration rate is {{ $value }} per hour."

      # 文章发布率异常低
      - alert: LowArticlePublishRate
        expr: rate(articles_published_total[1h]) < 0.5
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Low article publish rate"
          description: "Article publish rate is {{ $value }} per hour."

      # 错误登录尝试过多
      - alert: HighFailedLoginAttempts
        expr: rate(login_attempts_total{status="failed"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login attempts"
          description: "Failed login attempts rate is {{ $value }} per second."

  # 容器和Docker告警
  - name: onenews-containers
    rules:
      # 容器重启过于频繁
      - alert: ContainerRestartingTooOften
        expr: rate(container_start_time_seconds[15m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container restarting too often"
          description: "Container {{ $labels.name }} is restarting {{ $value }} times per second."

      # 容器内存使用率过高
      - alert: ContainerHighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container high memory usage"
          description: "Container {{ $labels.name }} memory usage is {{ $value | humanizePercentage }}."

      # 容器CPU使用率过高
      - alert: ContainerHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Container high CPU usage"
          description: "Container {{ $labels.name }} CPU usage is {{ $value | humanizePercentage }}."
