# OneNews Backend 开发环境 Dockerfile
# 支持热重载和调试功能

FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    postgresql-client \
    curl

# 安装nodemon用于热重载
RUN npm install -g nodemon

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm install

# 暴露端口
EXPOSE 5000 9229

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# 启动命令（使用nodemon进行热重载，启用调试）
CMD ["nodemon", "--inspect=0.0.0.0:9229", "server.js"]
