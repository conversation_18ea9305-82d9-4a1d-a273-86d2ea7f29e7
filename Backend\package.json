{"name": "newzora-backend", "version": "1.0.0", "description": "Backend API for Newzora", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js", "init-db": "node scripts/initDatabase.js", "db:create": "node ../scripts/web-database-manager.js create", "db:init": "node ../scripts/web-database-manager.js init", "db:setup": "node ../scripts/web-database-manager.js setup", "db:backup": "node ../scripts/web-database-manager.js backup", "db:status": "node ../scripts/web-database-manager.js status", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:integration": "jest --testPathPattern=integration"}, "keywords": ["content", "hub", "api", "express"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.18.1", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^3.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.5", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "pg": "^8.16.3", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.3", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "util-deprecate": "^1.0.2", "web-push": "^3.6.7", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.0.1"}}