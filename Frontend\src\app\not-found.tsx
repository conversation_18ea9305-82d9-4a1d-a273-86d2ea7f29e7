import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-300">404</h1>
        </div>

        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>
          <p className="text-gray-600">
            Sorry, we couldn't find the page you're looking for. The page might have been moved,
            deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4">
          <Link
            href="/"
            className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Home
          </Link>

          <div className="text-sm text-gray-500">
            <p>Or try searching for what you need:</p>
            <div className="mt-2">
              <Link href="/?search=technology" className="text-blue-600 hover:text-blue-800 mr-4">
                Technology
              </Link>
              <Link href="/?search=travel" className="text-blue-600 hover:text-blue-800 mr-4">
                Travel
              </Link>
              <Link href="/?search=lifestyle" className="text-blue-600 hover:text-blue-800">
                Lifestyle
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-12">
          <svg
            className="mx-auto h-24 w-24 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8a7.962 7.962 0 01-2 5.291z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}
