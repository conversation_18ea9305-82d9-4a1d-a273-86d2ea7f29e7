const express = require('express');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const router = express.Router();

// Temporary admin accounts for testing (will be replaced with database later)
const adminAccounts = [
  {
    id: '1',
    email: '<EMAIL>',
    password: '$2a$10$JjzCTaBPupA7lR3j2VFvZONtkq3KMgW80c.7px9v94hgBOcR4jadm', // admin123456
    username: 'admin',
    display_name: 'Super Administrator',
    role: 'super_admin',
    created_at: new Date().toISOString()
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: '$2a$10$S8cFG8gjozWy82uENisy6.2IvjOfPwE.O52jh7bDdLwAFl/K2Q4zW', // manager123456
    username: 'manager',
    display_name: 'Content Manager',
    role: 'admin',
    created_at: new Date().toISOString()
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: '$2a$10$a.AOhxSfqLpOHnVlaPpuPO.1khvxTkIDkEPxcIxIaLAs7L4uDp.MC', // moderator123456
    username: 'moderator',
    display_name: 'Content Moderator',
    role: 'moderator',
    created_at: new Date().toISOString()
  }
];

// Admin role validation
const VALID_ADMIN_ROLES = ['super_admin', 'admin', 'moderator'];

// Admin login with local authentication
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    // Find admin account
    const admin = adminAccounts.find(acc => acc.email.toLowerCase() === email.toLowerCase());

    if (!admin) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, admin.password);

    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Check if user has admin privileges
    if (!VALID_ADMIN_ROLES.includes(admin.role)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    // Generate JWT token for admin session
    const token = jwt.sign(
      {
        id: admin.id,
        email: admin.email,
        username: admin.username,
        role: admin.role
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: admin.id,
        email: admin.email,
        username: admin.username,
        display_name: admin.display_name,
        role: admin.role,
        created_at: admin.created_at
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
});

// Token verification endpoint
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({
        success: false,
        valid: false,
        message: 'No token provided'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret');

    // Find the admin account to ensure it still exists
    const admin = adminAccounts.find(acc => acc.id === decoded.id);

    if (!admin) {
      return res.status(401).json({
        success: false,
        valid: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      valid: true,
      user: {
        id: decoded.id,
        email: decoded.email,
        username: decoded.username,
        role: decoded.role
      }
    });
  } catch (error) {
    console.error('Token verification error:', error);
    res.status(401).json({
      success: false,
      valid: false,
      message: 'Invalid token'
    });
  }
});

// Logout endpoint
router.post('/logout', async (req, res) => {
  try {
    // For local authentication, we just need to confirm logout
    // The frontend will handle removing the token from localStorage

    res.json({
      success: true,
      message: 'Logout successful'
    });
  } catch (error) {
    console.error('Logout error:', error);
    res.json({
      success: true,
      message: 'Logout successful'
    });
  }
});

module.exports = router;