'use client';

import React, { useState, FormEvent } from 'react';
import { toast } from 'react-hot-toast';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentId: number;
  contentType: 'article' | 'comment' | 'user';
  contentTitle?: string;
}

const REPORT_REASONS = [
  { value: 'political_sensitive', label: 'Political Sensitive Content', description: 'Content involving sensitive political topics in the country' },
  { value: 'defamation', label: 'Defamation & False Accusations', description: 'Content that spreads false information or defames individuals/organizations' },
  { value: 'violent_content', label: 'Violent & Bloody Content', description: 'Content containing violence, gore, or disturbing imagery' },
  { value: 'similarity_plagiarism', label: 'Content Similarity & Plagiarism', description: 'Content that is highly similar to or copied from existing works' },
  { value: 'copyright_infringement', label: 'Copyright Infringement', description: 'Unauthorized use of copyrighted material' },
  { value: 'spam', label: 'Spam Content', description: 'Repetitive, meaningless, or promotional content' },
  { value: 'harassment', label: 'Harassment & Bullying', description: 'Malicious behavior targeting individuals' },
  { value: 'hate_speech', label: 'Hate Speech', description: 'Discriminatory content based on race, gender, religion, etc.' },
  { value: 'adult_content', label: 'Inappropriate Adult Content', description: 'Inappropriate adult or sexual content' },
  { value: 'other', label: 'Other Violations', description: 'Other types of violations not listed above' }
];

const ReportModal = ({ isOpen, onClose, contentId, contentType, contentTitle }: ReportModalProps) => {
  const [selectedReason, setSelectedReason] = useState('');
  const [description, setDescription] = useState('');
  const [loading, setLoading] = useState(false);

  // 重置表单状态
  const resetForm = () => {
    setSelectedReason('');
    setDescription('');
    setLoading(false);
  };

  // 处理表单提交
  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!selectedReason) {
      toast.error('Please select a reason for reporting');
      return;
    }

    try {
      setLoading(true);
      
      const response = await fetch('/api/reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: JSON.stringify({
          contentType,
          contentId,
          reason: selectedReason,
          description: description.trim()
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Report submitted successfully, we will process it as soon as possible');
        resetForm();
        onClose();
      } else {
        toast.error(data.message || 'Failed to submit report');
      }
    } catch (error) {
      console.error('举报提交错误:', error);
      toast.error('Network error, please try again later');
    } finally {
      setLoading(false);
    }
  };

  // 处理关闭
  const handleClose = () => {
    if (!loading) {
      resetForm();
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* 标题 */}
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Report Content</h3>
            <button
              onClick={handleClose}
              disabled={loading}
              className="text-gray-400 hover:text-gray-600 disabled:opacity-50"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 内容信息 */}
          {contentTitle && (
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">Reporting Content:</p>
              <p className="font-medium text-gray-900 truncate">{contentTitle}</p>
            </div>
          )}

          {/* 举报表单 */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 举报原因选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Please select a reason for reporting <span className="text-red-500">*</span>
              </label>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {REPORT_REASONS.map((reason) => (
                  <label
                    key={reason.value}
                    className={`flex items-start p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedReason === reason.value
                        ? 'border-red-500 bg-red-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="reason"
                      value={reason.value}
                      checked={selectedReason === reason.value}
                      onChange={(e) => setSelectedReason(e.target.value)}
                      disabled={loading}
                      className="mt-1 text-red-600 focus:ring-red-500"
                    />
                    <div className="ml-3">
                      <div className="font-medium text-gray-900">{reason.label}</div>
                      <div className="text-sm text-gray-600">{reason.description}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            {/* 详细描述 */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Detailed Description (Optional)
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                disabled={loading}
                rows={4}
                maxLength={500}
                placeholder="Please describe the violation in detail to help us handle it better..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {description.length}/500
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                disabled={loading}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !selectedReason}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Submitting...' : 'Submit Report'}
              </button>
            </div>
          </form>

          {/* 提示信息 */}
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              <span className="font-medium">Notice:</span> We will carefully review every report. Malicious reporting may result in account restrictions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportModal;