const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const WithdrawalRequest = sequelize.define('WithdrawalRequest', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 100.00, // 最低100美元
    },
  },
  currency: {
    type: DataTypes.STRING(3),
    allowNull: false,
    defaultValue: 'USD',
  },
  withdrawalMethod: {
    type: DataTypes.ENUM('bank_card', 'paypal', 'crypto'),
    allowNull: false,
  },
  withdrawalDetails: {
    type: DataTypes.JSONB,
    allowNull: false,
  },
  country: {
    type: DataTypes.STRING(2),
    allowNull: false,
  },
  taxRate: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
  },
  feeRate: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
  },
  taxAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
  },
  feeAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
  },
  netAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
  },
  status: {
    type: DataTypes.ENUM('pending', 'verified', 'processing', 'completed', 'rejected'),
    defaultValue: 'pending',
  },
  verificationToken: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  verificationExpires: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  processedAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  scheduledPaymentDate: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  tableName: 'withdrawal_requests',
  timestamps: true,
});

module.exports = WithdrawalRequest;