'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/Card';

const apiCategories = {
  auth: {
    title: '认证相关',
    endpoints: [
      {
        method: 'POST',
        path: '/api/auth/register',
        description: '用户注册',
        parameters: [
          { name: 'email', type: 'string', required: true, description: '邮箱地址' },
          { name: 'password', type: 'string', required: true, description: '密码' }
        ],
        response: '{ "success": true, "token": "jwt_token" }'
      }
    ]
  },
  articles: {
    title: '文章管理',
    endpoints: [
      {
        method: 'GET',
        path: '/api/articles',
        description: '获取文章列表',
        parameters: [
          { name: 'page', type: 'number', required: false, description: '页码' }
        ],
        response: '{ "success": true, "data": [...] }'
      }
    ]
  }
};

export default function ApiDocsPage() {
  const [selectedCategory, setSelectedCategory] = useState('auth');

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">API 文档</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <div className="lg:col-span-1">
          <Card className="p-4">
            <h2 className="font-semibold mb-4">API 分类</h2>
            <nav className="space-y-2">
              {Object.entries(apiCategories).map(([key, category]) => (
                <button
                  key={key}
                  onClick={() => setSelectedCategory(key)}
                  className={`w-full text-left px-3 py-2 rounded ${
                    selectedCategory === key ? 'bg-blue-100' : 'hover:bg-gray-100'
                  }`}
                >
                  {category.title}
                </button>
              ))}
            </nav>
          </Card>
        </div>

        <div className="lg:col-span-3">
          <Card className="p-6">
            <h2 className="text-2xl font-semibold mb-6">
              {apiCategories[selectedCategory as keyof typeof apiCategories].title}
            </h2>
            
            <div className="space-y-6">
              {apiCategories[selectedCategory as keyof typeof apiCategories].endpoints.map((endpoint, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded text-xs font-semibold">
                      {endpoint.method}
                    </span>
                    <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                      {endpoint.path}
                    </code>
                  </div>
                  
                  <p className="text-gray-700 mb-4">{endpoint.description}</p>
                  
                  <div>
                    <h4 className="font-semibold mb-2">响应示例</h4>
                    <pre className="bg-gray-100 p-3 rounded text-sm">
                      <code>{endpoint.response}</code>
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}