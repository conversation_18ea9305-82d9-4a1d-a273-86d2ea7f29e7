'use client';

import React from 'react';
import { FileCheck, Flag, Bell } from 'lucide-react';
import { useRouter } from 'next/navigation';

const QuickActions: React.FC = () => {
  const router = useRouter();

  const handlePendingContent = () => {
    router.push('/admin/content');
  };

  const handleUserReports = () => {
    router.push('/admin/users');
  };

  const handleSystemNotifications = () => {
    router.push('/admin/settings');
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div className="space-y-3">
        <button 
          onClick={handlePendingContent}
          className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200"
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 rounded-lg text-yellow-600">
              <FileCheck size={18} />
            </div>
            <div>
              <div className="font-medium text-gray-900">Pending Content</div>
              <div className="text-sm text-gray-500">Review pending articles and comments</div>
            </div>
          </div>
        </button>
        
        <button 
          onClick={handleUserReports}
          className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200"
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg text-red-600">
              <Flag size={18} />
            </div>
            <div>
              <div className="font-medium text-gray-900">User Reports</div>
              <div className="text-sm text-gray-500">View and handle user reports</div>
            </div>
          </div>
        </button>
        
        <button 
          onClick={handleSystemNotifications}
          className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200"
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
              <Bell size={18} />
            </div>
            <div>
              <div className="font-medium text-gray-900">System Notifications</div>
              <div className="text-sm text-gray-500">Publish system announcements</div>
            </div>
          </div>
        </button>
      </div>
    </div>
  );
};

export default QuickActions;