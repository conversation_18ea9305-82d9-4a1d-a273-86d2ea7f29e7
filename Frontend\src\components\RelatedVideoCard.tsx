'use client';

import React, { useState } from 'react';
import { Video } from '@/types';
import ReportModal from './ReportModal';

interface RelatedVideoCardProps {
  video: Video;
  onClick: () => void;
  className?: string;
}

/**
 * Related Video Card Component
 * Displays video thumbnail with proper loading states and error handling
 * Following code generation rules: complete TypeScript, no any types, includes error handling
 */
export default function RelatedVideoCard({ 
  video, 
  onClick, 
  className = '' 
}: RelatedVideoCardProps): JSX.Element {
  const [imageError, setImageError] = useState<boolean>(false);
  const [imageLoading, setImageLoading] = useState<boolean>(true);
  const [showReportModal, setShowReportModal] = useState<boolean>(false);

  const handleImageError = (): void => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = (): void => {
    setImageLoading(false);
  };

  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatViewCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const getAuthorName = (): string => {
    if (typeof video.author === 'object' && video.author?.name) {
      return video.author.name;
    }
    return 'Unknown Author';
  };

  const getThumbnailUrl = (): string => {
    if (imageError || !video.thumbnailUrl) {
      // Generate a placeholder thumbnail with video title
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(video.title)}&background=667eea&color=fff&size=200x120&format=png`;
    }
    return video.thumbnailUrl;
  };

  return (
    <>
    <div
      onClick={onClick}
      className={`flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-all duration-200 group ${className}`}
    >
      {/* Thumbnail Container */}
      <div className="relative w-24 h-16 flex-shrink-0 rounded overflow-hidden bg-gray-200">
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
        
        <img
          src={getThumbnailUrl()}
          alt={video.title}
          className={`w-full h-full object-cover transition-all duration-200 group-hover:scale-105 ${
            imageLoading ? 'opacity-0' : 'opacity-100'
          }`}
          onError={handleImageError}
          onLoad={handleImageLoad}
          loading="lazy"
        />

        {/* Duration Badge */}
        {video.duration && (
          <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 py-0.5 rounded">
            {formatDuration(video.duration)}
          </div>
        )}

        {/* Play Icon Overlay */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-20">
          <div className="w-6 h-6 bg-white bg-opacity-90 rounded-full flex items-center justify-center">
            <svg className="w-3 h-3 text-gray-800 ml-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
            </svg>
          </div>
        </div>
      </div>

      {/* Video Info */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1 group-hover:text-blue-600 transition-colors duration-200">
          {video.title}
        </h4>
        
        <div className="space-y-1">
          <p className="text-xs text-gray-600 hover:text-gray-800 transition-colors duration-200">
            {getAuthorName()}
          </p>
          
          {/* Video Stats */}
          <div className="flex items-center gap-2 text-xs text-gray-500">
            {video.viewCount !== undefined && (
              <span>{formatViewCount(video.viewCount)} views</span>
            )}
            
            {video.createdAt && (
              <>
                <span>•</span>
                <span>{new Date(video.createdAt).toLocaleDateString()}</span>
              </>
            )}
          </div>

          {/* Quality Badge and Report Button */}
          <div className="flex items-center justify-between">
            {video.resolution?.quality && (
              <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                {video.resolution.quality}
              </span>
            )}
            <button
              onClick={(e) => {
                e.stopPropagation();
                setShowReportModal(true);
              }}
              className="text-gray-400 hover:text-red-500 transition-colors p-1"
              title="Report video"
            >
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    
    {/* Report Modal */}
    <ReportModal
      isOpen={showReportModal}
      onClose={() => setShowReportModal(false)}
      contentId={video.id}
      contentType="video"
      contentTitle={video.title}
    />
  </>
  );
}

/**
 * Related Video Card Skeleton Component
 * Shows loading state while videos are being fetched
 */
export function RelatedVideoCardSkeleton(): JSX.Element {
  return (
    <div className="flex gap-3 p-2 rounded-lg animate-pulse">
      {/* Thumbnail Skeleton */}
      <div className="w-24 h-16 bg-gray-200 rounded flex-shrink-0"></div>
      
      {/* Content Skeleton */}
      <div className="flex-1 min-w-0 space-y-2">
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
      </div>
    </div>
  );
}

/**
 * Related Videos List Component
 * Container for multiple related video cards with loading states
 */
interface RelatedVideosListProps {
  videos: Video[];
  loading?: boolean;
  onVideoClick: (videoId: string) => void;
  className?: string;
}

export function RelatedVideosList({ 
  videos, 
  loading = false, 
  onVideoClick, 
  className = '' 
}: RelatedVideosListProps): JSX.Element {
  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {Array.from({ length: 5 }).map((_, index) => (
          <RelatedVideoCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  if (videos.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="text-gray-400 mb-2">
          <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
          </svg>
        </div>
        <p className="text-sm text-gray-500">No related videos found</p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {videos.map((video) => (
        <RelatedVideoCard
          key={video.id}
          video={video}
          onClick={() => onVideoClick(video.id)}
        />
      ))}
    </div>
  );
}
