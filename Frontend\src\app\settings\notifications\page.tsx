'use client';

import React, { useState } from 'react';
import Header from '@/components/Header';
import { BellIcon, DevicePhoneMobileIcon, EnvelopeIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface NotificationSettings {
  email: {
    comments: boolean;
    likes: boolean;
    follows: boolean;
    messages: boolean;
    systemUpdates: boolean;
    newsletter: boolean;
  };
  push: {
    comments: boolean;
    likes: boolean;
    follows: boolean;
    messages: boolean;
    systemUpdates: boolean;
  };
  inApp: {
    comments: boolean;
    likes: boolean;
    follows: boolean;
    messages: boolean;
    systemUpdates: boolean;
  };
}

export default function NotificationSettingsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings>({
    email: {
      comments: true,
      likes: false,
      follows: true,
      messages: true,
      systemUpdates: true,
      newsletter: false,
    },
    push: {
      comments: true,
      likes: true,
      follows: true,
      messages: true,
      systemUpdates: true,
    },
    inApp: {
      comments: true,
      likes: true,
      follows: true,
      messages: true,
      systemUpdates: true,
    },
  });

  const handleSettingChange = (category: keyof NotificationSettings, setting: string, value: boolean) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value,
      },
    }));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast.success('Notification settings updated successfully!');
    } catch (error) {
      toast.error('Failed to update notification settings');
    } finally {
      setIsLoading(false);
    }
  };

  const ToggleSwitch = ({ 
    checked, 
    onChange, 
    disabled = false 
  }: { 
    checked: boolean; 
    onChange: (value: boolean) => void; 
    disabled?: boolean;
  }) => (
    <label className="relative inline-flex items-center cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={checked}
        onChange={(e) => onChange(e.target.checked)}
        disabled={disabled}
      />
      <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}></div>
    </label>
  );

  const NotificationCategory = ({ 
    title, 
    icon, 
    category, 
    settings: categorySettings 
  }: { 
    title: string; 
    icon: React.ReactNode; 
    category: keyof NotificationSettings; 
    settings: any;
  }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          {icon}
          {title}
        </h2>
      </div>

      <div className="p-6 space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">Comments</h3>
            <p className="text-sm text-gray-600">When someone comments on your content</p>
          </div>
          <ToggleSwitch
            checked={categorySettings.comments}
            onChange={(value) => handleSettingChange(category, 'comments', value)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">Likes</h3>
            <p className="text-sm text-gray-600">When someone likes your content</p>
          </div>
          <ToggleSwitch
            checked={categorySettings.likes}
            onChange={(value) => handleSettingChange(category, 'likes', value)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">New Followers</h3>
            <p className="text-sm text-gray-600">When someone follows you</p>
          </div>
          <ToggleSwitch
            checked={categorySettings.follows}
            onChange={(value) => handleSettingChange(category, 'follows', value)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">Messages</h3>
            <p className="text-sm text-gray-600">When you receive a direct message</p>
          </div>
          <ToggleSwitch
            checked={categorySettings.messages}
            onChange={(value) => handleSettingChange(category, 'messages', value)}
          />
        </div>

        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">System Updates</h3>
            <p className="text-sm text-gray-600">Important platform announcements</p>
          </div>
          <ToggleSwitch
            checked={categorySettings.systemUpdates}
            onChange={(value) => handleSettingChange(category, 'systemUpdates', value)}
          />
        </div>

        {category === 'email' && (
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-900">Newsletter</h3>
              <p className="text-sm text-gray-600">Weekly digest and platform updates</p>
            </div>
            <ToggleSwitch
              checked={categorySettings.newsletter}
              onChange={(value) => handleSettingChange(category, 'newsletter', value)}
            />
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Notification Settings</h1>
          <p className="text-gray-600 mt-1">Manage how you receive notifications</p>
        </div>

        <div className="space-y-8">
          <NotificationCategory
            title="Email Notifications"
            icon={<EnvelopeIcon className="h-5 w-5 mr-2" />}
            category="email"
            settings={settings.email}
          />

          <NotificationCategory
            title="Push Notifications"
            icon={<DevicePhoneMobileIcon className="h-5 w-5 mr-2" />}
            category="push"
            settings={settings.push}
          />

          <NotificationCategory
            title="In-App Notifications"
            icon={<BellIcon className="h-5 w-5 mr-2" />}
            category="inApp"
            settings={settings.inApp}
          />

          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Notification Frequency</h2>
            </div>

            <div className="p-6 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Email Digest</h3>
                  <p className="text-sm text-gray-600">How often to receive email summaries</p>
                </div>
                <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                  <option value="immediate">Immediate</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="never">Never</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Quiet Hours</h3>
                  <p className="text-sm text-gray-600">Disable notifications during these hours</p>
                </div>
                <div className="flex items-center space-x-2">
                  <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    <option value="22">10 PM</option>
                    <option value="23">11 PM</option>
                    <option value="0">12 AM</option>
                  </select>
                  <span className="text-gray-500">to</span>
                  <select className="border border-gray-300 rounded-lg px-3 py-2 text-sm">
                    <option value="6">6 AM</option>
                    <option value="7">7 AM</option>
                    <option value="8">8 AM</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={isLoading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 font-medium"
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}