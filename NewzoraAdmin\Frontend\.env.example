# Admin System Configuration
NEXT_PUBLIC_APP_NAME=Newzora Admin
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_ENV=development

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:5001
NEXT_PUBLIC_FRONTEND_API_URL=http://localhost:3000

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Authentication
NEXT_PUBLIC_AUTH_REDIRECT_URL=http://localhost:3001/admin/dashboard
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# Database Configuration (if using direct database connection)
DATABASE_URL=postgresql://username:password@localhost:5432/newzora_admin
DB_HOST=localhost
DB_PORT=5432
DB_NAME=newzora_admin
DB_USER=your_db_user
DB_PASSWORD=your_db_password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>
FROM_NAME=Newzora Admin

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf
UPLOAD_PATH=uploads
NEXT_PUBLIC_UPLOAD_URL=http://localhost:3001/uploads

# Security Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SESSION_SECRET=your_session_secret_here

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/admin.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Analytics and Monitoring
GOOGLE_ANALYTICS_ID=
SENTRY_DSN=
NEW_RELIC_LICENSE_KEY=

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_NOTIFICATIONS=true
NEXT_PUBLIC_ENABLE_REAL_TIME_SYNC=true
NEXT_PUBLIC_ENABLE_DEBUG_MODE=false

# Third-party Integrations
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# Development Configuration
NEXT_PUBLIC_DEV_MODE=true
NEXT_PUBLIC_MOCK_API=false
NEXT_PUBLIC_SHOW_DEV_TOOLS=true

# Production Configuration
NEXT_PUBLIC_CDN_URL=
NEXT_PUBLIC_STATIC_URL=
NEXT_PUBLIC_ASSET_PREFIX=

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=
BACKUP_S3_REGION=
BACKUP_S3_ACCESS_KEY=
BACKUP_S3_SECRET_KEY=
