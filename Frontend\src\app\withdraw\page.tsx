'use client';

import { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter } from 'next/navigation';
import { Shield, AlertTriangle, CheckCircle, User, MapPin, FileText, Phone, Mail, Video, CreditCard, Clock, DollarSign } from 'lucide-react';
import AuthGuard from '@/components/AuthGuard';
import Header from '@/components/Header';

interface WithdrawalPreview {
  amount: number;
  taxRate: number;
  serviceFeeRate: number;
  methodFeeRate: number;
  taxAmount: number;
  serviceFeeAmount: number;
  methodFeeAmount: number;
  totalFeeAmount: number;
  netAmount: number;
  scheduledPaymentDate: string;
  estimatedArrival: string;
}



interface VerificationStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  required: boolean;
  estimatedTime: string;
  requirements: string[];
}

function WithdrawContent() {
  const { user, isLoading } = useSimpleAuth();
  const { balance, updateBalance, addWithdrawalRequest } = useData();
  const router = useRouter();
  const [amount, setAmount] = useState('');
  const [country, setCountry] = useState('US');
  const [withdrawalMethod, setWithdrawalMethod] = useState('bank_card');
  const [preview, setPreview] = useState<WithdrawalPreview | null>(null);
  const [showSecurity, setShowSecurity] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeVerificationStep, setActiveVerificationStep] = useState<string | null>(null);

  const [verificationSteps, setVerificationSteps] = useState<VerificationStep[]>([
    {
      id: 'identity',
      title: 'Manual Identity Verification',
      description: 'Human expert review for document authenticity and fraud detection',
      icon: User,
      status: 'completed',
      required: true,
      estimatedTime: '24-48 hours',
      requirements: ['Government-issued photo ID', 'Manual expert review', 'Document authenticity check', 'Anti-fraud verification']
    },
    {
      id: 'address',
      title: 'Manual Address Verification',
      description: 'Human verification of residential address documents',
      icon: MapPin,
      status: 'completed',
      required: true,
      estimatedTime: '24-48 hours',
      requirements: ['Utility bill (last 3 months)', 'Bank statement', 'Government correspondence', 'Manual document review']
    },
    {
      id: 'document',
      title: 'Manual Document Verification',
      description: 'Human expert review of supporting documents',
      icon: FileText,
      status: 'in_progress',
      required: true,
      estimatedTime: '24-72 hours',
      requirements: ['Tax documents', 'Business registration (if applicable)', 'Income verification', 'Manual expert review']
    },
    {
      id: 'phone',
      title: 'Manual Phone Verification',
      description: 'Human verification call to confirm phone number',
      icon: Phone,
      status: 'pending',
      required: true,
      estimatedTime: '1-3 business days',
      requirements: ['Active phone number', 'Manual verification call', 'Identity confirmation over phone']
    },
    {
      id: 'email',
      title: 'Manual Email Verification',
      description: 'Human verification of email address and identity',
      icon: Mail,
      status: 'completed',
      required: true,
      estimatedTime: '24-48 hours',
      requirements: ['Active email address', 'Manual verification process', 'Identity confirmation via email']
    },
    {
      id: 'video',
      title: 'Manual Video Call Verification',
      description: 'Live video call with human verification specialist',
      icon: Video,
      status: 'pending',
      required: true,
      estimatedTime: '1-3 business days',
      requirements: ['Scheduled video call', 'Government ID verification', 'Live identity confirmation', 'Human specialist review']
    }
  ]);

  const paymentMethods = {
    bank_card: { name: 'Bank Transfer', fee: 2.5, time: '1-3 business days', icon: CreditCard },
    paypal: { name: 'PayPal', fee: 3.5, time: 'Instant', icon: DollarSign },
    crypto_btc: { name: 'Bitcoin (BTC)', fee: 1.5, time: '30min-2hrs', icon: DollarSign },
    crypto_usdt: { name: 'USDT (TRC20)', fee: 1.0, time: '5-15min', icon: DollarSign }
  };

  const countries = {
    US: { name: 'United States', tax: 24, serviceFee: 2.5 },
    CN: { name: 'China', tax: 20, serviceFee: 1.8 },
    GB: { name: 'United Kingdom', tax: 20, serviceFee: 2.2 },
    DE: { name: 'Germany', tax: 26.375, serviceFee: 2.8 },
    JP: { name: 'Japan', tax: 20.315, serviceFee: 2.1 },
    CA: { name: 'Canada', tax: 26.76, serviceFee: 2.4 },
    AU: { name: 'Australia', tax: 32.5, serviceFee: 3.2 },
    FR: { name: 'France', tax: 30, serviceFee: 2.9 },
    IT: { name: 'Italy', tax: 26, serviceFee: 2.7 },
    ES: { name: 'Spain', tax: 24, serviceFee: 2.6 },
    NL: { name: 'Netherlands', tax: 25.8, serviceFee: 2.5 },
    SE: { name: 'Sweden', tax: 30, serviceFee: 3.0 },
    NO: { name: 'Norway', tax: 22, serviceFee: 2.8 },
    DK: { name: 'Denmark', tax: 27, serviceFee: 2.9 },
    FI: { name: 'Finland', tax: 30, serviceFee: 3.1 },
    CH: { name: 'Switzerland', tax: 35, serviceFee: 3.5 },
    AT: { name: 'Austria', tax: 27.5, serviceFee: 2.8 },
    BE: { name: 'Belgium', tax: 30, serviceFee: 3.0 },
    IE: { name: 'Ireland', tax: 20, serviceFee: 2.3 },
    PT: { name: 'Portugal', tax: 28, serviceFee: 2.7 },
    KR: { name: 'South Korea', tax: 22, serviceFee: 2.3 },
    SG: { name: 'Singapore', tax: 17, serviceFee: 2.0 },
    HK: { name: 'Hong Kong', tax: 17, serviceFee: 1.9 },
    TW: { name: 'Taiwan', tax: 20, serviceFee: 2.1 },
    MY: { name: 'Malaysia', tax: 24, serviceFee: 2.2 },
    TH: { name: 'Thailand', tax: 15, serviceFee: 1.8 },
    IN: { name: 'India', tax: 30, serviceFee: 2.5 },
    BR: { name: 'Brazil', tax: 27.5, serviceFee: 3.2 },
    MX: { name: 'Mexico', tax: 30, serviceFee: 2.8 },
    RU: { name: 'Russia', tax: 13, serviceFee: 2.5 },
    TR: { name: 'Turkey', tax: 20, serviceFee: 2.3 },
    SA: { name: 'Saudi Arabia', tax: 0, serviceFee: 2.0 },
    AE: { name: 'UAE', tax: 0, serviceFee: 1.8 },
    IL: { name: 'Israel', tax: 23, serviceFee: 2.4 },
    ZA: { name: 'South Africa', tax: 45, serviceFee: 3.5 },
    NG: { name: 'Nigeria', tax: 24, serviceFee: 2.2 },
    EG: { name: 'Egypt', tax: 22.5, serviceFee: 2.0 }
  };

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
      return;
    }
  }, [user, isLoading, router]);

  const getOverallProgress = () => {
    const completedSteps = verificationSteps.filter(step => step.status === 'completed').length;
    return Math.round((completedSteps / verificationSteps.length) * 100);
  };

  const isFullyVerified = () => {
    return verificationSteps.every(step => step.status === 'completed');
  };

  const handleStepAction = (stepId: string) => {
    setActiveVerificationStep(stepId);
    setVerificationSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status: step.status === 'pending' ? 'in_progress' : step.status }
        : step
    ));

    const processingTime = stepId === 'video' ? 5000 : stepId === 'identity' ? 8000 : 3000;
    
    setTimeout(() => {
      setVerificationSteps(prev => prev.map(step => 
        step.id === stepId 
          ? { ...step, status: 'completed' }
          : step
      ));
      setActiveVerificationStep(null);
      
      if (stepId === 'video') {
        alert('✅ Manual video verification completed! Identity confirmed by human specialist.');
      } else if (stepId === 'identity') {
        alert('✅ Manual identity verification passed! Document authenticity confirmed by expert.');
      } else {
        alert('✅ Manual verification completed! Documents reviewed and approved by human specialist.');
      }
    }, processingTime);
  };

  const handlePreview = () => {
    if (!isFullyVerified()) {
      alert('Please complete all verification steps before making a withdrawal.');
      return;
    }
    
    if (!amount || parseFloat(amount) < 100) {
      alert('Minimum withdrawal amount is $100');
      return;
    }

    if (parseFloat(amount) > balance.availableBalance) {
      alert(`Withdrawal amount cannot exceed available balance ($${balance.availableBalance.toFixed(2)})`);
      return;
    }

    const amountNum = parseFloat(amount);
    const method = paymentMethods[withdrawalMethod as keyof typeof paymentMethods];
    const countryInfo = countries[country as keyof typeof countries];
    
    const taxRate = countryInfo.tax / 100;
    const serviceFeeRate = countryInfo.serviceFee / 100;
    const methodFeeRate = method.fee / 100;
    
    const taxAmount = parseFloat((amountNum * taxRate).toFixed(2));
    const serviceFeeAmount = parseFloat((amountNum * serviceFeeRate).toFixed(2));
    const methodFeeAmount = parseFloat((amountNum * methodFeeRate).toFixed(2));
    const totalFeeAmount = serviceFeeAmount + methodFeeAmount;
    const netAmount = parseFloat((amountNum - taxAmount - totalFeeAmount).toFixed(2));

    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + 7);
    
    setPreview({
      amount: amountNum,
      taxRate,
      serviceFeeRate,
      methodFeeRate,
      taxAmount,
      serviceFeeAmount,
      methodFeeAmount,
      totalFeeAmount,
      netAmount,
      scheduledPaymentDate: scheduledDate.toISOString(),
      estimatedArrival: method.time
    });
  };

  const handleSecurityVerification = () => {
    if (!preview) return;
    setShowSecurity(true);
  };

  const handleFinalSubmit = async () => {
    if (verificationCode !== '123456') {
      alert('Invalid verification code. For demo, please enter: 123456');
      return;
    }

    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (preview) {
        // 实时更新余额数据
        updateBalance({
          availableBalance: balance.availableBalance - preview.amount,
          pendingBalance: balance.pendingBalance + preview.netAmount
        });
        
        // 添加提现记录
        const newRequest = {
          id: Date.now(),
          amount: preview.amount,
          netAmount: preview.netAmount,
          withdrawalMethod,
          status: 'processing',
          country,
          taxAmount: preview.taxAmount,
          feeAmount: preview.totalFeeAmount,
          scheduledPaymentDate: preview.scheduledPaymentDate,
          createdAt: new Date().toISOString()
        };
        
        addWithdrawalRequest(newRequest);
        
        // 触发storage事件以通知其他页面
        window.dispatchEvent(new Event('storage'));
      }
      
      alert('Withdrawal request submitted successfully!');
      router.push('/withdraw/history');
    } catch (error) {
      alert('Withdrawal request failed. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (showSecurity) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-md mx-auto pt-20 p-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Security Verification</h2>
              <p className="text-gray-600 text-sm">
                To protect your funds, please enter the verification code
              </p>
            </div>

            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start">
                  <Shield className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Two-Factor Authentication</p>
                    <p className="mb-2">We've sent a 6-digit verification code to your email</p>
                    <p className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">Demo: enter 123456</p>
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Verification Code
                </label>
                <input
                  type="text"
                  maxLength={6}
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                  className="w-full p-4 border border-gray-300 rounded-lg text-center text-xl tracking-widest focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="000000"
                />
              </div>

              {preview && (
                <div className="bg-gray-50 p-4 rounded-lg text-sm">
                  <h3 className="font-semibold mb-3 text-gray-900">Withdrawal Confirmation</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount:</span>
                      <span className="font-medium">${preview.amount}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Method:</span>
                      <span>{paymentMethods[withdrawalMethod as keyof typeof paymentMethods].name}</span>
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>Tax ({(preview.taxRate * 100).toFixed(1)}%):</span>
                      <span>-${preview.taxAmount}</span>
                    </div>
                    <div className="flex justify-between text-orange-600">
                      <span>Service Fee:</span>
                      <span>-${preview.serviceFeeAmount}</span>
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>Method Fee:</span>
                      <span>-${preview.methodFeeAmount}</span>
                    </div>
                    <hr className="my-2" />
                    <div className="flex justify-between font-bold text-green-600">
                      <span>Net Amount:</span>
                      <span>${preview.netAmount}</span>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowSecurity(false)}
                  className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg hover:bg-gray-300 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleFinalSubmit}
                  disabled={isSubmitting || verificationCode.length !== 6}
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 transition-all font-medium"
                >
                  {isSubmitting ? 'Processing...' : 'Confirm Withdrawal'}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-7xl mx-auto px-6 py-8 pt-20">
        {/* Top Section - Account Balance */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <DollarSign className="h-6 w-6 mr-3 text-blue-600" />
            Account Balance
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-green-50 rounded-lg border border-green-100">
              <p className="text-sm text-gray-600 mb-2">Available Balance</p>
              <p className="text-3xl font-bold text-green-600">${balance.availableBalance}</p>
            </div>
            <div className="text-center p-6 bg-yellow-50 rounded-lg border border-yellow-100">
              <p className="text-sm text-gray-600 mb-2">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">${balance.pendingBalance}</p>
            </div>
            <div className="text-center p-6 bg-blue-50 rounded-lg border border-blue-100">
              <p className="text-sm text-gray-600 mb-2">Total Earned</p>
              <p className="text-2xl font-bold text-blue-600">${balance.totalEarnings}</p>
            </div>
          </div>
        </div>

        {/* Middle Section - Verification & Withdrawal */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* AI + Manual Verification */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Verification Progress</h3>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${getOverallProgress()}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-500">{getOverallProgress()}% Complete</p>
            </div>

            <div className="space-y-4">
              {verificationSteps.map((step, index) => {
                const Icon = step.icon;
                return (
                  <div key={step.id} className={`flex items-start space-x-4 p-4 rounded-lg border transition-all duration-200 ${
                    step.status === 'completed' ? 'bg-green-50 border-green-200' :
                    step.status === 'in_progress' ? 'bg-blue-50 border-blue-200' :
                    'bg-gray-50 border-gray-200 hover:bg-gray-100'
                  }`}>
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      step.status === 'completed' ? 'bg-green-600' :
                      step.status === 'in_progress' ? 'bg-blue-600' :
                      'bg-gray-400'
                    }`}>
                      {step.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-white" />
                      ) : step.status === 'in_progress' ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      ) : (
                        <Icon className="h-5 w-5 text-white" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium text-gray-900">{step.title}</h4>
                        {step.status === 'completed' && (
                          <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                            ✓ Done
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                      <div className="text-xs text-blue-700 bg-blue-50 px-2 py-1 rounded mb-2">
                        Manual human verification required
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {step.estimatedTime}
                        </span>
                        {step.status !== 'completed' && (
                          <button
                            onClick={() => handleStepAction(step.id)}
                            disabled={activeVerificationStep === step.id}
                            className={`text-xs px-3 py-1 rounded-md transition-colors ${
                              step.status === 'in_progress' 
                                ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                                : 'bg-blue-600 text-white hover:bg-blue-700'
                            }`}
                          >
                            {step.status === 'in_progress' ? 'Under Review...' : 'Submit for Review'}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>

            {isFullyVerified() && (
              <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-sm font-medium text-green-800">
                    Verification completed! Safe to withdraw.
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Withdrawal Form */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Withdrawal Request</h2>
            
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Withdrawal Amount (USD)
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="number"
                    min="100"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Minimum $100"
                  />
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  Available balance: ${balance.availableBalance}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Country/Region
                </label>
                <select
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.entries(countries).map(([code, info]) => (
                    <option key={code} value={code}>
                      {info.name} (Tax: {info.tax}% | Service: {info.serviceFee}%)
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {Object.entries(paymentMethods).map(([key, method]) => {
                    const Icon = method.icon;
                    return (
                      <button
                        key={key}
                        onClick={() => setWithdrawalMethod(key)}
                        className={`p-4 border rounded-lg text-left transition-all ${
                          withdrawalMethod === key
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        <div className="flex items-center mb-2">
                          <Icon className="h-5 w-5 mr-2 text-gray-600" />
                          <span className="font-medium">{method.name}</span>
                        </div>
                        <div className="text-sm text-gray-600">
                          <p>Fee: {method.fee}%</p>
                          <p>Time: {method.time}</p>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={handlePreview}
                  disabled={!isFullyVerified() || !amount}
                  className={`flex-1 py-3 rounded-lg font-medium transition-all ${
                    isFullyVerified() && amount
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  Preview Withdrawal
                </button>
              </div>

              {!isFullyVerified() && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-yellow-800">
                        Verification Required
                      </p>
                      <p className="text-sm text-yellow-700 mt-1">
                        Complete all verification steps to enable withdrawals.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {preview && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="font-bold text-gray-900 mb-4 flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Withdrawal Preview
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Withdrawal Amount:</span>
                      <span className="font-medium text-lg">${preview.amount}</span>
                    </div>
                    <div className="flex justify-between items-center text-red-600">
                      <span>Tax ({(preview.taxRate * 100).toFixed(1)}%):</span>
                      <span>-${preview.taxAmount}</span>
                    </div>
                    <div className="flex justify-between items-center text-orange-600">
                      <span>Service Fee ({countries[country as keyof typeof countries].serviceFee}%):</span>
                      <span>-${preview.serviceFeeAmount}</span>
                    </div>
                    <div className="flex justify-between items-center text-red-600">
                      <span>Method Fee ({paymentMethods[withdrawalMethod as keyof typeof paymentMethods].fee}%):</span>
                      <span>-${preview.methodFeeAmount}</span>
                    </div>
                    <hr className="border-gray-300" />
                    <div className="flex justify-between items-center font-bold text-green-600 text-lg">
                      <span>Net Amount:</span>
                      <span>${preview.netAmount}</span>
                    </div>
                    <div className="text-sm text-gray-500 mt-4 space-y-1">
                      <p className="flex items-center">
                        <Clock className="h-4 w-4 mr-2" />
                        Processing: {new Date(preview.scheduledPaymentDate).toLocaleDateString()}
                      </p>
                      <p className="flex items-center">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Arrival: {preview.estimatedArrival}
                      </p>
                    </div>
                  </div>

                  <button
                    onClick={handleSecurityVerification}
                    className="w-full mt-6 bg-green-600 text-white py-3 rounded-lg hover:bg-green-700 transition-all font-medium flex items-center justify-center"
                  >
                    <Shield className="h-5 w-5 mr-2" />
                    Proceed to Security Verification
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>


      </div>
    </div>
  );
}

export default function WithdrawPage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <WithdrawContent />
    </AuthGuard>
  );
}