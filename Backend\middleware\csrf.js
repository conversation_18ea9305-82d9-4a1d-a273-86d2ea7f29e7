/**
 * CSRF 保护中间件
 */

const crypto = require('crypto');
const { logger, logSecurityEvent } = require('../config/logger');

/**
 * 生成CSRF令牌
 */
const generateCSRFToken = () => {
  return crypto.randomBytes(32).toString('hex');
};

/**
 * 验证CSRF令牌
 */
const verifyCSRFToken = (sessionToken, requestToken) => {
  if (!sessionToken || !requestToken) {
    return false;
  }
  return crypto.timingSafeEqual(
    Buffer.from(sessionToken, 'hex'),
    Buffer.from(requestToken, 'hex')
  );
};

/**
 * CSRF保护中间件
 */
const csrfProtection = (req, res, next) => {
  // 跳过GET、HEAD、OPTIONS请求
  if (['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // 跳过API认证请求（使用JWT）
  if (req.path.startsWith('/api/auth/login') || req.path.startsWith('/api/auth/register')) {
    return next();
  }

  // 检查会话中的CSRF令牌
  if (!req.session.csrfToken) {
    req.session.csrfToken = generateCSRFToken();
  }

  // 从请求头或请求体中获取CSRF令牌
  const requestToken = req.headers['x-csrf-token'] || 
                      req.body._csrf || 
                      req.query._csrf;

  // 验证CSRF令牌
  if (!verifyCSRFToken(req.session.csrfToken, requestToken)) {
    logSecurityEvent('CSRF_TOKEN_INVALID', {
      path: req.path,
      method: req.method,
      sessionToken: req.session.csrfToken ? 'present' : 'missing',
      requestToken: requestToken ? 'present' : 'missing'
    }, req);

    return res.status(403).json({
      success: false,
      message: 'Invalid CSRF token',
      code: 'CSRF_TOKEN_INVALID'
    });
  }

  next();
};

/**
 * 提供CSRF令牌的路由处理器
 */
const getCSRFToken = (req, res) => {
  if (!req.session.csrfToken) {
    req.session.csrfToken = generateCSRFToken();
  }

  res.json({
    success: true,
    csrfToken: req.session.csrfToken
  });
};

module.exports = {
  csrfProtection,
  getCSRFToken,
  generateCSRFToken,
  verifyCSRFToken
};