@echo off
title Newzora Development Server
color 0A

echo.
echo  ███╗   ██╗███████╗██╗    ██╗███████╗ ██████╗ ██████╗  █████╗ 
echo  ████╗  ██║██╔════╝██║    ██║╚══███╔╝██╔═══██╗██╔══██╗██╔══██╗
echo  ██╔██╗ ██║█████╗  ██║ █╗ ██║  ███╔╝ ██║   ██║██████╔╝███████║
echo  ██║╚██╗██║██╔══╝  ██║███╗██║ ███╔╝  ██║   ██║██╔══██╗██╔══██║
echo  ██║ ╚████║███████╗╚███╔███╔╝███████╗╚██████╔╝██║  ██║██║  ██║
echo  ╚═╝  ╚═══╝╚══════╝ ╚══╝╚══╝ ╚══════╝ ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═╝
echo.
echo  🚀 Development Server Starter
echo  =============================
echo.

:: 检查是否在正确的目录
if not exist "Frontend\package.json" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    echo    当前目录: %CD%
    echo    应该包含 Frontend\package.json 文件
    pause
    exit /b 1
)

:: 检查端口3000是否被占用
echo 🔍 检查端口 3000...
netstat -ano | findstr :3000 >nul 2>&1
if %errorlevel% == 0 (
    echo ⚠️  端口 3000 被占用!
    echo.
    echo 📋 占用详情:
    netstat -ano | findstr :3000
    echo.
    
    set /p choice="是否要释放端口 3000? (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo 🔄 正在释放端口 3000...
        
        :: 使用PowerShell获取并终止占用端口的进程
        powershell -Command "Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue | ForEach-Object { Stop-Process -Id $_.OwningProcess -Force -ErrorAction SilentlyContinue }"
        
        timeout /t 2 >nul
        echo ✅ 端口已释放!
    ) else (
        echo.
        echo 🔄 使用备用端口 3001...
        cd Frontend
        echo 📝 访问地址: http://localhost:3001
        echo.
        npm run dev -- -p 3001
        goto :end
    )
) else (
    echo ✅ 端口 3000 可用!
)

echo.
echo 🚀 启动 Newzora 前端开发服务器...
echo 📝 访问地址: http://localhost:3000
echo.

:: 切换到Frontend目录并启动
cd Frontend

:: 检查node_modules是否存在
if not exist "node_modules" (
    echo 📦 安装依赖...
    npm install
    echo.
)

:: 启动开发服务器
npm run dev

:end
echo.
echo 👋 开发服务器已停止
pause
