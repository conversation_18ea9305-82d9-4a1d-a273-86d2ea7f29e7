// 数据完整性测试工具
import { mockWorks } from '@/data/mockWorks';

export const testDataIntegrity = () => {
  console.log('🔍 Testing data integrity...');
  
  // 测试所有作品是否有有效的ID
  const invalidIds = mockWorks.filter(work => !work.id || work.id <= 0);
  if (invalidIds.length > 0) {
    console.error('❌ Found works with invalid IDs:', invalidIds);
  } else {
    console.log('✅ All works have valid IDs');
  }
  
  // 测试是否有重复的ID
  const ids = mockWorks.map(work => work.id);
  const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
  if (duplicateIds.length > 0) {
    console.error('❌ Found duplicate IDs:', duplicateIds);
  } else {
    console.log('✅ No duplicate IDs found');
  }
  
  // 测试视频作品是否有有效的视频URL
  const videos = mockWorks.filter(work => work.type === 'video');
  const videosWithoutUrl = videos.filter(video => !('videoUrl' in video) || !video.videoUrl);
  if (videosWithoutUrl.length > 0) {
    console.error('❌ Found videos without valid URLs:', videosWithoutUrl);
  } else {
    console.log('✅ All videos have valid URLs');
  }
  
  // 测试音频作品是否有有效的音频URL
  const audios = mockWorks.filter(work => work.type === 'audio');
  const audiosWithoutUrl = audios.filter(audio => !('audioUrl' in audio) || !audio.audioUrl);
  if (audiosWithoutUrl.length > 0) {
    console.error('❌ Found audios without valid URLs:', audiosWithoutUrl);
  } else {
    console.log('✅ All audios have valid URLs');
  }
  
  // 测试所有作品是否有作者信息
  const worksWithoutAuthor = mockWorks.filter(work => !work.author);
  if (worksWithoutAuthor.length > 0) {
    console.error('❌ Found works without author:', worksWithoutAuthor);
  } else {
    console.log('✅ All works have author information');
  }
  
  // 统计信息
  console.log('\n📊 Data Statistics:');
  console.log(`Total works: ${mockWorks.length}`);
  console.log(`Articles: ${mockWorks.filter(w => w.type === 'article').length}`);
  console.log(`Videos: ${mockWorks.filter(w => w.type === 'video').length}`);
  console.log(`Audios: ${mockWorks.filter(w => w.type === 'audio').length}`);
  
  const categories = [...new Set(mockWorks.map(w => w.category))];
  console.log(`Categories: ${categories.join(', ')}`);
  
  console.log('\n🎯 Test completed!');
};

// 在开发环境中自动运行测试
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // 延迟执行，确保模块加载完成
  setTimeout(() => {
    testDataIntegrity();
  }, 1000);
}