/**
 * Supabase Configuration Validator
 * Validates Supabase configuration and provides helpful error messages
 * Following code generation rules: complete TypeScript, no any types, includes error handling
 */

// Configuration validation result type
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Supabase configuration type
export interface SupabaseConfig {
  url: string;
  anonKey: string;
  jwtSecret?: string;
}

/**
 * Validates Supabase environment variables
 * @returns ValidationResult with detailed feedback
 */
export function validateSupabaseConfig(): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: []
  };

  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  const jwtSecret = process.env.NEXT_PUBLIC_SUPABASE_JWT_SECRET;

  // Check if environment variables exist
  if (!url) {
    result.isValid = false;
    result.errors.push('NEXT_PUBLIC_SUPABASE_URL is not defined in environment variables');
    result.suggestions.push('Add NEXT_PUBLIC_SUPABASE_URL to your .env.local file');
  }

  if (!anonKey) {
    result.isValid = false;
    result.errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is not defined in environment variables');
    result.suggestions.push('Add NEXT_PUBLIC_SUPABASE_ANON_KEY to your .env.local file');
  }

  // Validate URL format
  if (url) {
    try {
      const urlObj = new URL(url);
      
      // Check if it's a valid Supabase URL
      if (!urlObj.hostname.includes('supabase.co') && !urlObj.hostname.includes('localhost')) {
        result.warnings.push('URL does not appear to be a valid Supabase URL');
      }

      // Check protocol
      if (urlObj.protocol !== 'https:' && urlObj.protocol !== 'http:') {
        result.errors.push('Supabase URL must use HTTP or HTTPS protocol');
        result.isValid = false;
      }

      // Check for localhost in production
      if (process.env.NODE_ENV === 'production' && urlObj.hostname.includes('localhost')) {
        result.errors.push('Cannot use localhost URL in production environment');
        result.isValid = false;
      }
    } catch (error) {
      result.isValid = false;
      result.errors.push('NEXT_PUBLIC_SUPABASE_URL is not a valid URL');
    }
  }

  // Validate anonymous key format
  if (anonKey) {
    // Supabase anon keys typically start with 'eyJ' (JWT format)
    if (!anonKey.startsWith('eyJ')) {
      result.warnings.push('Anonymous key does not appear to be in JWT format');
    }

    // Check key length (Supabase keys are typically quite long)
    if (anonKey.length < 100) {
      result.warnings.push('Anonymous key appears to be too short');
    }

    // Check for placeholder values
    if (anonKey.includes('your-anon-key') || anonKey.includes('example')) {
      result.isValid = false;
      result.errors.push('Anonymous key appears to be a placeholder value');
      result.suggestions.push('Replace with your actual Supabase anonymous key from project settings');
    }
  }

  // Validate JWT secret if provided
  if (jwtSecret) {
    // Check for placeholder values
    if (jwtSecret.includes('your-jwt-secret') || jwtSecret.includes('example')) {
      result.warnings.push('JWT secret appears to be a placeholder value');
    }

    // JWT secrets should be UUIDs or long strings
    if (jwtSecret.length < 20) {
      result.warnings.push('JWT secret appears to be too short');
    }
  }

  // Add general suggestions if there are errors
  if (!result.isValid) {
    result.suggestions.push('Visit your Supabase project dashboard > Settings > API to get the correct values');
    result.suggestions.push('Ensure your .env.local file is in the Frontend directory');
    result.suggestions.push('Restart your development server after updating environment variables');
  }

  return result;
}

/**
 * Tests Supabase connection
 * @param config Supabase configuration
 * @returns Promise<boolean> indicating if connection is successful
 */
export async function testSupabaseConnection(config: SupabaseConfig): Promise<boolean> {
  try {
    // Simple fetch test to Supabase REST API
    const response = await fetch(`${config.url}/rest/v1/`, {
      method: 'GET',
      headers: {
        'apikey': config.anonKey,
        'Authorization': `Bearer ${config.anonKey}`,
        'Content-Type': 'application/json',
      },
    });

    // Supabase should return a response (even if it's an error about missing table)
    return response.status !== 0;
  } catch (error) {
    console.error('Supabase connection test failed:', error);
    return false;
  }
}

/**
 * Gets user-friendly error message for common Supabase issues
 * @param error Error object or message
 * @returns User-friendly error message
 */
export function getSupabaseErrorMessage(error: unknown): string {
  if (typeof error === 'string') {
    return getErrorMessageFromString(error);
  }

  if (error instanceof Error) {
    return getErrorMessageFromString(error.message);
  }

  return 'An unexpected error occurred. Please try again.';
}

/**
 * Helper function to convert technical error messages to user-friendly ones
 * @param message Technical error message
 * @returns User-friendly error message
 */
function getErrorMessageFromString(message: string): string {
  const lowerMessage = message.toLowerCase();

  if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
    return 'Network error. Please check your internet connection and try again.';
  }

  if (lowerMessage.includes('timeout')) {
    return 'Request timeout. Please check your connection and try again.';
  }

  if (lowerMessage.includes('already registered') || lowerMessage.includes('already exists')) {
    return 'This email address is already registered. Please try logging in instead.';
  }

  if (lowerMessage.includes('invalid email')) {
    return 'Please enter a valid email address.';
  }

  if (lowerMessage.includes('weak password') || lowerMessage.includes('password')) {
    return 'Password is too weak. Please choose a stronger password with at least 6 characters.';
  }

  if (lowerMessage.includes('database') || lowerMessage.includes('saving user data')) {
    return 'Database error occurred while saving user data. Please try again later.';
  }

  if (lowerMessage.includes('unauthorized') || lowerMessage.includes('permission')) {
    return 'Authentication error. Please check your credentials and try again.';
  }

  if (lowerMessage.includes('not found')) {
    return 'Resource not found. Please check your configuration.';
  }

  // Return original message if no specific pattern matches
  return message;
}

/**
 * Logs validation results to console with appropriate styling
 * @param result ValidationResult to log
 */
export function logValidationResults(result: ValidationResult): void {
  if (result.isValid) {
    console.log('✅ Supabase configuration is valid');
  } else {
    console.error('❌ Supabase configuration validation failed');
  }

  if (result.errors.length > 0) {
    console.group('🚨 Errors:');
    result.errors.forEach(error => console.error(`  • ${error}`));
    console.groupEnd();
  }

  if (result.warnings.length > 0) {
    console.group('⚠️ Warnings:');
    result.warnings.forEach(warning => console.warn(`  • ${warning}`));
    console.groupEnd();
  }

  if (result.suggestions.length > 0) {
    console.group('💡 Suggestions:');
    result.suggestions.forEach(suggestion => console.info(`  • ${suggestion}`));
    console.groupEnd();
  }
}
