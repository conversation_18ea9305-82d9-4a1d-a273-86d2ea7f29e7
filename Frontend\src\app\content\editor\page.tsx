'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Header from '@/components/Header';
import AuthGuard from '@/components/AuthGuard';
import RichTextEditor from '@/components/RichTextEditor';
import { Button } from '@/components/ui/Button';

interface DraftData {
  id: number;
  title: string;
  description: string;
  content: string;
  category: string;
  tags: string;
  lastSaved: string;
}

function ContentEditorContent() {
  const { user, isAuthenticated } = useSimpleAuth();
  const searchParams = useSearchParams();
  const router = useRouter();
  const draftId = searchParams.get('id');

  const [formData, setFormData] = useState<DraftData>({
    id: 0,
    title: '',
    description: '',
    content: '',
    category: '',
    tags: '',
    lastSaved: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  const categories = [
    { value: 'technology', label: 'Technology' },
    { value: 'finance', label: 'Finance' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'education', label: 'Education' },
    { value: 'health', label: 'Health & Fitness' },
    { value: 'travel', label: 'Travel' },
    { value: 'food', label: 'Food' },
    { value: 'sports', label: 'Sports' },
    { value: 'business', label: 'Business' },
    { value: 'science', label: 'Science' },
    { value: 'politics', label: 'Politics' },
    { value: 'history', label: 'History' },
    { value: 'news', label: 'News' },
  ];

  useEffect(() => {
    if (draftId) {
      loadDraft(draftId);
    } else {
      setIsLoading(false);
    }
  }, [draftId]);

  // 自动保存功能
  useEffect(() => {
    if (!isLoading && formData.title.trim()) {
      const autoSaveTimer = setTimeout(() => {
        handleSaveDraft(true); // 静默保存
      }, 30000); // 30秒自动保存

      return () => clearTimeout(autoSaveTimer);
    }
  }, [formData, isLoading]);

  const loadDraft = async (id: string) => {
    try {
      setIsLoading(true);
      
      const mockDataMap: { [key: string]: DraftData } = {
        '1': {
          id: 1,
          title: 'The Future of AI in Everyday Life',
          description: 'Exploring how AI will transform our daily routines and interactions',
          content: '<h2>Introduction</h2><p>Artificial Intelligence is rapidly becoming an integral part of our daily lives...</p>',
          category: 'technology',
          tags: 'AI, artificial intelligence, technology, future',
          lastSaved: '2024-01-14T15:30:00Z'
        },
        '2': {
          id: 2,
          title: 'Cybersecurity Best Practices',
          description: 'A comprehensive guide to staying safe online in 2024',
          content: '<h2>Introduction</h2><p>In today\'s digital age, cybersecurity has become more important than ever...</p>',
          category: 'technology',
          tags: 'cybersecurity, security, privacy, technology',
          lastSaved: '2024-01-14T10:30:00Z'
        },
        '3': {
          id: 3,
          title: 'Travel Guide to Southeast Asia',
          description: 'Complete travel guide with tips and recommendations',
          content: '<h2>Planning Your Trip</h2><p>Southeast Asia offers incredible diversity...</p>',
          category: 'travel',
          tags: 'travel, asia, guide, tourism',
          lastSaved: '2024-01-12T08:00:00Z'
        },
        '4': {
          id: 4,
          title: 'Meditation Music Collection',
          description: 'Relaxing sounds for meditation and focus',
          content: '<h2>About This Collection</h2><p>This audio collection features calming sounds...</p>',
          category: 'lifestyle',
          tags: 'meditation, music, relaxation, wellness',
          lastSaved: '2024-01-10T12:00:00Z'
        }
      };

      const mockData = mockDataMap[id] || mockDataMap['2'];
      setFormData(mockData);
      setLastSaved(new Date(mockData.lastSaved));
    } catch (error) {
      console.error('Error loading content:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof DraftData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveDraft = async (silent = false) => {
    if (!formData.title.trim()) {
      if (!silent) {
        alert('Please enter a title before saving');
      }
      return;
    }

    try {
      setIsSaving(true);
      
      // 模拟保存草稿
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setLastSaved(new Date());
      
      if (!silent) {
        alert('Draft saved successfully!');
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      if (!silent) {
        alert('Failed to save draft. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublish = async () => {
    if (!formData.title.trim() || !formData.content.trim()) {
      alert('Please fill in title and content before publishing');
      return;
    }

    try {
      setIsPublishing(true);
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('Content published successfully!');
      router.push('/content');
    } catch (error) {
      console.error('Error publishing content:', error);
      alert('Failed to publish content. Please try again.');
    } finally {
      setIsPublishing(false);
    }
  };

  const handleDiscard = () => {
    if (confirm('Are you sure you want to discard all changes? This action cannot be undone.')) {
      router.push('/content');
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Content Editor</h2>
          <p className="text-gray-600 mb-6">Please log in to edit your content</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-6 py-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
              <div className="h-64 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* Editor Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Content Editor</h1>
              <p className="mt-2 text-gray-600">
                {draftId ? 'Continue editing your content' : 'Create new content'}
              </p>
              {lastSaved && (
                <p className="text-sm text-gray-500 mt-1">
                  Last saved: {lastSaved.toLocaleString()}
                </p>
              )}
            </div>
            <div className="flex items-center space-x-3">
              <Button
                onClick={() => handleSaveDraft()}
                disabled={isSaving}
                variant="ghost"
                className="text-gray-600"
              >
                {isSaving ? 'Saving...' : 'Save Draft'}
              </Button>
              <Button
                onClick={handleDiscard}
                variant="ghost"
                className="text-red-600 hover:text-red-700"
              >
                Discard
              </Button>
            </div>
          </div>
        </div>

        {/* Editor Form */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-8">
            {/* Title */}
            <div className="mb-6">
              <label htmlFor="title" className="block text-lg font-semibold text-gray-900 mb-2">
                Title <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                placeholder="Enter a compelling title for your content"
                required
              />
            </div>

            {/* Description */}
            <div className="mb-6">
              <label htmlFor="description" className="block text-lg font-semibold text-gray-900 mb-2">
                Description
              </label>
              <textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                placeholder="Briefly describe what your content is about"
              />
            </div>

            {/* Category */}
            <div className="mb-6">
              <label htmlFor="category" className="block text-lg font-semibold text-gray-900 mb-2">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                id="category"
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-pointer hover:border-gray-400"
                required
              >
                <option value="">Select a category</option>
                {categories.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Tags */}
            <div className="mb-6">
              <label htmlFor="tags" className="block text-lg font-semibold text-gray-900 mb-2">
                Tags
              </label>
              <input
                type="text"
                id="tags"
                value={formData.tags}
                onChange={(e) => handleInputChange('tags', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all bg-white cursor-text hover:border-gray-400"
                placeholder="Enter tags separated by commas (e.g., technology, AI, future)"
              />
              <p className="mt-1 text-sm text-gray-500">
                Tags help users discover your content. Separate multiple tags with commas.
              </p>
            </div>

            {/* Content Editor */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <label htmlFor="content" className="block text-lg font-semibold text-gray-900">
                  Content <span className="text-red-500">*</span>
                </label>
                <div className="text-sm text-gray-500">
                  Rich Text Editor
                </div>
              </div>
              <RichTextEditor
                value={formData.content}
                onChange={(content) => handleInputChange('content', content)}
                placeholder="Write your article content here..."
                className="border border-gray-300 rounded-xl"
              />
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-500">
                {isSaving && (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Auto-saving...
                  </span>
                )}
              </div>
              
              <div className="flex space-x-4">
                <Button
                  onClick={() => handleSaveDraft()}
                  disabled={isSaving}
                  variant="ghost"
                  className="px-6 py-3"
                >
                  {isSaving ? 'Saving...' : 'Save Draft'}
                </Button>
                <Button
                  onClick={handlePublish}
                  disabled={isPublishing || !formData.title.trim() || !formData.content.trim()}
                  className="px-8 py-3 bg-gray-200 text-gray-700 rounded-xl font-medium transition-all hover:bg-gray-300 cursor-pointer"
                >
                  {isPublishing ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Publishing...
                    </span>
                  ) : (
                    '🚀 Publish Content'
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ContentEditorPage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <ContentEditorContent />
    </AuthGuard>
  );
}