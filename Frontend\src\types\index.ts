// 基础作品接口
export interface BaseWork {
  id: number;
  title: string;
  description?: string;
  excerpt?: string;
  category:
    | 'Technology'
    | 'Travel'
    | 'Lifestyle'
    | 'Food'
    | 'Entertainment'
    | 'Finance'
    | 'History'
    | 'Trending'
    | string;
  author:
    | {
        id: number;
        name: string;
        username: string;
        avatar: string;
        bio?: string;
        isFollowing?: boolean;
      }
    | string;
  tags: string[];
  views: number;
  likes: number;
  comments?: number;
  shares?: number;
  bookmarks?: number;
  featured?: boolean;
  published: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 文章类型
export interface Article extends BaseWork {
  type: 'article';
  content: string;
  image?: string;
  readTime: number;
}

// Video type
export interface Video extends BaseWork {
  type: 'video';
  videoUrl: string;
  thumbnailUrl?: string;
  duration: number; // seconds
  resolution: {
    width: number;
    height: number;
    quality: '480p' | '720p' | '1080p' | '1440p' | '2160p' | '4320p' | '8K';
  };
  fileSize: number; // bytes
}

// Audio type
export interface Audio extends BaseWork {
  type: 'audio';
  audioUrl: string;
  coverUrl?: string;
  duration: number; // seconds
  fileSize: number; // bytes
}

// Unified work type
export type Work = Article | Video | Audio;

export interface User {
  _id: string;
  username: string;
  email: string;
  avatar: string;
  role: 'user' | 'admin';
  preferences: {
    categories: string[];
  };
  bookmarks: string[];
  createdAt: string;
  updatedAt: string;
}

export interface Category {
  name: string;
  count: number;
}

export interface Comment {
  id: number;
  articleId?: number;
  author:
    | {
        id: number;
        name: string;
        username: string;
        avatar: string;
        bio?: string;
      }
    | string;
  avatar?: string; // 保持向后兼容
  content: string;
  likes: number;
  parentId?: number;
  replies?: Comment[];
  createdAt: string;
  updatedAt: string;
}

export interface AdSlot {
  id: number;
  name: string;
  description: string;
  width: number;
  height: number;
  position: string; // 'header', 'sidebar', 'content_top', 'content_bottom', 'footer'
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AdCampaign {
  id: number;
  name: string;
  advertiser: string;
  startDate: string;
  endDate: string;
  budget: number;
  spent: number;
  status: 'draft' | 'active' | 'paused' | 'completed';
  targeting: {
    categories?: string[];
    tags?: string[];
    minAge?: number;
    maxAge?: number;
    genders?: string[];
    locations?: string[];
  };
  creatives: AdCreative[];
  createdAt: string;
  updatedAt: string;
}

export interface AdCreative {
  id: number;
  campaignId: number;
  title: string;
  description: string;
  imageUrl: string;
  destinationUrl: string;
  createdAt: string;
}

export interface AdImpression {
  id: number;
  adSlotId: number;
  campaignId: number;
  userId?: number;
  timestamp: string;
  userAgent: string;
  ipAddress: string;
}

// 广告投放算法相关类型
export interface UserAdProfile {
  userId: number;
  interests: Array<{ category: string; score: number }>;
  demographics: {
    age: number;
    gender: string;
    location: string;
  };
  behavior: {
    categoriesViewed: string[];
    tagsViewed: string[];
    avgSessionDuration: number;
    lastActive: string;
  };
}

// 收入分成相关类型
export interface RevenueShareConfig {
  id: number;
  name: string;
  description: string;
  platformPercentage: number; // 平台分成比例
  creatorPercentage: number;  // 创作者分成比例
  minPayoutAmount: number;    // 最低提现金额
  payoutFrequency: 'daily' | 'weekly' | 'monthly'; // 结算周期
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreatorRevenue {
  userId: number;
  username: string;
  totalRevenue: number;
  platformShare: number;
  creatorShare: number;
  pendingPayout: number;
  lastPayoutDate?: string;
  paymentMethod?: string;
}

export interface PayoutRecord {
  id: number;
  userId: number;
  amount: number;
  currency: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  paymentMethod: string;
  transactionId?: string;
  createdAt: string;
  processedAt?: string;
}

// 视频处理相关类型
export interface VideoUpload {
  id: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  status: 'uploading' | 'uploaded' | 'processing' | 'processed' | 'failed';
  progress: number;
  uploadUrl?: string;
  processedUrl?: string;
  thumbnails?: string[];
  duration?: number;
  resolutions?: VideoResolution[];
  subtitles?: VideoSubtitle[];
  createdAt: string;
  updatedAt: string;
}

export interface VideoResolution {
  width: number;
  height: number;
  quality: '144p' | '240p' | '360p' | '480p' | '720p' | '1080p' | '4k';
  url: string;
  fileSize: number;
}

export interface VideoSubtitle {
  id: string;
  language: string;
  label: string;
  url: string;
  isDefault: boolean;
}

export interface VideoStream {
  id: string;
  title: string;
  description: string;
  isLive: boolean;
  streamKey: string;
  streamUrl?: string;
  playbackUrl?: string;
  viewerCount: number;
  startTime?: string;
  endTime?: string;
  status: 'pending' | 'live' | 'ended' | 'offline';
  createdAt: string;
  updatedAt: string;
}

// AI服务相关类型
export interface ContentRecommendation {
  contentId: number;
  contentType: 'article' | 'video' | 'audio';
  title: string;
  description: string;
  category: string;
  tags: string[];
  score: number; // 推荐分数
  reason: string; // 推荐理由
}

export interface ContentTag {
  id: number;
  name: string;
  confidence: number; // 置信度 0-1
  type: 'topic' | 'entity' | 'sentiment' | 'category';
}

export interface ContentModerationResult {
  isApproved: boolean;
  violations: Array<{
    type: 'violence' | 'adult' | 'hate_speech' | 'spam' | 'copyright' | 'other';
    confidence: number;
    description: string;
    highlight?: string; // 违规内容高亮
  }>;
  overallScore: number; // 总体合规分数 0-1
  recommendedAction: 'approve' | 'review' | 'reject';
}

export interface AICreationSuggestion {
  id: string;
  type: 'title' | 'outline' | 'paragraph' | 'conclusion' | 'tags' | 'seo';
  content: string;
  confidence: number;
}

export interface TranslationResult {
  sourceLanguage: string;
  targetLanguage: string;
  translatedText: string;
  confidence: number;
  detectedLanguage?: string;
}

// 用户兴趣画像
export interface UserInterestProfile {
  userId: number;
  interests: Array<{
    category: string;
    score: number; // 兴趣强度 0-1
    lastEngaged: string; // 最后互动时间
  }>;
  preferredContentTypes: Array<{
    type: 'article' | 'video' | 'audio';
    preferenceScore: number; // 偏好分数 0-1
  }>;
  preferredLanguages: string[];
  readingTimePreferences: {
    minMinutes: number;
    maxMinutes: number;
  };
}
