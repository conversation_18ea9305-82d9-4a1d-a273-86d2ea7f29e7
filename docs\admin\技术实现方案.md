# Newzora 后台管理系统技术实现方案

## 🏗️ 系统架构设计

### 整体架构
```
┌─────────────────────────────────────────────────────────┐
│                    Newzora 应用                         │
├─────────────────────────────────────────────────────────┤
│  前端 (Next.js 14)                                     │
│  ├── 用户界面 (/*)                                     │
│  └── 管理后台 (/admin/*)                               │
├─────────────────────────────────────────────────────────┤
│  后端 API (Node.js + Express)                          │
│  ├── 用户API (/api/*)                                  │
│  └── 管理API (/api/admin/*)                            │
├─────────────────────────────────────────────────────────┤
│  数据库 (PostgreSQL)                                   │
│  ├── 现有表结构                                        │
│  └── 管理扩展表                                        │
└─────────────────────────────────────────────────────────┘
```

## 📁 目录结构设计

### 前端目录结构
```
Frontend/src/
├── app/
│   ├── admin/                    # 管理后台页面
│   │   ├── layout.tsx           # 管理后台布局
│   │   ├── page.tsx             # 重定向到dashboard
│   │   ├── dashboard/           # 仪表板
│   │   │   └── page.tsx
│   │   ├── users/               # 用户管理
│   │   │   ├── page.tsx
│   │   │   └── [id]/
│   │   │       └── page.tsx
│   │   ├── content/             # 内容管理
│   │   │   ├── page.tsx
│   │   │   ├── articles/
│   │   │   ├── comments/
│   │   │   └── reviews/
│   │   ├── analytics/           # 数据分析
│   │   │   └── page.tsx
│   │   └── settings/            # 系统设置
│   │       └── page.tsx
│   └── (existing pages...)
├── components/
│   ├── admin/                   # 管理后台组件
│   │   ├── layout/
│   │   │   ├── AdminHeader.tsx
│   │   │   ├── AdminSidebar.tsx
│   │   │   └── AdminLayout.tsx
│   │   ├── dashboard/
│   │   │   ├── StatsCard.tsx
│   │   │   ├── TrendChart.tsx
│   │   │   └── QuickActions.tsx
│   │   ├── users/
│   │   │   ├── UserTable.tsx
│   │   │   ├── UserModal.tsx
│   │   │   └── UserFilters.tsx
│   │   ├── content/
│   │   │   ├── ArticleTable.tsx
│   │   │   ├── ReviewQueue.tsx
│   │   │   └── BulkActions.tsx
│   │   └── common/
│   │       ├── DataTable.tsx
│   │       ├── SearchFilter.tsx
│   │       └── ConfirmDialog.tsx
│   └── (existing components...)
├── services/
│   ├── admin/                   # 管理API服务
│   │   ├── dashboardService.ts
│   │   ├── userService.ts
│   │   ├── contentService.ts
│   │   └── analyticsService.ts
│   └── (existing services...)
├── types/
│   ├── admin.ts                 # 管理相关类型定义
│   └── (existing types...)
└── utils/
    ├── admin/                   # 管理工具函数
    │   ├── permissions.ts
    │   ├── formatters.ts
    │   └── validators.ts
    └── (existing utils...)
```

### 后端目录结构
```
Backend/
├── routes/
│   ├── admin/                   # 管理API路由
│   │   ├── index.js
│   │   ├── dashboard.js
│   │   ├── users.js
│   │   ├── content.js
│   │   ├── analytics.js
│   │   └── settings.js
│   └── (existing routes...)
├── middleware/
│   ├── adminAuth.js             # 管理员认证中间件
│   ├── permissions.js           # 权限控制中间件
│   └── (existing middleware...)
├── models/
│   ├── AdminLog.js              # 管理操作日志
│   ├── ContentReview.js         # 内容审核记录
│   └── (existing models...)
├── services/
│   ├── admin/                   # 管理业务服务
│   │   ├── dashboardService.js
│   │   ├── userManagementService.js
│   │   ├── contentModerationService.js
│   │   └── analyticsService.js
│   └── (existing services...)
└── utils/
    ├── admin/                   # 管理工具函数
    │   ├── permissions.js
    │   ├── statistics.js
    │   └── exporters.js
    └── (existing utils...)
```

## 🔐 权限控制实现

### 角色权限系统
```typescript
// types/admin.ts
export enum AdminRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  MODERATOR = 'moderator'
}

export enum Permission {
  // 用户管理
  USER_VIEW = 'user:view',
  USER_EDIT = 'user:edit',
  USER_DELETE = 'user:delete',
  USER_ROLE_CHANGE = 'user:role_change',
  
  // 内容管理
  CONTENT_VIEW = 'content:view',
  CONTENT_EDIT = 'content:edit',
  CONTENT_DELETE = 'content:delete',
  CONTENT_PUBLISH = 'content:publish',
  CONTENT_REVIEW = 'content:review',
  
  // 数据分析
  ANALYTICS_VIEW = 'analytics:view',
  ANALYTICS_EXPORT = 'analytics:export',
  
  // 系统设置
  SYSTEM_SETTINGS = 'system:settings',
  SYSTEM_LOGS = 'system:logs'
}

export const ROLE_PERMISSIONS: Record<AdminRole, Permission[]> = {
  [AdminRole.SUPER_ADMIN]: Object.values(Permission),
  [AdminRole.ADMIN]: [
    Permission.USER_VIEW,
    Permission.USER_EDIT,
    Permission.CONTENT_VIEW,
    Permission.CONTENT_EDIT,
    Permission.CONTENT_DELETE,
    Permission.CONTENT_PUBLISH,
    Permission.ANALYTICS_VIEW,
    Permission.ANALYTICS_EXPORT
  ],
  [AdminRole.MODERATOR]: [
    Permission.USER_VIEW,
    Permission.CONTENT_VIEW,
    Permission.CONTENT_REVIEW,
    Permission.ANALYTICS_VIEW
  ]
};
```

### 权限检查中间件
```javascript
// middleware/adminAuth.js
const jwt = require('jsonwebtoken');
const { User } = require('../models');

const authenticateAdmin = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({ 
        success: false, 
        message: 'Access denied. No token provided.' 
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.id);

    if (!user || !['admin', 'super_admin', 'moderator'].includes(user.role)) {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied. Admin privileges required.' 
      });
    }

    req.user = user;
    next();
  } catch (error) {
    res.status(401).json({ 
      success: false, 
      message: 'Invalid token.' 
    });
  }
};

const requirePermission = (permission) => {
  return (req, res, next) => {
    const userRole = req.user.role;
    const userPermissions = ROLE_PERMISSIONS[userRole] || [];
    
    if (!userPermissions.includes(permission)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions.'
      });
    }
    
    next();
  };
};

module.exports = { authenticateAdmin, requirePermission };
```

## 📊 数据库扩展设计

### 管理相关表结构
```sql
-- 管理员操作日志表
CREATE TABLE admin_logs (
  id SERIAL PRIMARY KEY,
  admin_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  action VARCHAR(100) NOT NULL,
  target_type VARCHAR(50), -- 'user', 'article', 'comment', etc.
  target_id INTEGER,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容审核记录表
CREATE TABLE content_reviews (
  id SERIAL PRIMARY KEY,
  content_type VARCHAR(20) NOT NULL, -- 'article', 'comment'
  content_id INTEGER NOT NULL,
  reviewer_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
  status VARCHAR(20) NOT NULL, -- 'pending', 'approved', 'rejected'
  reason TEXT,
  auto_review BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_settings (
  id SERIAL PRIMARY KEY,
  key VARCHAR(100) UNIQUE NOT NULL,
  value JSONB NOT NULL,
  description TEXT,
  updated_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户统计缓存表
CREATE TABLE user_stats_cache (
  user_id INTEGER PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  article_count INTEGER DEFAULT 0,
  comment_count INTEGER DEFAULT 0,
  follower_count INTEGER DEFAULT 0,
  following_count INTEGER DEFAULT 0,
  total_views INTEGER DEFAULT 0,
  total_likes INTEGER DEFAULT 0,
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 内容统计缓存表
CREATE TABLE content_stats_cache (
  date DATE PRIMARY KEY,
  new_users INTEGER DEFAULT 0,
  new_articles INTEGER DEFAULT 0,
  new_comments INTEGER DEFAULT 0,
  total_views INTEGER DEFAULT 0,
  total_likes INTEGER DEFAULT 0,
  active_users INTEGER DEFAULT 0
);

-- 创建索引
CREATE INDEX idx_admin_logs_admin_id ON admin_logs(admin_id);
CREATE INDEX idx_admin_logs_created_at ON admin_logs(created_at);
CREATE INDEX idx_content_reviews_content ON content_reviews(content_type, content_id);
CREATE INDEX idx_content_reviews_status ON content_reviews(status);
CREATE INDEX idx_system_settings_key ON system_settings(key);
```

## 🎨 前端组件实现

### 管理后台布局组件
```typescript
// components/admin/layout/AdminLayout.tsx
'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminHeader from './AdminHeader';
import AdminSidebar from './AdminSidebar';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();

  // 检查管理员权限
  React.useEffect(() => {
    if (!isAuthenticated || !['admin', 'super_admin', 'moderator'].includes(user?.role)) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, user, router]);

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminHeader />
      <div className="flex">
        <AdminSidebar />
        <main className="flex-1 p-6 ml-64">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
```

### 数据表格组件
```typescript
// components/admin/common/DataTable.tsx
'use client';

import React, { useState } from 'react';

interface Column<T> {
  key: keyof T;
  title: string;
  render?: (value: any, record: T) => React.ReactNode;
  sortable?: boolean;
  width?: string;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number) => void;
  };
  rowSelection?: {
    selectedRowKeys: string[];
    onChange: (selectedRowKeys: string[]) => void;
  };
  onRow?: (record: T) => {
    onClick?: () => void;
  };
}

function DataTable<T extends { id: string | number }>({
  data,
  columns,
  loading = false,
  pagination,
  rowSelection,
  onRow
}: DataTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof T;
    direction: 'asc' | 'desc';
  } | null>(null);

  const handleSort = (key: keyof T) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  const sortedData = React.useMemo(() => {
    if (!sortConfig) return data;

    return [...data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [data, sortConfig]);

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow">
        <div className="animate-pulse">
          <div className="h-12 bg-gray-200 rounded-t-lg"></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-100 border-t"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {rowSelection && (
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300"
                    checked={rowSelection.selectedRowKeys.length === data.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        rowSelection.onChange(data.map(item => String(item.id)));
                      } else {
                        rowSelection.onChange([]);
                      }
                    }}
                  />
                </th>
              )}
              {columns.map((column) => (
                <th
                  key={String(column.key)}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                    column.sortable ? 'cursor-pointer hover:bg-gray-100' : ''
                  }`}
                  style={{ width: column.width }}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-1">
                    <span>{column.title}</span>
                    {column.sortable && (
                      <div className="flex flex-col">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
                        </svg>
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {sortedData.map((record) => (
              <tr
                key={String(record.id)}
                className={`hover:bg-gray-50 ${onRow ? 'cursor-pointer' : ''}`}
                onClick={() => onRow?.(record)?.onClick?.()}
              >
                {rowSelection && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300"
                      checked={rowSelection.selectedRowKeys.includes(String(record.id))}
                      onChange={(e) => {
                        const key = String(record.id);
                        if (e.target.checked) {
                          rowSelection.onChange([...rowSelection.selectedRowKeys, key]);
                        } else {
                          rowSelection.onChange(
                            rowSelection.selectedRowKeys.filter(k => k !== key)
                          );
                        }
                      }}
                    />
                  </td>
                )}
                {columns.map((column) => (
                  <td key={String(column.key)} className="px-6 py-4 whitespace-nowrap">
                    {column.render
                      ? column.render(record[column.key], record)
                      : String(record[column.key] || '')
                    }
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {pagination && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => pagination.onChange(pagination.current - 1)}
              disabled={pagination.current <= 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Previous
            </button>
            <button
              onClick={() => pagination.onChange(pagination.current + 1)}
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">
                  {(pagination.current - 1) * pagination.pageSize + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(pagination.current * pagination.pageSize, pagination.total)}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span> results
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                {/* 分页按钮实现 */}
              </nav>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DataTable;
```

## 🔧 API服务实现

### 仪表板数据服务
```javascript
// services/admin/dashboardService.js
const { User, Article, Comment, sequelize } = require('../../models');
const { Op } = require('sequelize');

class DashboardService {
  async getOverviewStats() {
    const today = new Date();
    const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    
    const [
      totalUsers,
      todayNewUsers,
      totalArticles,
      todayNewArticles,
      totalComments,
      todayNewComments,
      activeUsers
    ] = await Promise.all([
      User.count(),
      User.count({ where: { created_at: { [Op.gte]: startOfToday } } }),
      Article.count({ where: { published: true } }),
      Article.count({ 
        where: { 
          published: true,
          created_at: { [Op.gte]: startOfToday }
        }
      }),
      Comment.count(),
      Comment.count({ where: { created_at: { [Op.gte]: startOfToday } } }),
      User.count({ 
        where: { 
          last_login_at: { 
            [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
          }
        }
      })
    ]);

    return {
      users: {
        total: totalUsers,
        todayNew: todayNewUsers,
        active: activeUsers
      },
      content: {
        totalArticles,
        todayPublished: todayNewArticles,
        pendingReview: 0 // TODO: 实现审核队列
      },
      engagement: {
        totalComments,
        todayComments: todayNewComments,
        averageReadTime: 5 // TODO: 实现阅读时间统计
      }
    };
  }

  async getUserGrowthTrend(days = 30) {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

    const result = await User.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [Op.between]: [startDate, endDate]
        }
      },
      group: [sequelize.fn('DATE', sequelize.col('created_at'))],
      order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']]
    });

    return result.map(item => ({
      date: item.getDataValue('date'),
      count: parseInt(item.getDataValue('count'))
    }));
  }

  async getContentTrend(days = 30) {
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

    const result = await Article.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: {
        created_at: {
          [Op.between]: [startDate, endDate]
        },
        published: true
      },
      group: [sequelize.fn('DATE', sequelize.col('created_at'))],
      order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']]
    });

    return result.map(item => ({
      date: item.getDataValue('date'),
      count: parseInt(item.getDataValue('count'))
    }));
  }

  async getCategoryStats() {
    const result = await Article.findAll({
      attributes: [
        'category',
        [sequelize.fn('COUNT', sequelize.col('id')), 'count']
      ],
      where: { published: true },
      group: ['category'],
      order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']]
    });

    return result.map(item => ({
      category: item.category,
      count: parseInt(item.getDataValue('count'))
    }));
  }
}

module.exports = new DashboardService();
```

### 用户管理服务
```javascript
// services/admin/userManagementService.js
const { User, Article, Comment, Follow, sequelize } = require('../../models');
const { Op } = require('sequelize');

class UserManagementService {
  async getUserList(options = {}) {
    const {
      page = 1,
      limit = 20,
      search = '',
      role = '',
      status = '',
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = options;

    const offset = (page - 1) * limit;
    const whereConditions = {};

    // 搜索条件
    if (search) {
      whereConditions[Op.or] = [
        { username: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } },
        { display_name: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // 角色筛选
    if (role) {
      whereConditions.role = role;
    }

    // 状态筛选
    if (status === 'active') {
      whereConditions.isActive = true;
    } else if (status === 'inactive') {
      whereConditions.isActive = false;
    }

    const { count, rows: users } = await User.findAndCountAll({
      where: whereConditions,
      attributes: { 
        exclude: ['password_hash', 'passwordResetToken', 'emailVerificationToken'] 
      },
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [[sortBy, sortOrder.toUpperCase()]]
    });

    // 获取用户统计信息
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        const [articleCount, commentCount, followerCount, followingCount] = await Promise.all([
          Article.count({ where: { authorId: user.id } }),
          Comment.count({ where: { authorId: user.id } }),
          Follow.count({ where: { followingId: user.id } }),
          Follow.count({ where: { followerId: user.id } })
        ]);

        return {
          ...user.toJSON(),
          stats: {
            articles: articleCount,
            comments: commentCount,
            followers: followerCount,
            following: followingCount
          }
        };
      })
    );

    return {
      users: usersWithStats,
      pagination: {
        current: parseInt(page),
        total: count,
        pageSize: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    };
  }

  async getUserDetail(userId) {
    const user = await User.findByPk(userId, {
      attributes: { 
        exclude: ['password_hash', 'passwordResetToken', 'emailVerificationToken'] 
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // 获取用户详细统计
    const [
      articles,
      recentComments,
      followerCount,
      followingCount,
      totalViews,
      totalLikes
    ] = await Promise.all([
      Article.findAll({
        where: { authorId: userId },
        order: [['created_at', 'DESC']],
        limit: 10
      }),
      Comment.findAll({
        where: { authorId: userId },
        include: [{ model: Article, attributes: ['title'] }],
        order: [['created_at', 'DESC']],
        limit: 10
      }),
      Follow.count({ where: { followingId: userId } }),
      Follow.count({ where: { followerId: userId } }),
      Article.sum('views', { where: { authorId: userId } }) || 0,
      Article.sum('likes', { where: { authorId: userId } }) || 0
    ]);

    return {
      ...user.toJSON(),
      stats: {
        articles: articles.length,
        comments: recentComments.length,
        followers: followerCount,
        following: followingCount,
        totalViews,
        totalLikes
      },
      recentArticles: articles,
      recentComments
    };
  }

  async updateUserStatus(userId, isActive) {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    await user.update({ isActive });
    return user;
  }

  async updateUserRole(userId, role) {
    const validRoles = ['user', 'admin', 'moderator', 'super_admin'];
    if (!validRoles.includes(role)) {
      throw new Error('Invalid role');
    }

    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    await user.update({ role });
    return user;
  }

  async deleteUser(userId) {
    const user = await User.findByPk(userId);
    if (!user) {
      throw new Error('User not found');
    }

    // 软删除：标记为非活跃状态
    await user.update({ 
      isActive: false,
      email: `deleted_${Date.now()}_${user.email}`
    });

    return user;
  }

  async bulkUpdateUsers(userIds, action, value) {
    const validActions = ['activate', 'deactivate', 'delete', 'changeRole'];
    if (!validActions.includes(action)) {
      throw new Error('Invalid action');
    }

    const users = await User.findAll({
      where: { id: { [Op.in]: userIds } }
    });

    if (users.length !== userIds.length) {
      throw new Error('Some users not found');
    }

    let updateData = {};
    switch (action) {
      case 'activate':
        updateData = { isActive: true };
        break;
      case 'deactivate':
        updateData = { isActive: false };
        break;
      case 'delete':
        updateData = { 
          isActive: false,
          email: sequelize.fn('CONCAT', 'deleted_', sequelize.fn('EXTRACT', 'EPOCH', sequelize.fn('NOW')), '_', sequelize.col('email'))
        };
        break;
      case 'changeRole':
        if (!['user', 'admin', 'moderator'].includes(value)) {
          throw new Error('Invalid role');
        }
        updateData = { role: value };
        break;
    }

    await User.update(updateData, {
      where: { id: { [Op.in]: userIds } }
    });

    return { updated: users.length };
  }
}

module.exports = new UserManagementService();
```

## 📈 性能优化策略

### 数据缓存
```javascript
// utils/admin/cache.js
const Redis = require('redis');
const client = Redis.createClient(process.env.REDIS_URL);

class AdminCache {
  constructor() {
    this.defaultTTL = 300; // 5分钟
  }

  async get(key) {
    try {
      const data = await client.get(`admin:${key}`);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  async set(key, data, ttl = this.defaultTTL) {
    try {
      await client.setEx(`admin:${key}`, ttl, JSON.stringify(data));
    } catch (error) {
      console.error('Cache set error:', error);
    }
  }

  async del(key) {
    try {
      await client.del(`admin:${key}`);
    } catch (error) {
      console.error('Cache delete error:', error);
    }
  }

  async invalidatePattern(pattern) {
    try {
      const keys = await client.keys(`admin:${pattern}`);
      if (keys.length > 0) {
        await client.del(keys);
      }
    } catch (error) {
      console.error('Cache invalidate error:', error);
    }
  }
}

module.exports = new AdminCache();
```

### 数据库查询优化
```sql
-- 创建必要的索引
CREATE INDEX CONCURRENTLY idx_users_role_active ON users(role, isActive);
CREATE INDEX CONCURRENTLY idx_users_created_at ON users(created_at);
CREATE INDEX CONCURRENTLY idx_articles_published_created ON articles(published, created_at);
CREATE INDEX CONCURRENTLY idx_comments_created_at ON comments(created_at);
CREATE INDEX CONCURRENTLY idx_admin_logs_admin_created ON admin_logs(admin_id, created_at);

-- 创建物化视图用于统计
CREATE MATERIALIZED VIEW admin_daily_stats AS
SELECT 
  date_trunc('day', created_at) as date,
  'user' as type,
  COUNT(*) as count
FROM users 
GROUP BY date_trunc('day', created_at)
UNION ALL
SELECT 
  date_trunc('day', created_at) as date,
  'article' as type,
  COUNT(*) as count
FROM articles 
WHERE published = true
GROUP BY date_trunc('day', created_at)
UNION ALL
SELECT 
  date_trunc('day', created_at) as date,
  'comment' as type,
  COUNT(*) as count
FROM comments 
GROUP BY date_trunc('day', created_at);

-- 创建刷新物化视图的函数
CREATE OR REPLACE FUNCTION refresh_admin_stats()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW admin_daily_stats;
END;
$$ LANGUAGE plpgsql;

-- 设置定时任务每小时刷新一次
SELECT cron.schedule('refresh-admin-stats', '0 * * * *', 'SELECT refresh_admin_stats();');
```

## 🔒 安全措施

### 操作日志记录
```javascript
// middleware/adminLogger.js
const { AdminLog } = require('../models');

const logAdminAction = (action, targetType = null) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // 记录操作日志
      if (req.user && res.statusCode < 400) {
        AdminLog.create({
          admin_id: req.user.id,
          action,
          target_type: targetType,
          target_id: req.params.id || null,
          details: {
            method: req.method,
            url: req.originalUrl,
            body: req.body,
            query: req.query
          },
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        }).catch(error => {
          console.error('Failed to log admin action:', error);
        });
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

module.exports = { logAdminAction };
```

### 敏感操作确认
```typescript
// components/admin/common/ConfirmDialog.tsx
'use client';

import React, { useState } from 'react';

interface ConfirmDialogProps {
  isOpen: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  requirePassword?: boolean;
  dangerous?: boolean;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  onConfirm,
  onCancel,
  requirePassword = false,
  dangerous = false
}) => {
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);

  const handleConfirm = async () => {
    if (requirePassword && !password) {
      return;
    }

    setLoading(true);
    try {
      await onConfirm();
    } finally {
      setLoading(false);
      setPassword('');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className={`text-lg font-medium mb-4 ${dangerous ? 'text-red-600' : 'text-gray-900'}`}>
          {title}
        </h3>
        
        <p className="text-gray-600 mb-6">{message}</p>
        
        {requirePassword && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Enter your password to confirm:
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Password"
            />
          </div>
        )}
        
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            disabled={loading}
            className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 disabled:opacity-50"
          >
            {cancelText}
          </button>
          <button
            onClick={handleConfirm}
            disabled={loading || (requirePassword && !password)}
            className={`px-4 py-2 text-white rounded-md disabled:opacity-50 ${
              dangerous 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {loading ? 'Processing...' : confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmDialog;
```

## 🚀 部署配置

### 环境变量配置
```env
# 管理后台配置
ADMIN_SECRET_KEY=your-super-secret-admin-key
ADMIN_SESSION_TIMEOUT=3600
ADMIN_MAX_LOGIN_ATTEMPTS=5
ADMIN_LOCKOUT_DURATION=900

# Redis缓存配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# 邮件通知配置
ADMIN_EMAIL_FROM=<EMAIL>
ADMIN_EMAIL_NOTIFICATIONS=true

# 日志配置
ADMIN_LOG_LEVEL=info
ADMIN_LOG_RETENTION_DAYS=90
```

### Docker配置
```dockerfile
# Dockerfile.admin
FROM node:18-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 构建管理后台
RUN npm run build:admin

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

### Nginx配置
```nginx
# nginx/admin.conf
location /admin {
    # 管理后台访问限制
    allow ***********/24;  # 内网访问
    allow 10.0.0.0/8;      # VPN访问
    deny all;

    # 代理到Next.js应用
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
}
```

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**最后更新**: 2024年12月  
**负责人**: 技术团队