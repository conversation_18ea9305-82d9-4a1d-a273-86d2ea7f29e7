#!/bin/bash

# OneNews Docker 生产环境管理脚本
# 用于管理生产环境的Docker容器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="onenews"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}OneNews Docker 生产环境管理脚本${NC}"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  deploy    部署生产环境"
    echo "  start     启动生产环境"
    echo "  stop      停止生产环境"
    echo "  restart   重启生产环境"
    echo "  build     重新构建镜像"
    echo "  logs      查看日志"
    echo "  status    查看服务状态"
    echo "  backup    备份数据库"
    echo "  restore   恢复数据库"
    echo "  update    更新应用"
    echo "  scale     扩缩容服务"
    echo "  health    健康检查"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 deploy         # 部署生产环境"
    echo "  $0 logs backend   # 查看后端日志"
    echo "  $0 scale backend 3 # 扩展后端服务到3个实例"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误: Docker未安装${NC}"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}错误: Docker Compose未安装${NC}"
        exit 1
    fi
}

# 检查环境变量
check_env() {
    if [ ! -f ".env.production" ]; then
        echo -e "${RED}错误: .env.production文件不存在${NC}"
        echo -e "${YELLOW}请先创建生产环境配置文件${NC}"
        exit 1
    fi
    
    # 检查关键环境变量
    source .env.production
    
    local required_vars=(
        "JWT_SECRET"
        "SESSION_SECRET"
        "DB_PASSWORD"
        "EMAIL_USER"
        "EMAIL_PASS"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            echo -e "${RED}错误: 环境变量 $var 未设置${NC}"
            exit 1
        fi
    done
}

# 部署生产环境
deploy_prod() {
    echo -e "${GREEN}部署OneNews生产环境...${NC}"
    
    check_env
    
    # 构建镜像
    echo -e "${YELLOW}构建生产镜像...${NC}"
    docker-compose build --no-cache
    
    # 启动服务
    echo -e "${YELLOW}启动生产服务...${NC}"
    docker-compose up -d
    
    # 等待服务启动
    echo -e "${YELLOW}等待服务启动...${NC}"
    sleep 30
    
    # 运行数据库迁移
    echo -e "${YELLOW}运行数据库迁移...${NC}"
    docker-compose exec backend npm run migrate
    
    # 健康检查
    health_check
    
    echo -e "${GREEN}生产环境部署完成!${NC}"
    show_urls
}

# 启动生产环境
start_prod() {
    echo -e "${GREEN}启动OneNews生产环境...${NC}"
    docker-compose up -d
    
    echo -e "${GREEN}等待服务启动...${NC}"
    sleep 15
    
    health_check
    show_urls
}

# 停止生产环境
stop_prod() {
    echo -e "${YELLOW}停止OneNews生产环境...${NC}"
    docker-compose down
    echo -e "${GREEN}生产环境已停止${NC}"
}

# 重启生产环境
restart_prod() {
    echo -e "${YELLOW}重启OneNews生产环境...${NC}"
    docker-compose restart
    
    echo -e "${GREEN}等待服务重启...${NC}"
    sleep 15
    
    health_check
    echo -e "${GREEN}生产环境已重启${NC}"
}

# 重新构建镜像
build_prod() {
    echo -e "${YELLOW}重新构建生产环境镜像...${NC}"
    docker-compose build --no-cache
    echo -e "${GREEN}镜像构建完成${NC}"
}

# 查看日志
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        echo -e "${BLUE}显示所有服务日志:${NC}"
        docker-compose logs -f --tail=100
    else
        echo -e "${BLUE}显示 $service 服务日志:${NC}"
        docker-compose logs -f --tail=100 "$service"
    fi
}

# 查看服务状态
show_status() {
    echo -e "${BLUE}OneNews生产环境服务状态:${NC}"
    docker-compose ps
    
    echo -e "\n${BLUE}资源使用情况:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
}

# 备份数据库
backup_db() {
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    echo -e "${YELLOW}备份数据库到 $backup_file...${NC}"
    
    docker-compose exec postgres pg_dump -U postgres onenews > "$backup_file"
    
    echo -e "${GREEN}数据库备份完成: $backup_file${NC}"
}

# 恢复数据库
restore_db() {
    local backup_file=$1
    if [ -z "$backup_file" ]; then
        echo -e "${RED}错误: 请指定备份文件${NC}"
        echo "用法: $0 restore backup_file.sql"
        exit 1
    fi
    
    if [ ! -f "$backup_file" ]; then
        echo -e "${RED}错误: 备份文件 $backup_file 不存在${NC}"
        exit 1
    fi
    
    echo -e "${YELLOW}从 $backup_file 恢复数据库...${NC}"
    
    # 停止应用服务
    docker-compose stop backend frontend
    
    # 恢复数据库
    docker-compose exec -T postgres psql -U postgres onenews < "$backup_file"
    
    # 重启应用服务
    docker-compose start backend frontend
    
    echo -e "${GREEN}数据库恢复完成${NC}"
}

# 更新应用
update_app() {
    echo -e "${YELLOW}更新OneNews应用...${NC}"
    
    # 拉取最新代码
    git pull origin main
    
    # 重新构建镜像
    build_prod
    
    # 滚动更新
    docker-compose up -d --force-recreate
    
    # 运行数据库迁移
    docker-compose exec backend npm run migrate
    
    health_check
    
    echo -e "${GREEN}应用更新完成${NC}"
}

# 扩缩容服务
scale_service() {
    local service=$1
    local replicas=$2
    
    if [ -z "$service" ] || [ -z "$replicas" ]; then
        echo -e "${RED}错误: 请指定服务名称和副本数${NC}"
        echo "用法: $0 scale <service> <replicas>"
        exit 1
    fi
    
    echo -e "${YELLOW}扩展 $service 服务到 $replicas 个实例...${NC}"
    docker-compose up -d --scale "$service=$replicas"
    
    echo -e "${GREEN}服务扩展完成${NC}"
    show_status
}

# 健康检查
health_check() {
    echo -e "${BLUE}执行健康检查...${NC}"
    
    local services=("frontend" "backend" "postgres" "redis")
    local healthy=true
    
    for service in "${services[@]}"; do
        local status=$(docker-compose ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "no-health-check")
        
        if [ "$status" = "healthy" ] || [ "$status" = "no-health-check" ]; then
            echo -e "${GREEN}✓ $service: 健康${NC}"
        else
            echo -e "${RED}✗ $service: 不健康 ($status)${NC}"
            healthy=false
        fi
    done
    
    if [ "$healthy" = true ]; then
        echo -e "${GREEN}所有服务健康${NC}"
    else
        echo -e "${RED}部分服务不健康，请检查日志${NC}"
    fi
}

# 显示访问地址
show_urls() {
    echo -e "${BLUE}服务访问地址:${NC}"
    echo -e "${YELLOW}前端: http://localhost:3000${NC}"
    echo -e "${YELLOW}后端API: http://localhost:5000${NC}"
    echo -e "${YELLOW}Nginx: http://localhost${NC}"
}

# 主函数
main() {
    check_docker
    
    case "${1:-help}" in
        deploy)
            deploy_prod
            ;;
        start)
            start_prod
            ;;
        stop)
            stop_prod
            ;;
        restart)
            restart_prod
            ;;
        build)
            build_prod
            ;;
        logs)
            show_logs "$2"
            ;;
        status)
            show_status
            ;;
        backup)
            backup_db
            ;;
        restore)
            restore_db "$2"
            ;;
        update)
            update_app
            ;;
        scale)
            scale_service "$2" "$3"
            ;;
        health)
            health_check
            ;;
        help|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
