# OneNews OAuth 社交登录配置指南

## 🔐 OAuth 社交登录概述

OneNews支持以下社交登录方式：
- **Google OAuth 2.0** - 使用Google账户登录
- **Facebook Login** - 使用Facebook账户登录

## 📋 配置前准备

### 必需信息
- **回调URL**: 您的应用域名 + 回调路径
- **开发环境**: `http://localhost:3000/auth/google/callback`
- **生产环境**: `https://your-domain.com/auth/google/callback`

### 安全要求
- 使用HTTPS（生产环境必需）
- 配置正确的重定向URI
- 保护客户端密钥安全

## 🔵 Google OAuth 配置

### 步骤1：创建Google Cloud项目

1. **访问Google Cloud Console**
   - 前往 [Google Cloud Console](https://console.cloud.google.com/)
   - 登录您的Google账户

2. **创建新项目**
   - 点击项目选择器
   - 点击"新建项目"
   - 输入项目名称：`OneNews`
   - 选择组织（可选）
   - 点击"创建"

### 步骤2：启用Google+ API

1. **启用API**
   - 在左侧菜单中选择"API和服务" > "库"
   - 搜索"Google+ API"
   - 点击"Google+ API"
   - 点击"启用"

2. **启用People API（推荐）**
   - 搜索"People API"
   - 点击"People API"
   - 点击"启用"

### 步骤3：配置OAuth同意屏幕

1. **设置同意屏幕**
   - 前往"API和服务" > "OAuth同意屏幕"
   - 选择"外部"用户类型
   - 点击"创建"

2. **填写应用信息**
   ```
   应用名称: OneNews
   用户支持电子邮件: <EMAIL>
   应用徽标: (可选，上传OneNews logo)
   应用主页链接: https://your-domain.com
   应用隐私政策链接: https://your-domain.com/privacy
   应用服务条款链接: https://your-domain.com/terms
   ```

3. **配置作用域**
   - 点击"添加或移除作用域"
   - 添加以下作用域：
     - `../auth/userinfo.email`
     - `../auth/userinfo.profile`
     - `openid`

4. **添加测试用户**（开发阶段）
   - 在"测试用户"部分添加您的邮箱地址

### 步骤4：创建OAuth客户端

1. **创建凭据**
   - 前往"API和服务" > "凭据"
   - 点击"创建凭据" > "OAuth客户端ID"
   - 选择应用类型："Web应用"

2. **配置客户端**
   ```
   名称: OneNews Web Client
   已获授权的JavaScript来源:
   - http://localhost:3000 (开发环境)
   - https://your-domain.com (生产环境)
   
   已获授权的重定向URI:
   - http://localhost:3000/auth/google/callback (开发环境)
   - https://your-domain.com/auth/google/callback (生产环境)
   ```

3. **获取凭据**
   - 复制"客户端ID"
   - 复制"客户端密钥"

### 步骤5：环境变量配置

```bash
# Google OAuth配置
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 🔵 Facebook Login 配置

### 步骤1：创建Facebook应用

1. **访问Facebook开发者平台**
   - 前往 [Facebook for Developers](https://developers.facebook.com/)
   - 登录您的Facebook账户

2. **创建应用**
   - 点击"我的应用" > "创建应用"
   - 选择"消费者"类型
   - 填写应用信息：
     ```
     应用名称: OneNews
     应用联系邮箱: <EMAIL>
     ```

### 步骤2：配置Facebook登录

1. **添加Facebook登录产品**
   - 在应用面板中点击"添加产品"
   - 找到"Facebook登录"，点击"设置"

2. **配置Web平台**
   - 在左侧菜单选择"Facebook登录" > "设置"
   - 在"有效OAuth重定向URI"中添加：
     ```
     http://localhost:3000/auth/facebook/callback (开发环境)
     https://your-domain.com/auth/facebook/callback (生产环境)
     ```

### 步骤3：基本设置

1. **配置基本信息**
   - 前往"设置" > "基本"
   - 填写以下信息：
     ```
     应用域名: your-domain.com
     隐私政策网址: https://your-domain.com/privacy
     服务条款网址: https://your-domain.com/terms
     应用图标: (上传OneNews logo)
     ```

2. **获取应用凭据**
   - 复制"应用编号"（App ID）
   - 复制"应用密钥"（App Secret）

### 步骤4：环境变量配置

```bash
# Facebook OAuth配置
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
```

## 🧪 测试OAuth配置

### 开发环境测试

1. **启动应用**
   ```bash
   cd Backend
   npm start
   ```

2. **测试Google登录**
   - 访问 `http://localhost:3000/auth/google`
   - 应该重定向到Google登录页面
   - 登录后应该重定向回应用

3. **测试Facebook登录**
   - 访问 `http://localhost:3000/auth/facebook`
   - 应该重定向到Facebook登录页面
   - 登录后应该重定向回应用

### 生产环境部署

1. **更新重定向URI**
   - 在Google Cloud Console中更新重定向URI
   - 在Facebook开发者平台中更新重定向URI

2. **发布Facebook应用**
   - 在Facebook开发者平台中将应用状态改为"上线"
   - 完成应用审核（如需要）

## 🔒 安全最佳实践

### 客户端密钥保护
- 永远不要在前端代码中暴露客户端密钥
- 使用环境变量存储敏感信息
- 定期轮换客户端密钥

### 重定向URI验证
- 只配置必要的重定向URI
- 使用HTTPS（生产环境）
- 验证重定向URI的完整性

### 作用域最小化
- 只请求必要的用户信息
- 明确告知用户数据使用目的
- 遵守数据保护法规

## 🚨 常见问题解决

### Google OAuth问题

1. **"redirect_uri_mismatch"错误**
   - 检查重定向URI是否完全匹配
   - 确保包含协议（http/https）
   - 检查端口号是否正确

2. **"access_denied"错误**
   - 检查OAuth同意屏幕配置
   - 确保应用已发布或用户在测试列表中
   - 检查请求的作用域是否正确

### Facebook Login问题

1. **"URL Blocked"错误**
   - 检查应用域名配置
   - 确保重定向URI在白名单中
   - 检查应用是否处于开发模式

2. **"App Not Setup"错误**
   - 确保Facebook登录产品已正确配置
   - 检查应用ID和密钥是否正确
   - 确保应用状态为"上线"

## 📊 监控和分析

### 登录统计
- 跟踪各种登录方式的使用情况
- 监控登录成功率
- 分析用户偏好

### 错误监控
- 记录OAuth错误日志
- 监控API调用失败率
- 设置错误告警

## 🔄 维护和更新

### 定期检查
- 监控API配额使用情况
- 检查OAuth应用状态
- 更新过期的凭据

### 版本更新
- 关注OAuth提供商的API更新
- 测试新版本兼容性
- 更新相关文档

---

**配置完成后，OneNews将支持：**
- 一键Google账户登录
- 一键Facebook账户登录
- 自动用户信息同步
- 安全的身份验证流程
