'use client';

import React from 'react';
import { LoadingSpinner, LoadingSpinnerProps } from '@/components/ui/LoadingSpinner';

export interface LoadingStateProps {
  isLoading: boolean;
  loadingText?: string;
  size?: LoadingSpinnerProps['size'];
  className?: string;
}

export default function LoadingState({
  isLoading,
  loadingText = 'Loading...',
  size = 'md',
  className = '',
}: LoadingStateProps) {
  if (!isLoading) {
    return null;
  }

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <LoadingSpinner size={size} />
      {loadingText && (
        <p className="mt-3 text-gray-500 text-sm">
          {loadingText}
        </p>
      )}
    </div>
  );
}