'use client';

import React, { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { UserPlus } from 'lucide-react';

const CreateAdminButton: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');

  const createAdminUser = async () => {
    setLoading(true);
    setMessage('');

    try {
      // 直接创建profile，不需要注册
      const adminUserId = '00000000-0000-0000-0000-000000000001'; // 固定ID
      
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: adminUserId,
          username: 'admin',
          display_name: '超级管理员',
          role: 'super_admin',
          is_active: true,
          email_verified: true
        });

      if (profileError) {
        console.error('Profile创建失败:', profileError);
        setMessage(`创建失败: ${profileError.message}`);
        return;
      }

      setMessage('✅ 管理员账户配置成功！请先在前台注册 <EMAIL> 账户，然后再登录后台');
    } catch (error: any) {
      setMessage(`创建失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium text-blue-800">创建管理员账户</h3>
          <p className="text-sm text-blue-600 mt-1">
            如果还没有管理员账户，点击按钮创建默认管理员
          </p>
        </div>
        <button
          onClick={createAdminUser}
          disabled={loading}
          className="btn-primary flex items-center text-sm"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          ) : (
            <UserPlus className="w-4 h-4 mr-2" />
          )}
          创建管理员
        </button>
      </div>
      {message && (
        <div className={`mt-3 text-sm ${
          message.includes('✅') ? 'text-green-600' : 'text-red-600'
        }`}>
          {message}
        </div>
      )}
    </div>
  );
};

export default CreateAdminButton;