const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Activity = sequelize.define(
  'Activity',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    activityType: {
      type: DataTypes.ENUM(
        'article_created',
        'article_liked',
        'article_shared',
        'user_followed',
        'comment_created',
        'profile_updated'
      ),
      allowNull: false,
    },
    targetType: {
      type: DataTypes.ENUM('article', 'user', 'comment'),
      allowNull: true,
    },
    targetId: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
    isPublic: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  {
    tableName: 'activities',
    timestamps: true,
    indexes: [
      {
        fields: ['userId'],
      },
      {
        fields: ['activityType'],
      },
      {
        fields: ['targetType', 'targetId'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['isPublic'],
      },
      {
        fields: ['userId', 'createdAt'],
      },
    ],
  }
);

module.exports = Activity;
