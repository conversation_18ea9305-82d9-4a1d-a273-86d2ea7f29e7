@echo off
title Newzora Quick Start
color 0A

echo.
echo ==========================================
echo    🚀 Newzora 快速启动脚本
echo ==========================================
echo.

echo 🔍 检查环境...

:: 检查Node.js
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 已安装
    for /f "tokens=*" %%i in ('node --version') do echo    版本: %%i
) else (
    echo ❌ Node.js 未安装
    echo.
    echo 💡 请先安装 Node.js:
    echo    1. 访问 https://nodejs.org/
    echo    2. 下载并安装 LTS 版本
    echo    3. 重新运行此脚本
    echo.
    pause
    exit /b 1
)

:: 检查npm
npm --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npm 已安装
    for /f "tokens=*" %%i in ('npm --version') do echo    版本: %%i
) else (
    echo ❌ npm 未安装
    pause
    exit /b 1
)

echo.
echo 📦 安装依赖...

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo 安装根目录依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

if not exist "Backend\node_modules" (
    echo 安装后端依赖...
    cd Backend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

if not exist "Frontend\node_modules" (
    echo 安装前端依赖...
    cd Frontend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
)

echo ✅ 依赖安装完成

echo.
echo 🚀 启动服务...

:: 启动开发服务
if exist "package.json" (
    echo 使用统一启动脚本...
    npm run dev
) else (
    echo 手动启动服务...
    echo 启动后端...
    start /b cmd /c "cd Backend && npm start"
    
    echo 等待后端启动...
    timeout /t 5 /nobreak >nul
    
    echo 启动前端...
    start /b cmd /c "cd Frontend && npm run dev"
)

echo.
echo ==========================================
echo    🎉 Newzora 启动完成！
echo ==========================================
echo.
echo 🌐 访问地址:
echo    前端: http://localhost:3000
echo    后端: http://localhost:5000
echo.
echo 💡 提示:
echo    - 等待 2-3 分钟让服务完全启动
echo    - 如果页面无法访问，请稍等片刻
echo    - 按 Ctrl+C 可以停止服务
echo.

pause
