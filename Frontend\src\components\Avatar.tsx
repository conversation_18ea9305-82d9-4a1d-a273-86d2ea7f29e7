'use client';

import { useState } from 'react';

interface AvatarProps {
  src?: string;
  alt: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  fallbackText?: string;
}

const sizeClasses = {
  sm: 'w-8 h-8 text-xs',
  md: 'w-12 h-12 text-sm',
  lg: 'w-16 h-16 text-base',
  xl: 'w-24 h-24 text-lg'
};

export default function Avatar({ 
  src, 
  alt, 
  size = 'md', 
  className = '', 
  fallbackText 
}: AvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  // 生成fallback文本（通常是名字的首字母）
  const getFallbackText = () => {
    if (fallbackText) return fallbackText;
    
    const words = alt.split(' ');
    if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }
    return alt.slice(0, 2).toUpperCase();
  };

  const baseClasses = `${sizeClasses[size]} rounded-full flex items-center justify-center ${className}`;

  if (!src || imageError) {
    return (
      <div className={`${baseClasses} bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold`}>
        {getFallbackText()}
      </div>
    );
  }

  return (
    <div className={`${baseClasses} relative overflow-hidden`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse rounded-full"></div>
      )}
      <img
        src={src}
        alt={alt}
        className={`w-full h-full object-cover rounded-full ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}
        onError={handleImageError}
        onLoad={handleImageLoad}
      />
    </div>
  );
}
