// 统一的表单验证规则 - Supabase模式

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// 邮箱验证 - 增强版
export const validateEmail = (email: string): ValidationResult => {
  // 更严格的邮箱验证正则
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

  if (!email.trim()) {
    return { isValid: false, error: 'Email is required' };
  }

  if (email.length > 254) {
    return { isValid: false, error: 'Email address is too long' };
  }

  if (!emailRegex.test(email)) {
    return { isValid: false, error: 'Please enter a valid email address' };
  }

  return { isValid: true };
};

// 密码验证 - 增强安全性
export const validatePassword = (password: string): ValidationResult => {
  if (!password) {
    return { isValid: false, error: 'Password is required' };
  }

  if (password.length < 8) {
    return { isValid: false, error: 'Password must be at least 8 characters long' };
  }

  if (password.length > 128) {
    return { isValid: false, error: 'Password must be less than 128 characters' };
  }

  if (!/(?=.*[a-z])/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one lowercase letter' };
  }

  if (!/(?=.*[A-Z])/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one uppercase letter' };
  }

  if (!/(?=.*\d)/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one number' };
  }

  if (!/(?=.*[!@#$%^&*(),.?":{}|<>])/.test(password)) {
    return { isValid: false, error: 'Password must contain at least one special character' };
  }

  return { isValid: true };
};

// 用户名验证
export const validateUsername = (username: string): ValidationResult => {
  if (!username.trim()) {
    return { isValid: false, error: 'Username is required' };
  }
  
  if (username.length < 3) {
    return { isValid: false, error: 'Username must be at least 3 characters long' };
  }
  
  if (username.length > 20) {
    return { isValid: false, error: 'Username must be less than 20 characters' };
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return { isValid: false, error: 'Username can only contain letters, numbers, and underscores' };
  }
  
  return { isValid: true };
};

// 确认密码验证
export const validateConfirmPassword = (password: string, confirmPassword: string): ValidationResult => {
  if (!confirmPassword) {
    return { isValid: false, error: 'Please confirm your password' };
  }
  
  if (password !== confirmPassword) {
    return { isValid: false, error: 'Passwords do not match' };
  }
  
  return { isValid: true };
};

// 密码强度检查
export interface PasswordStrength {
  hasLength: boolean;
  hasLower: boolean;
  hasUpper: boolean;
  hasNumber: boolean;
  hasSpecial: boolean;
  score: number; // 0-5
}

export const checkPasswordStrength = (password: string): PasswordStrength => {
  const hasLength = password.length >= 8;
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [hasLength, hasLower, hasUpper, hasNumber, hasSpecial].filter(Boolean).length;
  
  return {
    hasLength,
    hasLower,
    hasUpper,
    hasNumber,
    hasSpecial,
    score
  };
};

// 获取密码强度文本
export const getPasswordStrengthText = (score: number): { text: string; color: string } => {
  switch (score) {
    case 0:
    case 1:
      return { text: 'Very Weak', color: 'text-red-600' };
    case 2:
      return { text: 'Weak', color: 'text-orange-600' };
    case 3:
      return { text: 'Fair', color: 'text-yellow-600' };
    case 4:
      return { text: 'Good', color: 'text-blue-600' };
    case 5:
      return { text: 'Strong', color: 'text-green-600' };
    default:
      return { text: 'Unknown', color: 'text-gray-600' };
  }
};

// 表单数据类型定义
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterFormData {
  email: string;
  password: string;
  confirmPassword: string;
  username: string;
  displayName?: string;
}

export interface ResetPasswordFormData {
  password: string;
  confirmPassword: string;
}

export interface ForgotPasswordFormData {
  email: string;
}

// 验证整个登录表单
export const validateLoginForm = (data: LoginFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  const emailResult = validateEmail(data.email);
  if (!emailResult.isValid) {
    errors.email = emailResult.error!;
  }
  
  if (!data.password) {
    errors.password = 'Password is required';
  }
  
  return errors;
};

// 验证整个注册表单
export const validateRegisterForm = (data: RegisterFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  const emailResult = validateEmail(data.email);
  if (!emailResult.isValid) {
    errors.email = emailResult.error!;
  }
  
  const usernameResult = validateUsername(data.username);
  if (!usernameResult.isValid) {
    errors.username = usernameResult.error!;
  }
  
  const passwordResult = validatePassword(data.password);
  if (!passwordResult.isValid) {
    errors.password = passwordResult.error!;
  }
  
  const confirmPasswordResult = validateConfirmPassword(data.password, data.confirmPassword);
  if (!confirmPasswordResult.isValid) {
    errors.confirmPassword = confirmPasswordResult.error!;
  }
  
  return errors;
};

// 验证重置密码表单
export const validateResetPasswordForm = (data: ResetPasswordFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  const passwordResult = validatePassword(data.password);
  if (!passwordResult.isValid) {
    errors.password = passwordResult.error!;
  }
  
  const confirmPasswordResult = validateConfirmPassword(data.password, data.confirmPassword);
  if (!confirmPasswordResult.isValid) {
    errors.confirmPassword = confirmPasswordResult.error!;
  }
  
  return errors;
};
