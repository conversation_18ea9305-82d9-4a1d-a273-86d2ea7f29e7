const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://wdpprzeflzlardkmncfk.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndkcHByemVmbHpsYXJka21uY2ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDk4MjgsImV4cCI6MjA2NzY4NTgyOH0.yp_k9Sv6AMFZmUs_EWa_-rPZGyTxNNFTZOM4RaU668s';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createAdminUser() {
  try {
    console.log('正在创建管理员账户...');
    
    // 注册用户
    const { data, error } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'admin123456'
    });

    if (error) {
      console.error('注册失败:', error.message);
      return;
    }

    console.log('✅ 管理员账户创建成功!');
    console.log('邮箱: <EMAIL>');
    console.log('密码: admin123456');
    console.log('请在Supabase控制台中将用户角色设置为super_admin');
    
  } catch (error) {
    console.error('创建管理员失败:', error);
  }
}

createAdminUser();