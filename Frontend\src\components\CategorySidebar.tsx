'use client';

import React from 'react';
import Link from 'next/link';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Logo from './Logo';

interface Category {
  id: string;
  name: string;
}

interface CategorySidebarProps {
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  className?: string;
}

const categories: Category[] = [
  { id: 'trending', name: 'Trending' },
  { id: 'recommended', name: 'For You' },
  { id: 'all', name: 'All Content' },
  { id: 'technology', name: 'Technology' },
  { id: 'finance', name: 'Finance' },
  { id: 'entertainment', name: 'Entertainment' },
  { id: 'lifestyle', name: 'Lifestyle' },
  { id: 'education', name: 'Education' },
  { id: 'health', name: 'Health & Fitness' },
  { id: 'travel', name: 'Travel' },
  { id: 'food', name: 'Food' },
  { id: 'sports', name: 'Sports' },
  { id: 'business', name: 'Business' },
  { id: 'science', name: 'Science' },
  { id: 'politics', name: 'Politics' },
  { id: 'history', name: 'History' },
  { id: 'news', name: 'News' },
];

const platformLinks = [
  { name: 'About Platform', href: '/about', icon: '🏢' },
  { name: 'Help Center', href: '/help', icon: '❓' },
  { name: 'Feedback', href: '/feedback', icon: '💬' },
  { name: 'Privacy Policy', href: '/privacy', icon: '🔒' },
  { name: 'Community Rules', href: '/rules', icon: '📋' },
  { name: 'Advertising', href: '/advertising', icon: '📢' },
];

const socialPlatforms = [
  { name: 'Facebook', href: 'https://facebook.com/newzora', color: 'bg-blue-600 hover:bg-blue-700' },
  { name: 'X (Twitter)', href: 'https://x.com/newzora', color: 'bg-black hover:bg-gray-800' },
  { name: 'Reddit', href: 'https://reddit.com/r/newzora', color: 'bg-orange-500 hover:bg-orange-600' },
];

export default function CategorySidebar({
  selectedCategory,
  onCategoryChange,
  className,
}: CategorySidebarProps) {
  const { isAuthenticated, user } = useSimpleAuth();

  const handleShare = (platform: string, url: string) => {
    const currentUrl = window.location.href;
    const text = 'Check out Newzora - Your Gateway to Quality Content!';
    
    let shareUrl = '';
    switch (platform) {
      case 'Facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(currentUrl)}`;
        break;
      case 'X (Twitter)':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(currentUrl)}`;
        break;
      case 'Reddit':
        shareUrl = `https://reddit.com/submit?url=${encodeURIComponent(currentUrl)}&title=${encodeURIComponent(text)}`;
        break;
      default:
        window.open(url, '_blank');
        return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
  };

  return (
    <aside className={`w-64 bg-white h-screen sticky top-0 overflow-y-auto border-r border-gray-200 ${className || ''}`}>
      <div className="flex flex-col h-full">
        {/* Categories Section */}
        <div className="flex-1 p-3">
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden mb-3">

            
            {/* Categories List */}
            <nav className="p-2 space-y-0.5">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => onCategoryChange(category.id)}
                  className={`w-full flex items-center px-2.5 py-2 text-left transition-all duration-200 rounded-lg text-sm group ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-700 font-medium border border-blue-200 shadow-sm'
                      : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-gray-900 hover:border hover:border-gray-200 hover:shadow-sm'
                  }`}
                >
                  <div className={`w-1.5 h-1.5 rounded-full mr-2.5 transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-blue-500'
                      : 'bg-gray-300 group-hover:bg-gray-400'
                  }`} />
                  <span className="flex-1 font-medium">{category.name}</span>
                  {selectedCategory === category.id && (
                    <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4" />
                    </svg>
                  )}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-200 bg-gradient-to-b from-gray-50 to-gray-100">
          {/* User Profile & Account Settings */}
          {isAuthenticated && (
            <div className="p-3">
              <div className="grid grid-cols-2 gap-1.5">
                <Link
                  href={`/profile/${user?.username}`}
                  className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-green-50 hover:text-green-700 rounded-lg transition-all duration-200 group"
                >
                  <div className="w-6 h-6 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center mb-1.5 group-hover:from-green-200 group-hover:to-green-300">
                    <svg className="w-3.5 h-3.5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span className="text-center font-medium text-xs">Profile</span>
                </Link>
                
                <Link
                  href="/settings"
                  className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-all duration-200 group"
                >
                  <div className="w-6 h-6 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mb-1.5 group-hover:from-blue-200 group-hover:to-blue-300">
                    <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <span className="text-center font-medium text-xs">Settings</span>
                </Link>
              </div>
            </div>
          )}

          {/* Platform Section */}
          <div className="px-3 pb-3">
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
              {/* Platform Header */}
              <div className="px-3 py-2.5 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                <h3 className="text-xs font-semibold text-gray-700 flex items-center uppercase tracking-wider">
                  <svg className="w-3.5 h-3.5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Platform
                </h3>
              </div>
              
              {/* Main Platform Links */}
              <div className="p-2">
                <div className="grid grid-cols-2 gap-1.5 mb-2">
                  <Link href="/about" className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-blue-50 hover:text-blue-700 rounded-lg transition-all duration-200 group">
                    <div className="w-6 h-6 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center mb-1.5 group-hover:from-blue-200 group-hover:to-blue-300">
                      <svg className="w-3 h-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="text-center font-medium text-xs">About</span>
                  </Link>
                  
                  <Link href="/help" className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-green-50 hover:text-green-700 rounded-lg transition-all duration-200 group">
                    <div className="w-6 h-6 bg-gradient-to-br from-green-100 to-green-200 rounded-lg flex items-center justify-center mb-1.5 group-hover:from-green-200 group-hover:to-green-300">
                      <svg className="w-3 h-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <span className="text-center font-medium text-xs">Help</span>
                  </Link>
                  
                  <Link href="/privacy" className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-purple-50 hover:text-purple-700 rounded-lg transition-all duration-200 group">
                    <div className="w-6 h-6 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg flex items-center justify-center mb-1.5 group-hover:from-purple-200 group-hover:to-purple-300">
                      <svg className="w-3 h-3 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                    </div>
                    <span className="text-center font-medium text-xs">Privacy</span>
                  </Link>
                  
                  <Link href="/feedback" className="flex flex-col items-center p-2 text-xs text-gray-600 hover:bg-orange-50 hover:text-orange-700 rounded-lg transition-all duration-200 group">
                    <div className="w-6 h-6 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg flex items-center justify-center mb-1.5 group-hover:from-orange-200 group-hover:to-orange-300">
                      <svg className="w-3 h-3 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                      </svg>
                    </div>
                    <span className="text-center font-medium text-xs">Feedback</span>
                  </Link>
                </div>
                
                {/* Additional Links */}
                <div className="border-t border-gray-100 pt-2 space-y-0.5">
                  <Link href="/rules" className="flex items-center px-2 py-1.5 text-xs text-gray-600 hover:bg-gray-50 hover:text-gray-800 rounded-lg transition-colors group">
                    <div className="w-5 h-5 bg-gray-100 rounded-md flex items-center justify-center mr-2 group-hover:bg-gray-200">
                      <svg className="w-2.5 h-2.5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <span className="font-medium">Community Rules</span>
                  </Link>
                  
                  <Link href="/advertising" className="flex items-center px-2 py-1.5 text-xs text-gray-600 hover:bg-gray-50 hover:text-gray-800 rounded-lg transition-colors group">
                    <div className="w-5 h-5 bg-gray-100 rounded-md flex items-center justify-center mr-2 group-hover:bg-gray-200">
                      <svg className="w-2.5 h-2.5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                      </svg>
                    </div>
                    <span className="font-medium">Advertising</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="px-3 pb-3">
            <div className="text-center">
              <div className="flex justify-center space-x-2 mb-3">
                {socialPlatforms.map((platform) => (
                  <button
                    key={platform.name}
                    onClick={() => handleShare(platform.name, platform.href)}
                    className={`w-8 h-8 flex items-center justify-center rounded-lg text-white transition-all duration-200 hover:scale-110 hover:shadow-lg ${platform.color}`}
                    title={`Share on ${platform.name}`}
                  >
                    {platform.name === 'Facebook' && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                      </svg>
                    )}
                    {platform.name === 'X (Twitter)' && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                      </svg>
                    )}

                    {platform.name === 'Reddit' && (
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"/>
                      </svg>
                    )}
                  </button>
                ))}
              </div>
              
              <p className="text-xs text-gray-400">© 2024 Newzora</p>
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}