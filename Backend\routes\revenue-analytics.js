const express = require('express');
const router = express.Router();
const { Op, sequelize } = require('sequelize');
const auth = require('../middleware/auth');
const { UserBalance, WithdrawalRequest, Article, User, ReadingStats } = require('../models');

// 获取收益概览
router.get('/overview', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '30d' } = req.query;
    
    const dateRange = getDateRange(period);
    
    const totalEarnings = await UserBalance.sum('balance', { where: { userId } }) || 0;
    const periodEarnings = await UserBalance.sum('balance', {
      where: { userId, updatedAt: { [Op.gte]: dateRange.start } }
    }) || 0;
    const totalWithdrawn = await WithdrawalRequest.sum('amount', {
      where: { userId, status: 'completed' }
    }) || 0;
    const pendingWithdrawal = await WithdrawalRequest.sum('amount', {
      where: { userId, status: 'pending' }
    }) || 0;
    
    res.json({
      success: true,
      data: {
        totalEarnings,
        periodEarnings,
        totalWithdrawn,
        pendingWithdrawal,
        availableBalance: totalEarnings - totalWithdrawn - pendingWithdrawal
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// 收益趋势分析
router.get('/trends', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '30d', granularity = 'day' } = req.query;
    
    const dateRange = getDateRange(period);
    const trends = await getRevenueTrends(userId, dateRange, granularity);
    
    res.json({ success: true, data: trends });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// 收益来源分析
router.get('/sources', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const { period = '30d' } = req.query;
    
    const dateRange = getDateRange(period);
    
    const readingRevenue = await ReadingStats.sum('revenue', {
      include: [{ model: Article, where: { authorId: userId } }],
      where: { createdAt: { [Op.gte]: dateRange.start } }
    }) || 0;
    
    const adRevenue = Math.random() * 1000; // 模拟数据
    const tipRevenue = Math.random() * 500; // 模拟数据
    
    res.json({
      success: true,
      data: {
        reading: readingRevenue,
        advertising: adRevenue,
        tips: tipRevenue,
        total: readingRevenue + adRevenue + tipRevenue
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

function getDateRange(period) {
  const end = new Date();
  const start = new Date();
  
  switch (period) {
    case '7d': start.setDate(end.getDate() - 7); break;
    case '30d': start.setDate(end.getDate() - 30); break;
    case '90d': start.setDate(end.getDate() - 90); break;
    case '1y': start.setFullYear(end.getFullYear() - 1); break;
    default: start.setDate(end.getDate() - 30);
  }
  
  return { start, end };
}

async function getRevenueTrends(userId, dateRange, granularity) {
  const trends = [];
  const current = new Date(dateRange.start);
  
  while (current <= dateRange.end) {
    const nextPeriod = new Date(current);
    if (granularity === 'day') nextPeriod.setDate(current.getDate() + 1);
    
    const periodRevenue = Math.random() * 100; // 模拟数据
    
    trends.push({
      date: current.toISOString().split('T')[0],
      revenue: periodRevenue
    });
    
    current.setTime(nextPeriod.getTime());
  }
  
  return trends;
}

module.exports = router;