const express = require('express');
const router = express.Router();
const { WithdrawalRequest, UserBalance } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

// 获取用户余额
router.get('/balance', authenticateToken, async (req, res) => {
  try {
    let balance = await UserBalance.findOne({ where: { userId: req.user.userId } });
    
    if (!balance) {
      balance = await UserBalance.create({
        userId: req.user.userId,
        availableBalance: 1250.00, // 演示数据
        totalEarnings: 1250.00,
      });
    }

    res.json(balance);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// 创建提现请求
router.post('/request', authenticateToken, async (req, res) => {
  try {
    const withdrawalRequest = await WithdrawalService.createWithdrawalRequest(
      req.user.userId,
      req.body
    );

    res.status(201).json({
      message: '提现请求已创建，请完成验证',
      request: withdrawalRequest,
      verificationToken: withdrawalRequest.verificationToken,
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 验证提现请求
router.post('/verify', async (req, res) => {
  try {
    const { token, verificationCode } = req.body;
    
    const verifiedRequest = await WithdrawalService.verifyWithdrawal(token, verificationCode);
    
    res.json({
      message: '提现请求已验证，将在下个周三处理',
      request: verifiedRequest,
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 获取提现历史
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const requests = await WithdrawalRequest.findAll({
      where: { userId: req.user.userId },
      order: [['createdAt', 'DESC']],
    });

    res.json(requests);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// 计算提现费用预览
router.post('/preview', authenticateToken, async (req, res) => {
  try {
    const { amount, country, withdrawalMethod } = req.body;
    
    if (amount < 100) {
      return res.status(400).json({ message: '最低提现金额为100美元' });
    }

    const calculations = WithdrawalService.calculateTaxAndFees(amount, country, withdrawalMethod);
    const scheduledDate = WithdrawalService.getNextWednesday();

    res.json({
      amount,
      ...calculations,
      scheduledPaymentDate: scheduledDate,
      estimatedArrival: '3-5个工作日',
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;