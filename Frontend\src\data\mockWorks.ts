import { Work, Article, Video, Audio } from '@/types';

// 模拟文章数据
const mockArticles: Article[] = [
  {
    id: 1,
    type: 'article',
    title: 'The Future of Artificial Intelligence in 2024',
    description:
      'Exploring the latest developments in AI technology and their impact on various industries.',
    content: '<p>Artificial Intelligence has become one of the most transformative technologies of our time, reshaping industries and redefining how we work, communicate, and live. As we move into 2024, the pace of AI development continues to accelerate, bringing both unprecedented opportunities and new challenges.</p><p>The integration of AI into everyday applications has reached a tipping point. From smart assistants that understand natural language to recommendation systems that predict our preferences, AI is becoming increasingly sophisticated and ubiquitous. Machine learning algorithms now power everything from medical diagnosis to financial trading, demonstrating remarkable capabilities in pattern recognition and decision-making.</p><p>However, with great power comes great responsibility. The ethical implications of AI development cannot be ignored. Issues such as algorithmic bias, privacy concerns, and the potential displacement of human workers require careful consideration and proactive solutions. As we embrace the benefits of AI, we must also ensure that its development and deployment align with human values and societal needs.</p>',
    category: 'technology',
    image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',
    author: {
      id: 1,
      name: '<PERSON>',
      username: 'ale<PERSON><PERSON>',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Tech journalist and AI researcher',
      isFollowing: false,
    },
    readTime: 8,
    tags: ['AI', 'Technology', 'Future'],
    views: 15420,
    likes: 892,
    comments: 156,
    shares: 234,
    bookmarks: 445,
    featured: true,
    published: true,
    publishedAt: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
  },
  {
    id: 2,
    type: 'article',
    title: 'Sustainable Travel: A Guide to Eco-Friendly Adventures',
    description: 'Discover how to explore the world while minimizing your environmental impact.',
    content: '<p>Sustainable travel has evolved from a niche concept to a mainstream movement, as more travelers recognize their responsibility to protect the destinations they visit. The tourism industry, which accounts for approximately 8% of global greenhouse gas emissions, is undergoing a significant transformation toward more environmentally conscious practices.</p><p>The principles of sustainable travel extend beyond simply reducing carbon footprints. It encompasses supporting local communities, preserving cultural heritage, and protecting natural ecosystems. Travelers are increasingly seeking authentic experiences that contribute positively to local economies while minimizing environmental impact.</p><p>Practical steps toward sustainable travel include choosing eco-friendly accommodations, supporting local businesses, using public transportation, and participating in conservation activities. Many destinations now offer carbon offset programs and promote responsible tourism initiatives that allow visitors to give back to the communities they explore.</p>',
    category: 'travel',
    image: 'https://images.unsplash.com/photo-*************-56623f02e42e?w=800&h=400&fit=crop',
    author: {
      id: 2,
      name: 'Sarah Johnson',
      username: 'sarahj',
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=100&h=100&fit=crop&crop=face',
      bio: 'Travel blogger and environmental advocate',
      isFollowing: false,
    },
    readTime: 6,
    tags: ['Travel', 'Sustainability', 'Environment'],
    views: 8750,
    likes: 567,
    comments: 89,
    shares: 123,
    bookmarks: 234,
    featured: false,
    published: true,
    publishedAt: '2024-01-14T14:20:00Z',
    createdAt: '2024-01-14T14:20:00Z',
    updatedAt: '2024-01-14T14:20:00Z',
  },
];

// 模拟视频数据
const mockVideos: Video[] = [
  {
    id: 3,
    type: 'video',
    title: 'Building a Modern Web Application with Next.js 14',
    description:
      'A comprehensive tutorial on creating scalable web applications using the latest Next.js features.',
    category: 'technology',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=800&h=400&fit=crop',
    duration: 1845, // 30 minutes 45 seconds
    resolution: {
      width: 1920,
      height: 1080,
      quality: '1080p',
    },
    fileSize: *********, // 500MB
    author: {
      id: 3,
      name: 'Mike Rodriguez',
      username: 'mikedev',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Full-stack developer and educator',
      isFollowing: false,
    },
    tags: ['Next.js', 'React', 'Web Development', 'Tutorial'],
    views: 23450,
    likes: 1234,
    comments: 287,
    shares: 156,
    bookmarks: 678,
    featured: true,
    published: true,
    publishedAt: '2024-01-13T09:15:00Z',
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z',
  },
  {
    id: 4,
    type: 'video',
    title: 'Cooking Authentic Italian Pasta from Scratch',
    description:
      'Learn the traditional techniques for making perfect pasta dough and classic Italian sauces.',
    category: 'food',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1551183053-bf91a1d81141?w=800&h=400&fit=crop',
    duration: 1260, // 21 minutes
    resolution: {
      width: 2560,
      height: 1440,
      quality: '1440p',
    },
    fileSize: 1073741824, // 1GB
    author: {
      id: 4,
      name: 'Isabella Romano',
      username: 'chefisabella',
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      bio: 'Professional chef and culinary instructor',
      isFollowing: false,
    },
    tags: ['Cooking', 'Italian', 'Pasta', 'Recipe'],
    views: 45670,
    likes: 2890,
    comments: 456,
    shares: 234,
    bookmarks: 1234,
    featured: false,
    published: true,
    publishedAt: '2024-01-12T16:45:00Z',
    createdAt: '2024-01-12T16:45:00Z',
    updatedAt: '2024-01-12T16:45:00Z',
  },
  {
    id: 5,
    type: 'video',
    title: 'Exploring the Hidden Gems of Tokyo',
    description:
      "A cinematic journey through Tokyo's lesser-known neighborhoods and local culture.",
    category: 'travel',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
    thumbnailUrl:
      'https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?w=800&h=400&fit=crop',
    duration: 2340, // 39 minutes
    resolution: {
      width: 3840,
      height: 2160,
      quality: '2160p',
    },
    fileSize: 2147483648, // 2GB
    author: {
      id: 5,
      name: 'Kenji Tanaka',
      username: 'kenjitravels',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Travel filmmaker and cultural explorer',
      isFollowing: false,
    },
    tags: ['Travel', 'Tokyo', 'Japan', 'Culture', 'Documentary'],
    views: 67890,
    likes: 4567,
    comments: 789,
    shares: 456,
    bookmarks: 2345,
    featured: true,
    published: true,
    publishedAt: '2024-01-11T12:00:00Z',
    createdAt: '2024-01-11T12:00:00Z',
    updatedAt: '2024-01-11T12:00:00Z',
  },
  {
    id: 17,
    type: 'video',
    title: 'AI and Machine Learning Fundamentals',
    description:
      'Understanding the basics of artificial intelligence and machine learning algorithms.',
    category: 'technology',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop',
    duration: 2100, // 35 minutes
    resolution: {
      width: 1920,
      height: 1080,
      quality: '1080p',
    },
    fileSize: *********,
    author: {
      id: 17,
      name: 'Dr. Sarah Chen',
      username: 'drsarahchen',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b9c5e8e1?w=100&h=100&fit=crop&crop=face',
      bio: 'AI researcher and data scientist',
      isFollowing: false,
    },
    tags: ['AI', 'Machine Learning', 'Technology', 'Education'],
    views: 89000,
    likes: 5200,
    comments: 890,
    shares: 450,
    bookmarks: 2100,
    featured: true,
    published: true,
    publishedAt: '2024-01-15T14:30:00Z',
    createdAt: '2024-01-15T14:30:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
  },
  {
    id: 18,
    type: 'video',
    title: 'Fitness Workout: Full Body HIIT Training',
    description:
      'High-intensity interval training for building strength and endurance at home.',
    category: 'health',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerFun.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=400&fit=crop',
    duration: 1800, // 30 minutes
    resolution: {
      width: 1920,
      height: 1080,
      quality: '1080p',
    },
    fileSize: 380000000,
    author: {
      id: 18,
      name: 'Coach Maria Santos',
      username: 'coachmaria',
      avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=100&h=100&fit=crop&crop=face',
      bio: 'Certified fitness trainer and wellness coach',
      isFollowing: false,
    },
    tags: ['Fitness', 'HIIT', 'Workout', 'Health'],
    views: 45600,
    likes: 3200,
    comments: 567,
    shares: 289,
    bookmarks: 1450,
    featured: false,
    published: true,
    publishedAt: '2024-01-14T08:00:00Z',
    createdAt: '2024-01-14T08:00:00Z',
    updatedAt: '2024-01-14T08:00:00Z',
  },
];

// 模拟音频数据
const mockAudios: Audio[] = [
  {
    id: 6,
    type: 'audio',
    title: 'The Psychology of Success: A Deep Dive',
    description:
      'An in-depth discussion about the mental frameworks that drive successful individuals.',
    category: 'lifestyle',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=400&fit=crop',
    duration: 3600, // 1 hour
    fileSize: 86400000, // 82MB
    author: {
      id: 6,
      name: 'Dr. Emma Wilson',
      username: 'dremmaw',
      avatar:
        'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=100&h=100&fit=crop&crop=face',
      bio: 'Psychologist and bestselling author',
      isFollowing: false,
    },
    tags: ['Psychology', 'Success', 'Mindset', 'Podcast'],
    views: 12340,
    likes: 789,
    comments: 123,
    shares: 67,
    bookmarks: 456,
    featured: false,
    published: true,
    publishedAt: '2024-01-10T08:30:00Z',
    createdAt: '2024-01-10T08:30:00Z',
    updatedAt: '2024-01-10T08:30:00Z',
  },
  {
    id: 7,
    type: 'audio',
    title: 'Financial Planning for Young Professionals',
    description: 'Essential financial advice for millennials and Gen Z entering the workforce.',
    category: 'finance',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=400&fit=crop',
    duration: 2700, // 45 minutes
    fileSize: 64800000, // 62MB
    author: {
      id: 7,
      name: 'Robert Kim',
      username: 'robertfinance',
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Financial advisor and investment strategist',
      isFollowing: false,
    },
    tags: ['Finance', 'Investment', 'Career', 'Money'],
    views: 8900,
    likes: 567,
    comments: 89,
    shares: 45,
    bookmarks: 234,
    featured: false,
    published: true,
    publishedAt: '2024-01-09T15:20:00Z',
    createdAt: '2024-01-09T15:20:00Z',
    updatedAt: '2024-01-09T15:20:00Z',
  },
  {
    id: 8,
    type: 'audio',
    title: 'The History of Ancient Rome: Rise and Fall',
    description:
      'A fascinating exploration of Roman civilization from its founding to its collapse.',
    category: 'history',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?w=800&h=400&fit=crop',
    duration: 4500, // 1 hour 15 minutes
    fileSize: 108000000, // 103MB
    author: {
      id: 8,
      name: 'Prof. Marcus Thompson',
      username: 'profmarcus',
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'History professor and author',
      isFollowing: false,
    },
    tags: ['History', 'Rome', 'Ancient', 'Education'],
    views: 15670,
    likes: 1234,
    comments: 234,
    shares: 123,
    bookmarks: 567,
    featured: true,
    published: true,
    publishedAt: '2024-01-08T11:45:00Z',
    createdAt: '2024-01-08T11:45:00Z',
    updatedAt: '2024-01-08T11:45:00Z',
  },
  {
    id: 19,
    type: 'audio',
    title: 'Meditation and Mindfulness Practice',
    description:
      'Guided meditation sessions for stress relief and mental clarity.',
    category: 'health',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop',
    duration: 1800, // 30 minutes
    fileSize: 43200000,
    author: {
      id: 19,
      name: 'Zen Master Liu',
      username: 'zenmasterliu',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Meditation teacher and mindfulness coach',
      isFollowing: false,
    },
    tags: ['Meditation', 'Mindfulness', 'Wellness', 'Health'],
    views: 34500,
    likes: 2100,
    comments: 345,
    shares: 178,
    bookmarks: 890,
    featured: true,
    published: true,
    publishedAt: '2024-01-16T06:00:00Z',
    createdAt: '2024-01-16T06:00:00Z',
    updatedAt: '2024-01-16T06:00:00Z',
  },
  {
    id: 20,
    type: 'audio',
    title: 'Jazz Music Theory and Improvisation',
    description:
      'Learn the fundamentals of jazz theory and how to improvise like a pro.',
    category: 'music',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&h=400&fit=crop',
    duration: 2400, // 40 minutes
    fileSize: 57600000,
    author: {
      id: 20,
      name: 'Miles Davis Jr.',
      username: 'milesdavisjr',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Jazz musician and music educator',
      isFollowing: false,
    },
    tags: ['Jazz', 'Music Theory', 'Improvisation', 'Education'],
    views: 18900,
    likes: 1450,
    comments: 234,
    shares: 123,
    bookmarks: 567,
    featured: false,
    published: true,
    publishedAt: '2024-01-15T20:00:00Z',
    createdAt: '2024-01-15T20:00:00Z',
    updatedAt: '2024-01-15T20:00:00Z',
  },
];

// 原始作品数据（保持向后兼容）
const originalMockWorks: Work[] = [...mockArticles, ...mockVideos, ...mockAudios].sort(
  (a, b) =>
    new Date(b.publishedAt || b.createdAt).getTime() -
    new Date(a.publishedAt || a.createdAt).getTime()
);

// 按类型分组的数据
export const mockWorksByType = {
  articles: mockArticles,
  videos: mockVideos,
  audios: mockAudios,
};

// 特色作品
export const featuredWorks = originalMockWorks.filter((work) => work.featured);

// 热门作品（按观看量排序）
export const trendingWorks = [...originalMockWorks].sort((a, b) => b.views - a.views);

// 为缺少内容的分类添加更多作品
const additionalWorks: Work[] = [
  // Entertainment
  {
    id: 9,
    type: 'video',
    title: 'Behind the Scenes: Marvel Movie Magic',
    description: 'Exclusive look at how Marvel creates their stunning visual effects.',
    category: 'entertainment',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1489599735734-79b4169c2a78?w=800&h=400&fit=crop',
    duration: 1680,
    resolution: { width: 1920, height: 1080, quality: '1080p' },
    fileSize: 125000000,
    tags: ['Entertainment', 'Movies', 'VFX'],
    author: {
      id: 9,
      name: 'Movie Insider',
      username: 'movieinsider',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Entertainment industry expert',
      isFollowing: false,
    },
    views: 45000,
    likes: 2100,
    comments: 340,
    shares: 180,
    bookmarks: 890,
    featured: false,
    published: true,
    publishedAt: '2024-01-10T14:20:00Z',
    createdAt: '2024-01-10T14:20:00Z',
    updatedAt: '2024-01-10T14:20:00Z',
  },
  // Politics
  {
    id: 10,
    type: 'article',
    title: 'Understanding Modern Democracy: Challenges and Opportunities',
    description: 'An analysis of democratic institutions in the 21st century.',
    content: '<p>Modern democracy faces unprecedented challenges in the 21st century, from technological disruption to political polarization. Understanding these challenges and identifying opportunities for strengthening democratic institutions has become crucial for maintaining stable and representative governance.</p><p>The digital age has transformed how citizens engage with politics and how information spreads through society. Social media platforms have democratized political discourse but have also created echo chambers and enabled the rapid spread of misinformation.</p>',
    category: 'politics',
    image: 'https://images.unsplash.com/photo-1529107386315-e1a2ed48a620?w=800&h=400&fit=crop',
    author: {
      id: 10,
      name: 'Dr. Patricia Williams',
      username: 'drpwilliams',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      bio: 'Political science professor',
      isFollowing: false,
    },
    readTime: 12,
    tags: ['Politics', 'Democracy', 'Government'],
    views: 8900,
    likes: 456,
    comments: 89,
    shares: 123,
    bookmarks: 234,
    featured: false,
    published: true,
    publishedAt: '2024-01-08T09:15:00Z',
    createdAt: '2024-01-08T09:15:00Z',
    updatedAt: '2024-01-08T09:15:00Z',
  },
  // Science
  {
    id: 11,
    type: 'article',
    title: 'Quantum Computing: The Next Frontier',
    description: 'Exploring the potential of quantum computers to revolutionize technology.',
    content: '<p>Quantum computing represents a paradigm shift in computational capability, promising to solve problems that are intractable for classical computers. As we stand on the brink of the quantum era, understanding the potential and limitations of this technology becomes increasingly important.</p><p>Unlike classical computers that use bits to represent information as either 0 or 1, quantum computers use quantum bits or qubits that can exist in multiple states simultaneously.</p>',
    category: 'science',
    image: 'https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=800&h=400&fit=crop',
    author: {
      id: 11,
      name: 'Dr. Michael Chen',
      username: 'drmchen',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Quantum physicist and researcher',
      isFollowing: false,
    },
    readTime: 15,
    tags: ['Science', 'Quantum', 'Computing'],
    views: 12300,
    likes: 678,
    comments: 145,
    shares: 234,
    bookmarks: 567,
    featured: true,
    published: true,
    publishedAt: '2024-01-12T16:45:00Z',
    createdAt: '2024-01-12T16:45:00Z',
    updatedAt: '2024-01-12T16:45:00Z',
  },
  // Health
  {
    id: 12,
    type: 'audio',
    title: 'Mental Health in the Digital Age',
    description: 'Understanding how technology affects our mental wellbeing.',
    category: 'health',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=400&fit=crop',
    duration: 2400,
    fileSize: 57600000,
    tags: ['Health', 'Mental Health', 'Wellness'],
    author: {
      id: 12,
      name: 'Dr. Lisa Rodriguez',
      username: 'drlrodriguez',
      avatar: 'https://images.unsplash.com/photo-**********-2b71ea197ec2?w=100&h=100&fit=crop&crop=face',
      bio: 'Clinical psychologist',
      isFollowing: false,
    },
    views: 18500,
    likes: 890,
    comments: 167,
    shares: 145,
    bookmarks: 456,
    featured: false,
    published: true,
    publishedAt: '2024-01-09T11:30:00Z',
    createdAt: '2024-01-09T11:30:00Z',
    updatedAt: '2024-01-09T11:30:00Z',
  },
  // Sports
  {
    id: 13,
    type: 'video',
    title: 'The Science of Athletic Performance',
    description: 'How modern sports science is revolutionizing athletic training.',
    category: 'sports',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=400&fit=crop',
    duration: 1920,
    resolution: { width: 1920, height: 1080, quality: '1080p' },
    fileSize: 140000000,
    tags: ['Sports', 'Science', 'Training'],
    author: {
      id: 13,
      name: 'Coach Martinez',
      username: 'coachmartinez',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Sports performance specialist',
      isFollowing: false,
    },
    views: 23400,
    likes: 1200,
    comments: 189,
    shares: 156,
    bookmarks: 678,
    featured: false,
    published: true,
    publishedAt: '2024-01-11T13:45:00Z',
    createdAt: '2024-01-11T13:45:00Z',
    updatedAt: '2024-01-11T13:45:00Z',
  },
  // Business
  {
    id: 14,
    type: 'article',
    title: 'Startup Success: Lessons from Silicon Valley',
    description: 'Key insights from successful entrepreneurs and their journey to success.',
    content: '<p>The startup ecosystem in Silicon Valley has long been regarded as the gold standard for entrepreneurial success, producing some of the world\'s most valuable companies and innovative technologies. Understanding the key factors that contribute to startup success in this unique environment provides valuable insights for entrepreneurs worldwide.</p><p>Access to capital remains one of Silicon Valley\'s greatest advantages, with venture capital firms providing funding at various stages of company development.</p>',
    category: 'business',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop',
    author: {
      id: 14,
      name: 'Jennifer Kim',
      username: 'jenniferkim',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      bio: 'Business strategist and entrepreneur',
      isFollowing: false,
    },
    readTime: 10,
    tags: ['Business', 'Startup', 'Entrepreneurship'],
    views: 16700,
    likes: 834,
    comments: 123,
    shares: 267,
    bookmarks: 445,
    featured: false,
    published: true,
    publishedAt: '2024-01-07T08:30:00Z',
    createdAt: '2024-01-07T08:30:00Z',
    updatedAt: '2024-01-07T08:30:00Z',
  },
  // More Technology content
  {
    id: 15,
    type: 'audio',
    title: 'The Future of Web Development',
    description: 'Discussing emerging trends and technologies in web development.',
    category: 'technology',
    audioUrl: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    coverUrl: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=400&fit=crop',
    duration: 3300,
    fileSize: 79200000,
    tags: ['Technology', 'Web Development', 'Programming'],
    author: {
      id: 15,
      name: 'David Park',
      username: 'davidpark',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Senior software engineer',
      isFollowing: false,
    },
    views: 21000,
    likes: 1100,
    comments: 234,
    shares: 189,
    bookmarks: 567,
    featured: false,
    published: true,
    publishedAt: '2024-01-13T15:20:00Z',
    createdAt: '2024-01-13T15:20:00Z',
    updatedAt: '2024-01-13T15:20:00Z',
  },
  // More Lifestyle content
  {
    id: 16,
    type: 'video',
    title: 'Minimalist Living: A Complete Guide',
    description: 'How to embrace minimalism and simplify your life.',
    category: 'lifestyle',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=400&fit=crop',
    duration: 1560,
    resolution: { width: 1920, height: 1080, quality: '1080p' },
    fileSize: 115000000,
    tags: ['Lifestyle', 'Minimalism', 'Wellness'],
    author: {
      id: 16,
      name: 'Emma Thompson',
      username: 'emmathompson',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
      bio: 'Lifestyle blogger and minimalist',
      isFollowing: false,
    },
    views: 34500,
    likes: 1890,
    comments: 278,
    shares: 234,
    bookmarks: 789,
    featured: true,
    published: true,
    publishedAt: '2024-01-14T12:00:00Z',
    createdAt: '2024-01-14T12:00:00Z',
    updatedAt: '2024-01-14T12:00:00Z',
  },
  // Stock Market content
  {
    id: 21,
    type: 'article',
    title: 'Stock Market Analysis: Top Picks for 2024',
    description: 'Expert analysis of the most promising stocks for the upcoming year.',
    content: '<p>The stock market in 2024 presents both opportunities and challenges for investors navigating an increasingly complex global economic landscape. Understanding market trends, identifying promising sectors, and maintaining a disciplined investment approach are essential for long-term success.</p><p>Technology stocks continue to show strong potential, particularly in areas such as artificial intelligence, cloud computing, and cybersecurity.</p>',
    category: 'finance',
    image: 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=800&h=400&fit=crop',
    author: {
      id: 21,
      name: 'Warren Mitchell',
      username: 'warrenmitchell',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      bio: 'Financial analyst and investment advisor',
      isFollowing: false,
    },
    readTime: 12,
    tags: ['Stock Market', 'Investment', 'Finance', 'Analysis'],
    views: 28900,
    likes: 1567,
    comments: 234,
    shares: 189,
    bookmarks: 678,
    featured: false,
    published: true,
    publishedAt: '2024-01-16T09:30:00Z',
    createdAt: '2024-01-16T09:30:00Z',
    updatedAt: '2024-01-16T09:30:00Z',
  },
  // Gaming content
  {
    id: 22,
    type: 'video',
    title: 'Gaming Setup Guide: Build the Ultimate Gaming Rig',
    description: 'Complete guide to building a high-performance gaming computer.',
    category: 'technology',
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4',
    thumbnailUrl: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=800&h=400&fit=crop',
    duration: 2400,
    resolution: { width: 1920, height: 1080, quality: '1080p' },
    fileSize: 180000000,
    tags: ['Gaming', 'PC Building', 'Technology', 'Hardware'],
    author: {
      id: 22,
      name: 'Tech Guru Mike',
      username: 'techgurumike',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      bio: 'Gaming enthusiast and tech reviewer',
      isFollowing: false,
    },
    views: 67800,
    likes: 4200,
    comments: 567,
    shares: 345,
    bookmarks: 1890,
    featured: true,
    published: true,
    publishedAt: '2024-01-15T16:00:00Z',
    createdAt: '2024-01-15T16:00:00Z',
    updatedAt: '2024-01-15T16:00:00Z',
  },
];

// 合并所有作品
const allMockWorks = [...originalMockWorks, ...additionalWorks];

// 最新作品
export const latestWorks = [...allMockWorks].sort(
  (a, b) =>
    new Date(b.publishedAt || b.createdAt).getTime() -
    new Date(a.publishedAt || a.createdAt).getTime()
);

// 按分类分组的作品
export const mockWorksByCategory = {
  all: allMockWorks,
  trending: allMockWorks.filter((work) => work.featured || work.views > 50000),
  recommended: allMockWorks.slice(0, 10),
  technology: allMockWorks.filter((work) => work.category === 'technology' || work.tags?.includes('Gaming') || work.tags?.includes('Games')),
  finance: allMockWorks.filter((work) => work.category === 'finance' || work.tags?.includes('Stock Market') || work.tags?.includes('Investment') || work.tags?.includes('Cryptocurrency') || work.tags?.includes('Bitcoin')),
  entertainment: allMockWorks.filter((work) => work.category === 'entertainment' || work.category === 'music' || work.tags?.includes('Movies') || work.tags?.includes('Gaming')),
  lifestyle: allMockWorks.filter((work) => work.category === 'lifestyle' || work.tags?.includes('Fashion') || work.tags?.includes('Beauty') || work.tags?.includes('DIY') || work.tags?.includes('Crafts') || work.tags?.includes('Pets') || work.tags?.includes('Animals')),
  education: allMockWorks.filter((work) => work.category === 'education' || work.tags?.includes('Books') || work.tags?.includes('Literature') || work.tags?.includes('Novel') || work.tags?.includes('Fiction')),
  health: allMockWorks.filter((work) => work.category === 'health' || work.tags?.includes('Fitness') || work.tags?.includes('Workout') || work.tags?.includes('Wellness') || work.tags?.includes('Mindfulness')),
  travel: allMockWorks.filter((work) => work.category === 'travel'),
  food: allMockWorks.filter((work) => work.category === 'food'),
  sports: allMockWorks.filter((work) => work.category === 'sports'),
  business: allMockWorks.filter((work) => work.category === 'business' || work.tags?.includes('Real Estate') || work.tags?.includes('Property') || work.tags?.includes('Cars') || work.tags?.includes('Automotive')),
  science: allMockWorks.filter((work) => work.category === 'science'),
  politics: allMockWorks.filter((work) => work.category === 'politics'),
  history: allMockWorks.filter((work) => work.category === 'history'),
  news: allMockWorks.filter((work) => work.tags?.includes('News') || work.tags?.includes('Current Events')),
};

// 导出所有作品（保持向后兼容）
export { allMockWorks as mockWorks };

// 获取特定分类的作品
export const getWorksByCategory = (category: string) => {
  return allMockWorks.filter((work) => work.category === category);
};