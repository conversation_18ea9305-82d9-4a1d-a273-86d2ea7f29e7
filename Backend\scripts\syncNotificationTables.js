const sequelize = require('../config/database');
const models = require('../models/associations');

async function syncNotificationTables() {
  try {
    console.log('🔄 Starting notification tables synchronization...');

    // 同步数据库
    await sequelize.sync({ alter: true });

    console.log('✅ Notification tables synchronized successfully');
    console.log('📊 Available models:', Object.keys(models));

    // 检查通知相关表是否创建成功
    const [results] = await sequelize.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('notifications', 'notification_preferences', 'push_subscriptions')
      ORDER BY table_name;
    `);

    console.log('📋 Notification tables created:');
    results.forEach((row) => {
      console.log(`  ✓ ${row.table_name}`);
    });

    process.exit(0);
  } catch (error) {
    console.error('❌ Error syncing notification tables:', error);
    process.exit(1);
  }
}

syncNotificationTables();
