# Newzora Frontend .dockerignore
# Exclude files and directories that don't need to be copied to Docker image

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/

# Environment variable files
.env
.env.local
.env.development
.env.test
.env.production

# Log files
*.log

# Runtime files
pids/
*.pid
*.seed
*.pid.lock

# Coverage reports
coverage/
.nyc_output/

# Dependency directories
.npm
.eslintcache

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output binary files
*.tgz

# Yarn integrity file
.yarn-integrity

# dotenv environment variable files
.env*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Operating system files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Test files
test/
tests/
__tests__/
*.test.js
*.test.jsx
*.test.ts
*.test.tsx
*.spec.js
*.spec.jsx
*.spec.ts
*.spec.tsx

# Documentation
README.md
docs/
*.md

# Storybook
.storybook/
storybook-static/

# 其他
.editorconfig
.prettierrc
.eslintrc*
tsconfig.json
next.config.js
