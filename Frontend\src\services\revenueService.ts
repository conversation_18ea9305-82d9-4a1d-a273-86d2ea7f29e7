import { RevenueShareConfig, CreatorRevenue, PayoutRecord } from '@/types';

class RevenueService {
  // 默认分成配置
  private defaultConfig: RevenueShareConfig = {
    id: 1,
    name: 'Standard Revenue Share',
    description: '标准收入分成方案',
    platformPercentage: 30,  // 平台30%
    creatorPercentage: 70,   // 创作者70%
    minPayoutAmount: 100,    // 最低100元提现
    payoutFrequency: 'monthly',
    currency: 'USD',
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // 计算收入分成
  calculateRevenueShare(totalRevenue: number, config: RevenueShareConfig = this.defaultConfig): { 
    platformShare: number; 
    creatorShare: number; 
  } {
    const platformShare = totalRevenue * (config.platformPercentage / 100);
    const creatorShare = totalRevenue * (config.creatorPercentage / 100);
    
    return {
      platformShare,
      creatorShare
    };
  }

  // 获取创作者收入详情
  getCreatorRevenue(userId: number): CreatorRevenue {
    // 在实际应用中，这里会从数据库获取真实数据
    // 这里使用模拟数据
    const totalRevenue = Math.random() * 10000; // 模拟总收入
    const { platformShare, creatorShare } = this.calculateRevenueShare(totalRevenue);
    const pendingPayout = creatorShare * (0.1 + Math.random() * 0.9); // 模拟待提现金额
    
    return {
      userId,
      username: `creator_${userId}`,
      totalRevenue,
      platformShare,
      creatorShare,
      pendingPayout,
      lastPayoutDate: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString(),
      paymentMethod: ['PayPal', 'Bank Transfer', 'Alipay', 'WeChat Pay'][Math.floor(Math.random() * 4)]
    };
  }

  // 申请提现
  requestPayout(userId: number, amount: number): Promise<PayoutRecord> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const payout: PayoutRecord = {
          id: Math.floor(Math.random() * 10000),
          userId,
          amount,
          currency: 'USD',
          status: 'pending',
          paymentMethod: ['PayPal', 'Bank Transfer', 'Alipay', 'WeChat Pay'][Math.floor(Math.random() * 4)],
          createdAt: new Date().toISOString()
        };
        
        resolve(payout);
      }, 500);
    });
  }

  // 获取提现记录
  getPayoutHistory(userId: number): Promise<PayoutRecord[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟提现记录
        const history: PayoutRecord[] = [
          {
            id: 1001,
            userId,
            amount: 500,
            currency: 'USD',
            status: 'completed',
            paymentMethod: 'PayPal',
            transactionId: 'TXN123456789',
            createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(),
            processedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 1002,
            userId,
            amount: 320,
            currency: 'USD',
            status: 'completed',
            paymentMethod: 'Bank Transfer',
            transactionId: 'TXN987654321',
            createdAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
            processedAt: new Date(Date.now() - 44 * 24 * 60 * 60 * 1000).toISOString()
          }
        ];
        
        resolve(history);
      }, 300);
    });
  }

  // 获取分成配置
  getRevenueShareConfig(): RevenueShareConfig {
    return this.defaultConfig;
  }

  // 更新分成配置
  updateRevenueShareConfig(config: RevenueShareConfig): RevenueShareConfig {
    return {
      ...config,
      updatedAt: new Date().toISOString()
    };
  }
}

export const revenueService = new RevenueService();