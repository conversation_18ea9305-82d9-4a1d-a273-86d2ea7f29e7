/**
 * 全局错误处理器
 */

let isErrorHandlerInitialized = false;

export const initializeGlobalErrorHandler = () => {
  if (isErrorHandlerInitialized || typeof window === 'undefined') {
    return;
  }

  // 处理未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    console.warn('Unhandled promise rejection:', event.reason);
    
    // 防止默认的错误处理
    if (event.reason?.message?.includes('removeChild') || 
        event.reason?.message?.includes('DOM')) {
      event.preventDefault();
    }
  });

  // 处理未捕获的JavaScript错误
  window.addEventListener('error', (event) => {
    console.warn('Unhandled error:', event.error);
    
    // 防止DOM相关错误导致页面崩溃
    if (event.error?.message?.includes('removeChild') || 
        event.error?.message?.includes('DOM') ||
        event.error?.message?.includes('Node')) {
      event.preventDefault();
      return false;
    }
  });

  // React错误边界无法捕获的错误
  const originalConsoleError = console.error;
  console.error = (...args) => {
    const message = args.join(' ');
    
    // 过滤掉已知的无害DOM错误
    if (message.includes('removeChild') || 
        message.includes('Node') ||
        message.includes('Warning: Can\'t perform a React state update')) {
      console.warn('Filtered DOM error:', ...args);
      return;
    }
    
    originalConsoleError.apply(console, args);
  };

  isErrorHandlerInitialized = true;
  console.log('Global error handler initialized');
};

/**
 * 安全的异步操作包装器
 */
export const safeAsync = async <T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<T | undefined> => {
  try {
    return await operation();
  } catch (error) {
    console.warn('Safe async operation failed:', error);
    return fallback;
  }
};

/**
 * 安全的同步操作包装器
 */
export const safeSync = <T>(
  operation: () => T,
  fallback?: T
): T | undefined => {
  try {
    return operation();
  } catch (error) {
    console.warn('Safe sync operation failed:', error);
    return fallback;
  }
};