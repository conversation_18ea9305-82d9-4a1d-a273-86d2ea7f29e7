interface VerificationData {
  type: 'identity' | 'address' | 'document' | 'phone' | 'video';
  file?: File;
  phoneNumber?: string;
  videoBlob?: Blob;
}

interface AIAnalysisResult {
  score: number;
  confidence: number;
  checks: {
    documentQuality: number;
    textReadability: number;
    faceMatch?: number;
    documentAuthenticity: number;
    dataConsistency: number;
    securityFeatures?: number;
  };
  extractedData: {
    name?: string;
    dateOfBirth?: string;
    documentNumber?: string;
    address?: string;
    issueDate?: string;
    expiryDate?: string;
  };
  flags: string[];
  needsHumanReview: boolean;
  reviewReason?: string;
}

class AIVerificationService {
  private async analyzeDocument(file: File, type: string): Promise<AIAnalysisResult> {
    // 模拟AI文档分析
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));
    
    const checks = {
      documentQuality: 85 + Math.random() * 15,
      textReadability: 80 + Math.random() * 20,
      documentAuthenticity: 75 + Math.random() * 25,
      dataConsistency: 85 + Math.random() * 15,
      securityFeatures: type === 'identity' ? 70 + Math.random() * 30 : undefined
    };

    const extractedData = this.mockExtractedData(type);
    const flags = this.generateFlags(checks, extractedData);
    const score = this.calculateOverallScore(checks);
    const needsHumanReview = this.determineHumanReview(score, flags, type);

    return {
      score,
      confidence: 85 + Math.random() * 15,
      checks,
      extractedData,
      flags,
      needsHumanReview,
      reviewReason: needsHumanReview ? this.getReviewReason(flags, score) : undefined
    };
  }

  private async analyzeVideo(videoBlob: Blob): Promise<AIAnalysisResult> {
    // 模拟AI视频分析
    await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000));
    
    const checks = {
      documentQuality: 80 + Math.random() * 20,
      textReadability: 75 + Math.random() * 25,
      faceMatch: 70 + Math.random() * 30,
      documentAuthenticity: 85 + Math.random() * 15,
      dataConsistency: 90 + Math.random() * 10
    };

    const extractedData = {
      name: 'John Doe',
      documentNumber: 'ID123456789',
      faceConfidence: checks.faceMatch
    };

    const flags = this.generateVideoFlags(checks);
    const score = this.calculateOverallScore(checks);
    const needsHumanReview = score < 85 || flags.length > 0 || Math.random() < 0.4;

    return {
      score,
      confidence: 80 + Math.random() * 20,
      checks,
      extractedData,
      flags,
      needsHumanReview,
      reviewReason: needsHumanReview ? this.getVideoReviewReason(flags, score, checks.faceMatch!) : undefined
    };
  }

  private async analyzePhone(phoneNumber: string): Promise<AIAnalysisResult> {
    // 模拟电话号码验证分析
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const checks = {
      documentQuality: 100,
      textReadability: 100,
      documentAuthenticity: 95 + Math.random() * 5,
      dataConsistency: 90 + Math.random() * 10
    };

    const flags = [];
    if (phoneNumber.length < 10) flags.push('Phone number too short');
    if (!phoneNumber.match(/^\+?[\d\s-()]+$/)) flags.push('Invalid phone format');

    const score = this.calculateOverallScore(checks);
    const needsHumanReview = flags.length > 0 || Math.random() < 0.1;

    return {
      score,
      confidence: 95 + Math.random() * 5,
      checks,
      extractedData: { phoneNumber },
      flags,
      needsHumanReview,
      reviewReason: needsHumanReview ? 'Random quality check' : undefined
    };
  }

  private mockExtractedData(type: string) {
    switch (type) {
      case 'identity':
        return {
          name: 'John Doe',
          dateOfBirth: '1990-01-15',
          documentNumber: 'ID123456789',
          issueDate: '2020-01-01',
          expiryDate: '2030-01-01'
        };
      case 'address':
        return {
          name: 'John Doe',
          address: '123 Main Street, City, State 12345',
          issueDate: '2024-01-01'
        };
      case 'document':
        return {
          name: 'John Doe',
          documentNumber: 'DOC987654321',
          issueDate: '2023-12-01'
        };
      default:
        return {};
    }
  }

  private generateFlags(checks: any, extractedData: any): string[] {
    const flags = [];
    
    if (checks.documentQuality < 80) flags.push('Low document image quality');
    if (checks.textReadability < 75) flags.push('Text difficult to read');
    if (checks.documentAuthenticity < 80) flags.push('Document authenticity concerns');
    if (checks.dataConsistency < 85) flags.push('Data consistency issues');
    if (checks.securityFeatures && checks.securityFeatures < 75) flags.push('Security features unclear');
    
    // 数据一致性检查
    if (extractedData.name && extractedData.name.length < 2) flags.push('Name extraction failed');
    if (extractedData.expiryDate && new Date(extractedData.expiryDate) < new Date()) {
      flags.push('Document expired');
    }
    
    return flags;
  }

  private generateVideoFlags(checks: any): string[] {
    const flags = [];
    
    if (checks.faceMatch < 80) flags.push('Face matching confidence low');
    if (checks.documentQuality < 75) flags.push('ID document not clearly visible');
    if (checks.textReadability < 70) flags.push('Document text unreadable in video');
    
    return flags;
  }

  private calculateOverallScore(checks: any): number {
    const weights = {
      documentQuality: 0.25,
      textReadability: 0.2,
      faceMatch: 0.3,
      documentAuthenticity: 0.15,
      dataConsistency: 0.1
    };

    let totalScore = 0;
    let totalWeight = 0;

    Object.entries(checks).forEach(([key, value]) => {
      if (typeof value === 'number' && weights[key as keyof typeof weights]) {
        totalScore += value * weights[key as keyof typeof weights];
        totalWeight += weights[key as keyof typeof weights];
      }
    });

    return totalWeight > 0 ? totalScore / totalWeight : 0;
  }

  private determineHumanReview(score: number, flags: string[], type: string): boolean {
    // 强制人工审核条件
    if (score < 75) return true;
    if (flags.length > 2) return true;
    if (flags.some(flag => flag.includes('expired') || flag.includes('authenticity'))) return true;
    
    // 随机抽检比例
    const randomCheckRates = {
      identity: 0.3,
      address: 0.2,
      document: 0.25,
      phone: 0.1,
      video: 0.4
    };
    
    return Math.random() < randomCheckRates[type as keyof typeof randomCheckRates];
  }

  private getReviewReason(flags: string[], score: number): string {
    if (score < 75) return `Low confidence score: ${score.toFixed(1)}%`;
    if (flags.length > 2) return `Multiple issues detected: ${flags.join(', ')}`;
    if (flags.length > 0) return `Quality check needed: ${flags[0]}`;
    return 'Random quality assurance check';
  }

  private getVideoReviewReason(flags: string[], score: number, faceMatch: number): string {
    if (faceMatch < 80) return `Face matching confidence low: ${faceMatch.toFixed(1)}%`;
    if (score < 80) return `Overall confidence low: ${score.toFixed(1)}%`;
    if (flags.length > 0) return `Video quality issues: ${flags.join(', ')}`;
    return 'Standard video verification review';
  }

  async processVerification(data: VerificationData): Promise<AIAnalysisResult> {
    switch (data.type) {
      case 'identity':
      case 'address':
      case 'document':
        if (!data.file) throw new Error('File required for document verification');
        return this.analyzeDocument(data.file, data.type);
      
      case 'video':
        if (!data.videoBlob) throw new Error('Video required for video verification');
        return this.analyzeVideo(data.videoBlob);
      
      case 'phone':
        if (!data.phoneNumber) throw new Error('Phone number required');
        return this.analyzePhone(data.phoneNumber);
      
      default:
        throw new Error('Unsupported verification type');
    }
  }

  // 交叉验证不同文档间的数据一致性
  async crossValidateDocuments(results: AIAnalysisResult[]): Promise<{
    consistent: boolean;
    issues: string[];
    confidence: number;
  }> {
    const names = results.map(r => r.extractedData.name).filter(Boolean);
    const addresses = results.map(r => r.extractedData.address).filter(Boolean);
    
    const issues = [];
    
    // 姓名一致性检查
    const uniqueNames = [...new Set(names)];
    if (uniqueNames.length > 1) {
      issues.push(`Name inconsistency: ${uniqueNames.join(' vs ')}`);
    }
    
    // 地址一致性检查（如果有多个地址文档）
    if (addresses.length > 1) {
      const uniqueAddresses = [...new Set(addresses)];
      if (uniqueAddresses.length > 1) {
        issues.push('Address mismatch between documents');
      }
    }
    
    const confidence = issues.length === 0 ? 95 + Math.random() * 5 : 60 + Math.random() * 20;
    
    return {
      consistent: issues.length === 0,
      issues,
      confidence
    };
  }
}

export const aiVerificationService = new AIVerificationService();