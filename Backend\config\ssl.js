/**
 * SSL/HTTPS 配置管理
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { logger } = require('./logger');

/**
 * 加载SSL证书
 */
const loadSSLCertificates = () => {
  try {
    const sslConfig = {
      key: null,
      cert: null,
      ca: null
    };

    // 加载私钥
    if (process.env.SSL_KEY_PATH) {
      const keyPath = path.resolve(process.env.SSL_KEY_PATH);
      if (fs.existsSync(keyPath)) {
        sslConfig.key = fs.readFileSync(keyPath, 'utf8');
        logger.info('SSL private key loaded successfully');
      } else {
        throw new Error(`SSL private key not found: ${keyPath}`);
      }
    }

    // 加载证书
    if (process.env.SSL_CERT_PATH) {
      const certPath = path.resolve(process.env.SSL_CERT_PATH);
      if (fs.existsSync(certPath)) {
        sslConfig.cert = fs.readFileSync(certPath, 'utf8');
        logger.info('SSL certificate loaded successfully');
      } else {
        throw new Error(`SSL certificate not found: ${certPath}`);
      }
    }

    // 加载CA证书（可选）
    if (process.env.SSL_CA_PATH) {
      const caPath = path.resolve(process.env.SSL_CA_PATH);
      if (fs.existsSync(caPath)) {
        sslConfig.ca = fs.readFileSync(caPath, 'utf8');
        logger.info('SSL CA certificate loaded successfully');
      }
    }

    return sslConfig;
  } catch (error) {
    logger.error('Failed to load SSL certificates', { error: error.message });
    throw error;
  }
};

/**
 * 创建HTTPS服务器
 */
const createHTTPSServer = (app) => {
  try {
    const sslConfig = loadSSLCertificates();
    
    if (!sslConfig.key || !sslConfig.cert) {
      throw new Error('SSL key and certificate are required for HTTPS');
    }

    const httpsOptions = {
      key: sslConfig.key,
      cert: sslConfig.cert
    };

    if (sslConfig.ca) {
      httpsOptions.ca = sslConfig.ca;
    }

    const httpsServer = https.createServer(httpsOptions, app);
    
    logger.info('HTTPS server created successfully');
    return httpsServer;
  } catch (error) {
    logger.error('Failed to create HTTPS server', { error: error.message });
    throw error;
  }
};

/**
 * 验证SSL证书
 */
const validateSSLCertificate = (cert) => {
  try {
    const crypto = require('crypto');
    const certObj = crypto.createCertificate();
    
    // 这里可以添加更多的证书验证逻辑
    // 例如检查过期时间、域名匹配等
    
    logger.info('SSL certificate validation passed');
    return true;
  } catch (error) {
    logger.error('SSL certificate validation failed', { error: error.message });
    return false;
  }
};

/**
 * 强制HTTPS重定向中间件
 */
const forceHTTPS = (req, res, next) => {
  if (process.env.NODE_ENV === 'production' && !req.secure && req.get('x-forwarded-proto') !== 'https') {
    const httpsUrl = `https://${req.get('host')}${req.url}`;
    logger.info('Redirecting to HTTPS', { originalUrl: req.url, httpsUrl });
    return res.redirect(301, httpsUrl);
  }
  next();
};

/**
 * 生成自签名证书（仅用于开发环境）
 */
const generateSelfSignedCert = () => {
  const { execSync } = require('child_process');
  const certDir = path.join(__dirname, '../ssl');
  
  try {
    // 创建SSL目录
    if (!fs.existsSync(certDir)) {
      fs.mkdirSync(certDir, { recursive: true });
    }

    const keyPath = path.join(certDir, 'private.key');
    const certPath = path.join(certDir, 'certificate.crt');

    // 生成私钥
    execSync(`openssl genrsa -out ${keyPath} 2048`);
    
    // 生成自签名证书
    execSync(`openssl req -new -x509 -key ${keyPath} -out ${certPath} -days 365 -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`);

    logger.info('Self-signed certificate generated', { keyPath, certPath });
    
    return {
      keyPath,
      certPath
    };
  } catch (error) {
    logger.error('Failed to generate self-signed certificate', { error: error.message });
    throw error;
  }
};

/**
 * SSL配置检查
 */
const checkSSLConfiguration = () => {
  const checks = {
    sslEnabled: process.env.SSL_ENABLED === 'true',
    keyPathSet: !!process.env.SSL_KEY_PATH,
    certPathSet: !!process.env.SSL_CERT_PATH,
    keyExists: false,
    certExists: false
  };

  if (checks.keyPathSet) {
    checks.keyExists = fs.existsSync(path.resolve(process.env.SSL_KEY_PATH));
  }

  if (checks.certPathSet) {
    checks.certExists = fs.existsSync(path.resolve(process.env.SSL_CERT_PATH));
  }

  logger.info('SSL configuration check', checks);
  return checks;
};

module.exports = {
  loadSSLCertificates,
  createHTTPSServer,
  validateSSLCertificate,
  forceHTTPS,
  generateSelfSignedCert,
  checkSSLConfiguration
};