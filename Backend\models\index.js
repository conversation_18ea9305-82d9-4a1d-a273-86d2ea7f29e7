const { sequelize } = require('../config/database');
const User = require('./User');
const Article = require('./Article');
const Comment = require('./Comment');
const Follow = require('./Follow');
const Message = require('./Message');
const Tag = require('./Tag');
const UserTag = require('./UserTag');
const Activity = require('./Activity');
const Share = require('./Share');
const Draft = require('./Draft');
const MediaFile = require('./MediaFile');
const ContentReview = require('./ContentReview');
const ReviewRule = require('./ReviewRule');
const UserBehavior = require('./UserBehavior');
const ReadingStats = require('./ReadingStats');
const SearchLog = require('./SearchLog');
const UserProfile = require('./UserProfile');
const Notification = require('./Notification');
const NotificationPreference = require('./NotificationPreference');
const PushSubscription = require('./PushSubscription');
const WithdrawalRequest = require('./WithdrawalRequest');
const UserBalance = require('./UserBalance');

// 定义模型关联
const setupAssociations = () => {
  // User 和 Comment 的关联
  User.hasMany(Comment, {
    foreignKey: 'authorId',
    as: 'comments',
    onDelete: 'CASCADE',
  });

  Comment.belongsTo(User, {
    foreignKey: 'authorId',
    as: 'authorUser',
  });

  // Article 和 Comment 的关联
  Article.hasMany(Comment, {
    foreignKey: 'articleId',
    as: 'comments',
    onDelete: 'CASCADE',
  });

  Comment.belongsTo(Article, {
    foreignKey: 'articleId',
    as: 'article',
  });

  // Comment 自关联 (回复)
  Comment.hasMany(Comment, {
    foreignKey: 'parentId',
    as: 'replies',
    onDelete: 'CASCADE',
  });

  Comment.belongsTo(Comment, {
    foreignKey: 'parentId',
    as: 'parent',
  });
  
  // User 和 Follow 的关联
  User.belongsToMany(User, {
    through: Follow,
    as: 'following',
    foreignKey: 'followerId',
    otherKey: 'followingId',
  });

  User.belongsToMany(User, {
    through: Follow,
    as: 'followers',
    foreignKey: 'followingId',
    otherKey: 'followerId',
  });

  Follow.belongsTo(User, { as: 'follower', foreignKey: 'followerId' });
  Follow.belongsTo(User, { as: 'following', foreignKey: 'followingId' });

  console.log('✅ Model associations established successfully.');
};

// 同步数据库
const syncDatabase = async (force = false) => {
  try {
    // 建立关联
    setupAssociations();
    
    // 同步所有模型
    await sequelize.sync({ force });
    console.log('✅ Database synchronized successfully.');
  } catch (error) {
    console.error('❌ Database synchronization failed:', error.message);
    throw error;
  }
};

// 创建初始数据
const createInitialData = async () => {
  try {
    console.log('🌱 Creating initial data...');

    // 创建管理员用户
    const adminUser = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password_hash: 'admin123456',
      display_name: 'Administrator',
      role: 'admin',
      isEmailVerified: true,
      bio: 'Platform administrator',
    });

    // 创建示例用户
    const demoUser = await User.create({
      username: 'demo_user',
      email: '<EMAIL>',
      password_hash: 'demo123456',
      display_name: 'Demo User',
      role: 'user',
      isEmailVerified: true,
      bio: 'This is a demo user account for testing purposes.',
    });

    // 创建示例文章
    const sampleArticles = [
      {
        title: 'Welcome to Newzora: The Future of Content Sharing',
        description: 'Discover the revolutionary platform designed for modern content creators and readers.',
        content: `<h2>Introduction</h2>
        <p>Welcome to Newzora, a revolutionary platform designed for modern content creators and readers. Our mission is to provide a seamless, engaging experience for sharing and discovering quality content.</p>
        
        <h2>Key Features</h2>
        <ul>
          <li>Rich text editing with multimedia support</li>
          <li>Social interaction and community building</li>
          <li>Advanced search and discovery</li>
          <li>Mobile-first responsive design</li>
        </ul>
        
        <h2>Getting Started</h2>
        <p>Creating your first article is simple. Just click the "Create" button and start writing. Our intuitive editor supports formatting, images, and more.</p>
        
        <p>Join our growing community of writers and readers today!</p>`,
        authorName: 'Administrator',
        category: 'Technology',
        tags: ['welcome', 'platform', 'features'],
        published: true,
        featured: true,
        image: 'https://picsum.photos/800/400?random=1',
      },
      {
        title: 'The Art of Digital Storytelling in 2024',
        description: 'Explore modern techniques and best practices for creating compelling digital stories.',
        content: `<h2>The Evolution of Storytelling</h2>
        <p>Digital storytelling has transformed dramatically over the past decade. From simple blog posts to interactive multimedia experiences, the way we share stories continues to evolve.</p>
        
        <h2>Modern Techniques</h2>
        <p>Today's digital storytellers leverage various tools and techniques:</p>
        <ul>
          <li>Interactive elements and multimedia</li>
          <li>Data visualization and infographics</li>
          <li>Social media integration</li>
          <li>Mobile-optimized content</li>
        </ul>
        
        <h2>Best Practices</h2>
        <p>To create compelling digital stories, focus on:</p>
        <ol>
          <li>Clear narrative structure</li>
          <li>Engaging visuals</li>
          <li>Audience interaction</li>
          <li>Cross-platform compatibility</li>
        </ol>`,
        authorName: 'Demo User',
        category: 'Lifestyle',
        tags: ['storytelling', 'digital', 'content'],
        published: true,
        image: 'https://picsum.photos/800/400?random=2',
      },
      {
        title: 'Building Communities Through Content',
        description: 'Learn how content platforms can foster genuine communities and meaningful interactions.',
        content: `<h2>The Power of Community</h2>
        <p>Content platforms thrive when they foster genuine communities. It's not just about publishing content—it's about creating connections and meaningful interactions.</p>
        
        <h2>Community Building Strategies</h2>
        <p>Successful content communities share several characteristics:</p>
        <ul>
          <li>Clear community guidelines and values</li>
          <li>Active moderation and support</li>
          <li>Regular events and discussions</li>
          <li>Recognition and rewards for contributors</li>
        </ul>
        
        <h2>Engagement Techniques</h2>
        <p>To boost community engagement:</p>
        <ul>
          <li>Encourage comments and discussions</li>
          <li>Share user-generated content</li>
          <li>Host live events and Q&As</li>
          <li>Create collaborative projects</li>
        </ul>`,
        authorName: 'Administrator',
        category: 'Technology',
        tags: ['community', 'engagement', 'social'],
        published: true,
        image: 'https://picsum.photos/800/400?random=3',
      },
    ];

    const createdArticles = await Article.bulkCreate(sampleArticles);

    // 创建示例评论
    const sampleComments = [
      {
        articleId: createdArticles[0].id,
        authorId: demoUser.id,
        authorName: 'Demo User',
        content:
          'Great introduction to the platform! Looking forward to exploring all the features.',
      },
      {
        articleId: createdArticles[0].id,
        authorId: adminUser.id,
        authorName: 'Administrator',
        content: "Thank you for the feedback! We're excited to have you as part of our community.",
      },
      {
        articleId: createdArticles[1].id,
        authorId: adminUser.id,
        authorName: 'Administrator',
        content:
          'Excellent insights on digital storytelling. The techniques you mentioned are spot on.',
      },
    ];

    await Comment.bulkCreate(sampleComments);

    console.log('✅ Initial data created successfully.');
    console.log(`👤 Admin user: <EMAIL> / admin123456`);
    console.log(`👤 Demo user: <EMAIL> / demo123456`);
  } catch (error) {
    console.error('❌ Failed to create initial data:', error.message);
  }
};

module.exports = {
  sequelize,
  User,
  Article,
  Comment,
  Follow,
  Message,
  Tag,
  UserTag,
  Activity,
  Share,
  Draft,
  MediaFile,
  ContentReview,
  ReviewRule,
  UserBehavior,
  ReadingStats,
  SearchLog,
  UserProfile,
  Notification,
  NotificationPreference,
  PushSubscription,
  WithdrawalRequest,
  UserBalance,
  setupAssociations,
  syncDatabase,
  createInitialData,
};
