/**
 * 日志输入清理工具 - 防止日志注入攻击
 */

/**
 * 清理用户输入，防止日志注入
 * @param {string} input - 用户输入
 * @returns {string} - 清理后的安全输入
 */
const sanitizeLogInput = (input) => {
  if (typeof input !== 'string') {
    return String(input);
  }

  return input
    // 移除换行符和回车符，防止日志注入
    .replace(/[\r\n]/g, ' ')
    // 移除控制字符
    .replace(/[\x00-\x1f\x7f-\x9f]/g, '')
    // 限制长度，防止日志文件过大
    .substring(0, 1000)
    // 转义特殊字符
    .replace(/[<>&"']/g, (match) => {
      const escapeMap = {
        '<': '&lt;',
        '>': '&gt;',
        '&': '&amp;',
        '"': '&quot;',
        "'": '&#x27;'
      };
      return escapeMap[match];
    });
};

/**
 * 清理对象中的所有字符串值
 * @param {Object} obj - 要清理的对象
 * @returns {Object} - 清理后的对象
 */
const sanitizeLogObject = (obj) => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeLogInput(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(sanitizeLogObject);
  }

  if (typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeLogInput(key)] = sanitizeLogObject(value);
    }
    return sanitized;
  }

  return obj;
};

/**
 * 创建安全的日志消息
 * @param {string} message - 日志消息
 * @param {Object} meta - 元数据
 * @returns {Object} - 安全的日志数据
 */
const createSafeLogMessage = (message, meta = {}) => {
  return {
    message: sanitizeLogInput(message),
    meta: sanitizeLogObject(meta),
    timestamp: new Date().toISOString()
  };
};

module.exports = {
  sanitizeLogInput,
  sanitizeLogObject,
  createSafeLogMessage
};