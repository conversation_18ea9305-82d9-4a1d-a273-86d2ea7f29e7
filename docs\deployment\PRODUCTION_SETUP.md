# OneNews 生产环境部署指南

## 🚨 重要安全提醒

**在部署到生产环境之前，必须完成以下安全配置！**

## 📋 生产环境配置清单

### 1. 环境变量配置

#### 🔐 必须更改的安全密钥

```bash
# 生成强随机JWT密钥（至少64字符）
openssl rand -base64 64

# 生成Session密钥
openssl rand -base64 32

# 生成VAPID密钥对
npx web-push generate-vapid-keys
```

#### 🗄️ 数据库配置

1. **创建生产数据库**
```sql
CREATE DATABASE onenews_production;
CREATE USER onenews_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE onenews_production TO onenews_user;
```

2. **配置数据库连接**
```bash
# 方式1：分别配置
DB_HOST=your_production_db_host
DB_USER=onenews_user
DB_PASSWORD=your_secure_password
DB_NAME=onenews_production

# 方式2：使用连接URL（推荐）
DATABASE_URL=********************************************/onenews_production
```

#### 📧 邮件服务配置

**选项1：Gmail App Password**
1. 启用2FA
2. 生成应用专用密码
3. 配置环境变量

**选项2：SendGrid（推荐）**
1. 注册SendGrid账户
2. 获取API密钥
3. 验证发送域名

#### 🔑 OAuth 社交登录配置

**Google OAuth:**
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建OAuth 2.0凭据
5. 配置授权重定向URI

**Facebook OAuth:**
1. 访问 [Facebook Developers](https://developers.facebook.com/)
2. 创建新应用
3. 配置Facebook登录产品
4. 获取应用ID和密钥

### 2. 服务器环境准备

#### 系统要求
- Ubuntu 20.04+ / CentOS 8+ / Amazon Linux 2
- Node.js 18+
- PostgreSQL 12+
- Nginx 1.18+
- PM2 进程管理器

#### 安装依赖
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PostgreSQL
sudo apt install postgresql postgresql-contrib

# 安装Nginx
sudo apt install nginx

# 安装PM2
sudo npm install -g pm2
```

### 3. 应用部署步骤

#### 步骤1：克隆代码
```bash
git clone <your-repository-url>
cd OneNews
```

#### 步骤2：安装依赖
```bash
# 后端依赖
cd Backend
npm ci --production

# 前端构建
cd ../Frontend
npm ci
npm run build
```

#### 步骤3：配置环境变量
```bash
# 复制生产环境配置
cp Backend/.env.production Backend/.env

# 编辑配置文件，填入真实值
nano Backend/.env
```

#### 步骤4：数据库初始化
```bash
cd Backend
npm run seed
```

#### 步骤5：启动应用
```bash
# 使用PM2启动后端
pm2 start ecosystem.config.js

# 配置Nginx反向代理
sudo cp nginx.conf /etc/nginx/sites-available/onenews
sudo ln -s /etc/nginx/sites-available/onenews /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 4. SSL证书配置

#### 使用Let's Encrypt（免费）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 5. 监控和日志

#### 配置日志轮转
```bash
sudo nano /etc/logrotate.d/onenews
```

#### 设置监控告警
- 配置PM2监控
- 设置数据库监控
- 配置磁盘空间告警

### 6. 备份策略

#### 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
pg_dump -h localhost -U onenews_user onenews_production > backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 文件备份
```bash
# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz Backend/uploads/
```

### 7. 性能优化

#### 数据库优化
- 配置连接池
- 添加必要索引
- 定期VACUUM

#### 应用优化
- 启用Gzip压缩
- 配置静态文件缓存
- 使用CDN

### 8. 安全检查清单

- [ ] 更改所有默认密码和密钥
- [ ] 配置防火墙规则
- [ ] 启用HTTPS
- [ ] 配置CORS策略
- [ ] 设置速率限制
- [ ] 定期更新依赖包
- [ ] 配置安全头部
- [ ] 启用日志记录

## 🚀 快速部署命令

```bash
# 一键部署脚本（需要先配置环境变量）
chmod +x deploy.sh
./deploy.sh
```

## 📞 故障排除

### 常见问题
1. **数据库连接失败** - 检查防火墙和连接字符串
2. **邮件发送失败** - 验证SMTP凭据和端口
3. **OAuth登录失败** - 检查回调URL配置
4. **文件上传失败** - 检查目录权限和磁盘空间

### 日志位置
- 应用日志：`/app/logs/app.log`
- Nginx日志：`/var/log/nginx/`
- PostgreSQL日志：`/var/log/postgresql/`

## 📈 监控指标

关键监控指标：
- CPU使用率 < 80%
- 内存使用率 < 85%
- 磁盘使用率 < 90%
- 数据库连接数 < 80%
- 响应时间 < 500ms
- 错误率 < 1%

---

**⚠️ 重要提醒：在生产环境部署前，请务必在测试环境完整验证所有功能！**
