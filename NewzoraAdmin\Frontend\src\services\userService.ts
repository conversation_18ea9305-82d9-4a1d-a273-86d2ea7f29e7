import { supabaseService } from './supabaseService';
import { AdminUser } from '@/types/admin';

export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
}

export const userService = {
  // 获取用户列表
  async getUserList(params: UserListParams = {}) {
    try {
      return await supabaseService.getUsers(params);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  },

  // 获取用户详情
  async getUserDetail(userId: number) {
    try {
      const response = await api.get<ApiResponse<AdminUser>>(`/api/admin/users/${userId}`);
      return response.data.data!;
    } catch (error) {
      console.error('获取用户详情失败:', error);
      throw error;
    }
  },

  // 更新用户状态
  async updateUserStatus(userId: string, isActive: boolean) {
    try {
      return await supabaseService.updateUserStatus(userId, isActive);
    } catch (error) {
      console.error('更新用户状态失败:', error);
      throw error;
    }
  },

  // 更新用户角色
  async updateUserRole(userId: string, role: string) {
    try {
      return await supabaseService.updateUserRole(userId, role);
    } catch (error) {
      console.error('更新用户角色失败:', error);
      throw error;
    }
  },

  // 批量操作用户
  async bulkUpdateUsers(userIds: string[], action: string, value?: any) {
    try {
      return await supabaseService.bulkUpdateUsers(userIds, action, value);
    } catch (error) {
      console.error('批量操作用户失败:', error);
      throw error;
    }
  }
};