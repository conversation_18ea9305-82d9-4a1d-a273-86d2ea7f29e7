{"name": "newzora-admin-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "type-check": "tsc --noEmit", "clean": "rm -rf .next out node_modules/.cache", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.53.0", "@types/node": "^20.19.8", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.0", "axios": "^1.7.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "lucide-react": "^0.525.0", "next": "^14.2.30", "postcss": "^8.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "recharts": "^2.8.0", "socket.io-client": "^4.7.2", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.4.0", "typescript": "^5.8.3"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "eslint": "^8.0.0", "eslint-config-next": "14.2.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}}