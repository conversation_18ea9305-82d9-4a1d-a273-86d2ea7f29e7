'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';

interface Notification {
  id: number;
  type: string;
  title: string;
  content: string;
  isRead: boolean;
  createdAt: string;
  workId?: number;
  articleId?: number;
  work?: {
    id: number;
    title: string;
    type: string;
    image?: string;
  };
  fromUser?: {
    id: number;
    username: string;
    display_name: string;
    avatar_url?: string;
  };
}

export default function NotificationBell() {
  const { user, isAuthenticated, isLoading } = useSimpleAuth();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 所有useEffect必须在组件顶部，不能有条件调用
  useEffect(() => {
    if (user && isAuthenticated && !isLoading) {
      fetchNotifications();
      fetchUnreadCount();
    }
  }, [user, isAuthenticated, isLoading]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const fetchUnreadCount = async () => {
    if (!user || !isAuthenticated) return;

    try {
      setError(null);
      // 模拟未读数量
      setUnreadCount(2);
    } catch (error) {
      console.error('获取未读数量失败:', error);
      setUnreadCount(0);
      setError('无法获取通知');
    }
  };

  const fetchNotifications = async () => {
    if (!user || !isAuthenticated) return;

    setLoading(true);
    setError(null);

    try {
      // 模拟通知数据
      const mockNotifications: Notification[] = [
        {
          id: 1,
          type: 'like',
          title: '新的点赞',
          content: 'John 点赞了你的文章',
          isRead: false,
          createdAt: new Date(Date.now() - 3600000).toISOString(),
          workId: 1,
          work: {
            id: 1,
            title: 'JavaScript 最佳实践指南',
            type: 'article',
            image: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop'
          },
          fromUser: {
            id: 2,
            username: 'john_dev',
            display_name: 'John Developer',
            avatar_url: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face'
          }
        },
        {
          id: 2,
          type: 'comment',
          title: '新的评论',
          content: 'Sarah 评论了你的视频',
          isRead: false,
          createdAt: new Date(Date.now() - 7200000).toISOString(),
          workId: 2,
          work: {
            id: 2,
            title: 'React Hooks 深度解析',
            type: 'video',
            image: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop'
          },
          fromUser: {
            id: 3,
            username: 'sarah_ui',
            display_name: 'Sarah Designer',
            avatar_url: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face'
          }
        }
      ];

      setNotifications(mockNotifications);
    } catch (error) {
      console.error('获取通知失败:', error);
      setNotifications([]);
      setError('无法加载通知');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: number) => {
    if (!user) return;

    try {
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, isRead: true } : n)
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  const markAllAsRead = async () => {
    if (!user) return;

    try {
      setNotifications(prev => prev.map(n => ({ ...n, isRead: true })));
      setUnreadCount(0);
    } catch (error) {
      console.error('标记所有已读失败:', error);
    }
  };

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.isRead) {
      markAsRead(notification.id);
    }

    if (notification.workId) {
      const workType = notification.work?.type || 'article';
      router.push(`/${workType}/${notification.workId}`);
    } else if (notification.articleId) {
      router.push(`/article/${notification.articleId}`);
    } else if (notification.fromUser) {
      router.push(`/profile/${notification.fromUser.username}`);
    }

    setIsOpen(false);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'like':
        return '❤️';
      case 'comment':
        return '💬';
      case 'follow':
        return '👤';
      case 'article_published':
        return '📝';
      case 'system':
        return '🔔';
      default:
        return '📢';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}天前`;

    return date.toLocaleDateString();
  };

  // 如果正在加载认证状态，显示加载状态
  if (isLoading) {
    return (
      <div className="relative p-2">
        <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  // 如果用户未登录，不渲染组件
  if (!isAuthenticated || !user) {
    return null;
  }

  return (
    <div 
      className="relative"
      ref={dropdownRef}
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
    >
      <button
        className="relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
          <path strokeLinecap="round" strokeLinejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0" />
        </svg>

        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-96 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">通知</h3>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  全部已读
                </button>
              )}
            </div>
          </div>

          <div className="max-h-80 overflow-y-auto">
            {loading ? (
              <div className="p-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2">加载中...</p>
              </div>
            ) : error ? (
              <div className="p-8 text-center text-gray-500">
                <div className="text-4xl mb-2">⚠️</div>
                <p className="text-sm">{error}</p>
                <button
                  onClick={() => {
                    fetchUnreadCount();
                    fetchNotifications();
                  }}
                  className="mt-2 text-xs text-blue-600 hover:text-blue-700"
                >
                  重试
                </button>
              </div>
            ) : notifications.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                <div className="text-4xl mb-2">🔔</div>
                <p>暂无通知</p>
              </div>
            ) : (
              <div className="divide-y divide-gray-100">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    onClick={() => handleNotificationClick(notification)}
                    className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${!notification.isRead ? 'bg-blue-50' : ''}`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <span className="text-2xl">
                          {getNotificationIcon(notification.type)}
                        </span>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className={`text-sm font-medium ${!notification.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </p>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          )}
                        </div>

                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {notification.content}
                        </p>

                        {notification.work && (
                          <div className="flex items-center mt-2 p-2 bg-gray-50 rounded">
                            <div className="flex items-center mr-2">
                              <span className="text-xs px-1.5 py-0.5 bg-blue-100 text-blue-600 rounded">
                                {notification.work.type}
                              </span>
                            </div>
                            {notification.work.image && (
                              <img
                                src={notification.work.image}
                                alt=""
                                className="w-8 h-8 rounded object-cover mr-2"
                              />
                            )}
                            <span className="text-xs text-gray-600 truncate">
                              {notification.work.title}
                            </span>
                          </div>
                        )}

                        <p className="text-xs text-gray-500 mt-2">
                          {formatTimeAgo(notification.createdAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <button
              onClick={() => {
                router.push('/notifications');
                setIsOpen(false);
              }}
              className="w-full text-center text-sm text-blue-600 hover:text-blue-700 font-medium"
            >
              查看所有通知
            </button>
          </div>
        </div>
      )}
    </div>
  );
}