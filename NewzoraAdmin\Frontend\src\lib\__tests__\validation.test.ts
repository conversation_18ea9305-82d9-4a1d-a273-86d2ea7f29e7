import FormValidator, {
  createValidator,
  validateEmail,
  validatePassword,
  validateUrl,
  sanitizeInput,
  userValidationSchema,
  articleValidationSchema,
} from '../validation';

describe('FormValidator', () => {
  describe('Basic validation', () => {
    const schema = {
      name: { required: true, minLength: 2, maxLength: 50 },
      email: { required: true, email: true },
      age: { numeric: true, min: 18, max: 100 },
      website: { url: true },
    };

    const validator = new FormValidator(schema);

    it('validates required fields correctly', () => {
      const result = validator.validate({});
      
      expect(result.isValid).toBe(false);
      expect(result.errors.name).toBeDefined();
      expect(result.errors.email).toBeDefined();
      expect(result.errors.age).toBeUndefined(); // Not required
      expect(result.errors.website).toBeUndefined(); // Not required
    });

    it('validates string length correctly', () => {
      const result = validator.validate({
        name: 'A', // Too short
        email: '<EMAIL>',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.name).toContain('at least 2 characters');
    });

    it('validates email format correctly', () => {
      const result = validator.validate({
        name: 'John Doe',
        email: 'invalid-email',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.email).toContain('valid email address');
    });

    it('validates numeric fields correctly', () => {
      const result = validator.validate({
        name: 'John Doe',
        email: '<EMAIL>',
        age: 'not-a-number',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.age).toContain('must be a number');
    });

    it('validates numeric range correctly', () => {
      const result = validator.validate({
        name: 'John Doe',
        email: '<EMAIL>',
        age: 15, // Below minimum
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.age).toContain('at least 18');
    });

    it('validates URL format correctly', () => {
      const result = validator.validate({
        name: 'John Doe',
        email: '<EMAIL>',
        website: 'not-a-url',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.website).toContain('valid URL');
    });

    it('passes validation with valid data', () => {
      const result = validator.validate({
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25,
        website: 'https://johndoe.com',
      });

      expect(result.isValid).toBe(true);
      expect(Object.keys(result.errors)).toHaveLength(0);
    });
  });

  describe('Pattern validation', () => {
    const schema = {
      username: {
        required: true,
        pattern: /^[a-zA-Z0-9_-]+$/,
        message: 'Username can only contain letters, numbers, underscores, and hyphens',
      },
    };

    const validator = new FormValidator(schema);

    it('validates pattern correctly', () => {
      const result = validator.validate({
        username: 'invalid username!', // Contains space and special character
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.username).toBe('Username can only contain letters, numbers, underscores, and hyphens');
    });

    it('passes pattern validation with valid input', () => {
      const result = validator.validate({
        username: 'valid_username-123',
      });

      expect(result.isValid).toBe(true);
    });
  });

  describe('Custom validation', () => {
    const schema = {
      password: {
        required: true,
        custom: (value: string) => {
          if (value.length < 8) return 'Password must be at least 8 characters';
          if (!/[A-Z]/.test(value)) return 'Password must contain uppercase letter';
          if (!/[a-z]/.test(value)) return 'Password must contain lowercase letter';
          if (!/\d/.test(value)) return 'Password must contain a number';
          return null;
        },
      },
    };

    const validator = new FormValidator(schema);

    it('validates custom rules correctly', () => {
      const result = validator.validate({
        password: 'weak',
      });

      expect(result.isValid).toBe(false);
      expect(result.errors.password).toBe('Password must be at least 8 characters');
    });

    it('passes custom validation with valid input', () => {
      const result = validator.validate({
        password: 'StrongPass123',
      });

      expect(result.isValid).toBe(true);
    });
  });

  describe('Single field validation', () => {
    const schema = {
      email: { required: true, email: true },
      name: { required: true, minLength: 2 },
    };

    const validator = new FormValidator(schema);

    it('validates single field correctly', () => {
      const error = validator.validateSingle('email', 'invalid-email');
      expect(error).toContain('valid email address');
    });

    it('returns null for valid single field', () => {
      const error = validator.validateSingle('email', '<EMAIL>');
      expect(error).toBeNull();
    });

    it('returns null for non-existent field', () => {
      const error = validator.validateSingle('nonexistent', 'value');
      expect(error).toBeNull();
    });
  });
});

describe('Utility functions', () => {
  describe('validateEmail', () => {
    it('validates correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('user@')).toBe(false);
      expect(validateEmail('@domain.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
    });
  });

  describe('validatePassword', () => {
    it('validates strong passwords', () => {
      const result = validatePassword('StrongPass123!');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('identifies weak passwords', () => {
      const result = validatePassword('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('checks for minimum length', () => {
      const result = validatePassword('Short1!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must be at least 8 characters long');
    });

    it('checks for uppercase letters', () => {
      const result = validatePassword('lowercase123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('checks for lowercase letters', () => {
      const result = validatePassword('UPPERCASE123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('checks for numbers', () => {
      const result = validatePassword('NoNumbers!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('checks for special characters', () => {
      const result = validatePassword('NoSpecialChars123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character (@$!%*?&)');
    });
  });

  describe('validateUrl', () => {
    it('validates correct URLs', () => {
      expect(validateUrl('https://example.com')).toBe(true);
      expect(validateUrl('http://test.org')).toBe(true);
      expect(validateUrl('https://sub.domain.co.uk/path?query=value')).toBe(true);
    });

    it('rejects invalid URLs', () => {
      expect(validateUrl('not-a-url')).toBe(false);
      expect(validateUrl('ftp://invalid-protocol.com')).toBe(false);
      expect(validateUrl('https://')).toBe(false);
    });
  });

  describe('sanitizeInput', () => {
    it('removes dangerous characters', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script');
      expect(sanitizeInput('Normal text')).toBe('Normal text');
      expect(sanitizeInput('  Trimmed text  ')).toBe('Trimmed text');
    });
  });
});

describe('Predefined schemas', () => {
  describe('userValidationSchema', () => {
    const validator = createValidator(userValidationSchema);

    it('validates user data correctly', () => {
      const validUser = {
        username: 'john_doe',
        email: '<EMAIL>',
        display_name: 'John Doe',
        password: 'StrongPass123!',
      };

      const result = validator.validate(validUser);
      expect(result.isValid).toBe(true);
    });

    it('rejects invalid user data', () => {
      const invalidUser = {
        username: 'jo', // Too short
        email: 'invalid-email',
        display_name: 'J', // Too short
        password: 'weak',
      };

      const result = validator.validate(invalidUser);
      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors)).toHaveLength(4);
    });
  });

  describe('articleValidationSchema', () => {
    const validator = createValidator(articleValidationSchema);

    it('validates article data correctly', () => {
      const validArticle = {
        title: 'This is a valid article title',
        content: 'This is a valid article content that is long enough to meet the minimum requirements for an article.',
        category: 'Technology',
      };

      const result = validator.validate(validArticle);
      expect(result.isValid).toBe(true);
    });

    it('rejects invalid article data', () => {
      const invalidArticle = {
        title: 'Too', // Too short
        content: 'Short', // Too short
        // Missing category
      };

      const result = validator.validate(invalidArticle);
      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors)).toHaveLength(3);
    });
  });
});
