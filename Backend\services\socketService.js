const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Notification = require('../models/Notification');
const NotificationPreference = require('../models/NotificationPreference');
const Message = require('../models/Message');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.userSockets = new Map(); // socketId -> userId
    this.typingUsers = new Map(); // 存储正在输入的用户
  }

  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: process.env.FRONTEND_URL || 'http://localhost:3000',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupMiddleware();
    this.setupEventHandlers();

    console.log('🔌 Socket.IO server initialized');
    return this.io;
  }

  setupMiddleware() {
    // JWT认证中间件
    this.io.use(async (socket, next) => {
      try {
        const token =
          socket.handshake.auth.token ||
          socket.handshake.headers.authorization?.replace('Bearer ', '');

        if (!token) {
          return next(new Error('Authentication token required'));
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await User.findByPk(decoded.id, {
          attributes: ['id', 'username', 'email', 'role', 'isActive'],
        });

        if (!user || !user.isActive) {
          return next(new Error('User not found or inactive'));
        }

        socket.userId = user.id;
        socket.user = user;
        next();
      } catch (error) {
        console.error('Socket authentication error:', error.message);
        next(new Error('Invalid authentication token'));
      }
    });
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      this.handleConnection(socket);

      socket.on('disconnect', () => {
        this.handleDisconnection(socket);
      });

      socket.on('join_room', (data) => {
        this.handleJoinRoom(socket, data);
      });

      socket.on('leave_room', (data) => {
        this.handleLeaveRoom(socket, data);
      });

      socket.on('mark_notification_read', (data) => {
        this.handleMarkNotificationRead(socket, data);
      });

      socket.on('get_unread_count', () => {
        this.handleGetUnreadCount(socket);
      });

      // 聊天相关事件
      socket.on('send_message', (data) => {
        this.handleSendMessage(socket, data);
      });

      socket.on('typing_start', (data) => {
        this.handleTypingStart(socket, data);
      });

      socket.on('typing_stop', (data) => {
        this.handleTypingStop(socket, data);
      });

      socket.on('mark_message_read', (data) => {
        this.handleMarkMessageRead(socket, data);
      });
    });
  }

  handleConnection(socket) {
    const userId = socket.userId;
    console.log(`👤 User ${userId} connected with socket ${socket.id}`);

    // 存储用户连接信息
    this.connectedUsers.set(userId, socket.id);
    this.userSockets.set(socket.id, userId);

    // 加入用户专属房间
    socket.join(`user_${userId}`);

    // 发送连接成功消息
    socket.emit('connected', {
      message: 'Successfully connected to notification service',
      userId: userId,
      timestamp: new Date().toISOString(),
    });

    // 发送未读通知数量
    this.sendUnreadCount(userId);
  }

  handleDisconnection(socket) {
    const userId = this.userSockets.get(socket.id);
    if (userId) {
      console.log(`👤 User ${userId} disconnected from socket ${socket.id}`);
      this.connectedUsers.delete(userId);
      this.userSockets.delete(socket.id);
      
      // 清除所有相关的输入状态
      for (const [key, timeout] of this.typingUsers.entries()) {
        if (key.startsWith(`${userId}-`)) {
          clearTimeout(timeout);
          this.typingUsers.delete(key);
        }
      }
    }
  }

  handleJoinRoom(socket, data) {
    const { room } = data;
    if (room && typeof room === 'string') {
      socket.join(room);
      socket.emit('room_joined', { room, timestamp: new Date().toISOString() });
      console.log(`👤 User ${socket.userId} joined room: ${room}`);
    }
  }

  handleLeaveRoom(socket, data) {
    const { room } = data;
    if (room && typeof room === 'string') {
      socket.leave(room);
      socket.emit('room_left', { room, timestamp: new Date().toISOString() });
      console.log(`👤 User ${socket.userId} left room: ${room}`);
    }
  }

  async handleMarkNotificationRead(socket, data) {
    try {
      const { notificationId } = data;
      const userId = socket.userId;

      const notification = await Notification.findOne({
        where: { id: notificationId, userId },
      });

      if (notification && !notification.isRead) {
        await notification.markAsRead();

        // 发送更新后的未读数量
        this.sendUnreadCount(userId);

        socket.emit('notification_marked_read', {
          notificationId,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      socket.emit('error', { message: 'Failed to mark notification as read' });
    }
  }

  async handleGetUnreadCount(socket) {
    await this.sendUnreadCount(socket.userId);
  }

  // 发送实时通知给特定用户
  async sendNotificationToUser(userId, notification) {
    try {
      // 检查用户通知偏好
      const preference = await NotificationPreference.getUserPreference(userId);
      if (preference && !preference.isChannelEnabled(notification.type, 'web')) {
        return false; // 用户禁用了web通知
      }

      // 检查用户是否在线
      const socketId = this.connectedUsers.get(userId);
      if (!socketId) {
        console.log(`📱 User ${userId} is offline, notification will be stored`);
        return false;
      }

      // 发送通知到用户房间
      this.io.to(`user_${userId}`).emit('new_notification', {
        id: notification.id,
        type: notification.type,
        title: notification.title,
        content: notification.content,
        data: notification.data,
        priority: notification.priority,
        actionUrl: notification.actionUrl,
        imageUrl: notification.imageUrl,
        createdAt: notification.createdAt,
        timestamp: new Date().toISOString(),
      });

      // 更新未读数量
      this.sendUnreadCount(userId);

      console.log(`🔔 Notification sent to user ${userId}: ${notification.title}`);
      return true;
    } catch (error) {
      console.error('Error sending notification to user:', error);
      return false;
    }
  }

  // 发送通知给多个用户
  async sendNotificationToUsers(userIds, notification) {
    const results = [];
    for (const userId of userIds) {
      const sent = await this.sendNotificationToUser(userId, notification);
      results.push({ userId, sent });
    }
    return results;
  }

  // 广播通知给所有在线用户
  async broadcastNotification(notification, excludeUserIds = []) {
    try {
      const onlineUserIds = Array.from(this.connectedUsers.keys()).filter(
        (userId) => !excludeUserIds.includes(userId)
      );

      const results = await this.sendNotificationToUsers(onlineUserIds, notification);

      console.log(
        `📢 Broadcast notification sent to ${results.filter((r) => r.sent).length} users`
      );
      return results;
    } catch (error) {
      console.error('Error broadcasting notification:', error);
      return [];
    }
  }

  // 发送未读通知数量
  async sendUnreadCount(userId) {
    try {
      const count = await Notification.getUnreadCount(userId);
      const socketId = this.connectedUsers.get(userId);

      if (socketId) {
        this.io.to(`user_${userId}`).emit('unread_count_updated', {
          count,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error) {
      console.error('Error sending unread count:', error);
    }
  }

  // 发送系统消息
  sendSystemMessage(message, room = null) {
    const payload = {
      type: 'system',
      message,
      timestamp: new Date().toISOString(),
    };

    if (room) {
      this.io.to(room).emit('system_message', payload);
    } else {
      this.io.emit('system_message', payload);
    }
  }

  // 获取在线用户统计
  getOnlineStats() {
    return {
      totalConnections: this.io.sockets.sockets.size,
      authenticatedUsers: this.connectedUsers.size,
      rooms: Array.from(this.io.sockets.adapter.rooms.keys()),
    };
  }

  // 检查用户是否在线
  isUserOnline(userId) {
    return this.connectedUsers.has(userId);
  }

  // 获取用户的socket实例
  getUserSocket(userId) {
    const socketId = this.connectedUsers.get(userId);
    return socketId ? this.io.sockets.sockets.get(socketId) : null;
  }

  // 断开特定用户连接
  disconnectUser(userId, reason = 'Server disconnect') {
    const socket = this.getUserSocket(userId);
    if (socket) {
      socket.emit('force_disconnect', { reason });
      socket.disconnect(true);
    }
  }

  // 处理发送消息
  async handleSendMessage(socket, data) {
    try {
      const { receiverId, content, messageId } = data;
      const senderId = socket.userId;

      // 检查接收者是否在线
      const receiverSocket = this.getUserSocket(receiverId);
      
      if (receiverSocket) {
        // 实时发送给接收者
        receiverSocket.emit('new_message', {
          id: messageId,
          content,
          senderId,
          receiverId,
          createdAt: new Date().toISOString(),
          isRead: false,
          sender: {
            id: senderId,
            username: socket.user.username,
            avatar: socket.user.avatar
          }
        });
      }

      // 确认发送成功
      socket.emit('message_sent', { messageId });
      
    } catch (error) {
      console.error('发送消息错误:', error);
      socket.emit('error', { message: '发送失败' });
    }
  }

  // 处理开始输入
  handleTypingStart(socket, data) {
    const { receiverId } = data;
    const senderId = socket.userId;
    
    const receiverSocket = this.getUserSocket(receiverId);
    if (receiverSocket) {
      receiverSocket.emit('user_typing', {
        userId: senderId,
        isTyping: true
      });
    }

    // 设置输入状态超时
    const key = `${senderId}-${receiverId}`;
    if (this.typingUsers.has(key)) {
      clearTimeout(this.typingUsers.get(key));
    }
    
    // 3秒后自动停止输入状态
    const timeout = setTimeout(() => {
      if (receiverSocket) {
        receiverSocket.emit('user_typing', {
          userId: senderId,
          isTyping: false
        });
      }
      this.typingUsers.delete(key);
    }, 3000);
    
    this.typingUsers.set(key, timeout);
  }

  // 处理停止输入
  handleTypingStop(socket, data) {
    const { receiverId } = data;
    const senderId = socket.userId;
    
    const receiverSocket = this.getUserSocket(receiverId);
    if (receiverSocket) {
      receiverSocket.emit('user_typing', {
        userId: senderId,
        isTyping: false
      });
    }

    // 清除输入状态
    const key = `${senderId}-${receiverId}`;
    if (this.typingUsers.has(key)) {
      clearTimeout(this.typingUsers.get(key));
      this.typingUsers.delete(key);
    }
  }

  // 处理标记消息已读
  async handleMarkMessageRead(socket, data) {
    try {
      const { messageId } = data;
      const userId = socket.userId;

      // 更新数据库中的消息状态
      await Message.update(
        { isRead: true, readAt: new Date() },
        { where: { id: messageId, receiverId: userId } }
      );

      socket.emit('message_read_confirmed', { messageId });
      
    } catch (error) {
      console.error('标记消息已读错误:', error);
      socket.emit('error', { message: '操作失败' });
    }
  }
}

// 创建单例实例
const socketService = new SocketService();

module.exports = socketService;
