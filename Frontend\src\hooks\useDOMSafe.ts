import { useEffect, useRef, useCallback } from 'react';

/**
 * DOM安全操作Hook
 */
export const useDOMSafe = () => {
  const mountedRef = useRef(true);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const safeExecute = useCallback(<T>(operation: () => T, fallback?: T): T | undefined => {
    if (!mountedRef.current) return fallback;
    
    try {
      return operation();
    } catch (error) {
      console.warn('Safe DOM operation failed:', error);
      return fallback;
    }
  }, []);

  const safeSetState = useCallback(<T>(
    setState: React.Dispatch<React.SetStateAction<T>>,
    value: T | ((prev: T) => T)
  ) => {
    if (mountedRef.current) {
      try {
        setState(value);
      } catch (error) {
        console.warn('Safe setState failed:', error);
      }
    }
  }, []);

  return {
    isMounted: () => mountedRef.current,
    safeExecute,
    safeSetState,
  };
};

/**
 * 安全的事件监听器Hook
 */
export const useSafeEventListener = (
  element: Element | Window | Document | null,
  event: string,
  handler: EventListener,
  options?: boolean | AddEventListenerOptions
) => {
  const savedHandler = useRef<EventListener>();
  const { isMounted } = useDOMSafe();

  useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    if (!element || !element.addEventListener) return;

    const eventListener = (e: Event) => {
      if (isMounted() && savedHandler.current) {
        try {
          savedHandler.current(e);
        } catch (error) {
          console.warn('Event handler error:', error);
        }
      }
    };

    element.addEventListener(event, eventListener, options);

    return () => {
      try {
        if (element && element.removeEventListener) {
          element.removeEventListener(event, eventListener, options);
        }
      } catch (error) {
        console.warn('Event listener cleanup error:', error);
      }
    };
  }, [element, event, options, isMounted]);
};

/**
 * 安全的定时器Hook
 */
export const useSafeTimer = () => {
  const timersRef = useRef<Set<NodeJS.Timeout>>(new Set());
  const { isMounted } = useDOMSafe();

  useEffect(() => {
    return () => {
      timersRef.current.forEach(timer => {
        try {
          clearTimeout(timer);
        } catch (error) {
          console.warn('Timer cleanup error:', error);
        }
      });
      timersRef.current.clear();
    };
  }, []);

  const safeSetTimeout = useCallback((callback: () => void, delay: number) => {
    const timer = setTimeout(() => {
      if (isMounted()) {
        try {
          callback();
        } catch (error) {
          console.warn('Timer callback error:', error);
        }
      }
      timersRef.current.delete(timer);
    }, delay);

    timersRef.current.add(timer);
    return timer;
  }, [isMounted]);

  const safeClearTimeout = useCallback((timer: NodeJS.Timeout) => {
    try {
      clearTimeout(timer);
      timersRef.current.delete(timer);
    } catch (error) {
      console.warn('Clear timeout error:', error);
    }
  }, []);

  return { safeSetTimeout, safeClearTimeout };
};