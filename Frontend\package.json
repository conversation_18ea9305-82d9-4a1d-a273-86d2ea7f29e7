{"name": "newzora-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@heroicons/react": "^2.0.18", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.51.0", "@tinymce/tinymce-react": "^6.2.1", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^14.2.30", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "socket.io-client": "^4.7.2", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.19.8", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/supertest": "^6.0.3", "eslint": "^8", "eslint-config-next": "14.2.5", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "supertest": "^7.1.3", "tailwindcss": "^4", "typescript": "5.8.3"}}