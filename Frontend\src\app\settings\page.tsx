'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import Header from '@/components/Header';
import Avatar from '@/components/Avatar';
import Link from 'next/link';

export default function AccountSettingsPage() {
  const router = useRouter();
  const { isAuthenticated, user, isLoading: authLoading } = useSimpleAuth();
  const [formData, setFormData] = useState({
    nickname: '',
    bio: '',
    email: '<EMAIL>',
    avatar: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [hasChanges, setHasChanges] = useState(false);
  const [avatarLoading, setAvatarLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showBlockedUsersModal, setShowBlockedUsersModal] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [privacySettings, setPrivacySettings] = useState({
    contentVisibility: 'public',
    allowMessages: true,
    showOnlineStatus: true,
    allowFollows: true
  });
  const [blockedUsers, setBlockedUsers] = useState<any[]>([]);

  // 认证检查
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, authLoading, router]);

  // 如果正在加载认证状态或未认证，显示加载状态
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Account Settings</h2>
          <p className="text-gray-600 mb-6">Please log in to access your account settings</p>
          <Link
            href="/auth/login"
            className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Login Now
          </Link>
        </div>
      </div>
    );
  }

  // 验证函数
  const validateNickname = (nickname: string) => {
    if (nickname.trim().length > 50) {
      return 'Nickname must be less than 50 characters';
    }
    return '';
  };

  const validateBio = (bio: string) => {
    if (bio.trim().length > 500) {
      return 'Bio must be less than 500 characters';
    }
    return '';
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // 标记有变更
    setHasChanges(true);

    // 实时验证
    let fieldError = '';
    if (name === 'nickname') {
      fieldError = validateNickname(value);
    } else if (name === 'bio') {
      fieldError = validateBio(value);
    }

    setErrors((prev) => ({
      ...prev,
      [name]: fieldError,
    }));

    // 如果之前保存成功，现在有变更则重置保存状态
    if (isSaved) {
      setIsSaved(false);
    }
  };

  const handleSaveProfile = async () => {
    // 验证所有字段
    const nicknameError = validateNickname(formData.nickname);
    const bioError = validateBio(formData.bio);

    const newErrors = {
      nickname: nicknameError,
      bio: bioError,
    };

    setErrors(newErrors);

    // 如果有验证错误，不保存
    if (nicknameError || bioError) {
      return;
    }

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsSaved(true);
      setHasChanges(false);

      // 3秒后自动恢复按钮状态
      setTimeout(() => setIsSaved(false), 3000);
    } catch (error) {
      console.error('Failed to save profile:', error);
      setErrors({ submit: 'Failed to save profile. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    // Handle logout logic
    router.push('/');
  };

  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      setErrors({ avatar: 'Please select a valid image file' });
      return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
      setErrors({ avatar: 'Image size must be less than 5MB' });
      return;
    }

    setAvatarLoading(true);
    setErrors({ ...errors, avatar: '' });

    try {
      // 创建预览URL
      const previewUrl = URL.createObjectURL(file);

      // 模拟上传过程
      await new Promise(resolve => setTimeout(resolve, 1500));

      setFormData(prev => ({ ...prev, avatar: previewUrl }));
      setHasChanges(true);
    } catch (error) {
      setErrors({ avatar: 'Failed to upload image. Please try again.' });
    } finally {
      setAvatarLoading(false);
    }
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handlePasswordChange = () => {
    setShowPasswordModal(true);
  };
  
  const handlePasswordSubmit = async () => {
    if (!passwordData.currentPassword || !passwordData.newPassword || !passwordData.confirmPassword) {
      setErrors({ password: 'All password fields are required' });
      return;
    }
    
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setErrors({ password: 'New passwords do not match' });
      return;
    }
    
    if (passwordData.newPassword.length < 8) {
      setErrors({ password: 'New password must be at least 8 characters long' });
      return;
    }
    
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPasswordData({ currentPassword: '', newPassword: '', confirmPassword: '' });
      setShowPasswordModal(false);
      setErrors({});
      
      // Show success message
      alert('Password changed successfully!');
    } catch (error) {
      setErrors({ password: 'Failed to change password. Please try again.' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentVisibility = () => {
    setShowPrivacyModal(true);
  };
  
  const handlePrivacySubmit = async () => {
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setShowPrivacyModal(false);
      alert('Privacy settings updated successfully!');
    } catch (error) {
      alert('Failed to update privacy settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBlockedUsers = () => {
    setShowBlockedUsersModal(true);
    // Load blocked users (simulate API call)
    setBlockedUsers([
      { id: 1, name: 'John Doe', username: 'johndoe', avatar: 'https://ui-avatars.com/api/?name=John+Doe&background=6366f1&color=fff&size=48', blockedAt: '2024-01-15' },
      { id: 2, name: 'Jane Smith', username: 'janesmith', avatar: 'https://ui-avatars.com/api/?name=Jane+Smith&background=6366f1&color=fff&size=48', blockedAt: '2024-01-10' }
    ]);
  };
  
  const handleUnblockUser = async (userId: number) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setBlockedUsers(prev => prev.filter(user => user.id !== userId));
      alert('User unblocked successfully!');
    } catch (error) {
      alert('Failed to unblock user. Please try again.');
    }
  };

  const handleEarningsOverview = () => {
    router.push('/earnings');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Title */}
        <div className="mb-12">
          <h1 className="text-4xl font-bold text-gray-900">Account Settings</h1>
        </div>

        <div className="space-y-12">
          {/* Profile Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Profile</h2>

            {/* Profile Photo */}
            <div className="flex items-center space-x-4 mb-8">
              <div className="relative">
                {avatarLoading ? (
                  <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  <Avatar
                    src={formData.avatar || user?.avatar_url || user?.avatar}
                    alt={user?.username || user?.name || 'Profile'}
                    size="lg"
                    className="cursor-pointer hover:opacity-80 transition-opacity"
                    onClick={handleAvatarClick}
                  />
                )}
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Profile Photo</h3>
                <button
                  onClick={handleAvatarClick}
                  disabled={avatarLoading}
                  className="text-blue-500 hover:text-blue-600 transition-colors disabled:opacity-50"
                >
                  {avatarLoading ? 'Uploading...' : 'Change your profile photo'}
                </button>
                {errors.avatar && (
                  <p className="text-red-500 text-sm mt-1">{errors.avatar}</p>
                )}
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleAvatarChange}
                className="hidden"
              />
            </div>

            {/* Nickname */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-3">
                <label htmlFor="nickname" className="block text-lg font-semibold text-gray-900">
                  Nickname
                </label>
                <span
                  className={`text-sm ${
                    formData.nickname.length > 50
                      ? 'text-red-500'
                      : formData.nickname.length > 40
                        ? 'text-yellow-500'
                        : 'text-gray-500'
                  }`}
                >
                  {formData.nickname.length}/50
                </span>
              </div>
              <input
                type="text"
                id="nickname"
                name="nickname"
                value={formData.nickname}
                onChange={handleInputChange}
                maxLength={50}
                placeholder="Enter your nickname"
                className={`w-full px-4 py-3 border rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/40 transition-all duration-200 ${
                  errors.nickname
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              />
              {errors.nickname && <p className="mt-2 text-sm text-red-600">{errors.nickname}</p>}
            </div>

            {/* Bio */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-3">
                <label htmlFor="bio" className="block text-lg font-semibold text-gray-900">
                  Bio
                </label>
                <span
                  className={`text-sm ${
                    formData.bio.length > 500
                      ? 'text-red-500'
                      : formData.bio.length > 400
                        ? 'text-yellow-500'
                        : 'text-gray-500'
                  }`}
                >
                  {formData.bio.length}/500
                </span>
              </div>
              <textarea
                id="bio"
                name="bio"
                rows={6}
                value={formData.bio}
                onChange={handleInputChange}
                maxLength={500}
                placeholder="Tell us about yourself..."
                className={`w-full px-4 py-3 border rounded-lg bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/40 transition-all duration-200 resize-none ${
                  errors.bio
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500/20'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              />
              {errors.bio && <p className="mt-2 text-sm text-red-600">{errors.bio}</p>}
            </div>

            {/* Error Message */}
            {errors.submit && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <svg
                    className="w-5 h-5 text-red-500 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <p className="text-red-700 font-medium">{errors.submit}</p>
                </div>
              </div>
            )}

            {/* Save Profile Button */}
            <div className="flex justify-between items-center">
              {hasChanges && !isSaved && (
                <p className="text-sm text-yellow-600">You have unsaved changes</p>
              )}
              <div className="ml-auto">
                <button
                  onClick={handleSaveProfile}
                  disabled={isLoading || !hasChanges}
                  className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                    isSaved
                      ? 'bg-green-500 text-white'
                      : isLoading
                        ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        : !hasChanges
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-blue-600 text-white hover:bg-blue-700 transform hover:scale-105'
                  }`}
                >
                  {isSaved ? (
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Saved!</span>
                    </div>
                  ) : isLoading ? (
                    <div className="flex items-center space-x-2">
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </div>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              </div>
            </div>
          </section>

          {/* Account Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Account</h2>

            {/* Email */}
            <div className="flex items-center justify-between py-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Email</h3>
                <p className="text-blue-500">Linked to your email</p>
              </div>
              <span className="text-gray-900 font-medium">{formData.email}</span>
            </div>

            {/* Password */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Password</h3>
                <p className="text-blue-500">Change your password</p>
              </div>
              <button
                onClick={handlePasswordChange}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Change
              </button>
            </div>
          </section>

          {/* Privacy Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Privacy</h2>

            {/* Content Visibility */}
            <div className="flex items-center justify-between py-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Content Visibility</h3>
                <p className="text-blue-500">Control who can see your content</p>
              </div>
              <button
                onClick={handleContentVisibility}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Manage
              </button>
            </div>

            {/* Blocked Users */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Blocked Users</h3>
                <p className="text-blue-500">Manage your blocked users</p>
              </div>
              <button
                onClick={handleBlockedUsers}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                View
              </button>
            </div>
          </section>

          {/* Notifications Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Notifications</h2>

            {/* Notification Settings */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Notification Settings</h3>
                <p className="text-blue-500">Customize your notification preferences</p>
              </div>
              <button
                onClick={() => router.push('/notifications')}
                className="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                Edit
              </button>
            </div>
          </section>

          {/* Verification Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Verification</h2>

            {/* Account Verification */}
            <div className="flex items-center justify-between py-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Account Verification</h3>
                <p className="text-blue-500">Complete verification to enable withdrawals</p>
              </div>
              <button
                onClick={() => router.push('/settings/verification')}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                Verify
              </button>
            </div>
          </section>

          {/* Earnings Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Earnings</h2>

            {/* Earnings Overview */}
            <div className="flex items-center justify-between py-4 border-b border-gray-200">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Earnings Overview</h3>
                <p className="text-blue-500">View your earnings and payment details</p>
              </div>
              <button
                onClick={handleEarningsOverview}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors"
              >
                View
              </button>
            </div>
            
            {/* Advertising */}
            <div className="flex items-center justify-between py-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Advertising</h3>
                <p className="text-blue-500">Manage your ad campaigns and promotions</p>
              </div>
              <button
                onClick={() => router.push('/ads')}
                className="px-6 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
              >
                Manage Ads
              </button>
            </div>
          </section>

          {/* Other Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Other</h2>

            {/* Log Out */}
            <div className="flex items-center justify-between py-4">
              <h3 className="text-lg font-semibold text-gray-900">Log Out</h3>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M17 8l4 4m0 0l-4 4m4-4H3"
                  />
                </svg>
              </button>
            </div>
          </section>
        </div>
      </main>
      
      {/* Password Change Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Change Password</h3>
              <button
                onClick={() => setShowPasswordModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                <input
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, currentPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                <input
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, newPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                <input
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => setPasswordData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              
              {errors.password && (
                <p className="text-red-500 text-sm">{errors.password}</p>
              )}
              
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => setShowPasswordModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePasswordSubmit}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? 'Changing...' : 'Change Password'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Privacy Settings Modal */}
      {showPrivacyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Privacy Settings</h3>
              <button
                onClick={() => setShowPrivacyModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Content Visibility</label>
                <select
                  value={privacySettings.contentVisibility}
                  onChange={(e) => setPrivacySettings(prev => ({ ...prev, contentVisibility: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="public">Public - Everyone can see</option>
                  <option value="followers">Followers Only</option>
                  <option value="private">Private - Only me</option>
                </select>
              </div>
              
              <div className="space-y-3">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={privacySettings.allowMessages}
                    onChange={(e) => setPrivacySettings(prev => ({ ...prev, allowMessages: e.target.checked }))}
                    className="mr-3 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Allow direct messages</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={privacySettings.showOnlineStatus}
                    onChange={(e) => setPrivacySettings(prev => ({ ...prev, showOnlineStatus: e.target.checked }))}
                    className="mr-3 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Show online status</span>
                </label>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={privacySettings.allowFollows}
                    onChange={(e) => setPrivacySettings(prev => ({ ...prev, allowFollows: e.target.checked }))}
                    className="mr-3 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Allow new followers</span>
                </label>
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => setShowPrivacyModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                >
                  Cancel
                </button>
                <button
                  onClick={handlePrivacySubmit}
                  disabled={isLoading}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isLoading ? 'Saving...' : 'Save Settings'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Blocked Users Modal */}
      {showBlockedUsersModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Blocked Users</h3>
              <button
                onClick={() => setShowBlockedUsersModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            <div className="max-h-96 overflow-y-auto">
              {blockedUsers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <p>No blocked users</p>
                  <p className="text-sm mt-2">You can block users from their profile pages or comments</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {blockedUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="w-10 h-10 rounded-full"
                        />
                        <div>
                          <p className="font-medium text-gray-900">{user.name}</p>
                          <p className="text-sm text-gray-500">@{user.username}</p>
                          <p className="text-xs text-gray-400">Blocked on {user.blockedAt}</p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleUnblockUser(user.id)}
                        className="px-3 py-1 text-sm bg-red-100 text-red-600 rounded hover:bg-red-200"
                      >
                        Unblock
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
            
            <div className="pt-4">
              <button
                onClick={() => setShowBlockedUsersModal(false)}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
