const express = require('express');
const router = express.Router();
const { ContentReview, Article, User } = require('../models');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { Op } = require('sequelize');

// 获取待审核内容列表
router.get(
  '/pending',
  authenticateToken,
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('contentType')
      .optional()
      .isIn(['article', 'draft', 'comment', 'media_file', 'user_profile']),
    query('priority').optional().isIn(['low', 'normal', 'high', 'urgent']),
    query('riskLevel').optional().isIn(['low', 'medium', 'high', 'critical']),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { page = 1, limit = 20, contentType, priority, riskLevel } = req.query;

      const whereClause = {
        status: ['pending', 'in_review'],
      };

      if (contentType) whereClause.contentType = contentType;
      if (priority) whereClause.priority = priority;
      if (riskLevel) whereClause.riskLevel = riskLevel;

      const { count, rows: reviews } = await ContentReview.findAndCountAll({
        where: whereClause,
        order: [
          ['priority', 'DESC'],
          ['createdAt', 'ASC'],
        ],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email', 'avatar'],
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email', 'avatar'],
            required: false,
          },
        ],
      });

      res.json({
        success: true,
        data: {
          reviews,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / parseInt(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Error fetching pending reviews:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 获取单个审核详情
router.get('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const review = await ContentReview.findByPk(id, {
      include: [
        {
          model: User,
          as: 'submitter',
          attributes: ['id', 'username', 'email', 'avatar'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'username', 'email', 'avatar'],
          required: false,
        },
      ],
    });

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found',
      });
    }

    // 获取相关内容
    let content = null;
    try {
      switch (review.contentType) {
        case 'draft':
          content = await Draft.findByPk(review.contentId);
          break;
        case 'article':
          content = await Article.findByPk(review.contentId);
          break;
        case 'comment':
          content = await Comment.findByPk(review.contentId);
          break;
        case 'media_file':
          content = await MediaFile.findByPk(review.contentId);
          break;
      }
    } catch (error) {
      console.error('Error fetching content:', error);
    }

    res.json({
      success: true,
      data: {
        review,
        content,
      },
    });
  } catch (error) {
    console.error('Error fetching review:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// 开始审核
router.post(
  '/:id/start',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const { id } = req.params;

      const review = await ContentReview.findByPk(id);
      if (!review) {
        return res.status(404).json({
          success: false,
          message: 'Review not found',
        });
      }

      if (review.status !== 'pending') {
        return res.status(400).json({
          success: false,
          message: 'Review is not in pending status',
        });
      }

      await review.startReview(req.user.id);

      res.json({
        success: true,
        data: { review },
        message: 'Review started successfully',
      });
    } catch (error) {
      console.error('Error starting review:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 批准内容
router.post(
  '/:id/approve',
  authenticateToken,
  requireAdmin,
  [body('notes').optional().isLength({ max: 1000 })],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { notes } = req.body;

      const review = await ContentReview.findByPk(id);
      if (!review) {
        return res.status(404).json({
          success: false,
          message: 'Review not found',
        });
      }

      if (review.status !== 'in_review') {
        return res.status(400).json({
          success: false,
          message: 'Review is not in review status',
        });
      }

      await review.approve(req.user.id, notes);

      // 更新相关内容状态
      await updateContentStatus(review.contentType, review.contentId, 'approved');

      res.json({
        success: true,
        data: { review },
        message: 'Content approved successfully',
      });
    } catch (error) {
      console.error('Error approving content:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 拒绝内容
router.post(
  '/:id/reject',
  authenticateToken,
  requireAdmin,
  [
    body('reason').notEmpty().isLength({ min: 10, max: 1000 }),
    body('suggestions').optional().isLength({ max: 1000 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { reason, suggestions } = req.body;

      const review = await ContentReview.findByPk(id);
      if (!review) {
        return res.status(404).json({
          success: false,
          message: 'Review not found',
        });
      }

      if (review.status !== 'in_review') {
        return res.status(400).json({
          success: false,
          message: 'Review is not in review status',
        });
      }

      await review.reject(req.user.id, reason, suggestions);

      // 更新相关内容状态
      await updateContentStatus(review.contentType, review.contentId, 'rejected');

      res.json({
        success: true,
        data: { review },
        message: 'Content rejected successfully',
      });
    } catch (error) {
      console.error('Error rejecting content:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 升级审核
router.post(
  '/:id/escalate',
  authenticateToken,
  requireAdmin,
  [body('reason').notEmpty().isLength({ min: 10, max: 500 })],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const { reason } = req.body;

      const review = await ContentReview.findByPk(id);
      if (!review) {
        return res.status(404).json({
          success: false,
          message: 'Review not found',
        });
      }

      await review.escalate(reason);

      res.json({
        success: true,
        data: { review },
        message: 'Review escalated successfully',
      });
    } catch (error) {
      console.error('Error escalating review:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 辅助函数：更新内容状态
async function updateContentStatus(contentType, contentId, status) {
  try {
    switch (contentType) {
      case 'draft':
        await Draft.update({ status }, { where: { id: contentId } });
        break;
      case 'article':
        // 文章状态更新逻辑
        break;
      case 'comment':
        // 评论状态更新逻辑
        break;
      case 'media_file':
        await MediaFile.update({ status }, { where: { id: contentId } });
        break;
    }
  } catch (error) {
    console.error('Error updating content status:', error);
  }
}

// 提交内容审核
router.post(
  '/submit',
  authenticateToken,
  [
    body('contentType').isIn(['article', 'draft', 'comment', 'media_file', 'user_profile']),
    body('contentId').isInt({ min: 1 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { contentType, contentId } = req.body;

      // 检查是否已存在审核记录
      const existingReview = await ContentReview.findOne({
        where: {
          contentType,
          contentId,
          status: ['pending', 'in_review'],
        },
      });

      if (existingReview) {
        return res.status(400).json({
          success: false,
          message: 'Content is already under review',
        });
      }

      // 获取内容进行AI预审核
      let content = '';
      let metadata = {};

      try {
        switch (contentType) {
          case 'draft':
            const draft = await Draft.findByPk(contentId);
            if (!draft || draft.authorId !== req.user.id) {
              return res.status(404).json({
                success: false,
                message: 'Draft not found or access denied',
              });
            }
            content = `${draft.title}\n${draft.content}`;
            metadata = { user: req.user };
            break;
          case 'article':
            const article = await Article.findByPk(contentId);
            if (!article) {
              return res.status(404).json({
                success: false,
                message: 'Article not found',
              });
            }
            content = `${article.title}\n${article.content}`;
            break;
          // 其他内容类型...
        }
      } catch (error) {
        return res.status(404).json({
          success: false,
          message: 'Content not found',
        });
      }

      // 运行审核规则
      const triggeredRules = await ReviewRule.evaluateContent(content, contentType, metadata);

      // 确定风险等级和优先级
      let riskLevel = 'low';
      let priority = 'normal';
      let autoApproved = false;

      if (triggeredRules.length === 0) {
        // 没有触发任何规则，可以自动通过
        autoApproved = true;
        riskLevel = 'low';
        priority = 'low';
      } else {
        // 根据触发的规则确定风险等级
        const maxSeverity = Math.max(
          ...triggeredRules.map((r) => {
            switch (r.severity) {
              case 'critical':
                return 4;
              case 'error':
                return 3;
              case 'warning':
                return 2;
              case 'info':
                return 1;
              default:
                return 0;
            }
          })
        );

        switch (maxSeverity) {
          case 4:
            riskLevel = 'critical';
            priority = 'urgent';
            break;
          case 3:
            riskLevel = 'high';
            priority = 'high';
            break;
          case 2:
            riskLevel = 'medium';
            priority = 'normal';
            break;
          default:
            riskLevel = 'low';
            priority = 'low';
        }
      }

      // 创建审核记录
      const review = await ContentReview.create({
        contentType,
        contentId,
        submitterId: req.user.id,
        status: autoApproved ? 'approved' : 'pending',
        priority,
        riskLevel,
        autoApproved,
        aiFlags: triggeredRules.map((r) => r.rule.name),
        reviewCompletedAt: autoApproved ? new Date() : null,
      });

      if (autoApproved) {
        await updateContentStatus(contentType, contentId, 'approved');
      }

      res.status(201).json({
        success: true,
        data: {
          review,
          autoApproved,
          triggeredRules: triggeredRules.length,
        },
        message: autoApproved ? 'Content auto-approved' : 'Content submitted for review',
      });
    } catch (error) {
      console.error('Error submitting content for review:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 获取审核统计
router.get(
  '/stats/overview',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      // 状态统计
      const statusStats = await ContentReview.findAll({
        attributes: ['status', [ContentReview.sequelize.fn('COUNT', '*'), 'count']],
        group: ['status'],
        raw: true,
      });

      // 内容类型统计
      const typeStats = await ContentReview.findAll({
        attributes: ['contentType', [ContentReview.sequelize.fn('COUNT', '*'), 'count']],
        group: ['contentType'],
        raw: true,
      });

      // 风险等级统计
      const riskStats = await ContentReview.findAll({
        attributes: ['riskLevel', [ContentReview.sequelize.fn('COUNT', '*'), 'count']],
        group: ['riskLevel'],
        raw: true,
      });

      // 审核员工作量统计
      const reviewerStats = await ContentReview.findAll({
        where: {
          reviewerId: { [Op.not]: null },
          reviewCompletedAt: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 最近30天
          },
        },
        attributes: [
          'reviewerId',
          [ContentReview.sequelize.fn('COUNT', '*'), 'reviewCount'],
          [
            ContentReview.sequelize.fn(
              'AVG',
              ContentReview.sequelize.literal(
                'EXTRACT(EPOCH FROM ("reviewCompletedAt" - "reviewStartedAt"))/60'
              )
            ),
            'avgReviewTime',
          ],
        ],
        group: ['reviewerId'],
        include: [
          {
            model: User,
            as: 'reviewer',
            attributes: ['username'],
          },
        ],
        raw: true,
      });

      res.json({
        success: true,
        data: {
          statusStats,
          typeStats,
          riskStats,
          reviewerStats,
        },
      });
    } catch (error) {
      console.error('Error fetching review stats:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 获取我的审核历史
router.get(
  '/my-reviews',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status')
      .optional()
      .isIn(['pending', 'in_review', 'approved', 'rejected', 'needs_revision', 'escalated']),
  ],
  async (req, res) => {
    try {
      const { page = 1, limit = 20, status } = req.query;

      const whereClause = {
        submitterId: req.user.id,
      };

      if (status) {
        whereClause.status = status;
      }

      const { count, rows: reviews } = await ContentReview.findAndCountAll({
        where: whereClause,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        include: [
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username'],
            required: false,
          },
        ],
      });

      res.json({
        success: true,
        data: {
          reviews,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / parseInt(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Error fetching user reviews:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

module.exports = router;
