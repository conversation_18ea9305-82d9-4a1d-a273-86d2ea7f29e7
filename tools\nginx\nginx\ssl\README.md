# SSL 证书配置

## 开发环境

对于开发环境，您可以使用自签名证书：

```bash
# 生成自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout privkey.pem \
    -out fullchain.pem \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=OneNews/CN=localhost"
```

## 生产环境

### 使用 Let's Encrypt

1. 安装 Certbot：
```bash
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx
```

2. 获取证书：
```bash
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

3. 自动续期：
```bash
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 手动配置证书

将您的证书文件放置在此目录：
- `fullchain.pem` - 完整证书链
- `privkey.pem` - 私钥文件

确保文件权限正确：
```bash
chmod 600 privkey.pem
chmod 644 fullchain.pem
```
