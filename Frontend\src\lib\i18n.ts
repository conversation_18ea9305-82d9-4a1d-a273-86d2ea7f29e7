'use client';

// 语言配置
export const SUPPORTED_LANGUAGES = {
  'zh-CN': { name: '简体中文', flag: '🇨🇳' },
  'zh-TW': { name: '繁體中文', flag: '🇹🇼' },
  'en': { name: 'English', flag: '🇺🇸' },
  'ja': { name: '日本語', flag: '🇯🇵' },
  'ko': { name: '한국어', flag: '🇰🇷' },
  'es': { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', flag: '🇪🇸' },
  'fr': { name: 'Français', flag: '🇫🇷' },
  'de': { name: '<PERSON>uts<PERSON>', flag: '🇩🇪' },
  'ru': { name: 'Русский', flag: '🇷🇺' },
  'ar': { name: 'العربية', flag: '🇸🇦' }
};

// 翻译文本映射
const translations: Record<string, Record<string, string>> = {
  'zh-CN': {
    // 通用
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.cancel': '取消',
    'common.confirm': '确认',
    'common.save': '保存',
    'common.edit': '编辑',
    'common.delete': '删除',
    'common.search': '搜索',
    'common.more': '更多',
    'common.back': '返回',
    'common.next': '下一步',
    'common.previous': '上一步',
    'common.submit': '提交',
    'common.close': '关闭',
    
    // 导航
    'nav.home': '首页',
    'nav.explore': '发现',
    'nav.create': '创作',
    'nav.profile': '个人资料',
    'nav.settings': '设置',
    'nav.notifications': '通知',
    'nav.messages': '消息',
    'nav.analytics': '分析',
    'nav.earnings': '收益',
    
    // 认证
    'auth.login': '登录',
    'auth.register': '注册',
    'auth.logout': '退出登录',
    'auth.email': '邮箱',
    'auth.password': '密码',
    'auth.confirmPassword': '确认密码',
    'auth.username': '用户名',
    'auth.forgotPassword': '忘记密码？',
    'auth.rememberMe': '记住我',
    'auth.loginSuccess': '登录成功',
    'auth.registerSuccess': '注册成功',
    'auth.invalidCredentials': '用户名或密码错误',
    
    // 内容
    'content.title': '标题',
    'content.content': '内容',
    'content.publish': '发布',
    'content.draft': '草稿',
    'content.tags': '标签',
    'content.category': '分类',
    'content.views': '浏览量',
    'content.likes': '点赞',
    'content.comments': '评论',
    'content.shares': '分享',
    'content.readMore': '阅读更多',
    'content.noContent': '暂无内容',
    
    // 用户
    'user.profile': '个人资料',
    'user.followers': '关注者',
    'user.following': '关注中',
    'user.posts': '作品',
    'user.bio': '个人简介',
    'user.location': '位置',
    'user.website': '网站',
    'user.joinDate': '加入时间',
    'user.follow': '关注',
    'user.unfollow': '取消关注',
    'user.message': '私信',
    
    // 通知
    'notification.new': '新通知',
    'notification.markAllRead': '全部已读',
    'notification.noNotifications': '暂无通知',
    'notification.like': '点赞了你的作品',
    'notification.comment': '评论了你的作品',
    'notification.follow': '关注了你',
    'notification.message': '给你发送了消息',
    
    // 设置
    'settings.general': '通用设置',
    'settings.privacy': '隐私设置',
    'settings.notifications': '通知设置',
    'settings.language': '语言',
    'settings.theme': '主题',
    'settings.account': '账户设置',
    'settings.security': '安全设置',
    
    // 错误消息
    'error.networkError': '网络连接错误',
    'error.serverError': '服务器错误',
    'error.notFound': '页面未找到',
    'error.unauthorized': '未授权访问',
    'error.forbidden': '访问被禁止',
    'error.validationError': '输入验证失败',
    
    // 时间
    'time.now': '刚刚',
    'time.minutesAgo': '分钟前',
    'time.hoursAgo': '小时前',
    'time.daysAgo': '天前',
    'time.weeksAgo': '周前',
    'time.monthsAgo': '个月前',
    'time.yearsAgo': '年前'
  },
  
  'en': {
    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.search': 'Search',
    'common.more': 'More',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.submit': 'Submit',
    'common.close': 'Close',
    
    // Navigation
    'nav.home': 'Home',
    'nav.explore': 'Explore',
    'nav.create': 'Create',
    'nav.profile': 'Profile',
    'nav.settings': 'Settings',
    'nav.notifications': 'Notifications',
    'nav.messages': 'Messages',
    'nav.analytics': 'Analytics',
    'nav.earnings': 'Earnings',
    
    // Authentication
    'auth.login': 'Login',
    'auth.register': 'Register',
    'auth.logout': 'Logout',
    'auth.email': 'Email',
    'auth.password': 'Password',
    'auth.confirmPassword': 'Confirm Password',
    'auth.username': 'Username',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.rememberMe': 'Remember Me',
    'auth.loginSuccess': 'Login successful',
    'auth.registerSuccess': 'Registration successful',
    'auth.invalidCredentials': 'Invalid username or password',
    
    // Content
    'content.title': 'Title',
    'content.content': 'Content',
    'content.publish': 'Publish',
    'content.draft': 'Draft',
    'content.tags': 'Tags',
    'content.category': 'Category',
    'content.views': 'Views',
    'content.likes': 'Likes',
    'content.comments': 'Comments',
    'content.shares': 'Shares',
    'content.readMore': 'Read More',
    'content.noContent': 'No content available',
    
    // User
    'user.profile': 'Profile',
    'user.followers': 'Followers',
    'user.following': 'Following',
    'user.posts': 'Posts',
    'user.bio': 'Bio',
    'user.location': 'Location',
    'user.website': 'Website',
    'user.joinDate': 'Joined',
    'user.follow': 'Follow',
    'user.unfollow': 'Unfollow',
    'user.message': 'Message',
    
    // Notifications
    'notification.new': 'New Notification',
    'notification.markAllRead': 'Mark All Read',
    'notification.noNotifications': 'No notifications',
    'notification.like': 'liked your post',
    'notification.comment': 'commented on your post',
    'notification.follow': 'followed you',
    'notification.message': 'sent you a message',
    
    // Settings
    'settings.general': 'General',
    'settings.privacy': 'Privacy',
    'settings.notifications': 'Notifications',
    'settings.language': 'Language',
    'settings.theme': 'Theme',
    'settings.account': 'Account',
    'settings.security': 'Security',
    
    // Error messages
    'error.networkError': 'Network connection error',
    'error.serverError': 'Server error',
    'error.notFound': 'Page not found',
    'error.unauthorized': 'Unauthorized access',
    'error.forbidden': 'Access forbidden',
    'error.validationError': 'Validation failed',
    
    // Time
    'time.now': 'now',
    'time.minutesAgo': 'minutes ago',
    'time.hoursAgo': 'hours ago',
    'time.daysAgo': 'days ago',
    'time.weeksAgo': 'weeks ago',
    'time.monthsAgo': 'months ago',
    'time.yearsAgo': 'years ago'
  }
};

// 当前语言状态
let currentLanguage = 'zh-CN';

// 检测浏览器语言
export const detectBrowserLanguage = (): string => {
  if (typeof window === 'undefined') return 'zh-CN';
  
  const browserLang = navigator.language || navigator.languages?.[0] || 'zh-CN';
  
  // 精确匹配
  if (SUPPORTED_LANGUAGES[browserLang as keyof typeof SUPPORTED_LANGUAGES]) {
    return browserLang;
  }
  
  // 语言代码匹配（如 en-US -> en）
  const langCode = browserLang.split('-')[0];
  if (SUPPORTED_LANGUAGES[langCode as keyof typeof SUPPORTED_LANGUAGES]) {
    return langCode;
  }
  
  return 'zh-CN'; // 默认语言
};

// 初始化语言
export const initializeLanguage = (): string => {
  if (typeof window === 'undefined') return 'zh-CN';
  
  // 优先使用本地存储的语言设置
  const savedLanguage = localStorage.getItem('preferred_language');
  if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage as keyof typeof SUPPORTED_LANGUAGES]) {
    currentLanguage = savedLanguage;
    return savedLanguage;
  }
  
  // 其次使用浏览器语言
  const detectedLanguage = detectBrowserLanguage();
  currentLanguage = detectedLanguage;
  localStorage.setItem('preferred_language', detectedLanguage);
  
  return detectedLanguage;
};

// 设置语言
export const setLanguage = (language: string): void => {
  if (!SUPPORTED_LANGUAGES[language as keyof typeof SUPPORTED_LANGUAGES]) {
    console.warn(`Unsupported language: ${language}`);
    return;
  }
  
  currentLanguage = language;
  
  if (typeof window !== 'undefined') {
    localStorage.setItem('preferred_language', language);
    
    // 触发语言变更事件
    window.dispatchEvent(new CustomEvent('languageChanged', { 
      detail: { language } 
    }));
  }
};

// 获取当前语言
export const getCurrentLanguage = (): string => {
  return currentLanguage;
};

// 翻译函数
export const t = (key: string, params?: Record<string, string | number>): string => {
  const langTranslations = translations[currentLanguage] || translations['zh-CN'];
  let translation = langTranslations[key] || key;
  
  // 参数替换
  if (params) {
    Object.entries(params).forEach(([paramKey, paramValue]) => {
      translation = translation.replace(`{{${paramKey}}}`, String(paramValue));
    });
  }
  
  return translation;
};

// 格式化时间
export const formatTimeAgo = (date: Date | string): string => {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;
  const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return t('time.now');
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} ${t('time.minutesAgo')}`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} ${t('time.hoursAgo')}`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} ${t('time.daysAgo')}`;
  } else if (diffInSeconds < 2592000) {
    const weeks = Math.floor(diffInSeconds / 604800);
    return `${weeks} ${t('time.weeksAgo')}`;
  } else if (diffInSeconds < 31536000) {
    const months = Math.floor(diffInSeconds / 2592000);
    return `${months} ${t('time.monthsAgo')}`;
  } else {
    const years = Math.floor(diffInSeconds / 31536000);
    return `${years} ${t('time.yearsAgo')}`;
  }
};

// 格式化数字
export const formatNumber = (num: number): string => {
  const locale = currentLanguage === 'zh-CN' ? 'zh-CN' : 'en-US';
  
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + (currentLanguage === 'zh-CN' ? '万' : 'M');
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + (currentLanguage === 'zh-CN' ? '千' : 'K');
  }
  
  return num.toLocaleString(locale);
};

// React Hook for translations
export const useTranslation = () => {
  const [language, setCurrentLanguage] = React.useState(getCurrentLanguage());
  
  React.useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      setCurrentLanguage(event.detail.language);
    };
    
    window.addEventListener('languageChanged', handleLanguageChange as EventListener);
    
    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
    };
  }, []);
  
  return {
    t,
    language,
    setLanguage,
    formatTimeAgo,
    formatNumber,
    supportedLanguages: SUPPORTED_LANGUAGES
  };
};

// 初始化
if (typeof window !== 'undefined') {
  initializeLanguage();
}