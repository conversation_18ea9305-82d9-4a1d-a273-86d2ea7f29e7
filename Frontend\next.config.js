/** @type {import('next').NextConfig} */
const nextConfig = {
  // Keep React strict mode disabled to prevent DOM operation conflicts
  reactStrictMode: false,

  // Experimental features
  experimental: {
    // Next.js 14 enables App Router by default, no manual configuration needed
  },

  // TypeScript configuration
  typescript: {
    // Show errors in development environment, skip in production to ensure successful build
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },

  // ESLint configuration
  eslint: {
    // Show errors in development environment, skip in production to ensure successful build
    ignoreDuringBuilds: process.env.NODE_ENV === 'production',
  },

  // Webpack configuration for better error handling
  webpack: (config, { dev, isServer }) => {
    if (dev && !isServer) {
      // Add better error handling in development
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }
    return config;
  },

  // Compiler options
  compiler: {
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'ui-avatars.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'wdpprzemflzlardkmnfk.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
};

module.exports = nextConfig;
