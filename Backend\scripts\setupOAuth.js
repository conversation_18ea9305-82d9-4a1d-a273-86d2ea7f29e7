#!/usr/bin/env node

/**
 * OAuth配置向导
 * 帮助用户交互式配置Google和Facebook OAuth
 */

const readline = require('readline');
const fs = require('fs');
const path = require('path');

// 颜色输出函数
const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function displayWelcome() {
  console.log(colors.blue(colors.bold('\n🔐 OneNews OAuth社交登录配置向导\n')));
  console.log('此向导将帮助您配置Google和Facebook社交登录功能。');
  console.log('您需要先在相应的开发者平台创建应用并获取凭据。\n');
}

function displayGoogleInstructions() {
  console.log(colors.cyan('📋 Google OAuth配置步骤:'));
  console.log('1. 访问 https://console.cloud.google.com/');
  console.log('2. 创建新项目或选择现有项目');
  console.log('3. 启用Google+ API和People API');
  console.log('4. 配置OAuth同意屏幕');
  console.log('5. 创建OAuth客户端ID（Web应用类型）');
  console.log('6. 配置重定向URI:');
  console.log('   - 开发环境: http://localhost:3000/auth/google/callback');
  console.log('   - 生产环境: https://your-domain.com/auth/google/callback');
  console.log('7. 获取客户端ID和客户端密钥\n');
}

function displayFacebookInstructions() {
  console.log(colors.cyan('📋 Facebook OAuth配置步骤:'));
  console.log('1. 访问 https://developers.facebook.com/');
  console.log('2. 创建新应用（消费者类型）');
  console.log('3. 添加Facebook登录产品');
  console.log('4. 配置有效OAuth重定向URI:');
  console.log('   - 开发环境: http://localhost:3000/auth/facebook/callback');
  console.log('   - 生产环境: https://your-domain.com/auth/facebook/callback');
  console.log('5. 在基本设置中配置应用域名');
  console.log('6. 获取应用编号和应用密钥\n');
}

async function configureGoogle() {
  console.log(colors.yellow('\n🔵 配置Google OAuth'));
  displayGoogleInstructions();

  const configure = await question('是否要配置Google OAuth? (y/n): ');

  if (configure.toLowerCase() !== 'y') {
    return null;
  }

  const clientId = await question('请输入Google客户端ID: ');
  const clientSecret = await question('请输入Google客户端密钥: ');

  // 验证客户端ID格式
  if (!clientId.includes('.apps.googleusercontent.com')) {
    console.log(
      colors.yellow('⚠️ 警告: 客户端ID格式可能不正确，应该以.apps.googleusercontent.com结尾')
    );
  }

  return {
    GOOGLE_CLIENT_ID: clientId,
    GOOGLE_CLIENT_SECRET: clientSecret,
  };
}

async function configureFacebook() {
  console.log(colors.yellow('\n🔵 配置Facebook OAuth'));
  displayFacebookInstructions();

  const configure = await question('是否要配置Facebook OAuth? (y/n): ');

  if (configure.toLowerCase() !== 'y') {
    return null;
  }

  const appId = await question('请输入Facebook应用编号: ');
  const appSecret = await question('请输入Facebook应用密钥: ');

  // 验证应用ID格式
  if (!/^\d+$/.test(appId)) {
    console.log(colors.yellow('⚠️ 警告: Facebook应用编号应该是纯数字'));
  }

  return {
    FACEBOOK_APP_ID: appId,
    FACEBOOK_APP_SECRET: appSecret,
  };
}

async function configureFrontendUrl() {
  console.log(colors.yellow('\n🌐 配置前端URL'));
  console.log('前端URL用于构建OAuth回调地址。');
  console.log('开发环境通常是: http://localhost:3000');
  console.log('生产环境应该是: https://your-domain.com\n');

  const currentUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
  console.log(`当前配置: ${currentUrl}`);

  const change = await question('是否要修改前端URL? (y/n): ');

  if (change.toLowerCase() !== 'y') {
    return { FRONTEND_URL: currentUrl };
  }

  const newUrl = await question('请输入新的前端URL: ');

  // 验证URL格式
  try {
    new URL(newUrl);
  } catch {
    console.log(colors.red('❌ 无效的URL格式'));
    return null;
  }

  return { FRONTEND_URL: newUrl };
}

function updateEnvFile(config) {
  const envPath = path.join(__dirname, '../.env');
  let envContent = '';

  // 读取现有的.env文件
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }

  // 更新或添加OAuth配置
  Object.entries(config).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const line = `${key}=${value}`;

    if (regex.test(envContent)) {
      envContent = envContent.replace(regex, line);
    } else {
      envContent += `\n${line}`;
    }
  });

  // 写入文件
  fs.writeFileSync(envPath, envContent.trim() + '\n');
  console.log(colors.green(`\n✅ OAuth配置已保存到 ${envPath}`));
}

async function testConfiguration() {
  const test = await question('\n是否要测试OAuth配置? (y/n): ');

  if (test.toLowerCase() === 'y') {
    console.log(colors.yellow('\n🧪 正在测试OAuth配置...'));

    try {
      // 重新加载环境变量
      delete require.cache[require.resolve('dotenv')];
      require('dotenv').config();

      // 运行OAuth测试
      const { spawn } = require('child_process');
      const testProcess = spawn('node', ['scripts/testOAuth.js'], {
        stdio: 'inherit',
        cwd: process.cwd(),
      });

      testProcess.on('close', (code) => {
        if (code === 0) {
          console.log(colors.green('\n✅ OAuth配置测试完成'));
        } else {
          console.log(colors.red('\n❌ OAuth配置测试发现问题'));
        }
      });
    } catch (error) {
      console.log(colors.red(`❌ 测试失败: ${error.message}`));
    }
  }
}

function displayCallbackUrls(frontendUrl) {
  console.log(colors.cyan('\n🔗 请在OAuth应用中配置以下回调URL:'));
  console.log(`Google重定向URI: ${frontendUrl}/auth/google/callback`);
  console.log(`Facebook重定向URI: ${frontendUrl}/auth/facebook/callback`);
  console.log();
  console.log(colors.yellow('重要提醒:'));
  console.log('1. 确保在Google Cloud Console中添加上述Google重定向URI');
  console.log('2. 确保在Facebook开发者平台中添加上述Facebook重定向URI');
  console.log('3. 生产环境必须使用HTTPS');
  console.log('4. 重定向URI必须完全匹配（包括协议、域名、端口、路径）');
}

async function main() {
  try {
    displayWelcome();

    // 配置前端URL
    const frontendConfig = await configureFrontendUrl();
    if (!frontendConfig) {
      console.log(colors.red('前端URL配置失败，退出配置向导。'));
      return;
    }

    // 配置Google OAuth
    const googleConfig = await configureGoogle();

    // 配置Facebook OAuth
    const facebookConfig = await configureFacebook();

    // 合并配置
    const allConfig = {
      ...frontendConfig,
      ...(googleConfig || {}),
      ...(facebookConfig || {}),
    };

    if (Object.keys(allConfig).length === 1) {
      // 只有FRONTEND_URL
      console.log(colors.yellow('没有配置任何OAuth提供商，退出配置向导。'));
      return;
    }

    // 显示配置摘要
    console.log(colors.cyan('\n📝 配置摘要:'));
    Object.entries(allConfig).forEach(([key, value]) => {
      if (key.includes('SECRET') || key.includes('PASS')) {
        console.log(`   ${key}: ${'*'.repeat(value.length)}`);
      } else {
        console.log(`   ${key}: ${value}`);
      }
    });

    // 显示回调URL
    displayCallbackUrls(allConfig.FRONTEND_URL);

    const confirm = await question('确认保存此配置? (y/n): ');

    if (confirm.toLowerCase() === 'y') {
      updateEnvFile(allConfig);
      await testConfiguration();

      console.log(colors.green(colors.bold('\n🎉 OAuth配置完成！')));
      console.log('您现在可以使用Google和Facebook社交登录功能。');
      console.log(colors.cyan('\n下一步:'));
      console.log('1. 启动应用: npm start');
      console.log('2. 访问登录页面测试社交登录');
      console.log('3. 检查用户是否能成功通过社交账户登录');
    } else {
      console.log(colors.yellow('配置已取消。'));
    }
  } catch (error) {
    console.error(colors.red(`\n💥 配置过程中发生错误: ${error.message}`));
  } finally {
    rl.close();
  }
}

// 运行配置向导
main();
