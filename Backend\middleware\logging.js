/**
 * OneNews 日志中间件
 * 提供 HTTP 请求日志、错误日志、性能监控等功能
 */

const { logger, logRequest, logSecurityEvent, logPerformance } = require('../config/logger');

/**
 * HTTP 请求日志中间件
 * 记录所有 HTTP 请求的详细信息
 */
const requestLogger = (req, res, next) => {
  const startTime = Date.now();

  // 记录请求开始
  logger.info('HTTP Request Started', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    userId: req.user ? req.user.id : null,
  });

  // 监听响应完成
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;

    // 记录请求完成
    logRequest(req, res, responseTime);

    // 记录性能指标
    if (responseTime > 1000) {
      logPerformance('slow_request', responseTime, {
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
      });
    }

    // 记录错误响应
    if (res.statusCode >= 400) {
      logger.warn('HTTP Error Response', {
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
        responseTime: `${responseTime}ms`,
        ip: req.ip || req.connection.remoteAddress,
        userId: req.user ? req.user.id : null,
      });
    }
  });

  next();
};

/**
 * 错误日志中间件
 * 捕获和记录应用程序错误
 */
const errorLogger = (err, req, res, next) => {
  // 记录错误详情
  logger.error('Application Error', {
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query,
    },
    user: req.user
      ? {
          id: req.user.id,
          email: req.user.email,
        }
      : null,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString(),
  });

  // 如果是安全相关错误，记录到安全日志
  if (isSecurityError(err)) {
    logSecurityEvent(
      'application_error',
      {
        error: err.message,
        type: err.name,
        url: req.originalUrl,
      },
      req
    );
  }

  next(err);
};

/**
 * 安全事件日志中间件
 * 记录可疑的安全活动
 */
const securityLogger = (req, res, next) => {
  // 检测可疑的请求模式
  const suspiciousPatterns = [
    /\.\./, // 路径遍历
    /<script/i, // XSS 尝试
    /union.*select/i, // SQL 注入尝试
    /exec\(/i, // 代码执行尝试
    /eval\(/i, // 代码执行尝试
  ];

  const url = req.originalUrl;
  const body = JSON.stringify(req.body);

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(url) || pattern.test(body)) {
      logSecurityEvent(
        'suspicious_request',
        {
          pattern: pattern.toString(),
          url: url,
          body: body.length > 500 ? body.substring(0, 500) + '...' : body,
        },
        req
      );
      break;
    }
  }

  // 检测暴力破解尝试
  if (req.path.includes('/login') && req.method === 'POST') {
    const ip = req.ip || req.connection.remoteAddress;
    // 这里可以添加 IP 频率限制逻辑
    logger.info('Login Attempt', {
      ip: ip,
      email:
        (req.body && (req.body.email || req.body.username || req.body.identifier)) || 'unknown',
      userAgent: req.get('User-Agent'),
    });
  }

  next();
};

/**
 * 性能监控中间件
 * 监控应用程序性能指标
 */
const performanceMonitor = (req, res, next) => {
  const startTime = process.hrtime();
  const startMemory = process.memoryUsage();

  res.on('finish', () => {
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds * 1000 + nanoseconds / 1000000; // 转换为毫秒
    const endMemory = process.memoryUsage();

    // 记录性能指标
    logPerformance('request_duration', duration, {
      method: req.method,
      url: req.originalUrl,
      status: res.statusCode,
    });

    // 记录内存使用变化
    const memoryDiff = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
    };

    if (Math.abs(memoryDiff.heapUsed) > 10 * 1024 * 1024) {
      // 10MB 变化
      logPerformance('memory_change', memoryDiff.heapUsed, {
        method: req.method,
        url: req.originalUrl,
        memoryDiff,
      });
    }
  });

  next();
};

/**
 * 数据库查询日志中间件
 * 与 Sequelize 集成，记录数据库查询
 */
const setupDatabaseLogging = (sequelize) => {
  const { logDatabaseQuery } = require('../config/logger');

  // 重写 Sequelize 的日志函数
  const originalLogging = sequelize.options.logging;

  sequelize.options.logging = (sql, timing) => {
    // 记录到我们的日志系统
    logDatabaseQuery(sql, timing || 0);

    // 如果原来有日志函数，也调用它
    if (originalLogging && typeof originalLogging === 'function') {
      originalLogging(sql, timing);
    }
  };

  // 监听查询事件
  sequelize.addHook('beforeQuery', (options) => {
    options.startTime = Date.now();
  });

  sequelize.addHook('afterQuery', (options, query) => {
    if (options.startTime) {
      const duration = Date.now() - options.startTime;
      logDatabaseQuery(query.sql, duration);
    }
  });
};

/**
 * 判断是否为安全相关错误
 */
const isSecurityError = (err) => {
  const securityErrorTypes = [
    'UnauthorizedError',
    'ForbiddenError',
    'ValidationError',
    'JsonWebTokenError',
    'TokenExpiredError',
  ];

  return (
    securityErrorTypes.includes(err.name) ||
    err.message.toLowerCase().includes('unauthorized') ||
    err.message.toLowerCase().includes('forbidden') ||
    err.message.toLowerCase().includes('invalid token')
  );
};

/**
 * 系统健康状态日志
 */
const logSystemHealth = () => {
  const memUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();

  logPerformance('system_health', {
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024), // MB
    },
    cpu: {
      user: cpuUsage.user,
      system: cpuUsage.system,
    },
    uptime: Math.round(process.uptime()),
    timestamp: new Date().toISOString(),
  });
};

/**
 * 启动系统健康监控
 */
const startHealthMonitoring = () => {
  // 每5分钟记录一次系统健康状态
  setInterval(logSystemHealth, 5 * 60 * 1000);

  // 立即记录一次
  logSystemHealth();

  logger.info('Health monitoring started');
};

/**
 * 用户活动日志
 */
const logUserActivity = (userId, activity, details = {}) => {
  logger.info('User Activity', {
    userId,
    activity,
    details,
    timestamp: new Date().toISOString(),
  });
};

/**
 * API 使用统计
 */
const apiUsageStats = {};

const trackApiUsage = (req, res, next) => {
  const endpoint = `${req.method} ${req.route ? req.route.path : req.path}`;

  if (!apiUsageStats[endpoint]) {
    apiUsageStats[endpoint] = {
      count: 0,
      totalTime: 0,
      errors: 0,
    };
  }

  const startTime = Date.now();
  apiUsageStats[endpoint].count++;

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    apiUsageStats[endpoint].totalTime += duration;

    if (res.statusCode >= 400) {
      apiUsageStats[endpoint].errors++;
    }
  });

  next();
};

/**
 * 获取 API 使用统计
 */
const getApiUsageStats = () => {
  const stats = {};

  for (const [endpoint, data] of Object.entries(apiUsageStats)) {
    stats[endpoint] = {
      ...data,
      avgTime: data.count > 0 ? Math.round(data.totalTime / data.count) : 0,
      errorRate: data.count > 0 ? Math.round((data.errors / data.count) * 100) : 0,
    };
  }

  return stats;
};

module.exports = {
  requestLogger,
  errorLogger,
  securityLogger,
  performanceMonitor,
  setupDatabaseLogging,
  startHealthMonitoring,
  logUserActivity,
  trackApiUsage,
  getApiUsageStats,
};
