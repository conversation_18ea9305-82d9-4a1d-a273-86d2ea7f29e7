'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
// import Image from 'next/image'; // Temporarily disabled due to config issues
import Header from '@/components/Header';
// import VideoPlayer from '@/components/VideoPlayer';
// import AuthorCard from '@/components/AuthorCard';
// import InteractionStats from '@/components/InteractionStats';
import CommentSection from '@/components/CommentSection';
import AdBanner from '@/components/AdBanner';
import SocialShare from '@/components/SocialShare';
import TipModal from '@/components/TipModal';
import ReportModal from '@/components/ReportModal';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { Article, Comment } from '@/types';

export default function ArticlePage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useSimpleAuth();
  const { addNotification } = useNotifications();
  const [article, setArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [showTipModal, setShowTipModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [relatedArticles, setRelatedArticles] = useState<Article[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchArticle(params.id as string);
      fetchRelatedArticles(params.id as string);
    }
  }, [params.id]);

  const fetchArticle = async (id: string) => {
    try {
      setLoading(true);

      // 从mockWorks中获取文章数据
      const { mockWorks } = await import('@/data/mockWorks');
      const mockArticle = mockWorks.find((work) => 
        work.id.toString() === id && work.type === 'article'
      ) as Article;

      if (mockArticle) {
        setArticle(mockArticle);
        setLoading(false);
        return;
      }

      // 如果没有找到，显示404
      console.error('Article not found');
      setArticle(null);
    } catch (error) {
      console.error('Error fetching article:', error);
      setArticle(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedArticles = async (currentArticleId: string) => {
    try {
      // 从mockWorks获取相关文章
      const { mockWorks } = await import('@/data/mockWorks');
      const filtered = mockWorks
        .filter((work) => work.type === 'article' && work.id.toString() !== currentArticleId)
        .slice(0, 3) as Article[];
      setRelatedArticles(filtered);
    } catch (error) {
      console.error('Error fetching related articles:', error);
    }
  };

  const handleLike = async () => {
    if (!article) return;

    // 检查用户是否已登录
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    try {
      // 尝试API调用
      try {
        const response = await fetch(`http://localhost:5000/api/articles/${article.id}/like`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          setArticle((prev) => (prev ? { ...prev, likes: data.likes } : null));
          setIsLiked(!isLiked);
          return;
        }
      } catch (apiError) {
        console.log('API不可用，使用本地状态更新');
      }

      // 如果API不可用，直接更新本地状态
      const newLikedState = !isLiked;
      const newLikes = article.likes + (newLikedState ? 1 : -1);

      setArticle((prev) => (prev ? { ...prev, likes: newLikes } : null));
      setIsLiked(newLikedState);

      // 添加通知
      addNotification({
        type: 'success',
        title: newLikedState ? 'Article Liked' : 'Like Removed',
        message: newLikedState ? `You liked "${article.title}"` : `You removed your like from "${article.title}"`
      });

      console.log(`Article ${newLikedState ? 'liked' : 'unliked'} locally`);
    } catch (error) {
      console.error('Error liking article:', error);
    }
  };

  const handleSave = async () => {
    if (!article) return;

    // 检查用户是否已登录
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    try {
      // This would typically save to user's bookmarks
      // For now, just toggle the state
      const newSavedState = !isSaved;
      setIsSaved(newSavedState);

      // 添加通知
      addNotification({
        type: 'info',
        title: newSavedState ? 'Article Saved' : 'Article Removed',
        message: newSavedState ? `"${article.title}" saved to your bookmarks` : `"${article.title}" removed from bookmarks`
      });

      console.log(`Article ${newSavedState ? 'saved' : 'unsaved'}`);
    } catch (error) {
      console.error('Error saving article:', error);
    }
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-300 rounded mb-4"></div>
            <div className="h-64 bg-gray-300 rounded mb-6"></div>
            <div className="space-y-3">
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Article Not Found</h1>
          <button
            onClick={() => router.back()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      {/* Breadcrumb Navigation */}
      <nav className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 py-6">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span>/</span>
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Articles
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">
            {article?.title
              ? article.title.length > 50
                ? article.title.substring(0, 50) + '...'
                : article.title
              : 'Loading...'}
          </span>
        </div>
      </nav>

      <div className="flex max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 pb-12 gap-12">
        <article className="flex-1 max-w-4xl">
        {/* Article Image Section */}
        {article?.image && (
          <div className="mb-8">
            <img
              src={article.image}
              alt={article.title}
              className="w-full max-w-full h-auto rounded-lg"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(article.title)}&background=6366f1&color=fff&size=800x320`;
              }}
            />
          </div>
        )}

        {/* Article Title */}
        <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">
          {article?.title || 'Loading...'}
        </h1>

        {/* Author Info */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <img
                src={
                  typeof article?.author === 'object'
                    ? article.author.avatar
                    : 'https://ui-avatars.com/api/?name=Unknown+Author&background=6366f1&color=fff&size=48'
                }
                alt={typeof article?.author === 'object' ? article.author.name : 'Unknown Author'}
                className="w-12 h-12 rounded-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://ui-avatars.com/api/?name=Author&background=6366f1&color=fff&size=48`;
                }}
              />
              <div>
                <h3 className="font-semibold text-gray-900">
                  {typeof article?.author === 'object'
                    ? article.author.name
                    : article?.author || 'Unknown Author'}
                </h3>
                <p className="text-sm text-gray-500">
                  Published{' '}
                  {new Date(
                    article?.publishedAt || article?.createdAt || new Date()
                  ).toLocaleDateString()}{' '}
                  • {article?.readTime || 5} min read
                </p>
              </div>
            </div>
            {isAuthenticated && (
              <button 
                onClick={() => {
                  const newFollowingState = !isFollowing;
                  setIsFollowing(newFollowingState);
                  addNotification({
                    type: 'success',
                    title: newFollowingState ? 'Following' : 'Unfollowed',
                    message: newFollowingState 
                      ? `You are now following ${typeof article.author === 'object' ? article.author.name : 'this author'}`
                      : `You unfollowed ${typeof article.author === 'object' ? article.author.name : 'this author'}`
                  });
                }}
                className={`px-6 py-2 rounded-lg transition-colors font-medium ${
                  isFollowing
                    ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {isFollowing ? 'Following' : 'Follow'}
              </button>
            )}
          </div>
        </div>

        {/* Interaction Stats */}
        <div className="mb-8 flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-500">
            <span>👁️ {article?.views || 0} views</span>
            <span>💬 Comments</span>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            <button
              onClick={handleLike}
              className={`group flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 ${
                isLiked
                  ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                  : 'bg-white border-gray-200 text-gray-600 hover:border-red-200 hover:text-red-600'
              }`}
            >
              <svg className="w-4 h-4" fill={isLiked ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              <span className="text-sm font-medium">{article?.likes || 0}</span>
            </button>

            <button
              onClick={handleSave}
              className={`group flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 ${
                isSaved
                  ? 'bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100'
                  : 'bg-white border-gray-200 text-gray-600 hover:border-blue-200 hover:text-blue-600'
              }`}
            >
              <svg className="w-4 h-4" fill={isSaved ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
              </svg>
              <span className="text-sm font-medium">{isSaved ? 'Saved' : 'Save'}</span>
            </button>

            <SocialShare
              title={article.title}
              url={typeof window !== 'undefined' ? window.location.href : ''}
              description={article.description}
            />
            
            <button
              onClick={() => setShowTipModal(true)}
              className="group flex items-center space-x-2 px-4 py-2 rounded-full border border-amber-200 bg-amber-50 text-amber-600 hover:bg-amber-100 hover:border-amber-300 transition-all duration-200 hover:scale-105"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">Tip</span>
            </button>
            
            <button
              onClick={() => setShowReportModal(true)}
              className="group flex items-center space-x-2 px-4 py-2 rounded-full border border-gray-200 bg-white text-gray-500 hover:border-red-200 hover:text-red-500 transition-all duration-200 hover:scale-105"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <span className="text-sm font-medium">Report</span>
            </button>
          </div>
        </div>
        {/* Article Content */}
        <div className="prose prose-lg max-w-none mb-12">
          {article?.content ? (
            <div
              className="text-gray-700 leading-relaxed text-lg"
              dangerouslySetInnerHTML={{ __html: article.content }}
              translate="yes"
              lang="en"
            />
          ) : (
            <div className="text-gray-500 text-center py-8">
              <p>Loading article content...</p>
            </div>
          )}
        </div>

        {/* Comments Section */}
        <CommentSection workId={article.id} workType="article" />

        {/* Related Articles */}
        {relatedArticles.length > 0 && (
          <div className="mt-16 pt-8 border-t border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Related Articles</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedArticles.map((relatedArticle) => (
                <div
                  key={relatedArticle.id}
                  onClick={() => router.push(`/article/${relatedArticle.id}`)}
                  className="cursor-pointer group"
                >
                  <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden">
                    <div className="aspect-video bg-gray-200 relative overflow-hidden">
                      <img
                        src={relatedArticle.image}
                        alt={relatedArticle.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                      />
                    </div>
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-xs font-medium text-blue-600 bg-blue-50 px-2 py-1 rounded">
                          {relatedArticle.category}
                        </span>
                        <span className="text-xs text-gray-500">
                          {relatedArticle.readTime} min read
                        </span>
                      </div>
                      <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                        {relatedArticle.title}
                      </h3>
                      <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                        {relatedArticle.description}
                      </p>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>
                          By{' '}
                          {typeof relatedArticle.author === 'object'
                            ? relatedArticle.author.name
                            : relatedArticle.author}
                        </span>
                        <div className="flex items-center space-x-3">
                          <span>{relatedArticle.views} views</span>
                          <span>{relatedArticle.likes} likes</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        </article>
        
        {/* Right Sidebar Ads */}
        <div className="w-80 hidden xl:block">
          <div className="sticky top-24">
            <AdBanner position="sidebar" />
          </div>
        </div>
      </div>
      
      {/* Tip Modal */}
      {article && (
        <TipModal
          isOpen={showTipModal}
          onClose={() => setShowTipModal(false)}
          authorName={typeof article.author === 'object' ? article.author.name : article.author}
          contentTitle={article.title}
        />
      )}
      
      {/* Report Modal */}
      {article && (
        <ReportModal
          isOpen={showReportModal}
          onClose={() => setShowReportModal(false)}
          contentId={article.id}
          contentType="article"
          contentTitle={article.title}
        />
      )}
    </div>
  );
}
