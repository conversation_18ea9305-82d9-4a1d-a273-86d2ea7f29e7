import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

import Toast, { useToast, ToastContainer } from '../Toast';

// Mock component to test the useToast hook
const TestComponent: React.FC = () => {
  const { toasts, success, error, warning, info, removeToast } = useToast();

  return (
    <div>
      <button onClick={() => success('Success!', 'Operation completed')}>
        Add Success
      </button>
      <button onClick={() => error('Error!', 'Something went wrong')}>
        Add Error
      </button>
      <button onClick={() => warning('Warning!', 'Please be careful')}>
        Add Warning
      </button>
      <button onClick={() => info('Info!', 'Just so you know')}>
        Add Info
      </button>
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
};

describe('Toast Component', () => {
  beforeEach(() => {
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('renders toast with correct content', () => {
    const mockToast = {
      id: '1',
      type: 'success' as const,
      title: 'Success!',
      message: 'Operation completed successfully',
    };

    const mockOnClose = jest.fn();

    render(<Toast toast={mockToast} onClose={mockOnClose} />);

    expect(screen.getByText('Success!')).toBeInTheDocument();
    expect(screen.getByText('Operation completed successfully')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    const mockToast = {
      id: '1',
      type: 'success' as const,
      title: 'Success!',
      message: 'Operation completed successfully',
    };

    const mockOnClose = jest.fn();

    render(<Toast toast={mockToast} onClose={mockOnClose} />);

    const closeButton = screen.getByRole('button');
    fireEvent.click(closeButton);

    // Wait for the animation delay
    jest.advanceTimersByTime(300);

    expect(mockOnClose).toHaveBeenCalledWith('1');
  });

  it('auto-dismisses after specified duration', () => {
    const mockToast = {
      id: '1',
      type: 'success' as const,
      title: 'Success!',
      message: 'Operation completed successfully',
      duration: 1000,
    };

    const mockOnClose = jest.fn();

    render(<Toast toast={mockToast} onClose={mockOnClose} />);

    // Fast-forward time by the duration + animation delay
    jest.advanceTimersByTime(1000 + 300);

    expect(mockOnClose).toHaveBeenCalledWith('1');
  });

  it('renders different toast types with correct styling', () => {
    const toastTypes = ['success', 'error', 'warning', 'info'] as const;

    toastTypes.forEach(type => {
      const mockToast = {
        id: `${type}-toast`,
        type,
        title: `${type} toast`,
        message: `This is a ${type} message`,
      };

      const mockOnClose = jest.fn();

      const { unmount } = render(<Toast toast={mockToast} onClose={mockOnClose} />);

      expect(screen.getByText(`${type} toast`)).toBeInTheDocument();
      expect(screen.getByText(`This is a ${type} message`)).toBeInTheDocument();

      unmount();
    });
  });

  it('renders action button when provided', () => {
    const mockAction = jest.fn();
    const mockToast = {
      id: '1',
      type: 'info' as const,
      title: 'Info!',
      message: 'Click the action button',
      action: {
        label: 'Take Action',
        onClick: mockAction,
      },
    };

    const mockOnClose = jest.fn();

    render(<Toast toast={mockToast} onClose={mockOnClose} />);

    const actionButton = screen.getByText('Take Action');
    expect(actionButton).toBeInTheDocument();

    fireEvent.click(actionButton);
    expect(mockAction).toHaveBeenCalled();
  });
});

describe('useToast Hook', () => {
  beforeEach(() => {
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('adds different types of toasts', () => {
    render(<TestComponent />);

    // Add success toast
    fireEvent.click(screen.getByText('Add Success'));
    expect(screen.getByText('Success!')).toBeInTheDocument();
    expect(screen.getByText('Operation completed')).toBeInTheDocument();

    // Add error toast
    fireEvent.click(screen.getByText('Add Error'));
    expect(screen.getByText('Error!')).toBeInTheDocument();
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();

    // Add warning toast
    fireEvent.click(screen.getByText('Add Warning'));
    expect(screen.getByText('Warning!')).toBeInTheDocument();
    expect(screen.getByText('Please be careful')).toBeInTheDocument();

    // Add info toast
    fireEvent.click(screen.getByText('Add Info'));
    expect(screen.getByText('Info!')).toBeInTheDocument();
    expect(screen.getByText('Just so you know')).toBeInTheDocument();
  });

  it('removes toasts when close button is clicked', async () => {
    render(<TestComponent />);

    // Add a toast
    fireEvent.click(screen.getByText('Add Success'));
    expect(screen.getByText('Success!')).toBeInTheDocument();

    // Click close button
    const closeButton = screen.getAllByRole('button').find(
      button => button.getAttribute('aria-label') === 'Close' || 
      button.querySelector('svg') // Close icon
    );
    
    if (closeButton) {
      fireEvent.click(closeButton);
      
      // Wait for animation
      jest.advanceTimersByTime(300);
      
      await waitFor(() => {
        expect(screen.queryByText('Success!')).not.toBeInTheDocument();
      });
    }
  });

  it('handles multiple toasts correctly', () => {
    render(<TestComponent />);

    // Add multiple toasts
    fireEvent.click(screen.getByText('Add Success'));
    fireEvent.click(screen.getByText('Add Error'));
    fireEvent.click(screen.getByText('Add Warning'));

    // All toasts should be visible
    expect(screen.getByText('Success!')).toBeInTheDocument();
    expect(screen.getByText('Error!')).toBeInTheDocument();
    expect(screen.getByText('Warning!')).toBeInTheDocument();
  });
});

describe('ToastContainer Component', () => {
  it('renders multiple toasts', () => {
    const mockToasts = [
      {
        id: '1',
        type: 'success' as const,
        title: 'Success 1',
        message: 'First success message',
      },
      {
        id: '2',
        type: 'error' as const,
        title: 'Error 1',
        message: 'First error message',
      },
    ];

    const mockOnClose = jest.fn();

    render(<ToastContainer toasts={mockToasts} onClose={mockOnClose} />);

    expect(screen.getByText('Success 1')).toBeInTheDocument();
    expect(screen.getByText('Error 1')).toBeInTheDocument();
  });

  it('renders empty container when no toasts', () => {
    const mockOnClose = jest.fn();

    const { container } = render(<ToastContainer toasts={[]} onClose={mockOnClose} />);

    expect(container.firstChild?.childNodes).toHaveLength(0);
  });
});
