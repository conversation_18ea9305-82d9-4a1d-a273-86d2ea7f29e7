'use client';

import React, { useState } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';

interface TipModalProps {
  isOpen: boolean;
  onClose: () => void;
  authorName: string;
  contentTitle: string;
}

interface VirtualGift {
  id: string;
  name: string;
  icon: string;
  price: number;
  animation?: string;
}

const TipModal = ({ isOpen, onClose, authorName, contentTitle }: TipModalProps) => {
  const { isAuthenticated } = useSimpleAuth();
  const [activeTab, setActiveTab] = useState<'cash' | 'gifts'>('cash');
  const [cashAmount, setCashAmount] = useState('');
  const [selectedGift, setSelectedGift] = useState<VirtualGift | null>(null);
  const [message, setMessage] = useState('');
  const [userBalance] = useState(1000); // Mock user balance

  const cashAmounts = [1, 5, 10, 20, 50, 100];
  
  const virtualGifts: VirtualGift[] = [
    { id: '1', name: 'Coffee', icon: '☕', price: 5 },
    { id: '2', name: 'Heart', icon: '❤️', price: 10 },
    { id: '3', name: 'Star', icon: '⭐', price: 15 },
    { id: '4', name: 'Trophy', icon: '🏆', price: 25 },
    { id: '5', name: 'Crown', icon: '👑', price: 50 },
    { id: '6', name: 'Diamond', icon: '💎', price: 100 }
  ];

  const handleCashTip = () => {
    const amount = parseFloat(cashAmount);
    if (!amount || amount <= 0) return;
    
    // Mock payment processing
    alert(`Successfully tipped $${amount} to ${authorName}!`);
    onClose();
  };

  const handleGiftTip = () => {
    if (!selectedGift) return;
    
    if (userBalance < selectedGift.price) {
      alert('Insufficient balance! Please recharge your account.');
      return;
    }
    
    // Mock gift sending
    alert(`Successfully sent ${selectedGift.name} ${selectedGift.icon} to ${authorName}!`);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Support {authorName}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <p className="text-sm text-gray-600 mb-4">
          Show your appreciation for "{contentTitle}"
        </p>

        {!isAuthenticated ? (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">Please log in to send tips</p>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg">
              Log In
            </button>
          </div>
        ) : (
          <>
            {/* Tabs */}
            <div className="flex border-b border-gray-200 mb-4">
              <button
                onClick={() => setActiveTab('cash')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'cash' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'
                }`}
              >
                💰 Cash Tip
              </button>
              <button
                onClick={() => setActiveTab('gifts')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'gifts' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'
                }`}
              >
                🎁 Virtual Gifts
              </button>
            </div>

            {activeTab === 'cash' && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount ($)
                  </label>
                  <div className="grid grid-cols-3 gap-2 mb-3">
                    {cashAmounts.map((amount) => (
                      <button
                        key={amount}
                        onClick={() => setCashAmount(amount.toString())}
                        className={`px-3 py-2 border rounded-lg text-sm ${
                          cashAmount === amount.toString()
                            ? 'border-blue-500 bg-blue-50 text-blue-600'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        ${amount}
                      </button>
                    ))}
                  </div>
                  <input
                    type="number"
                    value={cashAmount}
                    onChange={(e) => setCashAmount(e.target.value)}
                    placeholder="Custom amount"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message (optional)
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Leave a message..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none"
                    rows={3}
                  />
                </div>

                <button
                  onClick={handleCashTip}
                  disabled={!cashAmount || parseFloat(cashAmount) <= 0}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300"
                >
                  Send Tip ${cashAmount || '0'}
                </button>
              </div>
            )}

            {activeTab === 'gifts' && (
              <div className="space-y-4">
                <div className="text-sm text-gray-600 mb-2">
                  Balance: {userBalance} coins
                </div>
                
                <div className="grid grid-cols-3 gap-3">
                  {virtualGifts.map((gift) => (
                    <button
                      key={gift.id}
                      onClick={() => setSelectedGift(gift)}
                      className={`p-3 border rounded-lg text-center ${
                        selectedGift?.id === gift.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="text-2xl mb-1">{gift.icon}</div>
                      <div className="text-xs font-medium">{gift.name}</div>
                      <div className="text-xs text-gray-500">{gift.price} coins</div>
                    </button>
                  ))}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message (optional)
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Leave a message..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none"
                    rows={3}
                  />
                </div>

                <button
                  onClick={handleGiftTip}
                  disabled={!selectedGift || userBalance < (selectedGift?.price || 0)}
                  className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-gray-300"
                >
                  {selectedGift ? `Send ${selectedGift.name} (${selectedGift.price} coins)` : 'Select a gift'}
                </button>

                {userBalance < (selectedGift?.price || 0) && selectedGift && (
                  <p className="text-red-500 text-sm text-center">
                    Insufficient balance. <button className="text-blue-600 underline">Recharge</button>
                  </p>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default TipModal;