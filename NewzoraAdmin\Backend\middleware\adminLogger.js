const { AdminLog } = require('../models');

// 记录管理员操作日志
const logAdminAction = (action, targetType = null) => {
  return async (req, res, next) => {
    // 保存原始的res.send方法
    const originalSend = res.send;
    
    // 重写res.send方法以捕获响应
    res.send = function(data) {
      // 只在成功响应时记录日志
      if (req.user && res.statusCode < 400) {
        AdminLog.create({
          admin_id: req.user.id,
          action,
          target_type: targetType,
          target_id: req.params.id || req.body.id || null,
          details: {
            method: req.method,
            url: req.originalUrl,
            body: req.body,
            query: req.query,
            params: req.params,
            statusCode: res.statusCode
          },
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        }).catch(error => {
          console.error('记录管理员操作日志失败:', error);
        });
      }
      
      // 调用原始的send方法
      originalSend.call(this, data);
    };
    
    next();
  };
};

// 批量操作日志记录
const logBulkAction = (action, targetType) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      if (req.user && res.statusCode < 400) {
        const targetIds = req.body.userIds || req.body.articleIds || req.body.commentIds || [];
        
        // 为每个目标记录一条日志
        const logPromises = targetIds.map(targetId => 
          AdminLog.create({
            admin_id: req.user.id,
            action: `bulk_${action}`,
            target_type: targetType,
            target_id: targetId,
            details: {
              method: req.method,
              url: req.originalUrl,
              body: req.body,
              query: req.query,
              batchSize: targetIds.length,
              statusCode: res.statusCode
            },
            ip_address: req.ip,
            user_agent: req.get('User-Agent')
          })
        );

        Promise.all(logPromises).catch(error => {
          console.error('记录批量操作日志失败:', error);
        });
      }
      
      originalSend.call(this, data);
    };
    
    next();
  };
};

// 获取管理员操作日志
const getAdminLogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      adminId,
      action,
      targetType,
      startDate,
      endDate
    } = req.query;

    const offset = (page - 1) * limit;
    const whereConditions = {};

    // 构建查询条件
    if (adminId) {
      whereConditions.admin_id = adminId;
    }

    if (action) {
      whereConditions.action = action;
    }

    if (targetType) {
      whereConditions.target_type = targetType;
    }

    if (startDate || endDate) {
      whereConditions.created_at = {};
      if (startDate) {
        whereConditions.created_at[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereConditions.created_at[Op.lte] = new Date(endDate);
      }
    }

    const { count, rows: logs } = await AdminLog.findAndCountAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'admin',
          attributes: ['id', 'username', 'display_name', 'role']
        }
      ],
      order: [['created_at', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: logs,
      pagination: {
        current: parseInt(page),
        total: count,
        pageSize: parseInt(limit),
        totalPages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('获取管理员日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取管理员日志失败'
    });
  }
};

module.exports = { 
  logAdminAction, 
  logBulkAction, 
  getAdminLogs 
};