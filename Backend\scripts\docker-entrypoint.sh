#!/bin/sh

# OneNews Backend Docker 启动脚本
# 包含健康检查、数据库迁移、优雅关闭等功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 OneNews Backend 启动中...${NC}"

# 环境变量验证
validate_env() {
    echo -e "${YELLOW}📋 验证环境变量...${NC}"
    
    required_vars="DB_HOST DB_PORT DB_NAME DB_USER DB_PASSWORD JWT_SECRET"
    missing_vars=""
    
    for var in $required_vars; do
        if [ -z "$(eval echo \$$var)" ]; then
            missing_vars="$missing_vars $var"
        fi
    done
    
    if [ -n "$missing_vars" ]; then
        echo -e "${RED}❌ 缺少必需的环境变量:$missing_vars${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境变量验证通过${NC}"
}

# 数据库连接检查
check_database() {
    echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" >/dev/null 2>&1; then
            echo -e "${GREEN}✅ 数据库连接成功${NC}"
            return 0
        fi
        
        echo -e "${YELLOW}⏳ 等待数据库连接... (尝试 $attempt/$max_attempts)${NC}"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo -e "${RED}❌ 数据库连接失败${NC}"
    exit 1
}

# 数据库迁移
run_migrations() {
    echo -e "${YELLOW}🔄 运行数据库迁移...${NC}"
    
    # 这里可以添加数据库迁移逻辑
    # 例如: npm run migrate
    
    echo -e "${GREEN}✅ 数据库迁移完成${NC}"
}

# 创建必要的目录
setup_directories() {
    echo -e "${YELLOW}📁 创建必要的目录...${NC}"
    
    mkdir -p uploads/images uploads/videos uploads/documents
    mkdir -p logs
    mkdir -p temp
    
    echo -e "${GREEN}✅ 目录创建完成${NC}"
}

# 优雅关闭处理
graceful_shutdown() {
    echo -e "${YELLOW}🛑 接收到关闭信号，正在优雅关闭...${NC}"
    
    # 停止接受新连接
    if [ -n "$SERVER_PID" ]; then
        echo -e "${YELLOW}⏳ 等待现有连接完成...${NC}"
        kill -TERM "$SERVER_PID"
        wait "$SERVER_PID"
    fi
    
    echo -e "${GREEN}✅ 服务已优雅关闭${NC}"
    exit 0
}

# 设置信号处理
trap graceful_shutdown TERM INT

# 主启动流程
main() {
    echo -e "${BLUE}🔧 OneNews Backend 初始化${NC}"
    
    # 验证环境
    validate_env
    
    # 检查数据库
    check_database
    
    # 设置目录
    setup_directories
    
    # 运行迁移
    run_migrations
    
    echo -e "${GREEN}🎉 初始化完成，启动服务器...${NC}"
    
    # 启动Node.js应用
    exec node server.js &
    SERVER_PID=$!
    
    # 等待服务器进程
    wait $SERVER_PID
}

# 健康检查函数
health_check() {
    if curl -f http://localhost:5000/api/health >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 健康检查通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 健康检查失败${NC}"
        return 1
    fi
}

# 根据参数执行不同操作
case "${1:-start}" in
    "start")
        main
        ;;
    "health")
        health_check
        ;;
    "migrate")
        validate_env
        check_database
        run_migrations
        ;;
    *)
        echo "用法: $0 {start|health|migrate}"
        exit 1
        ;;
esac
