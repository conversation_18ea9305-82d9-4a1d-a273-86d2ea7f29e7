'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { analyticsService, AnalyticsData, RealTimeStats } from '@/services/analyticsService';
import { mockWorks } from '@/data/mockWorks';

interface AnalyticsDashboardProps {
  className?: string;
  activeTab?: 'overview' | 'content' | 'audience' | 'revenue';
}

export default function AnalyticsDashboard({ className = '', activeTab: parentActiveTab }: AnalyticsDashboardProps) {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [realTimeStats, setRealTimeStats] = useState<RealTimeStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'content' | 'audience' | 'revenue'>(parentActiveTab || 'overview');
  const router = useRouter();

  // Sync activeTab from parent component
  useEffect(() => {
    if (parentActiveTab) {
      setActiveTab(parentActiveTab);
    }
  }, [parentActiveTab]);

  useEffect(() => {
    // Generate analytics report
    const data = analyticsService.generateAnalyticsReport(mockWorks);
    setAnalyticsData(data);

    // Get real-time stats
    const realTime = analyticsService.getRealTimeStats();
    setRealTimeStats(realTime);

    setLoading(false);

    // Update real-time data every 30 seconds
    const interval = setInterval(() => {
      const newRealTime = analyticsService.getRealTimeStats();
      setRealTimeStats(newRealTime);
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // 根据作品ID查找作品类型
  const getWorkTypeById = (workId: number) => {
    const work = mockWorks.find(w => w.id === workId);
    return work ? work.type : 'article'; // 默认为article
  };

  // 导航到作品详情页
  const navigateToWork = (workId: number) => {
    const workType = getWorkTypeById(workId);
    router.push(`/${workType}/${workId}`);
  };

  if (loading || !analyticsData || !realTimeStats) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Analytics Dashboard</h2>
        <p className="text-gray-600">Real-time insights and performance metrics</p>
      </div>

      {/* Real-time Stats - Overview mode only */}
      {activeTab === 'overview' && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🔴 Live Stats</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Active Users</p>
                  <p className="text-2xl font-bold text-green-900">{realTimeStats.activeUsers}</p>
                </div>
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">👥</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Current Views</p>
                  <p className="text-2xl font-bold text-blue-900">{realTimeStats.currentViews}</p>
                </div>
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">👁️</span>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Trending Now</p>
                  <p className="text-lg font-bold text-purple-900">
                    {realTimeStats.trendsLastHour[0]?.category || 'Technology'}
                  </p>
                </div>
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm">🔥</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Key Metrics - Overview mode only */}
      {activeTab === 'overview' && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 Key Metrics</h3>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{analyticsData.totalViews.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Total Views</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{analyticsData.totalLikes.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Total Likes</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{analyticsData.totalShares.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Total Shares</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{analyticsData.totalComments.toLocaleString()}</p>
              <p className="text-sm text-gray-600">Comments</p>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <p className="text-2xl font-bold text-gray-900">{(analyticsData.engagementRate * 100).toFixed(1)}%</p>
              <p className="text-sm text-gray-600">Engagement</p>
            </div>
          </div>
        </div>
      )}

      {/* Top Categories - Overview mode only */}
      {activeTab === 'overview' && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🏆 Top Categories</h3>
          <div className="space-y-3">
            {analyticsData.topCategories.slice(0, 5).map((category, index) => (
              <div key={category.category} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </span>
                  <span className="font-medium text-gray-900 capitalize">{category.category}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">{category.count} interactions</span>
                  <span className="text-sm font-medium text-blue-600">{category.percentage.toFixed(1)}%</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Top Tags - Overview mode only */}
      {activeTab === 'overview' && (
        <div className="mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🏷️ Popular Tags</h3>
          <div className="flex flex-wrap gap-2">
            {analyticsData.topTags.slice(0, 15).map((tag) => (
              <span
                key={tag.tag}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"
                title={`${tag.count} uses (${tag.percentage.toFixed(1)}%)`}
              >
                {tag.tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Content Performance - Overview mode only */}
      {activeTab === 'overview' && (
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">⭐ Content Performance</h3>
            <button 
              onClick={() => setActiveTab('content')}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              View Details
            </button>
          </div>
          <div className="space-y-3">
            {analyticsData.contentPerformance.slice(0, 5).map((content, index) => (
              <div 
                key={content.workId} 
                onClick={() => navigateToWork(content.workId)}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <span className="w-6 h-6 bg-yellow-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                    {index + 1}
                  </span>
                  <span className="font-medium text-gray-900">{content.title}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">{content.views} views</span>
                  <span className="text-sm font-medium text-yellow-600">Score: {content.score}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Detailed analytics content */}
      {activeTab !== 'overview' && activeTab && (
        <div className="mb-8">

          {/* Content Performance Details */}
          {activeTab === 'content' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-600">Total Views</h4>
                  <p className="text-2xl font-bold text-blue-900">{analyticsData.totalViews.toLocaleString()}</p>
                  <p className="text-sm text-blue-600">+12% this month</p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-green-600">Engagement Rate</h4>
                  <p className="text-2xl font-bold text-green-900">{(analyticsData.engagementRate * 100).toFixed(1)}%</p>
                  <p className="text-sm text-green-600">+5.2% this month</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-purple-600">Active Content</h4>
                  <p className="text-2xl font-bold text-purple-900">{analyticsData.contentPerformance.length}</p>
                  <p className="text-sm text-purple-600">published</p>
                </div>
                <div className="bg-orange-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-orange-600">Total Shares</h4>
                  <p className="text-2xl font-bold text-orange-900">{analyticsData.totalShares.toLocaleString()}</p>
                  <p className="text-sm text-orange-600">+8.1% this month</p>
                </div>
              </div>
              
              {/* Top Performing Content */}
              <div className="bg-white rounded-xl border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">🏆 Top Performing Content</h3>
                <div className="space-y-4">
                  {analyticsData.contentPerformance.slice(0, 10).map((content: any, index: number) => (
                    <div 
                      key={content.workId} 
                      onClick={() => navigateToWork(content.workId)}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                    >
                      <div className="flex items-center space-x-4">
                        <span className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                          index < 3 ? 'bg-yellow-500' : 'bg-gray-400'
                        }`}>
                          {index + 1}
                        </span>
                        <div>
                          <p className="font-medium text-gray-900">{content.title}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                            <span>👁️ {content.views?.toLocaleString() || 'N/A'} views</span>
                            <span>👍 {content.likes?.toLocaleString() || 'N/A'} likes</span>
                            <span>💬 {content.comments?.toLocaleString() || 'N/A'} comments</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-bold text-blue-600">Score: {content.score}</span>
                        <p className="text-sm text-gray-500">Performance Index</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Audience Insights */}
          {activeTab === 'audience' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">User Growth Trend</h4>
                  <div className="h-48 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl mb-2">📈</div>
                      <p className="text-gray-600">User Growth Chart</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Total Users: {analyticsData.userGrowth[analyticsData.userGrowth.length - 1]?.users.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Activity Hours Analysis</h4>
                  <div className="h-48 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl mb-2">⏰</div>
                      <p className="text-gray-600">Activity Hours Chart</p>
                      <p className="text-sm text-gray-500 mt-2">Peak: 8:00-10:00 PM</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Revenue Analytics */}
          {activeTab === 'revenue' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-green-600">Monthly Revenue</h4>
                  <p className="text-2xl font-bold text-green-900">${(analyticsData.totalViews * 0.01).toFixed(2)}</p>
                  <p className="text-sm text-green-600">+15.3% vs last month</p>
                </div>
                <div className="bg-blue-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-600">Average RPM</h4>
                  <p className="text-2xl font-bold text-blue-900">${(10 + Math.random() * 5).toFixed(2)}</p>
                  <p className="text-sm text-blue-600">per 1K views</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-purple-600">Total Revenue</h4>
                  <p className="text-2xl font-bold text-purple-900">${(analyticsData.totalViews * 0.05).toFixed(2)}</p>
                  <p className="text-sm text-purple-600">lifetime</p>
                </div>
              </div>
              <div className="bg-white border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h4>
                <div className="h-64 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-4xl mb-2">💹</div>
                    <p className="text-gray-600">Revenue Trend Chart</p>
                    <p className="text-sm text-gray-500 mt-2">Based on views and engagement rate</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}



      {/* Popular Content Right Now - Overview mode only */}
      {activeTab === 'overview' && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🔥 Popular Right Now</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {realTimeStats.popularContent.map((content) => (
              <div 
                key={content.workId} 
                onClick={() => navigateToWork(content.workId)}
                className="p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg hover:from-orange-100 hover:to-red-100 cursor-pointer transition-colors"
              >
                <h4 className="font-medium text-gray-900 mb-2">{content.title}</h4>
                <p className="text-sm text-gray-600">
                  <span className="font-medium text-orange-600">{content.currentViews}</span> watching now
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}