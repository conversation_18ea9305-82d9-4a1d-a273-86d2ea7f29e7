# Newzora Project Startup Script
# PowerShell script to start both frontend and backend services

param(
    [switch]$Production,
    [switch]$Development,
    [switch]$Help
)

# Color functions for better output
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    } else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success { Write-ColorOutput Green $args }
function Write-Warning { Write-ColorOutput Yellow $args }
function Write-Error { Write-ColorOutput Red $args }
function Write-Info { Write-ColorOutput Cyan $args }

# Help function
function Show-Help {
    Write-Info "Newzora Project Startup Script"
    Write-Info "================================"
    Write-Output ""
    Write-Output "Usage: .\start.ps1 [options]"
    Write-Output ""
    Write-Output "Options:"
    Write-Output "  -Development    Start in development mode (default)"
    Write-Output "  -Production     Start in production mode"
    Write-Output "  -Help           Show this help message"
    Write-Output ""
    Write-Output "Examples:"
    Write-Output "  .\start.ps1                 # Start in development mode"
    Write-Output "  .\start.ps1 -Development    # Start in development mode"
    Write-Output "  .\start.ps1 -Production     # Start in production mode"
    Write-Output ""
}

if ($Help) {
    Show-Help
    exit 0
}

# Default to development mode
if (-not $Production) {
    $Development = $true
}

Write-Info "🚀 Starting Newzora Project..."
Write-Output ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Success "✓ Node.js version: $nodeVersion"
} catch {
    Write-Error "❌ Node.js is not installed or not in PATH"
    Write-Error "Please install Node.js from https://nodejs.org/"
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Success "✓ npm version: $npmVersion"
} catch {
    Write-Error "❌ npm is not installed or not in PATH"
    exit 1
}

Write-Output ""

# Function to install dependencies
function Install-Dependencies {
    param($Directory, $Name)
    
    Write-Info "📦 Installing $Name dependencies..."
    Push-Location $Directory
    
    if (Test-Path "package.json") {
        try {
            npm install
            Write-Success "✓ $Name dependencies installed successfully"
        } catch {
            Write-Error "❌ Failed to install $Name dependencies"
            Pop-Location
            exit 1
        }
    } else {
        Write-Warning "⚠️  No package.json found in $Directory"
    }
    
    Pop-Location
}

# Install dependencies
Write-Info "🔧 Checking and installing dependencies..."
Install-Dependencies "Backend" "Backend"
Install-Dependencies "Frontend" "Frontend"

Write-Output ""

# Start services
if ($Development) {
    Write-Info "🛠️  Starting in Development Mode..."
    Write-Output ""
    
    # Start backend in background
    Write-Info "🔙 Starting Backend server..."
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd Backend; npm run dev" -WindowStyle Normal
    
    # Wait a moment for backend to start
    Start-Sleep -Seconds 3
    
    # Start frontend
    Write-Info "🎨 Starting Frontend server..."
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd Frontend; npm run dev" -WindowStyle Normal
    
    Write-Output ""
    Write-Success "✅ Services started successfully!"
    Write-Info "📱 Frontend: http://localhost:3000"
    Write-Info "🔧 Backend:  http://localhost:5000"
    Write-Output ""
    Write-Warning "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
    
} elseif ($Production) {
    Write-Info "🏭 Starting in Production Mode..."
    Write-Output ""
    
    # Build frontend
    Write-Info "🏗️  Building Frontend..."
    Push-Location "Frontend"
    try {
        npm run build
        Write-Success "✓ Frontend built successfully"
    } catch {
        Write-Error "❌ Frontend build failed"
        Pop-Location
        exit 1
    }
    Pop-Location
    
    # Start backend in production mode
    Write-Info "🔙 Starting Backend server..."
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd Backend; npm start" -WindowStyle Normal
    
    # Start frontend in production mode
    Write-Info "🎨 Starting Frontend server..."
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd Frontend; npm start" -WindowStyle Normal
    
    Write-Output ""
    Write-Success "✅ Production services started successfully!"
    Write-Info "📱 Frontend: http://localhost:3000"
    Write-Info "🔧 Backend:  http://localhost:5000"
    Write-Output ""
    Write-Warning "Press any key to exit..."
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

Write-Info "👋 Goodbye!"
