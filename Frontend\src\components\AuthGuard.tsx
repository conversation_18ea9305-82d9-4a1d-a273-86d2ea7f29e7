'use client';

import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  fallback?: React.ReactNode;
}

/**
 * 认证守卫组件 - 提供简单的认证状态检查
 */
export default function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo = '/auth/login'
}: AuthGuardProps) {
  const { isAuthenticated } = useSimpleAuth();
  const router = useRouter();

  useEffect(() => {
    if (requireAuth && !isAuthenticated) {
      // 构建重定向URL，包含当前页面作为回调
      const currentPath = window.location.pathname + window.location.search;
      const loginUrl = `${redirectTo}?redirect=${encodeURIComponent(currentPath)}`;
      router.replace(loginUrl);
    }
  }, [isAuthenticated, requireAuth, redirectTo, router]);

  // 直接渲染内容，不等待认证状态
  return <>{children}</>;
}