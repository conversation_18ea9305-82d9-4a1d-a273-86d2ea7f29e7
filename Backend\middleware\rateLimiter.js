const rateLimit = require('express-rate-limit');

// Disabled API rate limiter (for development)
const apiLimiter = (req, res, next) => next();

// Disabled rate limiter for authentication endpoints (for development)
const authLimiter = (req, res, next) => next();

// Disabled password reset rate limiter (for development)
const passwordResetLimiter = (req, res, next) => next();

// Disabled email verification rate limiter (for development)
const emailVerificationLimiter = (req, res, next) => next();

// Disabled registration rate limiter (for development)
const registrationLimiter = (req, res, next) => next();

module.exports = {
  apiLimiter,
  authLimiter,
  passwordResetLimiter,
  emailVerificationLimiter,
  registrationLimiter,
};
