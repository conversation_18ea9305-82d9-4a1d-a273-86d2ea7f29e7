# OneNews Prometheus 监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    monitor: 'onenews-monitor'
    environment: 'production'

# 规则文件
rule_files:
  - "alert_rules.yml"

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 抓取配置
scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # OneNews 后端应用监控
  - job_name: 'onenews-backend'
    static_configs:
      - targets: 
          - 'backend-1:5000'
          - 'backend-2:5000'
    scrape_interval: 10s
    metrics_path: /api/metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']

  # OneNews 前端应用监控
  - job_name: 'onenews-frontend'
    static_configs:
      - targets:
          - 'frontend-1:3000'
          - 'frontend-2:3000'
    scrape_interval: 30s
    metrics_path: /api/metrics
    scrape_timeout: 5s

  # PostgreSQL 数据库监控
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 15s
    metrics_path: /metrics
    params:
      target: ['postgres:5432']

  # Redis 缓存监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 15s
    metrics_path: /metrics

  # Nginx 反向代理监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    scrape_interval: 15s
    metrics_path: /nginx_status
    params:
      format: ['prometheus']

  # Node Exporter - 系统监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

  # cAdvisor - 容器监控
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s
    metrics_path: /metrics

  # 黑盒监控 - 外部服务检查
  - job_name: 'blackbox'
    metrics_path: /probe
    params:
      module: [http_2xx]
    static_configs:
      - targets:
          - https://newzora.com
          - https://api.newzora.com
          - https://newzora.com/api/health
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox-exporter:9115

  # SSL 证书监控
  - job_name: 'ssl-exporter'
    static_configs:
      - targets: ['ssl-exporter:9219']
    scrape_interval: 60s
    params:
      target: ['newzora.com:443']

# 远程写入配置 (可选 - 用于长期存储)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# 远程读取配置 (可选)
# remote_read:
#   - url: "https://prometheus-remote-read-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"
