export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: <PERSON><PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          username: string | null
          display_name: string | null
          avatar_url: string | null
          bio: string | null
          website: string | null
          location: string | null
          role: 'user' | 'admin' | 'moderator'
          status: 'active' | 'suspended' | 'banned'
          email_verified: boolean
          created_at: string
          updated_at: string
          last_login_at: string | null
          preferences: Json | null
          metadata: Json | null
        }
        Insert: {
          id: string
          email: string
          username?: string | null
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          location?: string | null
          role?: 'user' | 'admin' | 'moderator'
          status?: 'active' | 'suspended' | 'banned'
          email_verified?: boolean
          created_at?: string
          updated_at?: string
          last_login_at?: string | null
          preferences?: Json | null
          metadata?: Json | null
        }
        Update: {
          id?: string
          email?: string
          username?: string | null
          display_name?: string | null
          avatar_url?: string | null
          bio?: string | null
          website?: string | null
          location?: string | null
          role?: 'user' | 'admin' | 'moderator'
          status?: 'active' | 'suspended' | 'banned'
          email_verified?: boolean
          created_at?: string
          updated_at?: string
          last_login_at?: string | null
          preferences?: Json | null
          metadata?: Json | null
        }
        Relationships: []
      }
      articles: {
        Row: {
          id: number
          title: string
          content: string
          excerpt: string | null
          slug: string
          status: 'draft' | 'published' | 'archived'
          author_id: string
          category_id: number | null
          featured_image: string | null
          published_at: string | null
          created_at: string
          updated_at: string
          view_count: number
          like_count: number
          comment_count: number
          tags: string[] | null
          metadata: Json | null
        }
        Insert: {
          id?: number
          title: string
          content: string
          excerpt?: string | null
          slug: string
          status?: 'draft' | 'published' | 'archived'
          author_id: string
          category_id?: number | null
          featured_image?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
          view_count?: number
          like_count?: number
          comment_count?: number
          tags?: string[] | null
          metadata?: Json | null
        }
        Update: {
          id?: number
          title?: string
          content?: string
          excerpt?: string | null
          slug?: string
          status?: 'draft' | 'published' | 'archived'
          author_id?: string
          category_id?: number | null
          featured_image?: string | null
          published_at?: string | null
          created_at?: string
          updated_at?: string
          view_count?: number
          like_count?: number
          comment_count?: number
          tags?: string[] | null
          metadata?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "articles_author_id_fkey"
            columns: ["author_id"]
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'user' | 'admin' | 'moderator'
      user_status: 'active' | 'suspended' | 'banned'
      article_status: 'draft' | 'published' | 'archived'
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
