'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, ChevronDown } from 'lucide-react';

export interface DateRange {
  value: string;
  label: string;
  startDate: Date;
  endDate: Date;
}

interface DateRangeSelectorProps {
  value: string;
  onChange: (value: string, dateRange: DateRange) => void;
  options?: Array<{ value: string; label: string }>;
  className?: string;
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const DEFAULT_OPTIONS = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '90d', label: 'Last 90 days' },
  { value: '1y', label: 'Last year' },
  { value: 'custom', label: 'Custom range' }
];

const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  value,
  onChange,
  options = DEFAULT_OPTIONS,
  className = '',
  showIcon = true,
  size = 'md'
}) => {
  const [showCustomPicker, setShowCustomPicker] = useState(false);
  const [customStartDate, setCustomStartDate] = useState('');
  const [customEndDate, setCustomEndDate] = useState('');

  const calculateDateRange = (rangeValue: string): DateRange => {
    const now = new Date();
    let endDate = new Date(now);
    let startDate = new Date(now);

    switch (rangeValue) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      case 'today':
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        startDate.setDate(now.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        endDate.setDate(now.getDate() - 1);
        endDate.setHours(23, 59, 59, 999);
        break;
      case 'week':
        const dayOfWeek = now.getDay();
        startDate.setDate(now.getDate() - dayOfWeek);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'month':
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case 'custom':
        if (customStartDate && customEndDate) {
          startDate = new Date(customStartDate);
          endDate = new Date(customEndDate);
          endDate.setHours(23, 59, 59, 999);
        }
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    const selectedOption = options.find(opt => opt.value === rangeValue);
    return {
      value: rangeValue,
      label: selectedOption?.label || rangeValue,
      startDate,
      endDate
    };
  };

  const handleRangeChange = (newValue: string) => {
    if (newValue === 'custom') {
      setShowCustomPicker(true);
      return;
    }

    setShowCustomPicker(false);
    const dateRange = calculateDateRange(newValue);
    onChange(newValue, dateRange);
  };

  const handleCustomRangeApply = () => {
    if (!customStartDate || !customEndDate) {
      alert('Please select both start and end dates');
      return;
    }

    const startDate = new Date(customStartDate);
    const endDate = new Date(customEndDate);

    if (startDate > endDate) {
      alert('Start date cannot be after end date');
      return;
    }

    const dateRange = calculateDateRange('custom');
    onChange('custom', dateRange);
    setShowCustomPicker(false);
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-2 py-1 text-xs';
      case 'lg':
        return 'px-4 py-3 text-base';
      default:
        return 'px-3 py-2 text-sm';
    }
  };

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const getCurrentLabel = () => {
    if (value === 'custom' && customStartDate && customEndDate) {
      const start = new Date(customStartDate).toLocaleDateString();
      const end = new Date(customEndDate).toLocaleDateString();
      return `${start} - ${end}`;
    }
    return options.find(opt => opt.value === value)?.label || 'Select range';
  };

  useEffect(() => {
    // Initialize with current value if not custom
    if (value && value !== 'custom') {
      const dateRange = calculateDateRange(value);
      onChange(value, dateRange);
    }
  }, []);

  return (
    <div className={`relative ${className}`}>
      <div className="flex items-center space-x-2">
        {showIcon && <Calendar className="w-4 h-4 text-gray-500" />}
        
        <select
          value={value}
          onChange={(e) => handleRangeChange(e.target.value)}
          className={`
            form-select border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
            ${getSizeClasses()}
            ${className}
          `}
        >
          {options.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      {/* Custom Date Range Picker */}
      {showCustomPicker && (
        <div className="absolute top-full left-0 mt-2 p-4 bg-white border border-gray-300 rounded-lg shadow-lg z-50 min-w-80">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Custom Date Range</h4>
          
          <div className="grid grid-cols-2 gap-3 mb-4">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <input
                type="date"
                value={customStartDate}
                onChange={(e) => setCustomStartDate(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                max={formatDateForInput(new Date())}
              />
            </div>
            
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                End Date
              </label>
              <input
                type="date"
                value={customEndDate}
                onChange={(e) => setCustomEndDate(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                min={customStartDate}
                max={formatDateForInput(new Date())}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setShowCustomPicker(false)}
              className="px-3 py-1 text-xs font-medium text-gray-700 bg-gray-100 rounded hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleCustomRangeApply}
              className="px-3 py-1 text-xs font-medium text-white bg-blue-600 rounded hover:bg-blue-700"
            >
              Apply
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangeSelector;
