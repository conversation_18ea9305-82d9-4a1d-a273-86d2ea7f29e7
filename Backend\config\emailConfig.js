/**
 * 增强的邮件服务配置
 */

const { logger } = require('./logger');
const { sanitizeLogInput } = require('../utils/logSanitizer');

/**
 * 邮件服务提供商配置
 */
const EMAIL_PROVIDERS = {
  gmail: {
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'Gmail SMTP',
    rateLimit: {
      maxConnections: 5,
      maxMessages: 100
    }
  },
  sendgrid: {
    host: 'smtp.sendgrid.net',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'SendGrid',
    rateLimit: {
      maxConnections: 10,
      maxMessages: 1000
    }
  },
  aws_ses: {
    host: process.env.AWS_SES_HOST || 'email-smtp.us-east-1.amazonaws.com',
    port: 587,
    secure: false,
    requireTLS: true,
    description: 'Amazon SES',
    rateLimit: {
      maxConnections: 10,
      maxMessages: 200
    }
  },
  custom: {
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: process.env.EMAIL_SECURE === 'true',
    requireTLS: process.env.EMAIL_REQUIRE_TLS !== 'false',
    description: '自定义SMTP服务器',
    rateLimit: {
      maxConnections: 5,
      maxMessages: 50
    }
  }
};

/**
 * 验证邮件配置
 */
const validateEmailConfig = () => {
  const requiredVars = ['EMAIL_USER', 'EMAIL_PASS', 'EMAIL_FROM'];
  const missing = requiredVars.filter(varName => !process.env[varName]);

  if (missing.length > 0) {
    throw new Error(`Missing email configuration: ${missing.join(', ')}`);
  }

  const provider = process.env.EMAIL_PROVIDER || 'gmail';
  const config = EMAIL_PROVIDERS[provider];

  if (!config) {
    throw new Error(`Unknown email provider: ${provider}`);
  }

  if (provider === 'custom' && !process.env.EMAIL_HOST) {
    throw new Error('EMAIL_HOST is required for custom email provider');
  }

  logger.info('Email configuration validated', {
    provider: sanitizeLogInput(provider),
    host: sanitizeLogInput(config.host),
    port: config.port
  });

  return true;
};

/**
 * 获取邮件传输器配置
 */
const getTransporterConfig = () => {
  validateEmailConfig();

  const provider = process.env.EMAIL_PROVIDER || 'gmail';
  const config = EMAIL_PROVIDERS[provider];

  const transporterConfig = {
    host: config.host,
    port: config.port,
    secure: config.secure,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    },
    tls: {
      rejectUnauthorized: process.env.NODE_ENV === 'production'
    }
  };

  // 生产环境连接池配置
  if (process.env.NODE_ENV === 'production') {
    transporterConfig.pool = true;
    transporterConfig.maxConnections = config.rateLimit.maxConnections;
    transporterConfig.maxMessages = config.rateLimit.maxMessages;
    transporterConfig.rateDelta = 1000; // 1秒
    transporterConfig.rateLimit = 10; // 每秒最多10封邮件
  }

  // 重试配置
  transporterConfig.retryDelay = 1000;
  transporterConfig.maxRetries = 3;

  return transporterConfig;
};

/**
 * 邮件模板配置
 */
const EMAIL_TEMPLATES = {
  verification: {
    subject: 'Newzora - 验证您的邮箱地址',
    template: 'email-verification'
  },
  passwordReset: {
    subject: 'Newzora - 密码重置请求',
    template: 'password-reset'
  },
  welcome: {
    subject: 'Newzora - 欢迎加入！',
    template: 'welcome'
  },
  notification: {
    subject: 'Newzora - 新通知',
    template: 'notification'
  },
  dailySummary: {
    subject: 'Newzora - 每日摘要',
    template: 'daily-summary'
  },
  securityAlert: {
    subject: 'Newzora - 安全警报',
    template: 'security-alert'
  }
};

/**
 * 邮件发送配置
 */
const EMAIL_CONFIG = {
  from: process.env.EMAIL_FROM || 'Newzora <<EMAIL>>',
  replyTo: process.env.EMAIL_REPLY_TO,
  
  // 邮件队列配置
  queue: {
    enabled: process.env.EMAIL_QUEUE_ENABLED === 'true',
    maxRetries: 3,
    retryDelay: 5000, // 5秒
    batchSize: 10
  },

  // 邮件限制
  limits: {
    maxRecipientsPerEmail: 50,
    maxEmailsPerHour: 1000,
    maxEmailsPerDay: 10000
  },

  // 邮件验证
  validation: {
    validateRecipients: true,
    allowedDomains: process.env.ALLOWED_EMAIL_DOMAINS?.split(',') || [],
    blockedDomains: process.env.BLOCKED_EMAIL_DOMAINS?.split(',') || []
  }
};

/**
 * 验证邮箱地址
 */
const validateEmailAddress = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    return { valid: false, reason: 'Invalid email format' };
  }

  const domain = email.split('@')[1].toLowerCase();

  // 检查允许的域名
  if (EMAIL_CONFIG.validation.allowedDomains.length > 0) {
    if (!EMAIL_CONFIG.validation.allowedDomains.includes(domain)) {
      return { valid: false, reason: 'Domain not allowed' };
    }
  }

  // 检查被阻止的域名
  if (EMAIL_CONFIG.validation.blockedDomains.includes(domain)) {
    return { valid: false, reason: 'Domain blocked' };
  }

  return { valid: true };
};

/**
 * 邮件发送统计
 */
class EmailStats {
  constructor() {
    this.stats = {
      sent: 0,
      failed: 0,
      hourly: new Map(),
      daily: new Map()
    };
  }

  recordSent() {
    this.stats.sent++;
    this.recordHourly();
    this.recordDaily();
  }

  recordFailed() {
    this.stats.failed++;
  }

  recordHourly() {
    const hour = new Date().getHours();
    const current = this.stats.hourly.get(hour) || 0;
    this.stats.hourly.set(hour, current + 1);
  }

  recordDaily() {
    const day = new Date().toDateString();
    const current = this.stats.daily.get(day) || 0;
    this.stats.daily.set(day, current + 1);
  }

  getStats() {
    return {
      ...this.stats,
      hourly: Object.fromEntries(this.stats.hourly),
      daily: Object.fromEntries(this.stats.daily)
    };
  }

  checkLimits() {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.toDateString();

    const hourlyCount = this.stats.hourly.get(currentHour) || 0;
    const dailyCount = this.stats.daily.get(currentDay) || 0;

    return {
      hourlyLimitReached: hourlyCount >= EMAIL_CONFIG.limits.maxEmailsPerHour,
      dailyLimitReached: dailyCount >= EMAIL_CONFIG.limits.maxEmailsPerDay,
      hourlyCount,
      dailyCount
    };
  }
}

const emailStats = new EmailStats();

module.exports = {
  EMAIL_PROVIDERS,
  EMAIL_TEMPLATES,
  EMAIL_CONFIG,
  validateEmailConfig,
  getTransporterConfig,
  validateEmailAddress,
  emailStats
};