'use client';

import React, { useState } from 'react';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  <PERSON>ertT<PERSON>gle, 
  CheckCircle, 
  Clock,
  X,
  RotateCcw,
  Settings
} from 'lucide-react';
import { useDataSync } from '@/hooks/useDataSync';

interface DataSyncStatusProps {
  className?: string;
  showDetails?: boolean;
}

const DataSyncStatus: React.FC<DataSyncStatusProps> = ({ 
  className = '', 
  showDetails = false 
}) => {
  const {
    syncStatus,
    isLoading,
    forceSyncNow,
    retryPendingOperations,
    clearErrors,
    startAutoSync,
    stopAutoSync
  } = useDataSync();

  const [showDetailPanel, setShowDetailPanel] = useState(showDetails);

  const getConnectionIcon = () => {
    if (isLoading) {
      return <RefreshCw className="w-4 h-4 animate-spin" />;
    }
    
    return syncStatus.isConnected ? (
      <Wifi className="w-4 h-4 text-green-500" />
    ) : (
      <WifiOff className="w-4 h-4 text-red-500" />
    );
  };

  const getStatusColor = () => {
    if (!syncStatus.isConnected) return 'text-red-500';
    if (syncStatus.errors.length > 0) return 'text-yellow-500';
    if (syncStatus.pendingChanges > 0) return 'text-blue-500';
    return 'text-green-500';
  };

  const getStatusText = () => {
    if (!syncStatus.isConnected) return 'Disconnected';
    if (syncStatus.errors.length > 0) return 'Sync Errors';
    if (syncStatus.pendingChanges > 0) return 'Pending Changes';
    return 'Synchronized';
  };

  const formatLastSync = () => {
    if (!syncStatus.lastSync) return 'Never';
    
    const now = new Date();
    const lastSync = new Date(syncStatus.lastSync);
    const diffInMinutes = Math.floor((now.getTime() - lastSync.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    return lastSync.toLocaleDateString();
  };

  return (
    <div className={`relative ${className}`}>
      {/* Status Indicator */}
      <button
        onClick={() => setShowDetailPanel(!showDetailPanel)}
        className={`flex items-center space-x-2 px-3 py-1 rounded-lg border transition-colors ${
          syncStatus.isConnected 
            ? 'border-green-200 bg-green-50 hover:bg-green-100' 
            : 'border-red-200 bg-red-50 hover:bg-red-100'
        }`}
        title="Click to view sync details"
      >
        {getConnectionIcon()}
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        {syncStatus.pendingChanges > 0 && (
          <span className="inline-flex items-center justify-center px-2 py-0.5 text-xs font-bold leading-none text-white bg-blue-600 rounded-full">
            {syncStatus.pendingChanges}
          </span>
        )}
        {syncStatus.errors.length > 0 && (
          <AlertTriangle className="w-4 h-4 text-yellow-500" />
        )}
      </button>

      {/* Detail Panel */}
      {showDetailPanel && (
        <div className="absolute top-full right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Data Sync Status</h3>
              <button
                onClick={() => setShowDetailPanel(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Connection Status */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Connection</span>
                <div className="flex items-center space-x-2">
                  {syncStatus.isConnected ? (
                    <>
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-green-600">Connected</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="w-4 h-4 text-red-500" />
                      <span className="text-sm text-red-600">Disconnected</span>
                    </>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Sync</span>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-900">{formatLastSync()}</span>
                </div>
              </div>

              {syncStatus.pendingChanges > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Pending Changes</span>
                  <span className="text-sm font-medium text-blue-600">
                    {syncStatus.pendingChanges}
                  </span>
                </div>
              )}

              {syncStatus.errors.length > 0 && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Errors</span>
                    <span className="text-sm font-medium text-red-600">
                      {syncStatus.errors.length}
                    </span>
                  </div>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                    <div className="space-y-1">
                      {syncStatus.errors.slice(0, 3).map((error, index) => (
                        <div key={index} className="text-xs text-red-700">
                          {error}
                        </div>
                      ))}
                      {syncStatus.errors.length > 3 && (
                        <div className="text-xs text-red-600">
                          +{syncStatus.errors.length - 3} more errors
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="mt-4 space-y-2">
              <button
                onClick={forceSyncNow}
                disabled={isLoading}
                className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <RefreshCw className="w-4 h-4 animate-spin" />
                ) : (
                  <RefreshCw className="w-4 h-4" />
                )}
                <span>Sync Now</span>
              </button>

              {syncStatus.pendingChanges > 0 && (
                <button
                  onClick={retryPendingOperations}
                  disabled={isLoading}
                  className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <RotateCcw className="w-4 h-4" />
                  <span>Retry Pending ({syncStatus.pendingChanges})</span>
                </button>
              )}

              {syncStatus.errors.length > 0 && (
                <button
                  onClick={clearErrors}
                  className="w-full flex items-center justify-center space-x-2 px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                >
                  <X className="w-4 h-4" />
                  <span>Clear Errors</span>
                </button>
              )}
            </div>

            {/* Sync Settings */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Auto Sync</span>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={stopAutoSync}
                    className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
                  >
                    Stop
                  </button>
                  <button
                    onClick={startAutoSync}
                    className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200"
                  >
                    Start
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DataSyncStatus;
