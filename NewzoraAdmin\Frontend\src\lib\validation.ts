export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  url?: boolean;
  numeric?: boolean;
  min?: number;
  max?: number;
  custom?: (value: any) => string | null;
  message?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

export interface FormValidationSchema {
  [fieldName: string]: ValidationRule;
}

class FormValidator {
  private schema: FormValidationSchema;

  constructor(schema: FormValidationSchema) {
    this.schema = schema;
  }

  validate(data: Record<string, any>): ValidationResult {
    const errors: Record<string, string> = {};

    Object.keys(this.schema).forEach(fieldName => {
      const rule = this.schema[fieldName];
      const value = data[fieldName];
      const error = this.validateField(fieldName, value, rule);
      
      if (error) {
        errors[fieldName] = error;
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  validateField(fieldName: string, value: any, rule: ValidationRule): string | null {
    // Required validation
    if (rule.required && (value === undefined || value === null || value === '')) {
      return rule.message || `${fieldName} is required`;
    }

    // Skip other validations if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // String length validations
    if (typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        return rule.message || `${fieldName} must be at least ${rule.minLength} characters`;
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        return rule.message || `${fieldName} must not exceed ${rule.maxLength} characters`;
      }
    }

    // Pattern validation
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      return rule.message || `${fieldName} format is invalid`;
    }

    // Email validation
    if (rule.email && typeof value === 'string') {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(value)) {
        return rule.message || `${fieldName} must be a valid email address`;
      }
    }

    // URL validation
    if (rule.url && typeof value === 'string') {
      try {
        new URL(value);
      } catch {
        return rule.message || `${fieldName} must be a valid URL`;
      }
    }

    // Numeric validations
    if (rule.numeric) {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return rule.message || `${fieldName} must be a number`;
      }

      if (rule.min !== undefined && numValue < rule.min) {
        return rule.message || `${fieldName} must be at least ${rule.min}`;
      }

      if (rule.max !== undefined && numValue > rule.max) {
        return rule.message || `${fieldName} must not exceed ${rule.max}`;
      }
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        return customError;
      }
    }

    return null;
  }

  validateSingle(fieldName: string, value: any): string | null {
    const rule = this.schema[fieldName];
    if (!rule) return null;
    
    return this.validateField(fieldName, value, rule);
  }
}

// Common validation schemas
export const userValidationSchema: FormValidationSchema = {
  username: {
    required: true,
    minLength: 3,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: 'Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens'
  },
  email: {
    required: true,
    email: true,
    maxLength: 255
  },
  display_name: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  password: {
    required: true,
    minLength: 8,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must contain at least 8 characters with uppercase, lowercase, number, and special character'
  },
  bio: {
    maxLength: 500
  },
  website: {
    url: true
  }
};

export const articleValidationSchema: FormValidationSchema = {
  title: {
    required: true,
    minLength: 5,
    maxLength: 200
  },
  content: {
    required: true,
    minLength: 50,
    maxLength: 50000
  },
  category: {
    required: true
  },
  tags: {
    custom: (value: string[]) => {
      if (value && value.length > 10) {
        return 'Maximum 10 tags allowed';
      }
      return null;
    }
  }
};

export const categoryValidationSchema: FormValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 50
  },
  slug: {
    required: true,
    minLength: 2,
    maxLength: 50,
    pattern: /^[a-z0-9-]+$/,
    message: 'Slug must contain only lowercase letters, numbers, and hyphens'
  },
  description: {
    maxLength: 500
  },
  color: {
    required: true,
    pattern: /^#[0-9A-Fa-f]{6}$/,
    message: 'Color must be a valid hex color code'
  }
};

export const tagValidationSchema: FormValidationSchema = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 30,
    pattern: /^[a-zA-Z0-9\s-]+$/,
    message: 'Tag name can only contain letters, numbers, spaces, and hyphens'
  },
  slug: {
    required: true,
    minLength: 2,
    maxLength: 30,
    pattern: /^[a-z0-9-]+$/,
    message: 'Slug must contain only lowercase letters, numbers, and hyphens'
  },
  description: {
    maxLength: 200
  },
  color: {
    required: true,
    pattern: /^#[0-9A-Fa-f]{6}$/,
    message: 'Color must be a valid hex color code'
  }
};

export const settingsValidationSchema: FormValidationSchema = {
  siteName: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  siteDescription: {
    required: true,
    minLength: 10,
    maxLength: 500
  },
  siteUrl: {
    required: true,
    url: true
  },
  contactEmail: {
    required: true,
    email: true
  },
  supportEmail: {
    required: true,
    email: true
  },
  maxLoginAttempts: {
    required: true,
    numeric: true,
    min: 1,
    max: 10
  },
  sessionTimeout: {
    required: true,
    numeric: true,
    min: 5,
    max: 1440
  },
  passwordMinLength: {
    required: true,
    numeric: true,
    min: 6,
    max: 20
  }
};

// Utility functions
export const createValidator = (schema: FormValidationSchema) => {
  return new FormValidator(schema);
};

export const validateEmail = (email: string): boolean => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[@$!%*?&]/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export default FormValidator;
