/**
 * DOM safe operation utility functions
 */

/**
 * Safe DOM operation wrapper
 */
export const safeDOMOperation = (operation: () => void, fallback?: () => void) => {
  try {
    if (typeof window !== 'undefined' && document) {
      operation();
    }
  } catch (error) {
    console.warn('DOM operation failed safely:', error);
    fallback?.();
  }
};

/**
 * Safe element removal
 */
export const safeRemoveElement = (element: Element | null) => {
  if (!element || !element.parentNode) return;
  
  try {
    if (element.parentNode.contains(element)) {
      element.parentNode.removeChild(element);
    }
  } catch (error) {
    console.warn('Safe element removal failed:', error);
  }
};

/**
 * Safe event listener removal
 */
export const safeRemoveEventListener = (
  element: Element | Window | Document | null,
  event: string,
  handler: EventListener,
  options?: boolean | EventListenerOptions
) => {
  try {
    if (element && typeof element.removeEventListener === 'function') {
      element.removeEventListener(event, handler, options);
    }
  } catch (error) {
    console.warn('Safe event listener removal failed:', error);
  }
};

/**
 * Safe style setting
 */
export const safeSetStyle = (element: HTMLElement | null, styles: Partial<CSSStyleDeclaration>) => {
  if (!element) return;
  
  try {
    Object.assign(element.style, styles);
  } catch (error) {
    console.warn('Safe style setting failed:', error);
  }
};

/**
 * Safe class name operation
 */
export const safeToggleClass = (element: Element | null, className: string, force?: boolean) => {
  if (!element) return;
  
  try {
    if (typeof force !== 'undefined') {
      element.classList.toggle(className, force);
    } else {
      element.classList.toggle(className);
    }
  } catch (error) {
    console.warn('Safe class toggle failed:', error);
  }
};

/**
 * Debounce function - prevent frequent DOM operations
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function - limit DOM operation frequency
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};