#!/bin/bash

# OneNews SSL证书生成脚本
# 支持开发环境自签名证书和生产环境Let's Encrypt证书

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔒 OneNews SSL证书配置工具${NC}"
echo "=================================="

# 检查参数
if [ "$#" -eq 0 ]; then
    echo -e "${YELLOW}用法:${NC}"
    echo "  $0 dev                    # 生成开发环境自签名证书"
    echo "  $0 prod <domain>          # 生成生产环境Let's Encrypt证书"
    echo "  $0 staging <domain>       # 生成测试环境Let's Encrypt证书"
    exit 1
fi

ENV=$1
DOMAIN=${2:-"localhost"}

# 创建SSL目录
mkdir -p ssl/dev ssl/prod ssl/staging

case $ENV in
    "dev")
        echo -e "${YELLOW}📝 生成开发环境自签名证书...${NC}"
        
        # 生成私钥
        openssl genrsa -out ssl/dev/private.key 2048
        
        # 生成证书签名请求
        openssl req -new -key ssl/dev/private.key -out ssl/dev/cert.csr -subj "/C=CN/ST=Beijing/L=Beijing/O=OneNews/OU=Development/CN=localhost"
        
        # 生成自签名证书
        openssl x509 -req -days 365 -in ssl/dev/cert.csr -signkey ssl/dev/private.key -out ssl/dev/certificate.crt
        
        # 生成DH参数
        openssl dhparam -out ssl/dev/dhparam.pem 2048
        
        echo -e "${GREEN}✅ 开发环境SSL证书生成完成${NC}"
        echo -e "证书位置: ssl/dev/"
        echo -e "私钥: ssl/dev/private.key"
        echo -e "证书: ssl/dev/certificate.crt"
        ;;
        
    "prod"|"staging")
        if [ -z "$DOMAIN" ]; then
            echo -e "${RED}❌ 错误: 生产/测试环境需要指定域名${NC}"
            exit 1
        fi
        
        echo -e "${YELLOW}📝 为域名 $DOMAIN 生成 $ENV 环境证书...${NC}"
        
        # 检查certbot是否安装
        if ! command -v certbot &> /dev/null; then
            echo -e "${YELLOW}⚠️  certbot未安装，正在安装...${NC}"
            
            # 根据系统安装certbot
            if command -v apt-get &> /dev/null; then
                sudo apt-get update
                sudo apt-get install -y certbot
            elif command -v yum &> /dev/null; then
                sudo yum install -y certbot
            elif command -v brew &> /dev/null; then
                brew install certbot
            else
                echo -e "${RED}❌ 无法自动安装certbot，请手动安装${NC}"
                exit 1
            fi
        fi
        
        # 生成Let's Encrypt证书
        if [ "$ENV" = "staging" ]; then
            # 测试环境使用staging服务器
            sudo certbot certonly --standalone --staging \
                -d $DOMAIN \
                --email admin@$DOMAIN \
                --agree-tos \
                --non-interactive
        else
            # 生产环境使用正式服务器
            sudo certbot certonly --standalone \
                -d $DOMAIN \
                --email admin@$DOMAIN \
                --agree-tos \
                --non-interactive
        fi
        
        # 复制证书到项目目录
        sudo cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ssl/$ENV/certificate.crt
        sudo cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ssl/$ENV/private.key
        sudo chown $(whoami):$(whoami) ssl/$ENV/*
        
        # 生成DH参数
        openssl dhparam -out ssl/$ENV/dhparam.pem 2048
        
        echo -e "${GREEN}✅ $ENV 环境SSL证书生成完成${NC}"
        echo -e "证书位置: ssl/$ENV/"
        echo -e "域名: $DOMAIN"
        ;;
        
    *)
        echo -e "${RED}❌ 错误: 不支持的环境 '$ENV'${NC}"
        echo -e "支持的环境: dev, staging, prod"
        exit 1
        ;;
esac

# 设置证书权限
chmod 600 ssl/$ENV/private.key
chmod 644 ssl/$ENV/certificate.crt
chmod 644 ssl/$ENV/dhparam.pem

echo -e "${GREEN}🔒 SSL证书配置完成！${NC}"

# 生成nginx配置示例
cat > ssl/$ENV/nginx-ssl-example.conf << EOF
# OneNews $ENV 环境 SSL 配置示例
server {
    listen 80;
    server_name $DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    # SSL证书配置
    ssl_certificate /path/to/ssl/$ENV/certificate.crt;
    ssl_certificate_key /path/to/ssl/$ENV/private.key;
    ssl_dhparam /path/to/ssl/$ENV/dhparam.pem;

    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # 反向代理到OneNews应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

echo -e "${BLUE}📄 Nginx配置示例已生成: ssl/$ENV/nginx-ssl-example.conf${NC}"
