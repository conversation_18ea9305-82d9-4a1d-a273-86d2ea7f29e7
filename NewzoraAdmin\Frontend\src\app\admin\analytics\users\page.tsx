
'use client';

import React, { useState } from 'react';
import { Calendar, TrendingUp, Users, UserPlus, Activity } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import SafeDatePicker, { DateRange } from '@/components/admin/common/SafeDatePicker';

const UserAnalyticsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalUsers: 15420,
    newUsers: 1240,
    activeUsers: 8950,
    averageSessionTime: 12.5
  });

  // Mock data for user analytics
  const userGrowthData = [
    { date: '2023-06-01', total: 1200, new: 45, active: 980 },
    { date: '2023-06-02', total: 1250, new: 50, active: 1020 },
    { date: '2023-06-03', total: 1305, new: 55, active: 1070 },
    { date: '2023-06-04', total: 1360, new: 55, active: 1120 },
    { date: '2023-06-05', total: 1420, new: 60, active: 1180 },
    { date: '2023-06-06', total: 1480, new: 60, active: 1230 },
    { date: '2023-06-07', total: 1540, new: 60, active: 1280 },
  ];

  const userActivityData = [
    { time: '00:00', count: 120 },
    { time: '04:00', count: 80 },
    { time: '08:00', count: 320 },
    { time: '12:00', count: 580 },
    { time: '16:00', count: 640 },
    { time: '20:00', count: 420 },
    { time: '24:00', count: 180 },
  ];

  const userDemographicsData = [
    { age: '18-24', percentage: 25 },
    { age: '25-34', percentage: 35 },
    { age: '35-44', percentage: 20 },
    { age: '45-54', percentage: 12 },
    { age: '55+', percentage: 8 },
  ];

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);
    setIsLoading(true);

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    // 模拟数据加载和更新
    setTimeout(() => {
      setStats({
        totalUsers: Math.floor(15420 * multiplier),
        newUsers: Math.floor(1240 * Math.min(1, multiplier)), // 新用户不会超过总用户
        activeUsers: Math.floor(8950 * multiplier),
        averageSessionTime: 12.5 + (Math.random() - 0.5) * 2 // 小幅波动
      });
      setIsLoading(false);
    }, 1000);
  };

  const handleTimeRangeChange = (range: string) => {
    setTimeRange(range);
    setIsLoading(true);
    // In a real app, this would fetch new data
    setTimeout(() => setIsLoading(false), 500);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">User Analytics</h1>
        <div className="flex items-center space-x-2">
          <SafeDatePicker
            value={currentDateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            className="w-64"
          />
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <Users className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Total Users</h2>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalUsers.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <UserPlus className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">New Users</h2>
              <p className="text-2xl font-semibold text-gray-900">{stats.newUsers.toLocaleString()}</p>
              <p className="text-xs text-green-600">+12.5% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-100 text-purple-600">
              <Activity className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Active Users</h2>
              <p className="text-2xl font-semibold text-gray-900">{stats.activeUsers.toLocaleString()}</p>
              <p className="text-xs text-green-600">+8.3% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <TrendingUp className="h-6 w-6" />
            </div>
            <div className="ml-4">
              <h2 className="text-sm font-medium text-gray-500">Avg Session Time</h2>
              <p className="text-2xl font-semibold text-gray-900">{stats.averageSessionTime.toFixed(1)}m</p>
              <p className="text-xs text-green-600">+2.4% from last period</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Growth Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">User Growth</h2>
            <div className="flex items-center text-sm text-gray-500">
              <Calendar className="h-4 w-4 mr-1" />
              {currentDateRange ?
                `${currentDateRange.startDate.toLocaleDateString('en-US')} - ${currentDateRange.endDate.toLocaleDateString('en-US')}` :
                'Select date range'
              }
            </div>
          </div>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={userGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString('en-US')}
                    formatter={(value, name) => [value, name === 'total' ? 'Total Users' : name === 'new' ? 'New Users' : 'Active Users']}
                  />
                  <Line
                    type="monotone"
                    dataKey="total"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    name="Total Users"
                  />
                  <Line
                    type="monotone"
                    dataKey="new"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="New Users"
                  />
                  <Line
                    type="monotone"
                    dataKey="active"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="Active Users"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>

        {/* User Activity Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">User Activity by Time of Day</h2>

          {isLoading ? (
            <div className="h-64 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={userActivityData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [value, 'Active Users']}
                    labelFormatter={(label) => `Time: ${label}`}
                  />
                  <Bar
                    dataKey="count"
                    fill="#10b981"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </div>

      {/* User Demographics */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">User Demographics</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Age Distribution */}
          <div>
            <h3 className="text-md font-medium text-gray-700 mb-3">Age Distribution</h3>
            <div className="space-y-3">
              {userDemographicsData.map((data, index) => (
                <div key={index}>
                  <div className="flex justify-between mb-1">
                    <span className="text-sm text-gray-600">{data.age}</span>
                    <span className="text-sm font-medium text-gray-900">{data.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${data.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* User Engagement */}
          <div>
            <h3 className="text-md font-medium text-gray-700 mb-3">User Engagement</h3>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Daily Active Users</span>
                  <span className="text-sm font-medium text-gray-900">1,280</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: '83%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Weekly Active Users</span>
                  <span className="text-sm font-medium text-gray-900">1,420</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full" style={{ width: '92%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-600">Monthly Active Users</span>
                  <span className="text-sm font-medium text-gray-900">1,490</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full" style={{ width: '97%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserAnalyticsPage;
