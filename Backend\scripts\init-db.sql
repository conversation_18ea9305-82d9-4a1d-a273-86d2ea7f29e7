-- Newzora Web 数据库初始化脚本
-- 创建数据库和基础配置

-- 创建数据库（如果不存在）
SELECT 'CREATE DATABASE "PostgreSQL-newzora_web"'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'PostgreSQL-newzora_web');

-- 连接到 PostgreSQL-newzora_web 数据库
\c "PostgreSQL-newzora_web";

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建全文搜索配置
CREATE TEXT SEARCH CONFIGURATION IF NOT EXISTS chinese (COPY = simple);

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建基础索引（Sequelize会自动创建表结构）
-- 这里只是预留一些可能需要的配置

-- 创建用户角色（如果需要）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'newzora_web_app') THEN
        CREATE ROLE newzora_web_app WITH LOGIN PASSWORD 'wasd080980!';
    END IF;
END
$$;

-- 授权
GRANT CONNECT ON DATABASE "PostgreSQL-newzora_web" TO newzora_web_app;
GRANT USAGE ON SCHEMA public TO newzora_web_app;
GRANT CREATE ON SCHEMA public TO newzora_web_app;

-- 输出初始化完成信息
SELECT 'Newzora Web database initialized successfully' as status;
