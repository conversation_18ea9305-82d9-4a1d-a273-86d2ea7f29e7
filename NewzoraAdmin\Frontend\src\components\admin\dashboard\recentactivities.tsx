'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>heck, UserPlus } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface Activity {
  id: string;
  type: 'user_post' | 'content_approved' | 'user_registered';
  message: string;
  timestamp: string;
  read: boolean;
  link?: string;
}

const RecentActivities: React.FC = () => {
  const router = useRouter();
  const [activities, setActivities] = useState<Activity[]>([
    {
      id: '1',
      type: 'user_post',
      message: 'User <PERSON> published a new article',
      timestamp: '2 minutes ago',
      read: false,
      link: '/admin/content'
    },
    {
      id: '2',
      type: 'content_approved',
      message: '<PERSON><PERSON> approved an article',
      timestamp: '5 minutes ago',
      read: false,
      link: '/admin/content'
    },
    {
      id: '3',
      type: 'user_registered',
      message: 'New user <PERSON> completed registration',
      timestamp: '10 minutes ago',
      read: false,
      link: '/admin/users'
    },
    {
      id: '4',
      type: 'user_post',
      message: 'User <PERSON> published a new comment',
      timestamp: '15 minutes ago',
      read: true,
      link: '/admin/content'
    },
    {
      id: '5',
      type: 'content_approved',
      message: 'Admin <PERSON> approved a comment',
      timestamp: '20 minutes ago',
      read: true,
      link: '/admin/content'
    }
  ]);

  const markAsRead = (id: string) => {
    setActivities(prev => 
      prev.map(activity => 
        activity.id === id ? { ...activity, read: true } : activity
      )
    );
  };

  const markAllAsRead = () => {
    setActivities(prev => 
      prev.map(activity => ({ ...activity, read: true }))
    );
  };

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'user_post':
        return <FileCheck className="text-blue-600" size={16} />;
      case 'content_approved':
        return <UserCheck className="text-green-600" size={16} />;
      case 'user_registered':
        return <UserPlus className="text-yellow-600" size={16} />;
      default:
        return null;
    }
  };

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'user_post':
        return 'bg-blue-600';
      case 'content_approved':
        return 'bg-green-600';
      case 'user_registered':
        return 'bg-yellow-600';
      default:
        return 'bg-gray-600';
    }
  };

  const unreadCount = activities.filter(a => !a.read).length;

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
        {unreadCount > 0 && (
          <button 
            onClick={markAllAsRead}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Mark all as read
          </button>
        )}
      </div>
      
      <div className="space-y-3">
        {activities.map((activity) => (
          <div 
            key={activity.id}
            className={`flex items-start space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 ${!activity.read ? 'bg-blue-50' : ''}`}
            onClick={() => {
              markAsRead(activity.id);
              if (activity.link) {
                router.push(activity.link);
              }
            }}
          >
            <div className="flex-shrink-0 mt-1">
              <div className={`w-2 h-2 rounded-full ${getActivityColor(activity.type)}`}></div>
            </div>
            <div className="flex items-start space-x-2 flex-1 min-w-0">
              <div className="flex-shrink-0 mt-0.5">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm text-gray-900 truncate">{activity.message}</div>
                <div className="text-xs text-gray-500">{activity.timestamp}</div>
              </div>
              {!activity.read && (
                <div className="flex-shrink-0">
                  <span className="w-2 h-2 bg-blue-600 rounded-full inline-block"></span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecentActivities;