const { User, Article, Comment, sequelize } = require('../../models');
const { Op } = require('sequelize');

class DashboardService {
  // 获取仪表板概览统计
  async getOverviewStats() {
    try {
      const today = new Date();
      const startOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const startOfYesterday = new Date(startOfToday.getTime() - 24 * 60 * 60 * 1000);
      
      // 并行查询所有统计数据
      const [
        totalUsers,
        todayNewUsers,
        yesterdayNewUsers,
        totalArticles,
        todayNewArticles,
        yesterdayNewArticles,
        totalComments,
        todayNewComments,
        yesterdayNewComments,
        activeUsers
      ] = await Promise.all([
        // 用户统计
        User.count(),
        User.count({ where: { created_at: { [Op.gte]: startOfToday } } }),
        User.count({ 
          where: { 
            created_at: { 
              [Op.gte]: startOfYesterday,
              [Op.lt]: startOfToday
            }
          }
        }),
        
        // 文章统计
        Article.count({ where: { published: true } }),
        Article.count({ 
          where: { 
            published: true,
            created_at: { [Op.gte]: startOfToday }
          }
        }),
        Article.count({ 
          where: { 
            published: true,
            created_at: { 
              [Op.gte]: startOfYesterday,
              [Op.lt]: startOfToday
            }
          }
        }),
        
        // 评论统计
        Comment.count(),
        Comment.count({ where: { created_at: { [Op.gte]: startOfToday } } }),
        Comment.count({ 
          where: { 
            created_at: { 
              [Op.gte]: startOfYesterday,
              [Op.lt]: startOfToday
            }
          }
        }),
        
        // 活跃用户（7天内登录）
        User.count({ 
          where: { 
            last_login_at: { 
              [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) 
            }
          }
        })
      ]);

      return {
        users: {
          total: totalUsers,
          todayNew: todayNewUsers,
          activeUsers: activeUsers,
          change: yesterdayNewUsers > 0 ? 
            ((todayNewUsers - yesterdayNewUsers) / yesterdayNewUsers * 100).toFixed(1) : 0
        },
        content: {
          totalArticles: totalArticles,
          todayPublished: todayNewArticles,
          pendingReview: 0, // TODO: 实现审核队列统计
          change: yesterdayNewArticles > 0 ? 
            ((todayNewArticles - yesterdayNewArticles) / yesterdayNewArticles * 100).toFixed(1) : 0
        },
        engagement: {
          totalComments: totalComments,
          todayComments: todayNewComments,
          averageReadTime: 5, // TODO: 实现阅读时间统计
          change: yesterdayNewComments > 0 ? 
            ((todayNewComments - yesterdayNewComments) / yesterdayNewComments * 100).toFixed(1) : 0
        }
      };
    } catch (error) {
      console.error('获取仪表板统计失败:', error);
      throw new Error('获取仪表板统计失败');
    }
  }

  // 获取用户增长趋势
  async getUserGrowthTrend(days = 30) {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

      const result = await User.findAll({
        attributes: [
          [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: {
          created_at: {
            [Op.between]: [startDate, endDate]
          }
        },
        group: [sequelize.fn('DATE', sequelize.col('created_at'))],
        order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // 填充缺失的日期
      const trendData = [];
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const dateStr = date.toISOString().split('T')[0];
        const found = result.find(item => item.date === dateStr);
        
        trendData.push({
          date: dateStr,
          count: found ? parseInt(found.count) : 0
        });
      }

      return trendData;
    } catch (error) {
      console.error('获取用户增长趋势失败:', error);
      throw new Error('获取用户增长趋势失败');
    }
  }

  // 获取内容发布趋势
  async getContentTrend(days = 30) {
    try {
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);

      const result = await Article.findAll({
        attributes: [
          [sequelize.fn('DATE', sequelize.col('created_at')), 'date'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: {
          created_at: {
            [Op.between]: [startDate, endDate]
          },
          published: true
        },
        group: [sequelize.fn('DATE', sequelize.col('created_at'))],
        order: [[sequelize.fn('DATE', sequelize.col('created_at')), 'ASC']],
        raw: true
      });

      // 填充缺失的日期
      const trendData = [];
      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
        const dateStr = date.toISOString().split('T')[0];
        const found = result.find(item => item.date === dateStr);
        
        trendData.push({
          date: dateStr,
          count: found ? parseInt(found.count) : 0
        });
      }

      return trendData;
    } catch (error) {
      console.error('获取内容趋势失败:', error);
      throw new Error('获取内容趋势失败');
    }
  }

  // 获取分类统计
  async getCategoryStats() {
    try {
      const result = await Article.findAll({
        attributes: [
          'category',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: { 
          published: true,
          category: { [Op.ne]: null }
        },
        group: ['category'],
        order: [[sequelize.fn('COUNT', sequelize.col('id')), 'DESC']],
        limit: 10,
        raw: true
      });

      return result.map(item => ({
        category: item.category || '未分类',
        count: parseInt(item.count)
      }));
    } catch (error) {
      console.error('获取分类统计失败:', error);
      throw new Error('获取分类统计失败');
    }
  }

  // 获取最新活动
  async getRecentActivities(limit = 10) {
    try {
      // 获取最新用户注册
      const recentUsers = await User.findAll({
        attributes: ['id', 'username', 'display_name', 'created_at'],
        order: [['created_at', 'DESC']],
        limit: Math.ceil(limit / 3)
      });

      // 获取最新文章发布
      const recentArticles = await Article.findAll({
        attributes: ['id', 'title', 'created_at'],
        include: [{
          model: User,
          as: 'author',
          attributes: ['username', 'display_name']
        }],
        where: { published: true },
        order: [['created_at', 'DESC']],
        limit: Math.ceil(limit / 3)
      });

      // 获取最新评论
      const recentComments = await Comment.findAll({
        attributes: ['id', 'content', 'created_at'],
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['username', 'display_name']
          },
          {
            model: Article,
            as: 'article',
            attributes: ['title']
          }
        ],
        order: [['created_at', 'DESC']],
        limit: Math.ceil(limit / 3)
      });

      // 合并并排序活动
      const activities = [];

      recentUsers.forEach(user => {
        activities.push({
          type: 'user_register',
          message: `新用户 ${user.display_name} 完成注册`,
          time: user.created_at,
          data: user
        });
      });

      recentArticles.forEach(article => {
        activities.push({
          type: 'article_publish',
          message: `${article.author.display_name} 发布了文章《${article.title}》`,
          time: article.created_at,
          data: article
        });
      });

      recentComments.forEach(comment => {
        activities.push({
          type: 'comment_create',
          message: `${comment.author.display_name} 评论了《${comment.article.title}》`,
          time: comment.created_at,
          data: comment
        });
      });

      // 按时间排序并限制数量
      return activities
        .sort((a, b) => new Date(b.time) - new Date(a.time))
        .slice(0, limit);
    } catch (error) {
      console.error('获取最新活动失败:', error);
      throw new Error('获取最新活动失败');
    }
  }
}

module.exports = new DashboardService();