'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { Users, FileText, MessageCircle, TrendingUp } from 'lucide-react';
import SafeDatePicker, { DateRange } from '@/components/admin/common/SafeDatePicker';
import StatsCard from '@/components/admin/dashboard/StatsCard';
import { supabaseService } from '@/services/supabaseService';

const AnalyticsPage: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [dynamicStats, setDynamicStats] = useState({
    totalViews: 245680,
    totalUsers: 15420,
    totalArticles: 2340,
    totalComments: 8920
  });

  // Mock data
  const userGrowthData = [
    { date: '2024-12-01', users: 120 },
    { date: '2024-12-02', users: 132 },
    { date: '2024-12-03', users: 145 },
    { date: '2024-12-04', users: 158 },
    { date: '2024-12-05', users: 167 },
    { date: '2024-12-06', users: 178 },
    { date: '2024-12-07', users: 189 }
  ];

  const contentData = [
    { category: 'Technology', count: 45 },
    { category: 'Lifestyle', count: 32 },
    { category: 'Personal', count: 28 },
    { category: 'Tutorial', count: 21 },
    { category: 'Others', count: 15 }
  ];

  const activityData = [
    { time: '00:00', users: 12 },
    { time: '04:00', users: 8 },
    { time: '08:00', users: 45 },
    { time: '12:00', users: 78 },
    { time: '16:00', users: 65 },
    { time: '20:00', users: 89 },
    { time: '23:59', users: 34 }
  ];

  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'];

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);
    setLoading(true);

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    // 模拟数据加载和更新
    setTimeout(() => {
      setDynamicStats({
        totalViews: Math.floor(245680 * multiplier),
        totalUsers: Math.floor(15420 * multiplier),
        totalArticles: Math.floor(2340 * multiplier),
        totalComments: Math.floor(8920 * multiplier)
      });

      // 同时更新stats用于StatsCard显示
      setStats({
        users: {
          total: Math.floor(15420 * multiplier),
          activeUsers: Math.floor(8950 * multiplier)
        },
        content: {
          totalArticles: Math.floor(2340 * multiplier)
        },
        engagement: {
          totalComments: Math.floor(8920 * multiplier)
        }
      });

      setLoading(false);
    }, 1000);
  };

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const filters: any = {};
        if (currentDateRange) {
          filters.dateRange = currentDateRange;
        }
        const data = await supabaseService.getDashboardStats(filters);
        setStats(data);
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [currentDateRange]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page title */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
        <div className="flex items-center space-x-2">
          <SafeDatePicker
            value={currentDateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            className="w-64"
          />
          <button className="btn-secondary text-sm">Export Report</button>
        </div>
      </div>

      {/* Statistics cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value={stats?.users.total || 0}
          change={{ value: 12, type: 'increase' }}
          icon={<Users />}
          color="blue"
        />
        <StatsCard
          title="Total Articles"
          value={stats?.content.totalArticles || 0}
          change={{ value: 8, type: 'increase' }}
          icon={<FileText />}
          color="green"
        />
        <StatsCard
          title="Total Comments"
          value={stats?.engagement.totalComments || 0}
          change={{ value: 5, type: 'decrease' }}
          icon={<MessageCircle />}
          color="yellow"
        />
        <StatsCard
          title="Active Users"
          value={stats?.users.activeUsers || 0}
          change={{ value: 15, type: 'increase' }}
          icon={<TrendingUp />}
          color="red"
        />
      </div>

      {/* Charts area */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User growth trend */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Growth Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={userGrowthData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="users" stroke="#3b82f6" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Content category distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Content Category Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={contentData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {contentData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* More charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User activity time distribution */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Activity Time Distribution</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={activityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="users" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Popular content ranking */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Popular Content Ranking</h3>
          <div className="space-y-4">
            {[
              { title: 'React 18 New Features Explained', views: 1234, likes: 89 },
              { title: 'TypeScript Best Practices', views: 987, likes: 76 },
              { title: 'Next.js 14 Upgrade Guide', views: 856, likes: 65 },
              { title: 'Tailwind CSS Tips & Tricks', views: 743, likes: 54 },
              { title: 'JavaScript Performance Optimization', views: 621, likes: 43 }
            ].map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <span className="w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center mr-3">
                    {index + 1}
                  </span>
                  <span className="font-medium text-gray-900">{item.title}</span>
                </div>
                <div className="text-sm text-gray-500">
                  {item.views} views · {item.likes} likes
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;