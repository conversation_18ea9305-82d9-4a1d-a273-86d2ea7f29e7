'use client';

import React, { useState, useEffect } from 'react';
import { Search, Filter, Play, Eye, Heart, MessageCircle, Calendar, User, Music, Upload, Download, Edit, Trash2, Settings } from 'lucide-react';
import DataTable, { DataTableColumn } from '@/components/admin/common/DataTable';
import AudioUploadModal from '@/components/admin/media/AudioUploadModal';
import AudioPreviewModal from '@/components/admin/media/AudioPreviewModal';
import AudioEditModal from '@/components/admin/media/AudioEditModal';

interface Audio {
  id: string;
  title: string;
  description: string;
  cover: string;
  duration: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  category: string;
  tags: string[];
  status: 'published' | 'draft' | 'processing' | 'failed';
  publishedAt?: string;
  createdAt: string;
  views: number;
  likes: number;
  comments: number;
  fileSize: string;
  format: string;
}

const AudioPage: React.FC = () => {
  const [audios, setAudios] = useState<Audio[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [selectedAudios, setSelectedAudios] = useState<string[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    total: 0,
    pageSize: 20
  });

  // Modal states
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedAudio, setSelectedAudio] = useState<Audio | null>(null);

  const columns: DataTableColumn<Audio>[] = [
    {
      key: 'title',
      title: 'Audio Info',
      render: (_, record) => (
        <div className="flex items-center space-x-3">
          <div className="relative">
            <img 
              src={record.cover} 
              alt={record.title}
              className="w-16 h-16 object-cover rounded"
            />
            <div className="absolute inset-0 flex items-center justify-center">
              <Play className="w-4 h-4 text-white" />
            </div>
            <div className="absolute bottom-1 right-1 bg-black bg-opacity-75 text-white text-xs px-1 rounded">
              {record.duration}
            </div>
          </div>
          <div>
            <div className="font-medium text-gray-900 truncate max-w-xs">{record.title}</div>
            <div className="text-sm text-gray-500 flex items-center">
              <User className="w-3 h-3 mr-1" />
              {record.author.name}
            </div>
            <div className="text-xs text-gray-400">{record.format} • {record.fileSize}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      title: 'Category',
      sortable: true,
      render: (value) => (
        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">
          {value || 'Uncategorized'}
        </span>
      )
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      render: (value) => {
        const statusConfig = {
          published: { bg: 'bg-green-100', text: 'text-green-800', label: 'Published' },
          draft: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Draft' },
          processing: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Processing' },
          failed: { bg: 'bg-red-100', text: 'text-red-800', label: 'Failed' }
        };
        const config = statusConfig[value as keyof typeof statusConfig];
        return (
          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${config.bg} ${config.text}`}>
            {config.label}
          </span>
        );
      }
    },
    {
      key: 'views',
      title: 'Statistics',
      render: (_, record) => (
        <div className="text-sm text-gray-900">
          <div className="flex items-center">
            <Eye className="w-3 h-3 mr-1" />
            {record.views.toLocaleString()}
          </div>
          <div className="flex items-center">
            <Heart className="w-3 h-3 mr-1" />
            {record.likes.toLocaleString()}
          </div>
          <div className="flex items-center">
            <MessageCircle className="w-3 h-3 mr-1" />
            {record.comments.toLocaleString()}
          </div>
        </div>
      )
    },
    {
      key: 'createdAt',
      title: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          <div className="flex items-center">
            <Calendar className="w-3 h-3 mr-1" />
            {new Date(value).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, record) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handlePreview(record)}
            className="text-blue-600 hover:text-blue-900 text-sm flex items-center"
            title="Play Audio"
          >
            <Play className="w-3 h-3 mr-1" />
            Play
          </button>
          <button
            onClick={() => handleEdit(record)}
            className="text-green-600 hover:text-green-900 text-sm flex items-center"
            title="Edit Audio"
          >
            <Edit className="w-3 h-3 mr-1" />
            Edit
          </button>
          <button
            onClick={() => handleDownload(record)}
            className="text-purple-600 hover:text-purple-900 text-sm flex items-center"
            title="Download Audio"
          >
            <Download className="w-3 h-3 mr-1" />
            Download
          </button>
          <button
            onClick={() => handleDelete(record)}
            className="text-red-600 hover:text-red-900 text-sm flex items-center"
            title="Delete Audio"
          >
            <Trash2 className="w-3 h-3 mr-1" />
            Delete
          </button>
        </div>
      )
    }
  ];

  const fetchAudios = async () => {
    try {
      setLoading(true);
      // Mock data - in real app, this would fetch from API
      const mockAudios: Audio[] = [
        {
          id: '1',
          title: 'Podcast: Tech Trends 2024',
          description: 'Discussion about the latest technology trends and innovations',
          cover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=300&fit=crop',
          duration: '45:32',
          author: { id: '1', name: 'Tech Talk Team' },
          category: 'Podcast',
          tags: ['Technology', 'Trends', 'Discussion'],
          status: 'published',
          publishedAt: '2024-01-15T10:00:00Z',
          createdAt: '2024-01-15T09:00:00Z',
          views: 8450,
          likes: 542,
          comments: 89,
          fileSize: '65 MB',
          format: 'MP3'
        },
        {
          id: '2',
          title: 'Relaxing Piano Music',
          description: 'Peaceful piano melodies for relaxation and focus',
          cover: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?w=300&h=300&fit=crop',
          duration: '1:12:45',
          author: { id: '2', name: 'Piano Maestro' },
          category: 'Music',
          tags: ['Piano', 'Relaxation', 'Instrumental'],
          status: 'published',
          publishedAt: '2024-01-14T14:30:00Z',
          createdAt: '2024-01-14T13:00:00Z',
          views: 15760,
          likes: 1254,
          comments: 156,
          fileSize: '98 MB',
          format: 'MP3'
        },
        {
          id: '3',
          title: 'JavaScript Fundamentals Course',
          description: 'Complete audio course covering JavaScript basics',
          cover: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=300&h=300&fit=crop',
          duration: '2:34:18',
          author: { id: '3', name: 'Code Academy' },
          category: 'Education',
          tags: ['JavaScript', 'Programming', 'Course'],
          status: 'processing',
          createdAt: '2024-01-13T16:00:00Z',
          views: 0,
          likes: 0,
          comments: 0,
          fileSize: '245 MB',
          format: 'MP3'
        }
      ];

      setAudios(mockAudios);
      setPagination(prev => ({
        ...prev,
        total: mockAudios.length
      }));
    } catch (error) {
      console.error('Failed to fetch audios:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAudios();
  }, [pagination.current, searchQuery, categoryFilter, statusFilter]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchAudios();
  };

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on audios:`, selectedAudios);
    alert(`Bulk ${action} action performed on ${selectedAudios.length} audios`);
  };

  // Action handlers
  const handlePreview = (audio: Audio) => {
    setSelectedAudio(audio);
    setShowPreviewModal(true);
  };

  const handleEdit = (audio: Audio) => {
    setSelectedAudio(audio);
    setShowEditModal(true);
  };

  const handleDownload = async (audio: Audio) => {
    try {
      // In a real app, this would download the audio file
      const link = document.createElement('a');
      link.href = `/api/media/download/${audio.id}`;
      link.download = audio.title;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
      alert('Download failed. Please try again.');
    }
  };

  const handleDelete = async (audio: Audio) => {
    if (confirm(`Are you sure you want to delete "${audio.title}"?`)) {
      try {
        // In a real app, this would call the delete API
        setAudios(prev => prev.filter(a => a.id !== audio.id));
        alert('Audio deleted successfully');
      } catch (error) {
        console.error('Delete failed:', error);
        alert('Delete failed. Please try again.');
      }
    }
  };

  const handleUploadSuccess = (newAudio: Audio) => {
    setAudios(prev => [newAudio, ...prev]);
    setShowUploadModal(false);
  };

  const handleEditSuccess = (updatedAudio: Audio) => {
    setAudios(prev => prev.map(a => a.id === updatedAudio.id ? updatedAudio : a));
    setShowEditModal(false);
    setSelectedAudio(null);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Audio Management</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowUploadModal(true)}
            className="btn-primary flex items-center"
          >
            <Upload className="w-4 h-4 mr-2" />
            Upload Audio
          </button>
          <button className="btn-secondary flex items-center">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search audio titles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="form-input pl-10"
              />
            </div>
          </div>
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Categories</option>
            <option value="podcast">Podcast</option>
            <option value="music">Music</option>
            <option value="education">Education</option>
            <option value="audiobook">Audiobook</option>
          </select>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select w-32"
          >
            <option value="">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="processing">Processing</option>
            <option value="failed">Failed</option>
          </select>
          <button type="submit" className="btn-primary flex items-center">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </button>
        </form>
      </div>

      {/* Bulk Actions */}
      {selectedAudios.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              Selected {selectedAudios.length} audios
            </span>
            <div className="flex space-x-2">
              <button onClick={() => handleBulkAction('publish')} className="btn-sm btn-primary">Publish</button>
              <button onClick={() => handleBulkAction('unpublish')} className="btn-sm btn-secondary">Unpublish</button>
              <button onClick={() => handleBulkAction('delete')} className="btn-sm btn-danger">Delete</button>
            </div>
          </div>
        </div>
      )}

      {/* Audio List */}
      <DataTable
        data={audios}
        columns={columns}
        loading={loading}
        pagination={{
          current: pagination.current,
          total: pagination.total,
          pageSize: pagination.pageSize,
          onChange: (page) => setPagination(prev => ({ ...prev, current: page }))
        }}
        selection={{
          selectedRowKeys: selectedAudios,
          onChange: setSelectedAudios
        }}
      />

      {/* Modals */}
      {showUploadModal && (
        <AudioUploadModal
          onClose={() => setShowUploadModal(false)}
          onSuccess={handleUploadSuccess}
        />
      )}

      {showPreviewModal && selectedAudio && (
        <AudioPreviewModal
          audio={selectedAudio}
          onClose={() => {
            setShowPreviewModal(false);
            setSelectedAudio(null);
          }}
        />
      )}

      {showEditModal && selectedAudio && (
        <AudioEditModal
          audio={selectedAudio}
          onClose={() => {
            setShowEditModal(false);
            setSelectedAudio(null);
          }}
          onSuccess={handleEditSuccess}
        />
      )}
    </div>
  );
};

export default AudioPage;
