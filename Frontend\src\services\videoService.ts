import { VideoUpload, VideoStream } from '@/types';

class VideoService {
  // 初始化视频上传
  async initializeUpload(fileName: string, fileSize: number, mimeType: string): Promise<VideoUpload> {
    // 在实际应用中，这里会调用后端API来初始化上传
    // 返回一个包含上传URL和ID的对象
    
    return new Promise((resolve) => {
      setTimeout(() => {
        const upload: VideoUpload = {
          id: `upload_${Date.now()}`,
          fileName,
          fileSize,
          mimeType,
          status: 'uploading',
          progress: 0,
          uploadUrl: `/api/upload/${Date.now()}`, // 模拟上传URL
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(upload);
      }, 500);
    });
  }

  // 上传视频文件（分块上传）
  async uploadVideoFile(file: File, onProgress: (progress: number) => void): Promise<VideoUpload> {
    return new Promise((resolve, reject) => {
      // 模拟上传过程
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress >= 100) {
          progress = 100;
          clearInterval(interval);
          
          // 模拟上传完成
          const upload: VideoUpload = {
            id: `upload_${Date.now()}`,
            fileName: file.name,
            fileSize: file.size,
            mimeType: file.type,
            status: 'uploaded',
            progress: 100,
            processedUrl: `/videos/${Date.now()}.mp4`, // 模拟处理后的URL
            thumbnails: [
              `/thumbnails/${Date.now()}_1.jpg`,
              `/thumbnails/${Date.now()}_2.jpg`,
              `/thumbnails/${Date.now()}_3.jpg`,
            ],
            duration: 300 + Math.random() * 600, // 随机时长 5-15分钟
            resolutions: [
              { width: 256, height: 144, quality: '144p', url: `/videos/${Date.now()}_144p.mp4`, fileSize: file.size * 0.1 },
              { width: 426, height: 240, quality: '240p', url: `/videos/${Date.now()}_240p.mp4`, fileSize: file.size * 0.2 },
              { width: 640, height: 360, quality: '360p', url: `/videos/${Date.now()}_360p.mp4`, fileSize: file.size * 0.3 },
              { width: 854, height: 480, quality: '480p', url: `/videos/${Date.now()}_480p.mp4`, fileSize: file.size * 0.5 },
              { width: 1280, height: 720, quality: '720p', url: `/videos/${Date.now()}_720p.mp4`, fileSize: file.size * 0.8 },
              { width: 1920, height: 1080, quality: '1080p', url: `/videos/${Date.now()}_1080p.mp4`, fileSize: file.size },
            ],
            subtitles: [
              { id: 'subtitle_1', language: 'en', label: 'English', url: `/subtitles/${Date.now()}_en.srt`, isDefault: true },
              { id: 'subtitle_2', language: 'zh', label: '中文', url: `/subtitles/${Date.now()}_zh.srt`, isDefault: false },
              { id: 'subtitle_3', language: 'es', label: 'Español', url: `/subtitles/${Date.now()}_es.srt`, isDefault: false },
            ],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          
          resolve(upload);
        }
        onProgress(progress);
      }, 200);
    });
  }

  // 创建直播流
  async createStream(title: string, description: string): Promise<VideoStream> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const stream: VideoStream = {
          id: `stream_${Date.now()}`,
          title,
          description,
          isLive: false,
          streamKey: `sk_${Math.random().toString(36).substring(2, 15)}`,
          streamUrl: `rtmp://stream.newzora.com/live/`,
          playbackUrl: `/live/${Date.now()}.m3u8`,
          viewerCount: 0,
          status: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(stream);
      }, 500);
    });
  }

  // 开始直播
  async startStream(streamId: string): Promise<VideoStream> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const stream: VideoStream = {
          id: streamId,
          title: '直播标题',
          description: '直播描述',
          isLive: true,
          streamKey: `sk_${Math.random().toString(36).substring(2, 15)}`,
          streamUrl: `rtmp://stream.newzora.com/live/`,
          playbackUrl: `/live/${Date.now()}.m3u8`,
          viewerCount: 0,
          startTime: new Date().toISOString(),
          status: 'live',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(stream);
      }, 500);
    });
  }

  // 结束直播
  async endStream(streamId: string): Promise<VideoStream> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const stream: VideoStream = {
          id: streamId,
          title: '直播标题',
          description: '直播描述',
          isLive: false,
          streamKey: `sk_${Math.random().toString(36).substring(2, 15)}`,
          viewerCount: 0,
          startTime: new Date(Date.now() - 3600000).toISOString(), // 1小时前开始
          endTime: new Date().toISOString(),
          status: 'ended',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(stream);
      }, 500);
    });
  }

  // 获取视频处理状态
  async getUploadStatus(uploadId: string): Promise<VideoUpload> {
    // 在实际应用中，这里会调用后端API获取处理状态
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟处理完成的状态
        const upload: VideoUpload = {
          id: uploadId,
          fileName: 'example.mp4',
          fileSize: 102400000,
          mimeType: 'video/mp4',
          status: 'processed',
          progress: 100,
          processedUrl: `/videos/${uploadId}.mp4`,
          thumbnails: [
            `/thumbnails/${uploadId}_1.jpg`,
            `/thumbnails/${uploadId}_2.jpg`,
            `/thumbnails/${uploadId}_3.jpg`,
          ],
          duration: 600,
          resolutions: [
            { width: 256, height: 144, quality: '144p', url: `/videos/${uploadId}_144p.mp4`, fileSize: 5120000 },
            { width: 426, height: 240, quality: '240p', url: `/videos/${uploadId}_240p.mp4`, fileSize: 10240000 },
            { width: 640, height: 360, quality: '360p', url: `/videos/${uploadId}_360p.mp4`, fileSize: 20480000 },
            { width: 854, height: 480, quality: '480p', url: `/videos/${uploadId}_480p.mp4`, fileSize: 30720000 },
            { width: 1280, height: 720, quality: '720p', url: `/videos/${uploadId}_720p.mp4`, fileSize: 51200000 },
            { width: 1920, height: 1080, quality: '1080p', url: `/videos/${uploadId}_1080p.mp4`, fileSize: 102400000 },
          ],
          subtitles: [
            { id: 'subtitle_1', language: 'en', label: 'English', url: `/subtitles/${uploadId}_en.srt`, isDefault: true },
            { id: 'subtitle_2', language: 'zh', label: '中文', url: `/subtitles/${uploadId}_zh.srt`, isDefault: false },
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        resolve(upload);
      }, 1000);
    });
  }
}

export const videoService = new VideoService();