'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
    Bars3Icon,
    BellIcon,
    MagnifyingGlassIcon,
    UserCircleIcon,
    Cog6ToothIcon,
    ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

interface AdminHeaderProps {
    onMenuClick: () => void;
}

export default function AdminHeader({ onMenuClick }: AdminHeaderProps) {
    const { user, logout } = useAuth();
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState('');
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [notifications] = useState([
        { id: 1, message: 'New user registration', time: '2 min ago', unread: true },
        { id: 2, message: 'Article pending review', time: '5 min ago', unread: true },
        { id: 3, message: 'System backup completed', time: '1 hour ago', unread: false },
    ]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            router.push(`/admin/search?q=${encodeURIComponent(searchQuery)}`);
        }
    };

    const handleLogout = async () => {
        await logout();
        router.push('/');
    };

    const unreadCount = notifications.filter(n => n.unread).length;

    return (
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Left side */}
                    <div className="flex items-center">
                        {/* Mobile menu button */}
                        <button
                            type="button"
                            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
                            onClick={onMenuClick}
                        >
                            <Bars3Icon className="h-6 w-6" />
                        </button>

                        {/* Logo and title */}
                        <div className="flex items-center ml-4 lg:ml-0">
                            <div className="flex-shrink-0">
                                <h1 className="text-xl font-bold text-gray-900">
                                    Newzora <span className="text-blue-600">Admin</span>
                                </h1>
                            </div>
                        </div>
                    </div>

                    {/* Center - Search */}
                    <div className="flex-1 max-w-md mx-8">
                        <form onSubmit={handleSearch} className="relative">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                            </div>
                            <input
                                type="text"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                placeholder="Search users, articles, comments..."
                            />
                        </form>
                    </div>

                    {/* Right side */}
                    <div className="flex items-center space-x-4">
                        {/* Notifications */}
                        <div className="relative">
                            <button
                                type="button"
                                className="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg relative"
                            >
                                <BellIcon className="h-6 w-6" />
                                {unreadCount > 0 && (
                                    <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                        {unreadCount}
                                    </span>
                                )}
                            </button>
                        </div>

                        {/* User menu */}
                        <div className="relative">
                            <button
                                type="button"
                                className="flex items-center space-x-3 p-2 text-sm rounded-lg hover:bg-gray-100"
                                onClick={() => setShowUserMenu(!showUserMenu)}
                            >
                                <div className="flex items-center space-x-2">
                                    {user?.avatar ? (
                                        <img
                                            className="h-8 w-8 rounded-full"
                                            src={user.avatar}
                                            alt={user.username}
                                        />
                                    ) : (
                                        <UserCircleIcon className="h-8 w-8 text-gray-400" />
                                    )}
                                    <div className="hidden md:block text-left">
                                        <div className="font-medium text-gray-900">{user?.username}</div>
                                        <div className="text-xs text-gray-500 capitalize">{user?.role}</div>
                                    </div>
                                </div>
                            </button>

                            {/* User dropdown menu */}
                            {showUserMenu && (
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                                    <button
                                        onClick={() => {
                                            setShowUserMenu(false);
                                            router.push('/admin/settings');
                                        }}
                                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                        <Cog6ToothIcon className="h-4 w-4 mr-3" />
                                        Settings
                                    </button>
                                    <button
                                        onClick={() => {
                                            setShowUserMenu(false);
                                            router.push('/profile');
                                        }}
                                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                    >
                                        <UserCircleIcon className="h-4 w-4 mr-3" />
                                        View Profile
                                    </button>
                                    <hr className="my-1" />
                                    <button
                                        onClick={handleLogout}
                                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                                    >
                                        <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                                        Sign Out
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </header>
    );
}