'use client';

import { createClient } from '@supabase/supabase-js';
import { useRouter } from 'next/navigation';
import type { Database } from '@/lib/supabase/database.types';
import React, { useEffect, useState } from 'react';
import { validateSupabaseConfig, logValidationResults, getSupabaseErrorMessage } from '@/utils/supabaseValidator';

// 用户元数据类型定义
type UserMetadata = {
  name?: string;
  avatar_url?: string;
  username?: string;
};

// 连接状态类型定义
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'timeout';

// 认证上下文类型定义
export type AuthContextType = {
  user: Database['public']['Tables']['profiles']['Row'] | null;
  session: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  connectionStatus: ConnectionStatus;
  login: (email: string, password: string) => Promise<boolean>;
  loginWithProvider: (provider: 'google' | 'facebook' | 'twitter') => Promise<boolean>;
  register: (email: string, password: string, metadata?: UserMetadata) => Promise<{data: any, error: any}>;
  resetPassword: (email: string) => Promise<boolean>;
  updatePassword: (newPassword: string) => Promise<boolean>;
  logout: () => Promise<void>;
  clearError: () => void;
};

// 简化的Context实现
let globalAuthState: AuthContextType | null = null;
const authListeners: Array<() => void> = [];

export const SupabaseAuthProvider = ({ children }: { children: any }) => {
  // 验证 Supabase 配置
  const validationResult = validateSupabaseConfig();

  // 在开发环境中记录验证结果
  if (process.env.NODE_ENV === 'development') {
    logValidationResults(validationResult);
  }

  // Supabase客户端配置，包含超时设置
  const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        flowType: 'pkce'
      },
      global: {
        headers: {
          'X-Client-Info': 'newzora-frontend'
        }
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    }
  );

  const router = useRouter();

  // 状态管理
  const [user, setUser] = useState<Database['public']['Tables']['profiles']['Row'] | null>(null);
  const [session, setSession] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected' | 'timeout'>('connecting');

  // 初始化认证状态
  useEffect(() => {
    let mounted = true;

    const initAuth = async () => {
      try {
        console.log('🔄 初始化认证状态...');
        setConnectionStatus('connected'); // 直接设置为已连接，简化流程

        const { data: { session }, error } = await supabase.auth.getSession();

        if (!mounted) return;

        if (error) {
          console.error('❌ 获取会话失败:', error);
          setError(error.message);
          setIsAuthenticated(false);
          setIsLoading(false);
          return;
        }

        if (session) {
          console.log('✅ 找到有效会话');
          setSession(session);
          setIsAuthenticated(true);

          // 尝试获取用户资料，但不阻塞认证状态
          try {
            await fetchUserProfile(session.user.id);
          } catch (profileError) {
            console.warn('⚠️ 获取用户资料失败，但保持认证状态');
          }
        } else {
          console.log('ℹ️ 无有效会话');
          setIsAuthenticated(false);
        }
      } catch (err) {
        console.error('❌ 认证初始化失败:', err);
        if (mounted) {
          setError('认证初始化失败');
          setIsAuthenticated(false);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    // 延迟初始化，避免与其他组件冲突
    const initTimer = setTimeout(() => {
      if (mounted) {
        initAuth();
      }
    }, 100);

    return () => {
      mounted = false;
      if (initTimer) {
        clearTimeout(initTimer);
      }
    };
  }, []); // 空依赖数组，只在组件挂载时运行一次

  // 单独的useEffect处理认证状态变化监听
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event: any, session: any) => {
      console.log('🔄 认证状态变化:', event);

      if (event === 'SIGNED_IN' && session) {
        setSession(session);
        setIsAuthenticated(true);
        setError(null);
        try {
          await fetchUserProfile(session.user.id);
        } catch (error) {
          console.warn('⚠️ 获取用户资料失败');
        }
      } else if (event === 'SIGNED_OUT') {
        setSession(null);
        setUser(null);
        setIsAuthenticated(false);
        setError(null);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // 获取用户资料
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) {
        // 如果用户资料不存在，这是正常的（新用户）
        if (profileError.code === 'PGRST116') {
          console.log('ℹ️ User profile does not exist, this is a new user');
          return;
        }
        throw profileError;
      }

      setUser(data);
      console.log('✅ User profile loaded successfully');
    } catch (err) {
      console.error('Failed to fetch user profile:', err);
      // 不设置错误状态，因为这不应该阻止认证
    }
  };

  // 创建用户资料
  const createUserProfile = async (userId: string, profileData: {
    email: string;
    username: string | null;
    display_name: string | null;
    avatar_url: string | null;
  }) => {
    try {
      const { data, error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          email: profileData.email,
          username: profileData.username,
          display_name: profileData.display_name,
          avatar_url: profileData.avatar_url,
          bio: null,
          website: null,
          location: null,
          role: 'user',
          status: 'active',
          email_verified: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_login_at: null,
          preferences: null,
          metadata: null,
        }, {
          onConflict: 'id'
        });

      if (profileError) {
        throw profileError;
      }

      console.log('✅ User profile created successfully');
      return data;
    } catch (err) {
      console.error('Failed to create user profile:', err);
      throw err;
    }
  };

  // 登录功能
  const loginWithProvider = async (provider: 'google' | 'facebook' | 'twitter'): Promise<boolean> => {
    try {
      setIsLoading(true);
      clearError();

      const { error } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        setError(error.message);
        return false;
      }

      return true;
    } catch (err) {
      console.error(`${provider} login error:`, err);
      setError(`Failed to sign in with ${provider}. Please try again.`);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 登录功能 - 带超时处理的优化版本
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      clearError();

      console.log('🔐 开始登录流程...');

      // 检查连接状态
      if (connectionStatus === 'timeout' || connectionStatus === 'disconnected') {
        throw new Error('认证服务连接异常，请检查网络连接后重试');
      }

      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('登录请求超时，请检查网络连接'));
        }, 15000); // 15秒超时
      });

      // 1. 使用Supabase进行身份验证（带超时）
      const authPromise = supabase.auth.signInWithPassword({
        email,
        password,
      });

      const { data, error: authError } = await Promise.race([
        authPromise,
        timeoutPromise
      ]);

      if (authError) {
        console.error('登录认证错误:', authError);
        setError(authError.message);
        return false;
      }

      if (!data.user || !data.session) {
        console.error('登录失败: 未获取到用户或会话信息');
        setError('Login failed. Please try again.');
        return false;
      }

      console.log('✅ 登录认证成功:', data.user.email);

      // 2. 设置会话信息
      setSession(data.session);

      // 3. 尝试获取用户资料，如果失败也不影响登录
      try {
        await fetchUserProfile(data.user.id);
        console.log('✅ 用户资料获取成功');
      } catch (profileError) {
        console.warn('⚠️ 用户资料获取失败，但登录继续:', profileError);
        // 即使获取资料失败，也设置基本用户信息
        setUser({
          id: data.user.id,
          email: data.user.email || '',
          username: data.user.user_metadata?.username || null,
          display_name: data.user.user_metadata?.name || null,
          avatar_url: data.user.user_metadata?.avatar_url || null,
          bio: null,
          website: null,
          location: null,
          role: 'user',
          status: 'active',
          email_verified: !!data.user.email_confirmed_at,
          created_at: data.user.created_at,
          updated_at: data.user.updated_at || data.user.created_at,
          last_login_at: new Date().toISOString(),
          preferences: {},
          metadata: {}
        });
      }

      // 4. 设置认证状态
      setIsAuthenticated(true);
      console.log('🎉 登录流程完成');
      return true;

    } catch (err) {
      console.error('登录过程中发生错误:', err);
      const errorMessage = err instanceof Error ? err.message : 'Login failed. Please try again.';
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 注册功能 - 根据代码生成规则优化的完整版本
  const register = async (
    email: string,
    password: string,
    metadata?: UserMetadata
  ): Promise<{data: any, error: any}> => {
    try {
      clearError();
      console.log('📝 Starting registration process...');

      // 输入验证
      if (!email || !password) {
        const error = new Error('Email and password are required');
        setError(error.message);
        return { data: null, error };
      }

      // 邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        const error = new Error('Please enter a valid email address');
        setError(error.message);
        return { data: null, error };
      }

      // 密码强度验证
      if (password.length < 6) {
        const error = new Error('Password must be at least 6 characters long');
        setError(error.message);
        return { data: null, error };
      }

      // 检查连接状态
      if (connectionStatus === 'timeout' || connectionStatus === 'disconnected') {
        const error = new Error('Authentication service connection error. Please check your network connection and try again.');
        setError(error.message);
        return { data: null, error };
      }

      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Registration request timeout. Please check your network connection.'));
        }, 25000); // 25秒超时，注册可能需要更长时间
      });

      // 1. 使用Supabase注册用户（带超时和完整错误处理）
      const signUpPromise = supabase.auth.signUp({
        email: email.toLowerCase().trim(),
        password,
        options: {
          data: {
            name: metadata?.name || metadata?.username || '',
            username: metadata?.username || '',
            avatar_url: metadata?.avatar_url || '',
            display_name: metadata?.name || metadata?.username || '',
          },
          emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/callback`
        },
      });

      const { data: signUpData, error: signUpError } = await Promise.race([
        signUpPromise,
        timeoutPromise
      ]);

      if (signUpError) {
        console.error('Registration error:', signUpError);

        // 使用验证工具获取用户友好的错误信息
        const errorMessage = getSupabaseErrorMessage(signUpError.message);

        setError(errorMessage);
        return { data: null, error: { message: errorMessage } };
      }

      if (!signUpData?.user) {
        const error = new Error('Registration failed: User not created');
        setError(error.message);
        return { data: null, error };
      }

      console.log('✅ Registration successful:', signUpData.user.email);

      // 2. 处理注册后的状态
      if (signUpData.session) {
        console.log('🎉 Auto-login successful, email verification disabled');
        setSession(signUpData.session);
        setIsAuthenticated(true);

        // 尝试创建或更新用户资料
        try {
          await createUserProfile(signUpData.user.id, {
            email: email.toLowerCase().trim(),
            username: metadata?.username || null,
            display_name: metadata?.name || metadata?.username || null,
            avatar_url: metadata?.avatar_url || null,
          });

          await fetchUserProfile(signUpData.user.id);
        } catch (profileError) {
          console.warn('⚠️ Failed to create user profile, but registration continues:', profileError);
          // 不中断注册流程，只是记录警告
        }
      } else {
        console.log('📧 Email verification required');
      }

      return { data: signUpData, error: null };
    } catch (err) {
      console.error('Error during registration process:', err);

      // 使用验证工具获取用户友好的错误信息
      const errorMessage = getSupabaseErrorMessage(err);

      setError(errorMessage);
      return { data: null, error: { message: errorMessage } };
    }
  };

  // 登出功能
  const logout = async () => {
    try {
      setIsLoading(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      setUser(null);
      setIsAuthenticated(false);
      router.push('/auth/login');
    } catch (err) {
      console.error('Logout error:', err);
      setError('Failed to logout. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // 密码重置功能
  const resetPassword = async (email: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      });

      if (error) {
        setError(error.message);
        return false;
      }

      return true;
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 更新密码功能
  const updatePassword = async (newPassword: string): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const { error } = await supabase.auth.updateUser({
        password: newPassword
      });

      if (error) {
        setError(error.message);
        return false;
      }

      return true;
    } catch (err: any) {
      setError(err.message || 'Failed to update password');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // 清除错误
  const clearError = () => setError(null);



  // 更新全局状态
  globalAuthState = {
    user,
    session,
    isAuthenticated,
    isLoading,
    error,
    connectionStatus,
    login,
    loginWithProvider,
    register,
    resetPassword,
    updatePassword,
    logout,
    clearError,
  };

  return <div>{children}</div>;
};

export const useSupabaseAuth = (): AuthContextType => {
  if (!globalAuthState) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return globalAuthState;
};