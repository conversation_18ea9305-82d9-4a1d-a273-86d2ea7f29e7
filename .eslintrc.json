{"root": true, "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2020": true, "node": true}, "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "error", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "prefer-const": "error", "no-var": "error", "no-console": "warn"}, "overrides": [{"files": ["Frontend/**/*"], "extends": ["next/core-web-vitals", "next/typescript"], "rules": {"react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off", "@next/next/no-page-custom-font": "off"}}]}