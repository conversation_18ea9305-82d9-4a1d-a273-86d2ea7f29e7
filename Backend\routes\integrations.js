const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const { integrationConfig, getEnabledIntegrations } = require('../config/integrations');

// 获取集成列表
router.get('/', auth, async (req, res) => {
  try {
    const enabledIntegrations = getEnabledIntegrations();
    
    const integrationsList = [
      {
        id: 'wechat-pay',
        name: '微信支付',
        category: 'payment',
        status: enabledIntegrations.payment?.wechatPay ? 'connected' : 'available',
        description: '集成微信支付，支持用户打赏和付费内容',
        features: ['用户打赏', '付费内容', '自动结算']
      },
      {
        id: 'alipay',
        name: '支付宝',
        category: 'payment',
        status: enabledIntegrations.payment?.alipay ? 'connected' : 'available',
        description: '支付宝支付集成，提供多种支付方式',
        features: ['在线支付', '移动支付', '批量转账']
      },
      {
        id: 'qiniu-cloud',
        name: '七牛云',
        category: 'storage',
        status: enabledIntegrations.storage?.qiniuCloud ? 'connected' : 'available',
        description: '七牛云存储服务，用于媒体文件存储和CDN加速',
        features: ['文件存储', 'CDN加速', '图片处理']
      },
      {
        id: 'aliyun-oss',
        name: '阿里云OSS',
        category: 'storage',
        status: enabledIntegrations.storage?.aliyunOSS ? 'connected' : 'available',
        description: '阿里云对象存储服务',
        features: ['海量存储', '高可用性', '数据备份']
      },
      {
        id: 'baidu-ai',
        name: '百度AI',
        category: 'ai',
        status: enabledIntegrations.ai?.baiduAI ? 'connected' : 'available',
        description: '百度AI服务，提供内容审核和智能推荐',
        features: ['内容审核', '文本分析', '图像识别']
      },
      {
        id: 'tencent-sms',
        name: '腾讯云短信',
        category: 'communication',
        status: enabledIntegrations.communication?.tencentSMS ? 'connected' : 'available',
        description: '短信验证码和通知服务',
        features: ['验证码', '通知短信', '营销短信']
      }
    ];
    
    res.json({
      success: true,
      data: integrationsList
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取特定集成的配置状态
router.get('/:integrationId/status', auth, async (req, res) => {
  try {
    const { integrationId } = req.params;
    const enabledIntegrations = getEnabledIntegrations();
    
    // 根据集成ID返回状态
    let status = 'available';
    let config = {};
    
    switch (integrationId) {
      case 'wechat-pay':
        status = enabledIntegrations.payment?.wechatPay ? 'connected' : 'available';
        config = {
          hasAppId: !!integrationConfig.payment.wechatPay.appId,
          hasMchId: !!integrationConfig.payment.wechatPay.mchId,
          sandbox: integrationConfig.payment.wechatPay.sandbox
        };
        break;
      case 'alipay':
        status = enabledIntegrations.payment?.alipay ? 'connected' : 'available';
        config = {
          hasAppId: !!integrationConfig.payment.alipay.appId,
          hasPrivateKey: !!integrationConfig.payment.alipay.privateKey,
          sandbox: integrationConfig.payment.alipay.sandbox
        };
        break;
      case 'qiniu-cloud':
        status = enabledIntegrations.storage?.qiniuCloud ? 'connected' : 'available';
        config = {
          hasAccessKey: !!integrationConfig.storage.qiniuCloud.accessKey,
          hasBucket: !!integrationConfig.storage.qiniuCloud.bucket,
          region: integrationConfig.storage.qiniuCloud.region
        };
        break;
      case 'baidu-ai':
        status = enabledIntegrations.ai?.baiduAI ? 'connected' : 'available';
        config = {
          hasApiKey: !!integrationConfig.ai.baiduAI.apiKey,
          services: integrationConfig.ai.baiduAI.services
        };
        break;
    }
    
    res.json({
      success: true,
      data: {
        integrationId,
        status,
        config
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 测试集成连接
router.post('/:integrationId/test', auth, async (req, res) => {
  try {
    const { integrationId } = req.params;
    
    // 模拟测试不同集成的连接
    let testResult = {
      success: false,
      message: '测试失败',
      details: {}
    };
    
    switch (integrationId) {
      case 'wechat-pay':
        // 模拟微信支付连接测试
        testResult = {
          success: Math.random() > 0.3,
          message: Math.random() > 0.3 ? '连接成功' : 'AppId或密钥配置错误',
          details: {
            responseTime: Math.floor(Math.random() * 500) + 100,
            apiVersion: 'v3'
          }
        };
        break;
        
      case 'alipay':
        // 模拟支付宝连接测试
        testResult = {
          success: Math.random() > 0.3,
          message: Math.random() > 0.3 ? '连接成功' : '私钥格式错误',
          details: {
            responseTime: Math.floor(Math.random() * 300) + 50,
            gateway: 'openapi.alipay.com'
          }
        };
        break;
        
      case 'qiniu-cloud':
        // 模拟七牛云连接测试
        testResult = {
          success: Math.random() > 0.2,
          message: Math.random() > 0.2 ? '连接成功' : 'AccessKey无效',
          details: {
            responseTime: Math.floor(Math.random() * 200) + 30,
            region: 'Zone_z0',
            bucketExists: true
          }
        };
        break;
        
      case 'baidu-ai':
        // 模拟百度AI连接测试
        testResult = {
          success: Math.random() > 0.1,
          message: Math.random() > 0.1 ? '连接成功' : 'API密钥无效',
          details: {
            responseTime: Math.floor(Math.random() * 400) + 200,
            availableServices: ['content_censor', 'text_analysis']
          }
        };
        break;
        
      default:
        testResult = {
          success: false,
          message: '不支持的集成类型',
          details: {}
        };
    }
    
    res.json({
      success: true,
      data: testResult
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// 获取集成使用统计
router.get('/:integrationId/stats', auth, async (req, res) => {
  try {
    const { integrationId } = req.params;
    const { period = '30d' } = req.query;
    
    // 模拟统计数据
    const stats = {
      totalRequests: Math.floor(Math.random() * 10000) + 1000,
      successRate: (Math.random() * 10 + 90).toFixed(2),
      avgResponseTime: Math.floor(Math.random() * 200) + 50,
      errorCount: Math.floor(Math.random() * 100),
      lastUsed: new Date(Date.now() - Math.random() * 86400000).toISOString()
    };
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;