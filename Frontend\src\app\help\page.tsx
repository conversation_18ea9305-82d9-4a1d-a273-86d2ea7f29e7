'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Link from 'next/link';

const helpSections = [
  {
    title: 'Getting Started',
    icon: '🚀',
    items: [
      { title: 'How to create an account', content: 'Click "Sign Up" and fill in your email, username, and password. Verify your email to activate your account.' },
      { title: 'Setting up your profile', content: 'Go to your profile page and add a bio, profile picture, and other personal information.' },
      { title: 'Publishing your first article', content: 'Click "Create" > "Article" and use our rich text editor to write and publish your content.' },
      { title: 'Understanding the platform', content: 'Explore different categories, follow creators, and engage with content through likes and comments.' }
    ]
  },
  {
    title: 'Account & Settings',
    icon: '⚙️',
    items: [
      { title: 'Privacy settings', content: 'Control who can see your profile, content, and activity in Settings > Privacy.' },
      { title: 'Account management', content: 'Update your email, password, and account information in Account Settings.' },
      { title: 'Notification preferences', content: 'Choose which notifications you want to receive via email or in-app.' },
      { title: 'Security settings', content: 'Enable two-factor authentication and manage your login sessions.' }
    ]
  },
  {
    title: 'Content Creation',
    icon: '✍️',
    items: [
      { title: 'Writing and formatting articles', content: 'Use our rich text editor with formatting options, headings, lists, and more.' },
      { title: 'Adding images and media', content: 'Upload images directly or embed videos and audio content in your articles.' },
      { title: 'Publishing guidelines', content: 'Follow our community guidelines and ensure your content is original and valuable.' },
      { title: 'Content monetization', content: 'Earn from your content through our creator program and advertising revenue sharing.' }
    ]
  },
  {
    title: 'Community Guidelines',
    icon: '👥',
    items: [
      { title: 'Platform rules and policies', content: 'Review our community rules to understand what content is allowed on the platform.' },
      { title: 'Reporting inappropriate content', content: 'Use the report button on any content that violates our guidelines.' },
      { title: 'Community standards', content: 'We maintain high standards for respectful and constructive interactions.' },
      { title: 'Moderation process', content: 'Our moderation team reviews reported content and takes appropriate action.' }
    ]
  }
];

export default function HelpPage() {
  const [expandedItems, setExpandedItems] = useState<{[key: string]: boolean}>({});

  const toggleItem = (sectionIndex: number, itemIndex: number) => {
    const key = `${sectionIndex}-${itemIndex}`;
    setExpandedItems(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-6xl mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Center</h1>
          <p className="text-xl text-gray-600">Find answers to common questions and get support</p>
        </div>
        
        <div className="grid gap-8">
          {helpSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="bg-white rounded-lg shadow-sm p-8">
              <div className="flex items-center mb-6">
                <span className="text-3xl mr-3">{section.icon}</span>
                <h2 className="text-2xl font-bold text-gray-900">{section.title}</h2>
              </div>
              
              <div className="space-y-4">
                {section.items.map((item, itemIndex) => {
                  const key = `${sectionIndex}-${itemIndex}`;
                  const isExpanded = expandedItems[key];
                  
                  return (
                    <div key={itemIndex} className="border border-gray-200 rounded-lg">
                      <button
                        onClick={() => toggleItem(sectionIndex, itemIndex)}
                        className="w-full text-left p-4 hover:bg-gray-50 transition-colors flex items-center justify-between"
                      >
                        <h3 className="font-semibold text-gray-900">{item.title}</h3>
                        <svg
                          className={`w-5 h-5 text-gray-500 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      {isExpanded && (
                        <div className="px-4 pb-4">
                          <p className="text-gray-600 leading-relaxed">{item.content}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-blue-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Still Need Help?</h2>
          <p className="text-gray-600 mb-6">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              📧 Email Support
            </a>
            <Link
              href="/feedback"
              className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-medium rounded-lg border border-blue-600 hover:bg-blue-50 transition-colors"
            >
              💬 Send Feedback
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}