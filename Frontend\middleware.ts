import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  // 多种方式获取 token - 支持Supabase的token格式
  const token =
    request.cookies.get('sb-access-token')?.value ||  // Supabase标准cookie
    request.cookies.get('auth_token')?.value ||       // 自定义cookie
    request.cookies.get('auth-token')?.value ||       // 向后兼容
    request.headers.get('authorization')?.replace('Bearer ', '') ||
    request.headers.get('Authorization')?.replace('Bearer ', '');

  const { pathname } = request.nextUrl;

  console.log('🔍 中间件检查路径:', pathname, token ? '有token' : '无token');

  // 公开路由，不需要认证
  const publicPaths = [
    '/auth/login',      // 统一的登录页面
    '/auth/register',   // 统一的注册页面
    '/api/auth/login',
    '/api/auth/register',
    '/api/simple-auth', // 简化认证API
    '/test',
    '/simple-test',
    '/api/auth/forgot-password',
    '/api/auth/reset-password',
    '/api/auth/verify-email',
  ];

  // 精确的路径匹配
  if (publicPaths.some((path) => pathname.startsWith(path))) {
        // 防止已登录用户访问登录页
    const loginPages = ['/auth/login', '/auth/register'];
    if (token && loginPages.includes(pathname)) {
      console.log('🔄 已登录用户访问登录页，重定向到首页');
      return NextResponse.redirect(new URL('/', request.url));
    }
    return NextResponse.next();
  }

  // 未认证用户的处理
  if (!token) {
    console.log('🚫 未认证用户访问受保护路径:', pathname);
    // 如果是 API 路由，返回 401
    if (pathname.startsWith('/api/')) {
      return NextResponse.json({ success: false, message: 'Unauthorized' }, { status: 401 });
    }
    // 如果是页面路由，重定向到主登录页面
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
