/**
 * 凭据验证和环境变量检查工具
 */

const { logger } = require('../config/logger');

/**
 * 必需的环境变量列表
 */
const REQUIRED_ENV_VARS = [
  'NODE_ENV',
  'PORT',
  'DB_HOST',
  'DB_PORT', 
  'DB_NAME',
  'DB_USER',
  'DB_PASSWORD',
  'JWT_SECRET',
  'SESSION_SECRET'
];

/**
 * 生产环境必需的环境变量
 */
const PRODUCTION_REQUIRED_ENV_VARS = [
  ...REQUIRED_ENV_VARS,
  'EMAIL_HOST',
  'EMAIL_USER',
  'EMAIL_PASS',
  'VAPID_PUBLIC_KEY',
  'VAPID_PRIVATE_KEY'
];

/**
 * 敏感环境变量（不应出现在日志中）
 */
const SENSITIVE_ENV_VARS = [
  'DB_PASSWORD',
  'JWT_SECRET',
  'SESSION_SECRET',
  'EMAIL_PASS',
  'GOOGLE_CLIENT_SECRET',
  'FACEBOOK_APP_SECRET',
  'VAPID_PRIVATE_KEY',
  'AWS_SECRET_ACCESS_KEY',
  'SENDGRID_API_KEY'
];

/**
 * 验证环境变量是否存在
 */
const validateEnvironmentVariables = () => {
  const missing = [];
  const requiredVars = process.env.NODE_ENV === 'production' 
    ? PRODUCTION_REQUIRED_ENV_VARS 
    : REQUIRED_ENV_VARS;

  for (const envVar of requiredVars) {
    if (!process.env[envVar]) {
      missing.push(envVar);
    }
  }

  if (missing.length > 0) {
    logger.error('Missing required environment variables', { missing });
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  logger.info('Environment variables validation passed');
  return true;
};

/**
 * 检查是否存在硬编码凭据
 */
const checkForHardcodedCredentials = (content) => {
  const patterns = [
    // 常见的硬编码密码模式
    /password\s*[:=]\s*['"][^'"]{8,}['"]/i,
    /secret\s*[:=]\s*['"][^'"]{16,}['"]/i,
    /key\s*[:=]\s*['"][^'"]{16,}['"]/i,
    // API密钥模式
    /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/i,
    /access[_-]?token\s*[:=]\s*['"][^'"]+['"]/i,
    // 数据库连接字符串
    /postgres:\/\/[^:]+:[^@]+@/i,
    /mysql:\/\/[^:]+:[^@]+@/i,
    // JWT密钥
    /jwt[_-]?secret\s*[:=]\s*['"][^'"]+['"]/i
  ];

  const found = [];
  for (const pattern of patterns) {
    const matches = content.match(pattern);
    if (matches) {
      found.push(matches[0]);
    }
  }

  return found;
};

/**
 * 验证JWT密钥强度
 */
const validateJWTSecret = () => {
  const jwtSecret = process.env.JWT_SECRET;
  
  if (!jwtSecret) {
    throw new Error('JWT_SECRET is required');
  }

  if (jwtSecret.length < 32) {
    throw new Error('JWT_SECRET must be at least 32 characters long');
  }

  // 检查是否是默认值
  const defaultSecrets = [
    'your-super-secure-jwt-secret-key-here',
    'secret',
    'jwt-secret',
    'default-secret'
  ];

  if (defaultSecrets.includes(jwtSecret)) {
    throw new Error('JWT_SECRET cannot be a default value');
  }

  return true;
};

/**
 * 验证数据库凭据
 */
const validateDatabaseCredentials = () => {
  const { DB_PASSWORD, DB_USER } = process.env;

  if (!DB_PASSWORD || DB_PASSWORD.length < 8) {
    throw new Error('Database password must be at least 8 characters long');
  }

  // 检查是否是默认值
  const defaultPasswords = ['password', '123456', 'admin', 'root'];
  if (defaultPasswords.includes(DB_PASSWORD)) {
    throw new Error('Database password cannot be a common default value');
  }

  return true;
};

/**
 * 生成安全的随机密钥
 */
const generateSecureKey = (length = 64) => {
  const crypto = require('crypto');
  return crypto.randomBytes(length).toString('hex');
};

/**
 * 清理环境变量用于日志记录
 */
const sanitizeEnvForLogging = (env) => {
  const sanitized = { ...env };
  
  for (const sensitiveVar of SENSITIVE_ENV_VARS) {
    if (sanitized[sensitiveVar]) {
      sanitized[sensitiveVar] = '[REDACTED]';
    }
  }

  return sanitized;
};

/**
 * 完整的凭据安全检查
 */
const performSecurityCheck = () => {
  try {
    logger.info('Starting security credential check...');

    // 验证环境变量
    validateEnvironmentVariables();

    // 验证JWT密钥
    validateJWTSecret();

    // 验证数据库凭据
    validateDatabaseCredentials();

    logger.info('Security credential check completed successfully');
    return true;
  } catch (error) {
    logger.error('Security credential check failed', { error: error.message });
    throw error;
  }
};

module.exports = {
  validateEnvironmentVariables,
  checkForHardcodedCredentials,
  validateJWTSecret,
  validateDatabaseCredentials,
  generateSecureKey,
  sanitizeEnvForLogging,
  performSecurityCheck,
  REQUIRED_ENV_VARS,
  PRODUCTION_REQUIRED_ENV_VARS,
  SENSITIVE_ENV_VARS
};