'use client';

import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useRouter } from 'next/navigation';
import AuthGuard from '@/components/AuthGuard';

function ProfileContent() {
  const { user, logout } = useSimpleAuth();
  const router = useRouter();

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">User Profile</h1>
              <button
                onClick={handleLogout}
                className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>

        {/* Success Message */}
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-2xl">🎉</span>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-medium">Authentication Successful!</h3>
              <p className="mt-1">
                You have successfully accessed a protected page. The new authentication system is
                working correctly.
              </p>
            </div>
          </div>
        </div>

        {/* User Information */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Account Information</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500">Username</label>
                <p className="mt-1 text-lg text-gray-900">{user.username}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Email</label>
                <p className="mt-1 text-lg text-gray-900">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Role</label>
                <p className="mt-1 text-lg text-gray-900 capitalize">{user.role}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">User ID</label>
                <p className="mt-1 text-lg text-gray-900">{user.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500">Role</label>
                <p className="mt-1 text-lg capitalize">{user.role}</p>
              </div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Authentication System Status</h2>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-3">
              <div className="flex items-center">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span>New authentication system active</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span>User session authenticated</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span>Protected route access granted</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span>Middleware protection working</span>
              </div>
              <div className="flex items-center">
                <span className="text-green-500 text-xl mr-3">✅</span>
                <span>Token validation successful</span>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Quick Navigation</h2>
          </div>
          <div className="px-6 py-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <a
                href="/"
                className="bg-blue-500 text-white px-4 py-2 rounded text-center hover:bg-blue-600"
              >
                🏠 Home
              </a>
              <a
                href="/auth/login"
                className="bg-gray-500 text-white px-4 py-2 rounded text-center hover:bg-gray-600"
              >
                🔐 Login
              </a>
              <a
                href="/auth/register"
                className="bg-green-500 text-white px-4 py-2 rounded text-center hover:bg-green-600"
              >
                📝 Register
              </a>
              <button
                onClick={handleLogout}
                className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
              >
                🚪 Logout
              </button>
            </div>
          </div>
        </div>

        {/* Technical Information */}
        <div className="mt-6 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-800 mb-4">Technical Information</h3>
          <div className="space-y-2 text-sm text-blue-700">
            <p>
              <strong>Authentication Method:</strong> JWT Token + localStorage + Cookies
            </p>
            <p>
              <strong>Context Provider:</strong> AuthContext
            </p>
            <p>
              <strong>Middleware Protection:</strong> Active
            </p>
            <p>
              <strong>Session Persistence:</strong> 7 days
            </p>
            <p>
              <strong>Auto-refresh:</strong> On page load
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ProfilePage() {
  return (
    <AuthGuard requireAuth={true} redirectTo="/auth/login">
      <ProfileContent />
    </AuthGuard>
  );
}
