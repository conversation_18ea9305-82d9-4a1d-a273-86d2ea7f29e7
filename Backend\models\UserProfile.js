const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const UserProfile = sequelize.define(
  'UserProfile',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    // 基础统计信息
    totalPageViews: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '总页面浏览量',
    },
    totalReadingTime: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '总阅读时间（秒）',
    },
    totalArticlesRead: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '总阅读文章数',
    },
    totalSearches: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '总搜索次数',
    },

    // 活跃度指标
    lastActiveAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '最后活跃时间',
    },
    activeDays: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      comment: '活跃天数',
    },
    avgSessionDuration: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
      comment: '平均会话时长（分钟）',
    },
    engagementScore: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
      comment: '参与度评分（0-100）',
    },

    // 内容偏好
    preferredCategories: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '偏好分类及权重 {Technology: 0.4, Travel: 0.3, ...}',
    },
    readingPatterns: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '阅读模式 {preferredTime: "evening", avgReadingSpeed: 250, ...}',
    },
    topKeywords: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: [],
      comment: '用户感兴趣的关键词',
    },

    // 设备和行为偏好
    preferredDevice: {
      type: DataTypes.ENUM('desktop', 'mobile', 'tablet'),
      allowNull: true,
      comment: '偏好设备类型',
    },
    deviceUsage: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '设备使用统计 {desktop: 0.6, mobile: 0.4}',
    },

    // 社交行为
    socialActivity: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '社交活动统计 {likes: 10, shares: 5, comments: 8, follows: 3}',
    },

    // 个性化推荐相关
    recommendationPreferences: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '推荐偏好设置',
    },
    contentAffinityScores: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '内容亲和度评分',
    },

    // 用户分群
    userSegment: {
      type: DataTypes.ENUM(
        'new_user', // 新用户
        'casual_reader', // 休闲读者
        'active_reader', // 活跃读者
        'power_user', // 重度用户
        'content_creator', // 内容创作者
        'inactive' // 不活跃用户
      ),
      defaultValue: 'new_user',
    },

    // 质量指标
    contentQualityScore: {
      type: DataTypes.FLOAT,
      defaultValue: 0,
      comment: '内容质量评分（基于用户创建的内容）',
    },
    trustScore: {
      type: DataTypes.FLOAT,
      defaultValue: 50,
      comment: '信任度评分（0-100）',
    },

    // 更新时间戳
    lastUpdatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    profileVersion: {
      type: DataTypes.INTEGER,
      defaultValue: 1,
      comment: '画像版本号',
    },
  },
  {
    tableName: 'user_profiles',
    timestamps: true,
    indexes: [
      {
        fields: ['userId'],
      },
      {
        fields: ['userSegment'],
      },
      {
        fields: ['engagementScore'],
      },
      {
        fields: ['lastActiveAt'],
      },
      {
        fields: ['activeDays'],
      },
      {
        fields: ['trustScore'],
      },
      {
        fields: ['lastUpdatedAt'],
      },
    ],
  }
);

// 实例方法
UserProfile.prototype.calculateEngagementScore = function () {
  const weights = {
    readingTime: 0.3,
    articlesRead: 0.25,
    socialActivity: 0.2,
    activeDays: 0.15,
    searchActivity: 0.1,
  };

  // 标准化各项指标（基于平均值）
  const avgReadingTime = 1800; // 30分钟
  const avgArticlesRead = 20;
  const avgActiveDays = 15;
  const avgSearches = 10;

  let score = 0;
  score += Math.min(this.totalReadingTime / avgReadingTime, 1) * weights.readingTime * 100;
  score += Math.min(this.totalArticlesRead / avgArticlesRead, 1) * weights.articlesRead * 100;
  score += Math.min(this.activeDays / avgActiveDays, 1) * weights.activeDays * 100;
  score += Math.min(this.totalSearches / avgSearches, 1) * weights.searchActivity * 100;

  // 社交活动评分
  const social = this.socialActivity || {};
  const socialScore = (social.likes || 0) + (social.shares || 0) * 2 + (social.comments || 0) * 3;
  score += Math.min(socialScore / 50, 1) * weights.socialActivity * 100;

  this.engagementScore = Math.round(score * 100) / 100;
  return this.engagementScore;
};

UserProfile.prototype.updateUserSegment = function () {
  const engagement = this.engagementScore;
  const activeDays = this.activeDays;
  const social = this.socialActivity || {};
  const totalSocial = (social.likes || 0) + (social.shares || 0) + (social.comments || 0);

  if (activeDays < 3) {
    this.userSegment = 'new_user';
  } else if (engagement < 20) {
    this.userSegment = 'inactive';
  } else if (engagement < 40) {
    this.userSegment = 'casual_reader';
  } else if (engagement < 70) {
    this.userSegment = 'active_reader';
  } else if (totalSocial > 20) {
    this.userSegment = 'content_creator';
  } else {
    this.userSegment = 'power_user';
  }

  return this.userSegment;
};

UserProfile.prototype.getReadingTimeInHours = function () {
  return Math.round((this.totalReadingTime / 3600) * 100) / 100;
};

// 类方法
UserProfile.getUserSegmentDistribution = async function () {
  return await this.findAll({
    attributes: [
      'userSegment',
      [sequelize.fn('COUNT', '*'), 'count'],
      [sequelize.fn('AVG', sequelize.col('engagementScore')), 'avgEngagement'],
    ],
    group: ['userSegment'],
    order: [[sequelize.literal('count'), 'DESC']],
  });
};

UserProfile.getTopUsers = async function (metric = 'engagement', limit = 50) {
  const orderField =
    metric === 'engagement'
      ? 'engagementScore'
      : metric === 'reading'
        ? 'totalReadingTime'
        : metric === 'articles'
          ? 'totalArticlesRead'
          : 'engagementScore';

  return await this.findAll({
    order: [[orderField, 'DESC']],
    limit,
  });
};

module.exports = UserProfile;
