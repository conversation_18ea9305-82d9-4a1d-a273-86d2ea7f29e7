'use client';

import React, { useState } from 'react';
import { Calendar, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';
import EnhancedDatePicker, { DateRange } from '@/components/admin/common/EnhancedDatePicker';

const TestCalendarFixPage: React.FC = () => {
  const [selectedRange, setSelectedRange] = useState<DateRange | null>(null);
  const [testCount, setTestCount] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);

  const handleDateRangeChange = (dateRange: DateRange) => {
    try {
      setSelectedRange(dateRange);
      setTestCount(prev => prev + 1);
      console.log('Date range changed successfully:', dateRange);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setErrors(prev => [...prev, `Test ${testCount + 1}: ${errorMessage}`]);
      console.error('Error in date range change:', error);
    }
  };

  const resetTest = () => {
    setSelectedRange(null);
    setTestCount(0);
    setErrors([]);
  };

  const testScenarios = [
    'Click on a single date',
    'Select a date range (start and end)',
    'Use year/month picker',
    'Navigate between months',
    'Clear selection',
    'Close and reopen calendar'
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Calendar Fix Testing</h1>
          <p className="text-gray-600 mt-2">Test the fixed calendar component for DOM errors</p>
        </div>
        <button
          onClick={resetTest}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Reset Test</span>
        </button>
      </div>

      {/* Error Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Error Status</h2>
          <div className="flex items-center space-x-2">
            {errors.length === 0 ? (
              <>
                <CheckCircle className="w-5 h-5 text-green-600" />
                <span className="text-green-600 font-medium">No Errors</span>
              </>
            ) : (
              <>
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <span className="text-red-600 font-medium">{errors.length} Error(s)</span>
              </>
            )}
          </div>
        </div>

        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            ))}
          </div>
        )}

        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-700">
            <strong>Tests completed:</strong> {testCount}
          </p>
          <p className="text-sm text-gray-700 mt-1">
            <strong>Success rate:</strong> {testCount > 0 ? Math.round(((testCount - errors.length) / testCount) * 100) : 0}%
          </p>
        </div>
      </div>

      {/* Calendar Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Calendar Component Test</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Enhanced Date Picker</h3>
            <EnhancedDatePicker
              value={selectedRange}
              onChange={handleDateRangeChange}
              placeholder="Select date range to test"
              className="w-full"
            />
            
            {selectedRange && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">✅ Selection Successful</h4>
                <div className="text-sm text-green-800 space-y-1">
                  <p><strong>Start:</strong> {selectedRange.startDate.toLocaleDateString('en-US')}</p>
                  <p><strong>End:</strong> {selectedRange.endDate.toLocaleDateString('en-US')}</p>
                  <p><strong>Days:</strong> {Math.ceil(
                    (selectedRange.endDate.getTime() - selectedRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
                  ) + 1}</p>
                </div>
              </div>
            )}
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Test Scenarios</h3>
            <div className="space-y-2">
              {testScenarios.map((scenario, index) => (
                <div key={index} className="flex items-center space-x-3 p-2 bg-gray-50 rounded">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                  </div>
                  <span className="text-sm text-gray-700">{scenario}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Fix Details */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Applied Fixes</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Event Listener Cleanup</p>
                <p className="text-sm text-gray-600">Properly remove event listeners when component unmounts</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">State Update Safety</p>
                <p className="text-sm text-gray-600">Use setTimeout to avoid state update conflicts</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Error Boundary</p>
                <p className="text-sm text-gray-600">Wrap component in error boundary for graceful error handling</p>
              </div>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">DOM Reference Safety</p>
                <p className="text-sm text-gray-600">Check DOM node existence before operations</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Render Error Handling</p>
                <p className="text-sm text-gray-600">Try-catch blocks in render functions</p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <p className="font-medium text-gray-900">Unused Variables Cleanup</p>
                <p className="text-sm text-gray-600">Remove unused state variables to prevent warnings</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Browser Console Check */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-yellow-800">Console Check</h3>
            <p className="text-sm text-yellow-700 mt-1">
              Open your browser's developer console (F12) to check for any remaining DOM errors. 
              The "NotFoundError: Failed to execute 'removeChild' on 'Node'" error should no longer appear.
            </p>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Testing Instructions</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p>• <strong>Open Calendar:</strong> Click on the date picker to open the calendar</p>
          <p>• <strong>Select Dates:</strong> Click on dates to select start and end dates</p>
          <p>• <strong>Navigate:</strong> Use arrow buttons or click month/year header to navigate</p>
          <p>• <strong>Year/Month Picker:</strong> Click on the month/year header to access quick navigation</p>
          <p>• <strong>Clear Selection:</strong> Use the "Clear" button to reset</p>
          <p>• <strong>Close Calendar:</strong> Click outside the calendar or select a complete range</p>
          <p>• <strong>Monitor Console:</strong> Check browser console for any DOM errors</p>
        </div>
      </div>
    </div>
  );
};

export default TestCalendarFixPage;
