'use client';

import { useState, useEffect } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useToast } from '@/components/Toast';
import socketService from '@/services/socketService';

interface User {
  id: number;
  username: string;
  name: string;
  avatar: string;
  isFollowing: boolean;
  isVerified: boolean;
}

export default function FollowingPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const { isAuthenticated, user, session } = useSimpleAuth();
  const toast = useToast();
  const username = params.username as string;
  
  // 从查询参数获取初始标签页
  const initialTab = searchParams.get('tab') === 'followers' ? 'followers' : 'following';
  
  const [users, setUsers] = useState<User[]>([]);
  const [activeTab, setActiveTab] = useState<'following' | 'followers'>(initialTab as any);
  const [loading, setLoading] = useState(true);
  const [profileUser, setProfileUser] = useState<{ username: string; name: string; isCurrentUser: boolean } | null>(null);

  useEffect(() => {
    // 更新标签页时也更新URL
    if (activeTab === 'followers') {
      router.push(`/profile/${username}/following?tab=followers`, { scroll: false });
    } else {
      router.push(`/profile/${username}/following?tab=following`, { scroll: false });
    }
    
    loadUsers();
    loadProfileUser();
    
    // 设置Socket.IO监听器用于实时关注更新
    if (isAuthenticated && session?.access_token) {
      socketService.connect(session.access_token).catch(console.error);
      
      // 监听关注状态更新
      const handleFollowUpdate = (data: any) => {
        if (data.targetUsername === username || data.followerUsername === username) {
          // 重新加载用户列表
          loadUsers();
        }
        
        // 更新当前列表中的用户状态
        setUsers(prev => prev.map(u => 
          u.username === data.targetUsername 
            ? { ...u, isFollowing: data.isFollowing }
            : u
        ));
      };
      
      socketService.on('follow_updated', handleFollowUpdate);
      
      return () => {
        socketService.off('follow_updated', handleFollowUpdate);
      };
    }
  }, [activeTab, username, isAuthenticated, session]);

  const loadProfileUser = async () => {
    try {
      // 模拟API调用获取用户信息
      await new Promise((resolve) => setTimeout(resolve, 200));
      
      const isCurrentUser = user?.username === username;
      setProfileUser({
        username: username,
        name: username.charAt(0).toUpperCase() + username.slice(1),
        isCurrentUser
      });
    } catch (error) {
      console.error('Error loading profile user:', error);
    }
  };

  const loadUsers = async () => {
    try {
      setLoading(true);
      
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      // 模拟数据 - 根据不同用户和标签页返回不同数据
      const mockUsers: User[] = [
        {
          id: 1,
          username: 'alice_j',
          name: 'Alice Johnson',
          avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face',
          isFollowing: Math.random() > 0.5,
          isVerified: false,
        },
        {
          id: 2,
          username: 'bob_smith',
          name: 'Bob Smith',
          avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
          isFollowing: Math.random() > 0.5,
          isVerified: true,
        },
        {
          id: 3,
          username: 'carol_d',
          name: 'Carol Davis',
          avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
          isFollowing: Math.random() > 0.5,
          isVerified: false,
        },
        {
          id: 4,
          username: 'david_w',
          name: 'David Wilson',
          avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
          isFollowing: Math.random() > 0.5,
          isVerified: true,
        },
        {
          id: 5,
          username: 'emma_r',
          name: 'Emma Roberts',
          avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=face',
          isFollowing: Math.random() > 0.5,
          isVerified: false,
        },
        {
          id: 6,
          username: 'frank_m',
          name: 'Frank Miller',
          avatar: 'https://images.unsplash.com/photo-1504593811423-6dd665756598?w=100&h=100&fit=crop&crop=face',
          isFollowing: Math.random() > 0.5,
          isVerified: true,
        }
      ];

      // 根据标签页过滤数据
      const filteredUsers = activeTab === 'following' 
        ? mockUsers.slice(0, 4) // 模拟关注的用户
        : mockUsers.slice(2, 6); // 模拟粉丝

      setUsers(filteredUsers);
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleFollow = async (userId: number) => {
    if (!isAuthenticated) {
      toast.warning('Please log in first', 'You need to log in to follow users');
      router.push('/auth/login');
      return;
    }

    const targetUser = users.find(u => u.id === userId);
    if (!targetUser) return;

    try {
      const newFollowingState = !targetUser.isFollowing;
      
      // 更新本地状态
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { ...user, isFollowing: newFollowingState } 
          : user
      ));

      // 通过Socket.IO广播关注更新
      if (socketService.isSocketConnected()) {
        socketService.emit('follow_updated', {
          targetUsername: targetUser.username,
          followerUsername: user?.username,
          isFollowing: newFollowingState
        });
      }

      toast.success(
        newFollowingState 
          ? `You are now following ${targetUser.name}` 
          : `You unfollowed ${targetUser.name}`
      );
    } catch (error) {
      console.error('Error updating follow status:', error);
      toast.error('Failed to update follow status');
      
      // 恢复状态
      setUsers(prev => prev.map(user => 
        user.id === userId 
          ? { ...user, isFollowing: !user.isFollowing } 
          : user
      ));
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-2xl mx-auto px-6 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="flex items-center gap-4 p-4 bg-white rounded-lg shadow-sm">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                  <div className="w-20 h-8 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="max-w-2xl mx-auto px-6 py-8">
        {/* 用户信息头部 */}
        {profileUser && (
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {profileUser.isCurrentUser ? 'Your' : `${profileUser.name}'s`} Connections
            </h1>
            <p className="text-gray-600">
              {profileUser.isCurrentUser 
                ? 'Manage your following and followers' 
                : `View ${profileUser.name}'s following and followers`}
            </p>
          </div>
        )}
        
        <div className="bg-white rounded-lg shadow-sm">
          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex">
              <button
                onClick={() => setActiveTab('following')}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'following'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Following ({users.length})
              </button>
              <button
                onClick={() => setActiveTab('followers')}
                className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                  activeTab === 'followers'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                Followers ({users.length})
              </button>
            </nav>
          </div>

          {/* Users List */}
          <div className="divide-y divide-gray-100">
            {users.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">👥</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {activeTab === 'following' ? 'Not following anyone yet' : 'No followers yet'}
                </h3>
                <p className="text-gray-600">
                  {profileUser?.isCurrentUser 
                    ? (activeTab === 'following' 
                        ? "You haven't followed anyone yet. Discover interesting people to follow!" 
                        : "You don't have any followers yet. Create great content to attract followers!")
                    : (activeTab === 'following' 
                        ? `${profileUser?.name} hasn't followed anyone yet.` 
                        : `${profileUser?.name} doesn't have any followers yet.`)}
                </p>
                {profileUser?.isCurrentUser && (
                  <button
                    onClick={() => router.push('/explore')}
                    className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Discover People
                  </button>
                )}
              </div>
            ) : (
              users.map((user) => (
                <div key={user.id} className="flex items-center gap-4 p-4 hover:bg-gray-50 transition-colors">
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-12 h-12 rounded-full object-cover cursor-pointer hover:ring-2 hover:ring-blue-500/30 transition-all"
                    onClick={() => router.push(`/profile/${user.username}`)}
                  />
                  <div className="flex-1 min-w-0 cursor-pointer" onClick={() => router.push(`/profile/${user.username}`)}>
                    <div className="flex items-center gap-2">
                      <h3 className="font-medium text-gray-900 truncate hover:text-blue-600 transition-colors">{user.name}</h3>
                      {user.isVerified && (
                        <span className="text-blue-500 text-xs">✓</span>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 truncate hover:text-gray-800 transition-colors">@{user.username}</p>
                  </div>
                  {/* 只有当不是查看自己的资料时才显示关注按钮 */}
                  {user?.username !== username && (
                    <button
                      onClick={() => handleFollow(user.id)}
                      disabled={!isAuthenticated}
                      className={`px-4 py-1.5 text-sm rounded-full font-medium transition-colors ${
                        !isAuthenticated
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : user.isFollowing
                          ? 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                      title={!isAuthenticated ? 'Please log in to follow users' : ''}
                    >
                      {!isAuthenticated ? 'Follow' : (user.isFollowing ? 'Following' : 'Follow')}
                    </button>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}