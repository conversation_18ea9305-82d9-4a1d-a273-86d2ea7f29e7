import type { Metadata } from 'next';
import { Inter, Space_Grotesk, JetBrains_Mono } from 'next/font/google';
import './globals.css';
import ClientLayout from '@/components/ClientLayout';

// Primary font for body text
const inter = Inter({
  variable: '--font-inter',
  subsets: ['latin'],
  display: 'swap',
});

// Modern font for headings and brand
const spaceGrotesk = Space_Grotesk({
  variable: '--font-space-grotesk',
  subsets: ['latin'],
  display: 'swap',
});

// Monospace font for code
const jetbrainsMono = JetBrains_Mono({
  variable: '--font-jetbrains-mono',
  subsets: ['latin'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'Newzora - Your Gateway to Quality Content',
  description:
    'Discover amazing articles on technology, lifestyle, travel, food and more. Your trusted source for quality news and content.',
  other: {
    google: 'translate',
    'google-translate-customization': 'auto',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="google" content="translate" />
        <meta name="google-translate-customization" content="auto" />
        <meta name="google-site-verification" content="notranslate" />
        <style dangerouslySetInnerHTML={{
          __html: `
            .notranslate { -webkit-transform: none !important; }
            [translate="no"] { -webkit-transform: none !important; }
            .brand-name { -webkit-transform: none !important; transform: none !important; }
            .goog-te-banner-frame { display: none !important; }
          `
        }} />
      </head>
      <body
        className={`${inter.variable} ${spaceGrotesk.variable} ${jetbrainsMono.variable} font-inter antialiased`}
        suppressHydrationWarning
      >
        <ClientLayout>
          {children}
        </ClientLayout>
        {/* Move Google Translate script to bottom of body to avoid React DOM conflicts */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                if (typeof window !== 'undefined' && !window.googleTranslateElementInit) {
                  window.googleTranslateElementInit = function() {
                    try {
                      new google.translate.TranslateElement({
                        pageLanguage: 'en',
                        autoDisplay: false,
                        layout: google.translate.TranslateElement.InlineLayout.SIMPLE
                      }, 'google_translate_element');
                    } catch (e) {
                      console.warn('Google Translate initialization failed:', e);
                    }
                  };
                }
              })();
            `,
          }}
        />
        <script
          type="text/javascript"
          src="//translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"
          async
        />
      </body>
    </html>
  );
}