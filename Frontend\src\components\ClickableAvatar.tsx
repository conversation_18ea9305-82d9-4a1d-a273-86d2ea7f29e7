'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

interface User {
  id: number;
  username: string;
  avatar?: string;
  name?: string;
  bio?: string;
  followersCount?: number;
  followingCount?: number;
  worksCount?: number;
  isFollowing?: boolean;
}

interface ClickableAvatarProps {
  user: User;
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
  onClick?: () => void;
}

export default function ClickableAvatar({
  user,
  size = 'md',
  showTooltip = true,
  className = '',
  onClick,
}: ClickableAvatarProps) {
  const { isAuthenticated } = useAuth();
  const [showUserCard, setShowUserCard] = useState(false);
  const [imageError, setImageError] = useState(false);

  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const handleMouseEnter = () => {
    if (showTooltip && isAuthenticated) {
      setShowUserCard(true);
    }
  };

  const handleMouseLeave = () => {
    setShowUserCard(false);
  };

  const avatarContent = (
    <div
      className={`${sizeClasses[size]} rounded-full overflow-hidden cursor-pointer hover:ring-2 hover:ring-blue-500 hover:ring-offset-2 transition-all duration-200 ${className}`}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {user.avatar && !imageError ? (
        <img
          src={user.avatar}
          alt={user.username}
          className="w-full h-full object-cover"
          onError={() => setImageError(true)}
        />
      ) : (
        <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
          <span className="text-white font-medium text-lg">
            {user.username.charAt(0).toUpperCase()}
          </span>
        </div>
      )}
    </div>
  );

  return (
    <div className="relative">
      {isAuthenticated ? <Link href={`/profile/${user.id}`}>{avatarContent}</Link> : avatarContent}

      {/* User Card Tooltip */}
      {showUserCard && showTooltip && isAuthenticated && (
        <div
          className="absolute z-50 top-full left-1/2 transform -translate-x-1/2 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 p-6"
          onMouseEnter={() => setShowUserCard(true)}
          onMouseLeave={() => setShowUserCard(false)}
        >
          {/* Arrow */}
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white border-l border-t border-gray-200 rotate-45"></div>

          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
              {user.avatar && !imageError ? (
                <img
                  src={user.avatar}
                  alt={user.username}
                  className="w-full h-full object-cover"
                  onError={() => setImageError(true)}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <span className="text-white font-medium text-xl">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>

            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 text-lg truncate">
                {user.name || user.username}
              </h3>
              <p className="text-gray-500 text-sm">@{user.username}</p>

              {user.bio && <p className="text-gray-700 text-sm mt-2 line-clamp-2">{user.bio}</p>}

              <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                <span>
                  <span className="font-medium text-gray-900">{user.followersCount || 0}</span>{' '}
                  Followers
                </span>
                <span>
                  <span className="font-medium text-gray-900">{user.followingCount || 0}</span>{' '}
                  Following
                </span>
                <span>
                  <span className="font-medium text-gray-900">{user.worksCount || 0}</span> Works
                </span>
              </div>

              <div className="flex items-center space-x-2 mt-4">
                <Link
                  href={`/profile/${user.id}`}
                  className="flex-1 bg-blue-600 text-white text-center py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
                >
                  View Profile
                </Link>

                {!user.isFollowing ? (
                  <button className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium">
                    Follow
                  </button>
                ) : (
                  <button className="bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors text-sm font-medium">
                    Following
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
