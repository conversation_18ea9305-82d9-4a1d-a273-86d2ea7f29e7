'use client';

import React from 'react';
import Link from 'next/link';
import Logo from '@/components/Logo';

/**
 * Terms of Service Page
 * Following code generation rules: Complete TypeScript, no any types, includes error handling
 */
export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <Logo size="md" href="/" />
            <Link
              href="/"
              className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-6 py-12">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Terms of Service</h1>
            <p className="text-gray-600">Last updated: October 1, 2025</p>
          </div>

          <div className="prose prose-gray max-w-none">
            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">1. Acceptance of Terms</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Welcome to the Newzora platform. By accessing or using our services, you agree to be bound by these Terms of Service.
                If you do not agree to these terms, please do not use our services.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">2. Service Description</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Newzora is a creative content sharing platform that allows users to create, share, and discover various forms of creative content,
                including but not limited to articles, images, videos, and audio content.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">3. User Accounts</h2>
              <div className="text-gray-700 leading-relaxed space-y-3">
                <p>• You must provide accurate and complete registration information</p>
                <p>• You are responsible for maintaining account security and password confidentiality</p>
                <p>• You are responsible for all activities under your account</p>
                <p>• Each person may only register one account</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">4. Content Policy</h2>
              <div className="text-gray-700 leading-relaxed space-y-3">
                <p>User-posted content must comply with the following rules:</p>
                <p>• Must not contain illegal, harmful, threatening, abusive, or defamatory content</p>
                <p>• Must not infringe on others' intellectual property or privacy rights</p>
                <p>• Must not contain spam, advertisements, or malicious software</p>
                <p>• Must not impersonate others or make false identity claims</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">5. Intellectual Property</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Users retain ownership of their created content but grant Newzora a license to use, display, and distribute such content on the platform.
                The intellectual property of the Newzora platform itself belongs to us.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">6. Privacy Protection</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                We value your privacy. Please review our
                <Link href="/legal/privacy" className="text-blue-600 hover:text-blue-700 underline mx-1">
                  Privacy Policy
                </Link>
                to understand how we collect, use, and protect your personal information.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">7. Service Changes</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                We reserve the right to modify or terminate services at any time. Major changes will be communicated to users in advance.
                Continued use of the service indicates acceptance of the modified terms.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">8. Disclaimer</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                Services are provided "as is" without any express or implied warranties. We are not responsible for service interruptions,
                data loss, or other damages.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">9. Contact Us</h2>
              <p className="text-gray-700 leading-relaxed mb-4">
                If you have any questions about these Terms of Service, please contact us through the following methods:
              </p>
              <div className="text-gray-700 leading-relaxed space-y-2">
                <p>• Email: <EMAIL></p>
                <p>• Address: Xiamen, China</p>
              </div>
            </section>
          </div>
        </div>
      </main>
    </div>
  );
}
