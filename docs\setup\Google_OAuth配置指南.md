# 🔐 Google OAuth配置指南

## 📋 问题说明

**错误信息**: "Unable to exchange external code"  
**原因**: Google OAuth需要在Google Cloud Console中配置授权域名，localhost不被允许作为生产环境域名。

## 🛠️ 解决方案

### 方案1: 生产环境配置 (推荐)

#### 1. 创建Google Cloud项目
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用Google+ API和Google Identity API

#### 2. 配置OAuth同意屏幕
1. 转到 **APIs & Services** → **OAuth consent screen**
2. 选择 **External** 用户类型
3. 填写应用信息：
   - 应用名称: Newzora
   - 用户支持邮箱: 您的邮箱
   - 开发者联系信息: 您的邮箱

#### 3. 创建OAuth客户端ID
1. 转到 **APIs & Services** → **Credentials**
2. 点击 **Create Credentials** → **OAuth client ID**
3. 选择 **Web application**
4. 配置授权重定向URI：
   ```
   https://yourdomain.com/auth/callback/google
   https://wdpprzeflzlardkmncfk.supabase.co/auth/v1/callback
   ```

#### 4. 在Supabase中配置
1. 登录Supabase Dashboard
2. 转到 **Authentication** → **Providers**
3. 启用Google提供商
4. 输入Client ID和Client Secret
5. 设置重定向URL

### 方案2: 开发环境配置

#### 使用ngrok进行本地测试
```bash
# 安装ngrok
npm install -g ngrok

# 启动应用
npm run dev

# 在新终端中启动ngrok
ngrok http 3000
```

然后在Google Cloud Console中添加ngrok提供的HTTPS URL作为授权域名。

### 方案3: 暂时禁用 (当前方案)

在开发阶段暂时禁用Google登录，使用邮箱密码登录进行开发和测试。

## 🔧 代码配置

### 环境变量配置
```env
# .env.local
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### Supabase配置
```typescript
// 在生产环境中启用
const enableGoogleAuth = process.env.NODE_ENV === 'production';

// 条件渲染Google登录按钮
{enableGoogleAuth && (
  <button onClick={() => loginWithProvider('google')}>
    <Google className="h-5 w-5" />
  </button>
)}
```

## 📝 当前状态

- ✅ Google登录UI已实现
- ✅ Supabase OAuth集成已完成
- ❌ Google Cloud Console配置待完成
- ❌ 生产域名待配置

## 🚀 部署后配置步骤

1. **获取生产域名**
   - 部署应用到Vercel/Netlify等平台
   - 获取正式域名 (如: newzora.vercel.app)

2. **更新Google Cloud配置**
   - 添加生产域名到授权域名列表
   - 更新重定向URI

3. **测试OAuth流程**
   - 在生产环境测试Google登录
   - 验证用户数据正确保存

4. **启用功能**
   - 移除开发环境限制
   - 启用Google登录按钮

## 💡 开发建议

1. **优先使用邮箱登录**: 在开发阶段专注于核心功能
2. **模拟OAuth数据**: 可以创建模拟的Google用户数据进行测试
3. **延后OAuth配置**: 等应用基本功能完成后再配置社交登录

---

**更新时间**: 2024年12月  
**状态**: Google OAuth暂时禁用，等待生产环境配置