'use client';

import { usePathname, useRouter } from 'next/navigation';
import {
    HomeIcon,
    UsersIcon,
    DocumentTextIcon,
    ChatBubbleLeftRightIcon,
    ChartBarIcon,
    ShieldCheckIcon,
    Cog6ToothIcon,
    XMarkIcon,
    EyeIcon,
    ExclamationTriangleIcon,
    CpuChipIcon
} from '@heroicons/react/24/outline';

interface AdminSidebarProps {
    isOpen: boolean;
    onClose: () => void;
}

interface MenuItem {
    name: string;
    href: string;
    icon: React.ComponentType<{ className?: string }>;
    badge?: number;
    children?: MenuItem[];
}

export default function AdminSidebar({ isOpen, onClose }: AdminSidebarProps) {
    const pathname = usePathname();
    const router = useRouter();

    const menuItems: MenuItem[] = [
        {
            name: 'Dashboard',
            href: '/admin',
            icon: HomeIcon,
        },
        {
            name: 'User Management',
            href: '/admin/users',
            icon: UsersIcon,
            children: [
                { name: 'All Users', href: '/admin/users', icon: UsersIcon },
                { name: 'User Analytics', href: '/admin/users/analytics', icon: ChartBarIcon },
                { name: 'Role Management', href: '/admin/users/roles', icon: ShieldCheckIcon },
            ],
        },
        {
            name: 'Content Management',
            href: '/admin/content',
            icon: DocumentTextIcon,
            children: [
                { name: 'Articles', href: '/admin/content/articles', icon: DocumentTextIcon },
                { name: 'Comments', href: '/admin/content/comments', icon: ChatBubbleLeftRightIcon },
                { name: 'Categories', href: '/admin/content/categories', icon: DocumentTextIcon },
                { name: 'Review Queue', href: '/admin/content/reviews', icon: EyeIcon, badge: 12 },
            ],
        },
        {
            name: 'Analytics',
            href: '/admin/analytics',
            icon: ChartBarIcon,
            children: [
                { name: 'User Analytics', href: '/admin/analytics/users', icon: UsersIcon },
                { name: 'Content Analytics', href: '/admin/analytics/content', icon: DocumentTextIcon },
                { name: 'System Analytics', href: '/admin/analytics/system', icon: CpuChipIcon },
            ],
        },
        {
            name: 'AI Patrol System',
            href: '/admin/ai-patrol',
            icon: CpuChipIcon,
        },
        {
            name: 'System Monitoring',
            href: '/admin/monitoring',
            icon: ExclamationTriangleIcon,
        },
        {
            name: 'System Settings',
            href: '/admin/settings',
            icon: Cog6ToothIcon,
            children: [
                { name: 'General Settings', href: '/admin/settings/general', icon: Cog6ToothIcon },
                { name: 'Security Settings', href: '/admin/settings/security', icon: ShieldCheckIcon },
                { name: 'Email Settings', href: '/admin/settings/email', icon: DocumentTextIcon },
            ],
        },
    ];

    const isActiveRoute = (href: string) => {
        if (href === '/admin') {
            return pathname === '/admin';
        }
        return pathname.startsWith(href);
    };

    const handleNavigation = (href: string) => {
        router.push(href);
        onClose();
    };

    return (
        <>
            {/* Sidebar */}
            <div className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        lg:translate-x-0 lg:static lg:inset-0
      `}>
                <div className="flex items-center justify-between h-16 px-6 bg-gray-900">
                    <h2 className="text-white text-lg font-semibold">Admin Panel</h2>
                    <button
                        onClick={onClose}
                        className="lg:hidden text-gray-400 hover:text-white"
                    >
                        <XMarkIcon className="h-6 w-6" />
                    </button>
                </div>

                <nav className="mt-8 px-4">
                    <div className="space-y-2">
                        {menuItems.map((item) => (
                            <div key={item.name}>
                                <button
                                    onClick={() => handleNavigation(item.href)}
                                    className={`
                    w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors
                    ${isActiveRoute(item.href)
                                            ? 'bg-blue-700 text-white'
                                            : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                                        }
                  `}
                                >
                                    <item.icon className="h-5 w-5 mr-3" />
                                    <span className="flex-1 text-left">{item.name}</span>
                                    {item.badge && (
                                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                            {item.badge}
                                        </span>
                                    )}
                                </button>

                                {/* Submenu */}
                                {item.children && isActiveRoute(item.href) && (
                                    <div className="ml-4 mt-2 space-y-1">
                                        {item.children.map((child) => (
                                            <button
                                                key={child.name}
                                                onClick={() => handleNavigation(child.href)}
                                                className={`
                          w-full flex items-center px-4 py-2 text-sm rounded-lg transition-colors
                          ${pathname === child.href
                                                        ? 'bg-blue-600 text-white'
                                                        : 'text-gray-400 hover:bg-gray-700 hover:text-white'
                                                    }
                        `}
                                            >
                                                <child.icon className="h-4 w-4 mr-3" />
                                                <span className="flex-1 text-left">{child.name}</span>
                                                {child.badge && (
                                                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                                                        {child.badge}
                                                    </span>
                                                )}
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                </nav>

                {/* Footer */}
                <div className="absolute bottom-0 left-0 right-0 p-4 bg-gray-900">
                    <div className="text-xs text-gray-400 text-center">
                        <div>Newzora Admin v1.0</div>
                        <div className="mt-1">© 2024 All rights reserved</div>
                    </div>
                </div>
            </div>
        </>
    );
}