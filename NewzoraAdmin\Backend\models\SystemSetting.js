const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SystemSetting = sequelize.define('system_settings', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: '设置键名'
  },
  value: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: '设置值，使用JSON格式存储'
  },
  description: {
    type: DataTypes.TEXT,
    comment: '设置描述'
  },
  category: {
    type: DataTypes.STRING(50),
    comment: '设置分类，如：basic, security, email'
  },
  is_public: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    allowNull: false,
    comment: '是否为公开设置（前端可访问）'
  },
  updated_by: {
    type: DataTypes.INTEGER,
    references: {
      model: 'users',
      key: 'id'
    },
    onUpdate: 'CASCADE',
    onDelete: 'SET NULL',
    comment: '最后更新者ID'
  }
}, {
  tableName: 'system_settings',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      unique: true,
      fields: ['key']
    },
    {
      fields: ['category']
    },
    {
      fields: ['is_public']
    }
  ]
});

// 静态方法：获取设置值
SystemSetting.getSetting = async function(key, defaultValue = null) {
  try {
    const setting = await this.findOne({ where: { key } });
    return setting ? setting.value : defaultValue;
  } catch (error) {
    console.error(`获取设置 ${key} 失败:`, error);
    return defaultValue;
  }
};

// 静态方法：设置值
SystemSetting.setSetting = async function(key, value, description = null, updatedBy = null) {
  try {
    const [setting, created] = await this.findOrCreate({
      where: { key },
      defaults: {
        key,
        value,
        description,
        updated_by: updatedBy
      }
    });

    if (!created) {
      await setting.update({
        value,
        description: description || setting.description,
        updated_by: updatedBy
      });
    }

    return setting;
  } catch (error) {
    console.error(`设置 ${key} 失败:`, error);
    throw error;
  }
};

module.exports = SystemSetting;