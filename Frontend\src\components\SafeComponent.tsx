'use client';

import React, { useEffect, useRef, useCallback } from 'react';

/**
 * 安全组件包装器 - 防止DOM操作错误
 */
interface SafeComponentProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const SafeComponent: React.FC<SafeComponentProps> = ({ 
  children, 
  fallback = null 
}) => {
  const mountedRef = useRef(true);
  const [hasError, setHasError] = React.useState(false);

  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const handleError = useCallback((error: Error) => {
    console.warn('SafeComponent caught error:', error);
    if (mountedRef.current) {
      setHasError(true);
    }
  }, []);

  if (hasError) {
    return <>{fallback}</>;
  }

  return (
    <ErrorBoundary onError={handleError}>
      {children}
    </ErrorBoundary>
  );
};

/**
 * 错误边界组件
 */
interface ErrorBoundaryProps {
  children: React.ReactNode;
  onError?: (error: Error) => void;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, { hasError: boolean }> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.warn('ErrorBoundary caught error:', error, errorInfo);
    this.props.onError?.(error);
  }

  render() {
    if (this.state.hasError) {
      return null;
    }

    return this.props.children;
  }
}

export default SafeComponent;