# Newzora Production Deployment Script

param(
    [switch]$Build,
    [switch]$Deploy,
    [switch]$Restart,
    [switch]$Stop,
    [switch]$Logs,
    [switch]$Status,
    [switch]$Backup,
    [switch]$All
)

# Colors for output
function Write-Success { Write-Host $args -ForegroundColor Green }
function Write-Warning { Write-Host $args -ForegroundColor Yellow }
function Write-Error { Write-Host $args -ForegroundColor Red }
function Write-Info { Write-Host $args -ForegroundColor Cyan }
function Write-Header { Write-Host $args -ForegroundColor Magenta }

Write-Header "🚀 Newzora Production Deployment"
Write-Header "=================================="

# Check if Docker is available
function Test-Docker {
    try {
        docker --version | Out-Null
        docker-compose --version | Out-Null
        return $true
    } catch {
        Write-Error "❌ Docker or Docker Compose not found"
        return $false
    }
}

# Backup database
function Backup-Database {
    Write-Info "📦 Creating database backup..."
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFile = "backups/newzora_backup_$timestamp.sql"
    
    # Create backup directory if it doesn't exist
    if (!(Test-Path "backups")) {
        New-Item -ItemType Directory -Path "backups"
    }
    
    try {
        docker-compose -f config/docker-compose.production.yml exec -T postgres pg_dump -U newzora_user newzora_production > $backupFile
        Write-Success "✅ Database backup created: $backupFile"
        return $true
    } catch {
        Write-Error "❌ Database backup failed: $($_.Exception.Message)"
        return $false
    }
}

# Build production images
function Build-Production {
    Write-Info "🔨 Building production images..."
    
    try {
        # Build backend
        Write-Info "Building backend image..."
        docker build -f Backend/Dockerfile.prod -t newzora-backend:latest Backend/
        
        # Build frontend
        Write-Info "Building frontend image..."
        docker build -f Frontend/Dockerfile.prod -t newzora-frontend:latest Frontend/ `
            --build-arg NEXT_PUBLIC_API_URL=https://api.newzora.com `
            --build-arg NEXT_PUBLIC_FRONTEND_URL=https://newzora.com
        
        Write-Success "✅ Production images built successfully"
        return $true
    } catch {
        Write-Error "❌ Build failed: $($_.Exception.Message)"
        return $false
    }
}

# Deploy to production
function Deploy-Production {
    Write-Info "🚀 Deploying to production..."
    
    # Check if .env.production exists
    if (!(Test-Path "Backend/.env.production")) {
        Write-Error "❌ Backend/.env.production not found"
        Write-Info "💡 Please create production environment file first"
        return $false
    }
    
    if (!(Test-Path "Frontend/.env.production")) {
        Write-Error "❌ Frontend/.env.production not found"
        Write-Info "💡 Please create frontend production environment file first"
        return $false
    }
    
    try {
        # Copy production environment files
        Copy-Item "Backend/.env.production" "Backend/.env"
        Copy-Item "Frontend/.env.production" "Frontend/.env.local"
        
        # Deploy with docker-compose
        docker-compose -f config/docker-compose.production.yml up -d --build
        
        Write-Success "✅ Production deployment completed"
        return $true
    } catch {
        Write-Error "❌ Deployment failed: $($_.Exception.Message)"
        return $false
    }
}

# Restart services
function Restart-Services {
    Write-Info "🔄 Restarting production services..."
    
    try {
        docker-compose -f config/docker-compose.production.yml restart
        Write-Success "✅ Services restarted successfully"
        return $true
    } catch {
        Write-Error "❌ Restart failed: $($_.Exception.Message)"
        return $false
    }
}

# Stop services
function Stop-Services {
    Write-Info "🛑 Stopping production services..."
    
    try {
        docker-compose -f config/docker-compose.production.yml down
        Write-Success "✅ Services stopped successfully"
        return $true
    } catch {
        Write-Error "❌ Stop failed: $($_.Exception.Message)"
        return $false
    }
}

# Show logs
function Show-Logs {
    Write-Info "📋 Showing production logs..."
    docker-compose -f config/docker-compose.production.yml logs -f --tail=100
}

# Show status
function Show-Status {
    Write-Info "📊 Production services status:"
    docker-compose -f config/docker-compose.production.yml ps
    
    Write-Info "`n🔍 Health checks:"
    
    # Check frontend
    try {
        $response = Invoke-WebRequest -Uri "https://newzora.com" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "✅ Frontend: Online"
        } else {
            Write-Warning "⚠️ Frontend: Status $($response.StatusCode)"
        }
    } catch {
        Write-Error "❌ Frontend: Offline"
    }
    
    # Check backend API
    try {
        $response = Invoke-WebRequest -Uri "https://api.newzora.com/health" -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Success "✅ Backend API: Online"
        } else {
            Write-Warning "⚠️ Backend API: Status $($response.StatusCode)"
        }
    } catch {
        Write-Error "❌ Backend API: Offline"
    }
}

# Main execution
if (!(Test-Docker)) {
    exit 1
}

# Change to project root directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectRoot = Split-Path -Parent $scriptPath
Set-Location $projectRoot

# Execute based on parameters
$success = $true

if ($All) {
    Write-Header "🎯 Full production deployment process"
    
    if (Backup-Database) {
        if (Build-Production) {
            if (Deploy-Production) {
                Start-Sleep -Seconds 30
                Show-Status
            } else { $success = $false }
        } else { $success = $false }
    } else { $success = $false }
}
else {
    if ($Backup) { $success = $success -and (Backup-Database) }
    if ($Build) { $success = $success -and (Build-Production) }
    if ($Deploy) { $success = $success -and (Deploy-Production) }
    if ($Restart) { $success = $success -and (Restart-Services) }
    if ($Stop) { $success = $success -and (Stop-Services) }
    if ($Logs) { Show-Logs }
    if ($Status) { Show-Status }
}

if ($success) {
    Write-Success "`n🎉 Production deployment completed successfully!"
    Write-Info "🌐 Frontend: https://newzora.com"
    Write-Info "🔧 API: https://api.newzora.com"
} else {
    Write-Error "`n❌ Production deployment failed!"
    exit 1
}
