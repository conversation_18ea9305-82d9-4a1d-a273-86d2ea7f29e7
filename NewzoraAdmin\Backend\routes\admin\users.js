const express = require('express');
const router = express.Router();
const { body, validationResult } = require('express-validator');
const userManagementService = require('../../services/admin/userManagementService');
const { requirePermission } = require('../../middleware/adminAuth');
const { logAdminAction, logBulkAction } = require('../../middleware/adminLogger');

// 获取用户列表
router.get('/',
  requirePermission('user:view'),
  logAdminAction('view_user_list'),
  async (req, res) => {
    try {
      const result = await userManagementService.getUserList(req.query);
      res.json({
        success: true,
        data: result.users,
        pagination: result.pagination
      });
    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户列表失败'
      });
    }
  }
);

// 获取用户详情
router.get('/:id',
  requirePermission('user:view'),
  logAdminAction('view_user_detail', 'user'),
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      if (isNaN(userId)) {
        return res.status(400).json({
          success: false,
          message: '无效的用户ID'
        });
      }

      const user = await userManagementService.getUserDetail(userId);
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      if (error.message === '用户不存在') {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      console.error('获取用户详情失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户详情失败'
      });
    }
  }
);

// 创建用户
router.post('/',
  requirePermission('user:edit'),
  [
    body('username')
      .isLength({ min: 3, max: 50 })
      .withMessage('Username must be between 3-50 characters')
      .isAlphanumeric()
      .withMessage('Username can only contain letters and numbers'),
    body('email')
      .isEmail()
      .withMessage('Please enter a valid email address'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters'),
    body('display_name')
      .isLength({ min: 1, max: 100 })
      .withMessage('Display name must be between 1-100 characters'),
    body('role')
      .optional()
      .isIn(['user', 'moderator', 'admin'])
      .withMessage('Invalid role')
  ],
  logAdminAction('create_user', 'user'),
  async (req, res) => {
    try {
      // 检查验证错误
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Input validation failed',
          errors: errors.array()
        });
      }

      const user = await userManagementService.createUser(req.body);
      res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully'
      });
    } catch (error) {
      if (error.message === 'Username or email already exists') {
        return res.status(409).json({
          success: false,
          message: 'Username or email already exists'
        });
      }

      console.error('Failed to create user:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create user'
      });
    }
  }
);

// 更新用户状态
router.put('/:id/status',
  requirePermission('user:edit'),
  [
    body('isActive')
      .isBoolean()
      .withMessage('状态值必须为布尔类型')
  ],
  logAdminAction('update_user_status', 'user'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入数据验证失败',
          errors: errors.array()
        });
      }

      const userId = parseInt(req.params.id);
      const { isActive } = req.body;

      const user = await userManagementService.updateUserStatus(userId, isActive);
      res.json({
        success: true,
        data: user,
        message: `用户已${isActive ? '激活' : '禁用'}`
      });
    } catch (error) {
      if (error.message === '用户不存在') {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      console.error('更新用户状态失败:', error);
      res.status(500).json({
        success: false,
        message: '更新用户状态失败'
      });
    }
  }
);

// 更新用户角色
router.put('/:id/role',
  requirePermission('user:role_change'),
  [
    body('role')
      .isIn(['user', 'moderator', 'admin', 'super_admin'])
      .withMessage('无效的角色')
  ],
  logAdminAction('update_user_role', 'user'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入数据验证失败',
          errors: errors.array()
        });
      }

      const userId = parseInt(req.params.id);
      const { role } = req.body;

      const user = await userManagementService.updateUserRole(userId, role);
      res.json({
        success: true,
        data: user,
        message: '用户角色更新成功'
      });
    } catch (error) {
      if (error.message === '用户不存在') {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      console.error('更新用户角色失败:', error);
      res.status(500).json({
        success: false,
        message: '更新用户角色失败'
      });
    }
  }
);

// 删除用户
router.delete('/:id',
  requirePermission('user:delete'),
  logAdminAction('delete_user', 'user'),
  async (req, res) => {
    try {
      const userId = parseInt(req.params.id);
      const user = await userManagementService.deleteUser(userId);
      res.json({
        success: true,
        data: user,
        message: '用户删除成功'
      });
    } catch (error) {
      if (error.message === '用户不存在') {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      console.error('删除用户失败:', error);
      res.status(500).json({
        success: false,
        message: '删除用户失败'
      });
    }
  }
);

// 批量操作用户
router.post('/bulk-action',
  requirePermission('user:edit'),
  [
    body('userIds')
      .isArray({ min: 1 })
      .withMessage('用户ID列表不能为空'),
    body('action')
      .isIn(['activate', 'deactivate', 'delete', 'changeRole'])
      .withMessage('无效的操作类型'),
    body('value')
      .optional()
      .custom((value, { req }) => {
        if (req.body.action === 'changeRole' && !['user', 'moderator', 'admin'].includes(value)) {
          throw new Error('无效的角色');
        }
        return true;
      })
  ],
  logBulkAction('user_bulk_action', 'user'),
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '输入数据验证失败',
          errors: errors.array()
        });
      }

      const { userIds, action, value } = req.body;
      const result = await userManagementService.bulkUpdateUsers(userIds, action, value);
      
      res.json({
        success: true,
        data: result,
        message: `批量操作完成，影响 ${result.updated} 个用户`
      });
    } catch (error) {
      console.error('批量操作用户失败:', error);
      res.status(500).json({
        success: false,
        message: error.message || '批量操作失败'
      });
    }
  }
);

// 获取用户统计
router.get('/stats/overview',
  requirePermission('user:view'),
  logAdminAction('view_user_stats'),
  async (req, res) => {
    try {
      const stats = await userManagementService.getUserStats();
      res.json({
        success: true,
        data: stats
      });
    } catch (error) {
      console.error('获取用户统计失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户统计失败'
      });
    }
  }
);

module.exports = router;