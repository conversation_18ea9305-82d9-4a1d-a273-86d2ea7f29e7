'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/components/Toast';
import {
  PhotoIcon,
  VideoCameraIcon,
  SpeakerWaveIcon,
  DocumentIcon,
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  TrashIcon,
  EyeIcon,
  DocumentDuplicateIcon,
  FolderIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';

interface MediaFile {
  id: number;
  filename: string;
  originalName: string;
  mimeType: string;
  fileType: 'image' | 'video' | 'audio' | 'document';
  size: number;
  url: string;
  thumbnailUrl?: string;
  duration?: number;
  dimensions?: { width: number; height: number };
  folder: string;
  description?: string;
  altText?: string;
  tags: string[];
  status: 'uploading' | 'processing' | 'ready' | 'error';
  createdAt: string;
  updatedAt: string;
}

interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

interface MediaManagerProps {
  className?: string;
  onSelect?: (file: MediaFile) => void;
  allowMultiple?: boolean;
  fileTypes?: string[];
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

const fileTypeConfig = {
  image: { label: 'Images', icon: PhotoIcon, color: 'text-green-600', bgColor: 'bg-green-100' },
  video: { label: 'Videos', icon: VideoCameraIcon, color: 'text-blue-600', bgColor: 'bg-blue-100' },
  audio: {
    label: 'Audio',
    icon: SpeakerWaveIcon,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
  document: {
    label: 'Documents',
    icon: DocumentIcon,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
  },
};

const folders = [
  { value: '', label: 'All Folders' },
  { value: 'uploads', label: 'Default Upload' },
  { value: 'articles', label: 'Article Images' },
  { value: 'avatars', label: 'Avatars' },
  { value: 'thumbnails', label: 'Thumbnails' },
  { value: 'documents', label: 'Documents' },
];

export default function MediaManager({
  className = '',
  onSelect,
  allowMultiple = false,
  fileTypes = [],
}: MediaManagerProps) {
  const { user, token, isAuthenticated } = useAuth();
  const toast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<MediaFile[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 24,
    total: 0,
    pages: 0,
  });

  // Filter and search state
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [folderFilter, setFolderFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);

  const fetchFiles = useCallback(
    async (page: number = 1) => {
      if (!isAuthenticated || !token) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        const params = new URLSearchParams({
          page: page.toString(),
          limit: pagination.limit.toString(),
        });

        if (searchQuery) params.append('search', searchQuery);
        if (typeFilter) params.append('fileType', typeFilter);
        if (folderFilter) params.append('folder', folderFilter);
        if (fileTypes.length > 0) params.append('allowedTypes', fileTypes.join(','));

        const response = await fetch(`${API_BASE_URL}/media?${params}`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setFiles(data.data.files);
            setPagination(data.data.pagination);
          } else {
            toast.error('Load Failed', data.message || 'Unable to get file list');
          }
        } else if (response.status === 401) {
          toast.error('Authentication Failed', 'Please log in again');
        } else {
          toast.error('Load Failed', 'Server error, please try again later');
        }
      } catch (error) {
        console.error('Error fetching files:', error);
        toast.error('Load Failed', 'Network error, please check connection');
      } finally {
        setLoading(false);
      }
    },
    [
      isAuthenticated,
      token,
      searchQuery,
      typeFilter,
      folderFilter,
      fileTypes,
      pagination.limit,
      toast,
    ]
  );

  useEffect(() => {
    fetchFiles(1);
  }, [fetchFiles]);

  const handleFileUpload = async (uploadFiles: FileList) => {
    if (!uploadFiles.length) return;

    setUploading(true);
    const uploadPromises = Array.from(uploadFiles).map(async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', folderFilter || 'uploads');

      try {
        const response = await fetch(`${API_BASE_URL}/media/upload`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            return data.data.mediaFile;
          } else {
            throw new Error(data.message || 'Upload failed');
          }
        } else {
          throw new Error('Upload failed');
        }
      } catch (error) {
        console.error('Upload error:', error);
        toast.error(
          'Upload Failed',
          `${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
        return null;
      }
    });

    try {
      const results = await Promise.all(uploadPromises);
      const successCount = results.filter(Boolean).length;

      if (successCount > 0) {
        toast.success('Upload Successful', `Successfully uploaded ${successCount} files`);
        fetchFiles(pagination.page);
      }
    } catch (error) {
      console.error('Upload batch error:', error);
    } finally {
      setUploading(false);
      setShowUploadModal(false);
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchFiles(1);
  };

  const handleDeleteFile = async (fileId: number) => {
    if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/media/${fileId}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        toast.success('Delete Successful', 'File has been deleted');
        fetchFiles(pagination.page);
      } else {
        const data = await response.json();
        toast.error('Delete Failed', data.message || 'Unable to delete file');
      }
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Delete Failed', 'Network error, please check connection');
    }
  };

  const handleFileSelect = (file: MediaFile) => {
    if (onSelect) {
      if (allowMultiple) {
        const isSelected = selectedFiles.some((f) => f.id === file.id);
        if (isSelected) {
          setSelectedFiles(selectedFiles.filter((f) => f.id !== file.id));
        } else {
          setSelectedFiles([...selectedFiles, file]);
        }
      } else {
        onSelect(file);
      }
    }
  };

  const handlePageChange = (newPage: number) => {
    fetchFiles(newPage);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!isAuthenticated) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">Please log in first</h3>
        <p className="mt-2 text-sm text-gray-500">Log in to manage your media files</p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Media Management</h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage your images, videos, audio and document files
          </p>
        </div>
        <button
          onClick={() => setShowUploadModal(true)}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <CloudArrowUpIcon className="h-4 w-4 mr-2" />
          Upload Files
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg border p-4">
        <form onSubmit={handleSearch} className="flex items-center space-x-4">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search filename or description..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filter
          </button>
          <button
            type="submit"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Search
          </button>
        </form>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">File Type</label>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Types</option>
                  {Object.entries(fileTypeConfig).map(([value, config]) => (
                    <option key={value} value={value}>
                      {config.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Folder</label>
                <select
                  value={folderFilter}
                  onChange={(e) => setFolderFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {folders.map((folder) => (
                    <option key={folder.value} value={folder.value}>
                      {folder.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Selected Files Info */}
      {allowMultiple && selectedFiles.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <DocumentDuplicateIcon className="h-5 w-5 text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-900">
                Selected {selectedFiles.length} files
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onSelect && onSelect(selectedFiles[0])}
                className="text-sm font-medium text-blue-600 hover:text-blue-500"
              >
                Confirm Selection
              </button>
              <button
                onClick={() => setSelectedFiles([])}
                className="text-sm font-medium text-gray-500 hover:text-gray-400"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Files Grid */}
      <div className="bg-white rounded-lg border">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-500">Loading...</p>
          </div>
        ) : files.length === 0 ? (
          <div className="p-8 text-center">
            <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900">No Files</h3>
            <p className="mt-2 text-sm text-gray-500">
              {searchQuery || typeFilter || folderFilter
                ? 'No files found matching the criteria'
                : 'Start by uploading your first file'}
            </p>
            {!searchQuery && !typeFilter && !folderFilter && (
              <button
                onClick={() => setShowUploadModal(true)}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                Upload Files
              </button>
            )}
          </div>
        ) : (
          <div className="p-6">
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {files.map((file) => {
                const TypeIcon = fileTypeConfig[file.fileType].icon;
                const isSelected = selectedFiles.some((f) => f.id === file.id);

                return (
                  <div
                    key={file.id}
                    className={`relative group cursor-pointer rounded-lg border-2 transition-all ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                    }`}
                    onClick={() => handleFileSelect(file)}
                  >
                    <div className="aspect-square p-3">
                      {file.fileType === 'image' && file.url ? (
                        <img
                          src={file.thumbnailUrl || file.url}
                          alt={file.altText || file.originalName}
                          className="w-full h-full object-cover rounded"
                        />
                      ) : (
                        <div
                          className={`w-full h-full flex items-center justify-center rounded ${fileTypeConfig[file.fileType].bgColor}`}
                        >
                          <TypeIcon className={`h-8 w-8 ${fileTypeConfig[file.fileType].color}`} />
                        </div>
                      )}

                      {/* Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(file.url, '_blank');
                            }}
                            className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-100"
                            title="View File"
                          >
                            <EyeIcon className="h-4 w-4 text-gray-600" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteFile(file.id);
                            }}
                            className="p-2 bg-white rounded-full shadow-lg hover:bg-red-50"
                            title="Delete File"
                          >
                            <TrashIcon className="h-4 w-4 text-red-600" />
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="p-2 border-t border-gray-200">
                      <p
                        className="text-xs font-medium text-gray-900 truncate"
                        title={file.originalName}
                      >
                        {file.originalName}
                      </p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                        {file.duration && (
                          <span className="text-xs text-gray-500">
                            {formatDuration(file.duration)}
                          </span>
                        )}
                      </div>
                      {file.tags && file.tags.length > 0 && (
                        <div className="mt-1 flex flex-wrap gap-1">
                          {file.tags.slice(0, 2).map((tag, index) => (
                            <span
                              key={index}
                              className="inline-block px-1 py-0.5 text-xs bg-gray-100 text-gray-600 rounded"
                            >
                              {tag}
                            </span>
                          ))}
                          {file.tags.length > 2 && (
                            <span className="text-xs text-gray-400">+{file.tags.length - 2}</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg border">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing{' '}
                <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(pagination.page * pagination.limit, pagination.total)}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span> results
              </p>
            </div>
            <div>
              <nav
                className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px"
                aria-label="Pagination"
              >
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const pageNum =
                    Math.max(1, Math.min(pagination.pages - 4, pagination.page - 2)) + i;
                  return (
                    <button
                      key={pageNum}
                      onClick={() => handlePageChange(pageNum)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNum === pagination.page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}

                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Upload Files</h3>
              <button
                onClick={() => setShowUploadModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Folder
                </label>
                <select
                  value={folderFilter}
                  onChange={(e) => setFolderFilter(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {folders
                    .filter((f) => f.value)
                    .map((folder) => (
                      <option key={folder.value} value={folder.value}>
                        {folder.label}
                      </option>
                    ))}
                </select>
              </div>

              <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-sm text-gray-600">
                  Click to select files or drag files here
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  Supports images, videos, audio and document files
                </p>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={fileTypes.length > 0 ? fileTypes.join(',') : '*/*'}
                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                className="hidden"
              />

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowUploadModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
              </div>
            </div>

            {uploading && (
              <div className="mt-4 p-4 bg-blue-50 rounded-md">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                  <span className="text-sm text-blue-900">Uploading files...</span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
