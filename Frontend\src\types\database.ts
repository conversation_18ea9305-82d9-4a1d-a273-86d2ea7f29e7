// Supabase数据库类型定义
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          username: string;
          display_name: string | null;
          avatar_url: string | null;
          bio: string | null;
          website: string | null;
          location: string | null;
          created_at: string;
          updated_at: string;
          email_verified: boolean;
          is_active: boolean;
          role: 'user' | 'admin' | 'moderator';
          preferences: J<PERSON> | null;
          last_login_at: string | null;
        };
        Insert: {
          id: string;
          email: string;
          username: string;
          display_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          website?: string | null;
          location?: string | null;
          created_at?: string;
          updated_at?: string;
          email_verified?: boolean;
          is_active?: boolean;
          role?: 'user' | 'admin' | 'moderator';
          preferences?: Json | null;
          last_login_at?: string | null;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string;
          display_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          website?: string | null;
          location?: string | null;
          created_at?: string;
          updated_at?: string;
          email_verified?: boolean;
          is_active?: boolean;
          role?: 'user' | 'admin' | 'moderator';
          preferences?: Json | null;
          last_login_at?: string | null;
        };
      };
      works: {
        Row: {
          id: string;
          title: string;
          description: string | null;
          content: string;
          type: 'article' | 'video' | 'audio' | 'image';
          status: 'draft' | 'published' | 'archived';
          author_id: string;
          category_id: string | null;
          tags: string[] | null;
          metadata: Json | null;
          view_count: number;
          like_count: number;
          comment_count: number;
          created_at: string;
          updated_at: string;
          published_at: string | null;
        };
        Insert: {
          id?: string;
          title: string;
          description?: string | null;
          content: string;
          type: 'article' | 'video' | 'audio' | 'image';
          status?: 'draft' | 'published' | 'archived';
          author_id: string;
          category_id?: string | null;
          tags?: string[] | null;
          metadata?: Json | null;
          view_count?: number;
          like_count?: number;
          comment_count?: number;
          created_at?: string;
          updated_at?: string;
          published_at?: string | null;
        };
        Update: {
          id?: string;
          title?: string;
          description?: string | null;
          content?: string;
          type?: 'article' | 'video' | 'audio' | 'image';
          status?: 'draft' | 'published' | 'archived';
          author_id?: string;
          category_id?: string | null;
          tags?: string[] | null;
          metadata?: Json | null;
          view_count?: number;
          like_count?: number;
          comment_count?: number;
          created_at?: string;
          updated_at?: string;
          published_at?: string | null;
        };
      };
      categories: {
        Row: {
          id: string;
          name: string;
          slug: string;
          description: string | null;
          parent_id: string | null;
          sort_order: number;
          is_active: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          slug: string;
          description?: string | null;
          parent_id?: string | null;
          sort_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          slug?: string;
          description?: string | null;
          parent_id?: string | null;
          sort_order?: number;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      comments: {
        Row: {
          id: string;
          content: string;
          work_id: string;
          author_id: string;
          parent_id: string | null;
          is_approved: boolean;
          like_count: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          content: string;
          work_id: string;
          author_id: string;
          parent_id?: string | null;
          is_approved?: boolean;
          like_count?: number;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          content?: string;
          work_id?: string;
          author_id?: string;
          parent_id?: string | null;
          is_approved?: boolean;
          like_count?: number;
          created_at?: string;
          updated_at?: string;
        };
      };
      likes: {
        Row: {
          id: string;
          user_id: string;
          work_id: string | null;
          comment_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          work_id?: string | null;
          comment_id?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          work_id?: string | null;
          comment_id?: string | null;
          created_at?: string;
        };
      };
      follows: {
        Row: {
          id: string;
          follower_id: string;
          following_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          follower_id: string;
          following_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          follower_id?: string;
          following_id?: string;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      user_role: 'user' | 'admin' | 'moderator';
      work_type: 'article' | 'video' | 'audio' | 'image';
      work_status: 'draft' | 'published' | 'archived';
    };
  };
}

// JSON类型
export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

// 数据库行类型
export type Profile = Database['public']['Tables']['profiles']['Row'];
export type Work = Database['public']['Tables']['works']['Row'];
export type Category = Database['public']['Tables']['categories']['Row'];
export type Comment = Database['public']['Tables']['comments']['Row'];
export type Like = Database['public']['Tables']['likes']['Row'];
export type Follow = Database['public']['Tables']['follows']['Row'];

// 插入类型
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type WorkInsert = Database['public']['Tables']['works']['Insert'];
export type CategoryInsert = Database['public']['Tables']['categories']['Insert'];
export type CommentInsert = Database['public']['Tables']['comments']['Insert'];
export type LikeInsert = Database['public']['Tables']['likes']['Insert'];
export type FollowInsert = Database['public']['Tables']['follows']['Insert'];

// 更新类型
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];
export type WorkUpdate = Database['public']['Tables']['works']['Update'];
export type CategoryUpdate = Database['public']['Tables']['categories']['Update'];
export type CommentUpdate = Database['public']['Tables']['comments']['Update'];
export type LikeUpdate = Database['public']['Tables']['likes']['Update'];
export type FollowUpdate = Database['public']['Tables']['follows']['Update'];

// 枚举类型
export type UserRole = Database['public']['Enums']['user_role'];
export type WorkType = Database['public']['Enums']['work_type'];
export type WorkStatus = Database['public']['Enums']['work_status'];
