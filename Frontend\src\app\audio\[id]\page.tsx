'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Header from '@/components/Header';
import AudioPlayer from '@/components/AudioPlayer';
import CommentSection from '@/components/CommentSection';
import SocialShare from '@/components/SocialShare';
import TipModal from '@/components/TipModal';
import ReportModal from '@/components/ReportModal';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useNotifications } from '@/contexts/NotificationContext';
import { Audio, Comment } from '@/types';
import { mockWorks } from '@/data/mockWorks';

export default function AudioPage() {
  const params = useParams();
  const router = useRouter();
  const { isAuthenticated } = useSimpleAuth();
  const { addNotification } = useNotifications();
  const [audio, setAudio] = useState<Audio | null>(null);
  const [loading, setLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showTipModal, setShowTipModal] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [relatedAudios, setRelatedAudios] = useState<Audio[]>([]);
  const [isFollowing, setIsFollowing] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchAudio(params.id as string);
      fetchRelatedAudios(params.id as string);
    }
  }, [params.id]);

  const fetchAudio = async (id: string) => {
    try {
      setLoading(true);

      // 从mockWorks中获取音频数据
      const mockAudio = mockWorks.find((work) => 
        work.id.toString() === id && work.type === 'audio'
      ) as Audio;

      if (mockAudio) {
        setAudio(mockAudio);
        setDuration(mockAudio.duration);
        setLoading(false);
        return;
      }

      // 如果没有找到，显示404
      console.error('Audio not found');
      setAudio(null);
    } catch (error) {
      console.error('Error fetching audio:', error);
      setAudio(null);
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedAudios = async (id: string) => {
    try {
      // 获取相关音频（同类型的其他音频）
      const related = mockWorks
        .filter((work) => work.type === 'audio' && work.id.toString() !== id)
        .slice(0, 4) as Audio[];
      setRelatedAudios(related);
    } catch (error) {
      console.error('Error fetching related audios:', error);
    }
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
    // TODO: 实际的音频播放控制
  };

  const handleLike = () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    const newLikedState = !isLiked;
    setIsLiked(newLikedState);

    // 添加通知
    addNotification({
      type: 'success',
      title: newLikedState ? 'Audio Liked' : 'Like Removed',
      message: newLikedState ? `You liked "${audio?.title}"` : `You removed your like from "${audio?.title}"`
    });
  };

  const handleSave = () => {
    if (!isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    const newSavedState = !isSaved;
    setIsSaved(newSavedState);

    // 添加通知
    addNotification({
      type: 'info',
      title: newSavedState ? 'Audio Saved' : 'Audio Removed',
      message: newSavedState ? `"${audio?.title}" saved to your bookmarks` : `"${audio?.title}" removed from bookmarks`
    });
  };



  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="aspect-square bg-gray-200 rounded-lg mb-6 max-w-md mx-auto"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4 mb-4 mx-auto"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-6 mx-auto"></div>
            <div className="flex gap-4 mb-6 justify-center">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1 max-w-xs">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!audio) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-6xl mx-auto px-4 py-8 text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Audio Not Found</h1>
          <p className="text-gray-600 mb-6">The audio you're looking for doesn't exist.</p>
          <button
            onClick={() => router.back()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      {/* Breadcrumb Navigation */}
      <nav className="max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 py-6">
        <div className="flex items-center space-x-2 text-sm text-gray-500">
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Home
          </button>
          <span>/</span>
          <button
            onClick={() => router.push('/')}
            className="hover:text-blue-600 transition-colors"
          >
            Audio
          </button>
          <span>/</span>
          <span className="text-gray-900 font-medium">
            {audio?.title
              ? audio.title.length > 50
                ? audio.title.substring(0, 50) + '...'
                : audio.title
              : 'Loading...'}
          </span>
        </div>
      </nav>
      
      <div className="flex max-w-8xl mx-auto px-6 sm:px-8 lg:px-12 pb-12 gap-12">
        <div className="flex-1 max-w-4xl">
          {/* Main Content */}
          <div className="w-full">
            {/* Audio Player */}
            <div className="mb-6">
              <AudioPlayer
                src={audio.audioUrl}
                title={audio.title}
                artist={typeof audio.author === 'object' ? audio.author.name : audio.author}
                cover={audio.coverUrl}
                className="w-full"
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onTimeUpdate={(current, total) => {
                  setCurrentTime(current);
                  setDuration(total);
                }}
              />
            </div>

            {/* Audio Title */}
            <h1 className="text-3xl font-bold text-gray-900 mb-6 leading-tight">{audio.title}</h1>

            {/* Author Info */}
            {typeof audio.author === 'object' && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <img
                      src={audio.author.avatar}
                      alt={audio.author.name}
                      className="w-12 h-12 rounded-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(audio.author.name)}&background=6366f1&color=fff&size=48`;
                      }}
                    />
                    <div>
                      <h3 className="font-semibold text-gray-900">{audio.author.name}</h3>
                      <p className="text-sm text-gray-500">
                        Published {audio.publishedAt} • {Math.floor(audio.duration / 60)}:{(audio.duration % 60).toString().padStart(2, '0')} min
                      </p>
                    </div>
                  </div>
                  {isAuthenticated && (
                    <button 
                      onClick={() => {
                        const newFollowingState = !isFollowing;
                        setIsFollowing(newFollowingState);
                        addNotification({
                          type: 'success',
                          title: newFollowingState ? 'Following' : 'Unfollowed',
                          message: newFollowingState 
                            ? `You are now following ${typeof audio.author === 'object' ? audio.author.name : 'this creator'}`
                            : `You unfollowed ${typeof audio.author === 'object' ? audio.author.name : 'this creator'}`
                        });
                      }}
                      className={`px-6 py-2 rounded-lg transition-colors font-medium ${
                        isFollowing
                          ? 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
                          : 'bg-blue-600 text-white hover:bg-blue-700'
                      }`}
                    >
                      {isFollowing ? 'Following' : 'Follow'}
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Audio Info */}
            <div className="bg-white rounded-lg p-6 mb-6">
              {/* Interaction Stats */}
              <div className="mb-8 flex items-center justify-between">
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <span>👁️ {audio.views.toLocaleString()} plays</span>
                  <span>💬 Comments</span>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleLike}
                    className={`group flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 ${
                      isLiked
                        ? 'bg-red-50 border-red-200 text-red-600 hover:bg-red-100'
                        : 'bg-white border-gray-200 text-gray-600 hover:border-red-200 hover:text-red-600'
                    }`}
                  >
                    <svg className="w-4 h-4" fill={isLiked ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                    <span className="text-sm font-medium">{audio.likes.toLocaleString()}</span>
                  </button>

                  <button
                    onClick={handleSave}
                    className={`group flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 hover:scale-105 ${
                      isSaved
                        ? 'bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100'
                        : 'bg-white border-gray-200 text-gray-600 hover:border-blue-200 hover:text-blue-600'
                    }`}
                  >
                    <svg className="w-4 h-4" fill={isSaved ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                    <span className="text-sm font-medium">{isSaved ? 'Saved' : 'Save'}</span>
                  </button>

                  <SocialShare
                    title={audio.title}
                    url={typeof window !== 'undefined' ? window.location.href : ''}
                    description={audio.description}
                  />
                  
                  <button
                    onClick={() => setShowTipModal(true)}
                    className="group flex items-center space-x-2 px-4 py-2 rounded-full border border-amber-200 bg-amber-50 text-amber-600 hover:bg-amber-100 hover:border-amber-300 transition-all duration-200 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium">Tip</span>
                  </button>
                  
                  <button
                    onClick={() => setShowReportModal(true)}
                    className="group flex items-center space-x-2 px-4 py-2 rounded-full border border-gray-200 bg-white text-gray-500 hover:border-red-200 hover:text-red-500 transition-all duration-200 hover:scale-105"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span className="text-sm font-medium">Report</span>
                  </button>
                </div>
              </div>

              {/* Description */}
              {audio.description && (
                <div className="border-t pt-4">
                  <p className="text-gray-700 leading-relaxed" translate="yes" lang="en">{audio.description}</p>
                </div>
              )}
            </div>

            {/* Comments */}
            <CommentSection workId={audio.id} workType="audio" />
          </div>

        </div>
        
        {/* Right Sidebar */}
        <div className="w-80 hidden xl:block">
          <div className="sticky top-24">
            <div className="bg-white rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Audio</h3>
              <div className="space-y-4">
                {relatedAudios.map((relatedAudio) => (
                  <div
                    key={relatedAudio.id}
                    onClick={() => router.push(`/audio/${relatedAudio.id}`)}
                    className="flex gap-3 cursor-pointer hover:bg-gray-50 p-2 rounded-lg transition-colors"
                  >
                    <img
                      src={relatedAudio.coverUrl}
                      alt={relatedAudio.title}
                      className="w-16 h-16 object-cover rounded"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(relatedAudio.title)}&background=6366f1&color=fff&size=64`;
                      }}
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 line-clamp-2 mb-1">
                        {relatedAudio.title}
                      </h4>
                      <p className="text-xs text-gray-600">
                        {typeof relatedAudio.author === 'object' && relatedAudio.author.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {relatedAudio.views.toLocaleString()} plays • {formatTime(relatedAudio.duration)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Tip Modal */}
      <TipModal
        isOpen={showTipModal}
        onClose={() => setShowTipModal(false)}
        authorName={typeof audio.author === 'object' ? audio.author.name : 'Unknown'}
        contentTitle={audio.title}
      />
      
      {/* Report Modal */}
      <ReportModal
        isOpen={showReportModal}
        onClose={() => setShowReportModal(false)}
        contentId={audio.id}
        contentType="audio"
        contentTitle={audio.title}
      />
    </div>
  );
}
