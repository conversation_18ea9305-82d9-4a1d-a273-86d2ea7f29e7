const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Comment = sequelize.define(
  'Comment',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'articles',
        key: 'id',
      },
    },
    authorName: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 100],
      },
    },
    authorId: {
      type: DataTypes.INTEGER,
      allowNull: true, // Allow null for legacy comments
      references: {
        model: 'users',
        key: 'id',
      },
    },
    avatar: {
      type: DataTypes.STRING,
      defaultValue:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [1, 1000],
      },
    },
    likes: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'comments',
        key: 'id',
      },
    },
  },
  {
    tableName: 'comments',
    timestamps: true,
    indexes: [
      {
        fields: ['articleId'],
      },
      {
        fields: ['parentId'],
      },
      {
        fields: ['createdAt'],
      },
    ],
  }
);

// Associations are defined in models/index.js

module.exports = Comment;
