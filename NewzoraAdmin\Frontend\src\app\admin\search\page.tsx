
'use client';

import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Search, FileText, Users, MessageSquare, AlertTriangle } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';

interface SearchResult {
  id: string;
  type: 'user' | 'article' | 'comment' | 'report';
  title: string;
  description?: string;
  date: string;
  url: string;
}

const SearchResultsPage: React.FC = () => {
  const searchParams = useSearchParams();
  const query = searchParams.get('q') || '';
  const { t } = useLanguage();
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchSearchResults = async () => {
      if (!query) return;

      setLoading(true);
      try {
        // Simulate API call
        // In a real application, this would be an actual API call
        setTimeout(() => {
          const mockResults: SearchResult[] = [
            {
              id: '1',
              type: 'article',
              title: `${query} - Article Example`,
              description: 'This is a sample article that matches your search query.',
              date: '2023-10-15',
              url: '/admin/content/articles/1'
            },
            {
              id: '2',
              type: 'user',
              title: `${query} - User Example`,
              description: 'This is a sample user that matches your search query.',
              date: '2023-10-10',
              url: '/admin/users/2'
            },
            {
              id: '3',
              type: 'comment',
              title: `${query} - Comment Example`,
              description: 'This is a sample comment that matches your search query.',
              date: '2023-10-05',
              url: '/admin/content/comments/3'
            },
            {
              id: '4',
              type: 'report',
              title: `${query} - Report Example`,
              description: 'This is a sample report that matches your search query.',
              date: '2023-10-01',
              url: '/admin/content/reports/4'
            }
          ];
          setResults(mockResults);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching search results:', error);
        setLoading(false);
      }
    };

    fetchSearchResults();
  }, [query]);

  const getIcon = (type: string) => {
    switch (type) {
      case 'user':
        return <Users className="w-5 h-5 text-blue-500" />;
      case 'article':
        return <FileText className="w-5 h-5 text-green-500" />;
      case 'comment':
        return <MessageSquare className="w-5 h-5 text-yellow-500" />;
      case 'report':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <Search className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'user':
        return 'User';
      case 'article':
        return 'Article';
      case 'comment':
        return 'Comment';
      case 'report':
        return 'Report';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Search Results</h1>
          <p className="text-gray-600">
            Showing results for: <span className="font-semibold">"{query}"</span>
          </p>
        </div>

        {loading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : results.length === 0 ? (
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <Search className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-500">
              We couldn't find any results matching your search query. Try different keywords.
            </p>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <ul className="divide-y divide-gray-200">
              {results.map((result) => (
                <li key={result.id} className="p-4 hover:bg-gray-50 transition-colors duration-150">
                  <a href={result.url} className="block">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        {getIcon(result.type)}
                      </div>
                      <div className="ml-4 flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600">
                            {result.title}
                          </h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {getTypeLabel(result.type)}
                          </span>
                        </div>
                        {result.description && (
                          <p className="mt-1 text-gray-600">{result.description}</p>
                        )}
                        <div className="mt-2 text-sm text-gray-500">
                          {result.date}
                        </div>
                      </div>
                    </div>
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchResultsPage;
