// 关注系统服务

export interface FollowRelation {
  id: string;
  followerId: string;
  followingId: string;
  createdAt: Date;
  followerName: string;
  followingName: string;
  followerAvatar?: string;
  followingAvatar?: string;
}

export interface UserStats {
  userId: string;
  followersCount: number;
  followingCount: number;
  isFollowing: boolean;
  isFollowedBy: boolean;
}

export class FollowService {
  private followRelations: Map<string, FollowRelation[]> = new Map();
  private userStats: Map<string, UserStats> = new Map();

  // 关注用户
  async followUser(followerId: string, followingId: string, followerName: string, followingName: string): Promise<{ success: boolean; error?: string }> {
    if (followerId === followingId) {
      return { success: false, error: 'Cannot follow yourself' };
    }

    const relationId = `${followerId}-${followingId}`;
    
    // 检查是否已经关注
    if (this.isFollowing(followerId, followingId)) {
      return { success: false, error: 'Already following this user' };
    }

    const relation: FollowRelation = {
      id: relationId,
      followerId,
      followingId,
      createdAt: new Date(),
      followerName,
      followingName
    };

    // 添加关注关系
    if (!this.followRelations.has(followerId)) {
      this.followRelations.set(followerId, []);
    }
    this.followRelations.get(followerId)!.push(relation);

    // 更新统计数据
    this.updateUserStats(followerId);
    this.updateUserStats(followingId);

    return { success: true };
  }

  // 取消关注
  async unfollowUser(followerId: string, followingId: string): Promise<{ success: boolean; error?: string }> {
    const userRelations = this.followRelations.get(followerId) || [];
    const relationIndex = userRelations.findIndex(r => r.followingId === followingId);

    if (relationIndex === -1) {
      return { success: false, error: 'Not following this user' };
    }

    // 移除关注关系
    userRelations.splice(relationIndex, 1);

    // 更新统计数据
    this.updateUserStats(followerId);
    this.updateUserStats(followingId);

    return { success: true };
  }

  // 检查是否关注
  isFollowing(followerId: string, followingId: string): boolean {
    const userRelations = this.followRelations.get(followerId) || [];
    return userRelations.some(r => r.followingId === followingId);
  }

  // 获取关注列表
  getFollowing(userId: string): FollowRelation[] {
    return this.followRelations.get(userId) || [];
  }

  // 获取粉丝列表
  getFollowers(userId: string): FollowRelation[] {
    const followers: FollowRelation[] = [];
    
    for (const [followerId, relations] of this.followRelations.entries()) {
      const relation = relations.find(r => r.followingId === userId);
      if (relation) {
        followers.push(relation);
      }
    }
    
    return followers;
  }

  // 获取用户统计数据
  getUserStats(userId: string, currentUserId?: string): UserStats {
    const following = this.getFollowing(userId);
    const followers = this.getFollowers(userId);
    
    let isFollowing = false;
    let isFollowedBy = false;
    
    if (currentUserId && currentUserId !== userId) {
      isFollowing = this.isFollowing(currentUserId, userId);
      isFollowedBy = this.isFollowing(userId, currentUserId);
    }

    const stats: UserStats = {
      userId,
      followersCount: followers.length,
      followingCount: following.length,
      isFollowing,
      isFollowedBy
    };

    this.userStats.set(userId, stats);
    return stats;
  }

  // 更新用户统计数据
  private updateUserStats(userId: string) {
    const stats = this.getUserStats(userId);
    this.userStats.set(userId, stats);
  }

  // 获取推荐关注用户
  getRecommendedUsers(currentUserId: string, limit: number = 5): Array<{
    userId: string;
    name: string;
    avatar?: string;
    mutualFollowers: number;
    reason: string;
  }> {
    // 模拟推荐用户数据
    const recommendedUsers = [
      {
        userId: 'user1',
        name: 'Tech Enthusiast',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 5,
        reason: 'Popular in Technology'
      },
      {
        userId: 'user2',
        name: 'Creative Writer',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 3,
        reason: 'Trending Author'
      },
      {
        userId: 'user3',
        name: 'Digital Artist',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 8,
        reason: 'Similar Interests'
      },
      {
        userId: 'user4',
        name: 'Science Blogger',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 2,
        reason: 'New Creator'
      },
      {
        userId: 'user5',
        name: 'Travel Vlogger',
        avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
        mutualFollowers: 6,
        reason: 'Recommended for You'
      }
    ];

    return recommendedUsers
      .filter(user => user.userId !== currentUserId)
      .filter(user => !this.isFollowing(currentUserId, user.userId))
      .slice(0, limit);
  }

  // 获取活动动态
  getFollowingActivity(userId: string, limit: number = 10): Array<{
    id: string;
    type: 'follow' | 'like' | 'comment' | 'publish';
    userId: string;
    userName: string;
    userAvatar?: string;
    targetId?: string;
    targetTitle?: string;
    timestamp: Date;
    description: string;
  }> {
    const following = this.getFollowing(userId);
    const activities = [];

    // 模拟活动数据
    for (let i = 0; i < Math.min(limit, following.length * 2); i++) {
      const randomFollowing = following[Math.floor(Math.random() * following.length)];
      if (!randomFollowing) continue;

      const activityTypes = ['follow', 'like', 'comment', 'publish'] as const;
      const type = activityTypes[Math.floor(Math.random() * activityTypes.length)];
      
      const activity = {
        id: `activity-${i}`,
        type,
        userId: randomFollowing.followingId,
        userName: randomFollowing.followingName,
        userAvatar: randomFollowing.followingAvatar,
        targetId: `content-${i}`,
        targetTitle: `Sample Content ${i}`,
        timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000), // 过去7天内
        description: this.getActivityDescription(type, randomFollowing.followingName)
      };

      activities.push(activity);
    }

    return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  private getActivityDescription(type: string, userName: string): string {
    const descriptions = {
      follow: `${userName} followed a new creator`,
      like: `${userName} liked a post`,
      comment: `${userName} commented on a post`,
      publish: `${userName} published new content`
    };
    return descriptions[type as keyof typeof descriptions] || `${userName} was active`;
  }

  // 批量关注（用于模拟数据）
  simulateFollowData(userId: string) {
    const users = [
      { id: 'user1', name: 'Alice Johnson' },
      { id: 'user2', name: 'Bob Smith' },
      { id: 'user3', name: 'Carol Davis' },
      { id: 'user4', name: 'David Wilson' },
      { id: 'user5', name: 'Eva Brown' }
    ];

    // 模拟一些关注关系
    users.forEach((user, index) => {
      if (index < 3) { // 关注前3个用户
        this.followUser(userId, user.id, 'Current User', user.name);
      }
      if (index > 1) { // 被后3个用户关注
        this.followUser(user.id, userId, user.name, 'Current User');
      }
    });
  }
}

// 创建全局关注服务实例
export const followService = new FollowService();
