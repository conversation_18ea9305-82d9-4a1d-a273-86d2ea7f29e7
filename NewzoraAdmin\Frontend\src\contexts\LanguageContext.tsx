import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { adminLocales } from '@/config/locales/admin-locales';

type LanguageContextType = {
  language: string;
  t: (key: string) => string;
  setLanguage: (lang: string) => void;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [language, setLanguage] = useState('en');

  useEffect(() => {
    // Set language to English by default
    setLanguage('en');
  }, []);

  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = adminLocales[language as keyof typeof adminLocales];
    
    for (const k of keys) {
      if (value && k in value) {
        value = value[k];
      } else {
        console.warn(`Translation key not found: ${key}`);
        return key;
      }
    }
    
    return value as string;
  };

  return (
    <LanguageContext.Provider value={{ language, t, setLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}
