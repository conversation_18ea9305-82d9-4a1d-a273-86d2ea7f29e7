const express = require('express');
const router = express.Router();
const { body, validationResult, query } = require('express-validator');
const { authenticateToken, requireAdmin } = require('../middleware/auth');
const { ContentReview, Article, Comment, User } = require('../models');
const { Op } = require('sequelize');

// 提交举报
router.post(
  '/',
  authenticateToken,
  [
    body('contentType').isIn(['article', 'comment', 'user']),
    body('contentId').isInt({ min: 1 }),
    body('reason').isIn([
      'spam', 'harassment', 'hate_speech', 'violence', 
      'adult_content', 'copyright', 'misinformation', 'other'
    ]),
    body('description').optional().isLength({ max: 500 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { contentType, contentId, reason, description } = req.body;

      // 检查是否已经举报过相同内容
      const existingReport = await ContentReview.findOne({
        where: {
          contentType,
          contentId,
          submitterId: req.user.id,
          createdAt: {
            [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // 24小时内
          }
        }
      });

      if (existingReport) {
        return res.status(400).json({
          success: false,
          message: '您已经举报过此内容，请等待处理结果'
        });
      }

      // 验证被举报内容是否存在
      let targetContent = null;
      let contentTitle = '';

      switch (contentType) {
        case 'article':
          targetContent = await Article.findByPk(contentId);
          if (targetContent) {
            contentTitle = targetContent.title;
          }
          break;
        case 'comment':
          targetContent = await Comment.findByPk(contentId);
          if (targetContent) {
            contentTitle = targetContent.content.substring(0, 50) + '...';
          }
          break;
        case 'user':
          targetContent = await User.findByPk(contentId);
          if (targetContent) {
            contentTitle = `用户: ${targetContent.username}`;
          }
          break;
      }

      if (!targetContent) {
        return res.status(404).json({
          success: false,
          message: '举报的内容不存在'
        });
      }

      // 不能举报自己的内容
      if (contentType !== 'user' && targetContent.authorId === req.user.id) {
        return res.status(400).json({
          success: false,
          message: '不能举报自己的内容'
        });
      }

      // 不能举报自己
      if (contentType === 'user' && contentId === req.user.id) {
        return res.status(400).json({
          success: false,
          message: '不能举报自己'
        });
      }

      // 根据举报原因确定优先级和风险等级
      let priority = 'normal';
      let riskLevel = 'medium';

      const highPriorityReasons = ['harassment', 'hate_speech', 'violence'];
      const criticalReasons = ['violence', 'hate_speech'];

      if (criticalReasons.includes(reason)) {
        priority = 'urgent';
        riskLevel = 'critical';
      } else if (highPriorityReasons.includes(reason)) {
        priority = 'high';
        riskLevel = 'high';
      }

      // 创建举报记录
      const report = await ContentReview.create({
        contentType,
        contentId,
        submitterId: req.user.id,
        status: 'pending',
        priority,
        riskLevel,
        reportReason: reason,
        reportDescription: description || null,
        aiFlags: [reason], // 将举报原因作为AI标记
        autoApproved: false
      });

      res.status(201).json({
        success: true,
        data: { report },
        message: '举报已提交，我们会尽快处理'
      });

    } catch (error) {
      console.error('Error submitting report:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// 获取我的举报历史
router.get(
  '/my-reports',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 50 }),
    query('status').optional().isIn(['pending', 'in_review', 'resolved', 'rejected'])
  ],
  async (req, res) => {
    try {
      const { page = 1, limit = 20, status } = req.query;

      const whereClause = {
        submitterId: req.user.id,
        reportReason: { [Op.not]: null } // 只获取举报记录
      };

      if (status) {
        whereClause.status = status;
      }

      const { count, rows: reports } = await ContentReview.findAndCountAll({
        where: whereClause,
        order: [['createdAt', 'DESC']],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        include: [
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username'],
            required: false
          }
        ]
      });

      res.json({
        success: true,
        data: {
          reports,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / parseInt(limit))
          }
        }
      });

    } catch (error) {
      console.error('Error fetching user reports:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// 管理员获取举报列表
router.get(
  '/admin/reports',
  authenticateToken,
  requireAdmin,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status').optional().isIn(['pending', 'in_review', 'resolved', 'rejected']),
    query('reason').optional().isIn([
      'spam', 'harassment', 'hate_speech', 'violence', 
      'adult_content', 'copyright', 'misinformation', 'other'
    ]),
    query('priority').optional().isIn(['low', 'normal', 'high', 'urgent'])
  ],
  async (req, res) => {
    try {
      const { page = 1, limit = 20, status, reason, priority } = req.query;

      const whereClause = {
        reportReason: { [Op.not]: null } // 只获取举报记录
      };

      if (status) whereClause.status = status;
      if (reason) whereClause.reportReason = reason;
      if (priority) whereClause.priority = priority;

      const { count, rows: reports } = await ContentReview.findAndCountAll({
        where: whereClause,
        order: [
          ['priority', 'DESC'],
          ['createdAt', 'ASC']
        ],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        include: [
          {
            model: User,
            as: 'submitter',
            attributes: ['id', 'username', 'email', 'avatar']
          },
          {
            model: User,
            as: 'reviewer',
            attributes: ['id', 'username', 'email'],
            required: false
          }
        ]
      });

      res.json({
        success: true,
        data: {
          reports,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / parseInt(limit))
          }
        }
      });

    } catch (error) {
      console.error('Error fetching admin reports:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// 处理举报
router.post(
  '/:id/resolve',
  authenticateToken,
  requireAdmin,
  [
    body('action').isIn(['approve_report', 'reject_report', 'remove_content']),
    body('notes').optional().isLength({ max: 1000 })
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const { action, notes } = req.body;

      const report = await ContentReview.findByPk(id);
      if (!report || !report.reportReason) {
        return res.status(404).json({
          success: false,
          message: '举报记录不存在'
        });
      }

      // 更新举报状态
      let status = 'resolved';
      let reviewNotes = notes || '';

      switch (action) {
        case 'approve_report':
          // 举报成立，但不删除内容
          reviewNotes = `举报成立: ${reviewNotes}`;
          break;
        case 'reject_report':
          // 举报不成立
          status = 'rejected';
          reviewNotes = `举报不成立: ${reviewNotes}`;
          break;
        case 'remove_content':
          // 举报成立，删除内容
          reviewNotes = `举报成立，内容已删除: ${reviewNotes}`;
          // TODO: 实际删除或隐藏内容的逻辑
          break;
      }

      await report.update({
        status,
        reviewerId: req.user.id,
        reviewNotes,
        reviewCompletedAt: new Date()
      });

      res.json({
        success: true,
        data: { report },
        message: '举报处理完成'
      });

    } catch (error) {
      console.error('Error resolving report:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

// 获取举报统计
router.get(
  '/admin/stats',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      // 按状态统计
      const statusStats = await ContentReview.findAll({
        where: {
          reportReason: { [Op.not]: null }
        },
        attributes: [
          'status',
          [ContentReview.sequelize.fn('COUNT', '*'), 'count']
        ],
        group: ['status'],
        raw: true
      });

      // 按举报原因统计
      const reasonStats = await ContentReview.findAll({
        where: {
          reportReason: { [Op.not]: null }
        },
        attributes: [
          'reportReason',
          [ContentReview.sequelize.fn('COUNT', '*'), 'count']
        ],
        group: ['reportReason'],
        raw: true
      });

      // 按内容类型统计
      const typeStats = await ContentReview.findAll({
        where: {
          reportReason: { [Op.not]: null }
        },
        attributes: [
          'contentType',
          [ContentReview.sequelize.fn('COUNT', '*'), 'count']
        ],
        group: ['contentType'],
        raw: true
      });

      res.json({
        success: true,
        data: {
          statusStats,
          reasonStats,
          typeStats
        }
      });

    } catch (error) {
      console.error('Error fetching report stats:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
);

module.exports = router;