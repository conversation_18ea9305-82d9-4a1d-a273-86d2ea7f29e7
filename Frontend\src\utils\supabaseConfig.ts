// Supabase配置检查和调试工具

export const SUPABASE_CONFIG = {
  url: 'https://wdpprzeflzlardkmncfk.supabase.co',
  anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndkcHByemVmbHpsYXJka21uY2ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDk4MjgsImV4cCI6MjA2NzY4NTgyOH0.yp_k9Sv6AMFZmUs_EWa_-rPZGyTxNNFTZOM4RaU668s',
  projectRef: 'wdpprzeflzlardkmncfk'
};

// 检查Supabase配置是否正确
export const validateSupabaseConfig = () => {
  const errors: string[] = [];
  
  if (!SUPABASE_CONFIG.url) {
    errors.push('Supabase URL is missing');
  }
  
  if (!SUPABASE_CONFIG.anonKey) {
    errors.push('Supabase anon key is missing');
  }
  
  if (!SUPABASE_CONFIG.url.includes(SUPABASE_CONFIG.projectRef)) {
    errors.push('Project reference does not match URL');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// 获取Supabase认证状态
export const getSupabaseAuthStatus = () => {
  if (typeof window === 'undefined') return null;
  
  const cookies = document.cookie;
  const authCookies = cookies
    .split(';')
    .filter(cookie => cookie.includes('sb-') && cookie.includes('auth-token'))
    .map(cookie => cookie.trim());
    
  return {
    hasCookies: authCookies.length > 0,
    cookies: authCookies,
    localStorage: {
      hasSession: !!localStorage.getItem('sb-wdpprzemflzlardkmnfk-auth-token'),
      sessionData: localStorage.getItem('sb-wdpprzemflzlardkmnfk-auth-token')
    }
  };
};

// 清理所有认证状态
export const clearAllAuthState = () => {
  if (typeof window === 'undefined') return;
  
  // 清理localStorage
  const keys = Object.keys(localStorage);
  keys.forEach(key => {
    if (key.includes('auth') || key.includes('sb-')) {
      localStorage.removeItem(key);
    }
  });
  
  // 清理cookies (通过设置过期时间)
  const cookies = document.cookie.split(';');
  cookies.forEach(cookie => {
    const eqPos = cookie.indexOf('=');
    const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
    if (name.includes('auth') || name.includes('sb-')) {
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    }
  });
  
  console.log('🧹 All auth state cleared');
};

// 调试信息收集
export const collectDebugInfo = () => {
  const config = validateSupabaseConfig();
  const authStatus = getSupabaseAuthStatus();
  
  return {
    timestamp: new Date().toISOString(),
    config,
    authStatus,
    environment: {
      isDevelopment: process.env.NODE_ENV === 'development',
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'SSR',
      url: typeof window !== 'undefined' ? window.location.href : 'SSR'
    }
  };
};

// Test account configuration removed - using real Supabase authentication

// Error code mapping
export const SUPABASE_ERROR_CODES = {
  'Invalid login credentials': 'Invalid login credentials, please check your email and password',
  'Email not confirmed': 'Email not verified, please check your email and click the verification link',
  'User already registered': 'User already exists, please use a different email or try logging in',
  'Password should be at least 6 characters': 'Password must be at least 6 characters',
  'Unable to validate email address: invalid format': 'Invalid email format',
  'signup_disabled': 'Registration is disabled',
  'email_address_invalid': 'Invalid email address',
  'password_too_short': 'Password is too short',
  'weak_password': 'Password is not strong enough'
};

// Get friendly error message
export const getFriendlyErrorMessage = (error: string): string => {
  return SUPABASE_ERROR_CODES[error as keyof typeof SUPABASE_ERROR_CODES] || error;
};

// Check if in development mode
export const isDevelopmentMode = () => {
  return process.env.NODE_ENV === 'development';
};

// 日志工具
export const supabaseLogger = {
  info: (message: string, data?: any) => {
    if (isDevelopmentMode()) {
      console.log(`🔵 Supabase: ${message}`, data || '');
    }
  },
  
  success: (message: string, data?: any) => {
    if (isDevelopmentMode()) {
      console.log(`✅ Supabase: ${message}`, data || '');
    }
  },
  
  warning: (message: string, data?: any) => {
    if (isDevelopmentMode()) {
      console.warn(`⚠️ Supabase: ${message}`, data || '');
    }
  },
  
  error: (message: string, error?: any) => {
    if (isDevelopmentMode()) {
      console.error(`❌ Supabase: ${message}`, error || '');
    }
  }
};
