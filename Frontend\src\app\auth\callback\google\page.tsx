'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { supabase } from '@/lib/supabase';
import AuthLayout from '@/components/AuthLayout';
import BrandName from '@/components/BrandName';

export default function GoogleCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState('');

  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated } = useSimpleAuth();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const code = searchParams.get('code');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          setStatus('error');
          setErrorMessage(errorDescription || 'Google login was cancelled or failed');
          return;
        }

        if (code) {
          // 使用Supabase处理OAuth回调
          const { data, error: exchangeError } = await supabase.auth.exchangeCodeForSession(code);

          if (exchangeError) {
            setStatus('error');
            setErrorMessage(exchangeError.message || 'Failed to complete Google login');
            return;
          }

          if (data.session && data.user) {
            setStatus('success');
            // 延迟跳转让用户看到成功消息
            setTimeout(() => {
              router.push('/');
            }, 2000);
            return;
          }
        }

        // 检查是否已经认证
        if (isAuthenticated) {
          setStatus('success');
          setTimeout(() => {
            router.push('/');
          }, 1000);
          return;
        }

        // 如果没有code且未认证，显示错误
        setStatus('error');
        setErrorMessage('Authorization code not received');

      } catch (error: any) {
        console.error('Google OAuth callback error:', error);
        setStatus('error');
        setErrorMessage(error.message || 'An unexpected error occurred during login');
      }
    };

    handleCallback();
  }, [searchParams, router, isAuthenticated]);

  if (status === 'loading') {
    return (
      <AuthLayout title="Google Login in Progress..." subtitle="Completing your login">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
          <p className="text-sm text-gray-600">Completing Google login, please wait...</p>
        </div>
      </AuthLayout>
    );
  }

  if (status === 'success') {
    return (
      <AuthLayout title="Login Successful" subtitle={<>Welcome to <BrandName /></>}>
        <div className="text-center space-y-4">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <svg
              className="h-6 w-6 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>

          <div className="space-y-2">
            <p className="text-sm text-gray-600">You have successfully logged in with Google!</p>
            <p className="text-sm text-gray-600">Redirecting to homepage...</p>
          </div>
        </div>
      </AuthLayout>
    );
  }

  // Error state
  return (
    <AuthLayout
      title="Google Login Failed"
      subtitle="An issue occurred during login"
      showBackButton
      backHref="/auth/login"
    >
      <div className="text-center space-y-4">
        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
          <svg
            className="h-6 w-6 text-red-600"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-gray-600">
            {errorMessage || 'Google login failed, please try again.'}
          </p>
        </div>

        <div className="space-y-3 pt-4">
          <button
            onClick={() => router.push('/auth/login')}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Try Again
          </button>
          <button
            onClick={() => router.push('/')}
            className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Back to Home
          </button>
        </div>
      </div>
    </AuthLayout>
  );
}
