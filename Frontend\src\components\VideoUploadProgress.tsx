'use client';

import React, { useState, useRef, FormEvent } from 'react';
import { toast } from 'react-hot-toast';

interface VideoUploadProgressProps {
  onUploadComplete: (videoData: any) => void;
  onCancel: () => void;
}

interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  stage: 'uploading' | 'processing' | 'generating_thumbnail' | 'completed' | 'error';
  message: string;
}

const VideoUploadProgress = ({ onUploadComplete, onCancel }: VideoUploadProgressProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [progress, setProgress] = useState<UploadProgress | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [thumbnailUrl, setThumbnailUrl] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 处理文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('video/')) {
      toast.error('请选择视频文件');
      return;
    }

    // 验证文件大小 (最大500MB)
    const maxSize = 500 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('视频文件不能超过500MB');
      return;
    }

    setSelectedFile(file);
    
    // 创建预览URL
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };

  // 开始上传
  const startUpload = async () => {
    if (!selectedFile) return;

    try {
      // 创建AbortController用于取消上传
      abortControllerRef.current = new AbortController();

      const formData = new FormData();
      formData.append('video', selectedFile);
      formData.append('title', selectedFile.name.replace(/\.[^/.]+$/, ''));

      setProgress({
        loaded: 0,
        total: selectedFile.size,
        percentage: 0,
        stage: 'uploading',
        message: '正在上传视频...'
      });

      // 上传请求
      const response = await fetch('/api/media/upload-video', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
        },
        body: formData,
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error('上传失败');
      }

      // 处理上传进度
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法读取响应');
      }

      let receivedLength = 0;
      const chunks: Uint8Array[] = [];

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        chunks.push(value);
        receivedLength += value.length;
        
        // 更新上传进度
        const percentage = Math.round((receivedLength / selectedFile.size) * 100);
        setProgress(prev => prev ? {
          ...prev,
          loaded: receivedLength,
          percentage,
          message: `上传中... ${percentage}%`
        } : null);
      }

      // 解析响应
      const responseText = new TextDecoder().decode(new Uint8Array(chunks.reduce((acc, chunk) => [...acc, ...chunk], [] as number[])));
      const data = JSON.parse(responseText);

      if (data.success) {
        // 开始处理阶段
        setProgress(prev => prev ? {
          ...prev,
          stage: 'processing',
          percentage: 100,
          message: '正在处理视频...'
        } : null);

        // 轮询处理状态
        await pollProcessingStatus(data.data.videoId);
      } else {
        throw new Error(data.message || '上传失败');
      }

    } catch (error: any) {
      if (error.name === 'AbortError') {
        setProgress(null);
        toast.error('上传已取消');
      } else {
        console.error('上传错误:', error);
        setProgress(prev => prev ? {
          ...prev,
          stage: 'error',
          message: error.message || '上传失败'
        } : null);
        toast.error(error.message || '上传失败');
      }
    }
  };

  // 轮询处理状态
  const pollProcessingStatus = async (videoId: string) => {
    const maxAttempts = 60; // 最多轮询5分钟
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch(`/api/media/video-status/${videoId}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          }
        });

        const data = await response.json();
        
        if (data.success) {
          const status = data.data.status;
          
          switch (status) {
            case 'processing':
              setProgress(prev => prev ? {
                ...prev,
                stage: 'processing',
                message: '正在转码处理...'
              } : null);
              break;
              
            case 'generating_thumbnail':
              setProgress(prev => prev ? {
                ...prev,
                stage: 'generating_thumbnail',
                message: '正在生成缩略图...'
              } : null);
              break;
              
            case 'completed':
              setProgress(prev => prev ? {
                ...prev,
                stage: 'completed',
                message: '处理完成！'
              } : null);
              
              // 设置缩略图
              if (data.data.thumbnailUrl) {
                setThumbnailUrl(data.data.thumbnailUrl);
              }
              
              // 通知完成
              onUploadComplete(data.data);
              toast.success('视频上传成功！');
              return;
              
            case 'error':
              throw new Error(data.data.error || '处理失败');
          }
          
          // 继续轮询
          attempts++;
          if (attempts < maxAttempts) {
            setTimeout(poll, 5000); // 5秒后再次检查
          } else {
            throw new Error('处理超时');
          }
        } else {
          throw new Error(data.message || '获取状态失败');
        }
      } catch (error: any) {
        console.error('轮询状态错误:', error);
        setProgress(prev => prev ? {
          ...prev,
          stage: 'error',
          message: error.message || '处理失败'
        } : null);
        toast.error(error.message || '处理失败');
      }
    };

    poll();
  };

  // 取消上传
  const cancelUpload = () => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setProgress(null);
    setSelectedFile(null);
    setPreviewUrl('');
    setThumbnailUrl('');
    onCancel();
  };

  // 重新选择文件
  const resetUpload = () => {
    setProgress(null);
    setSelectedFile(null);
    setPreviewUrl('');
    setThumbnailUrl('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">视频上传</h3>
      
      {!selectedFile ? (
        // 文件选择界面
        <div>
          <div 
            onClick={() => fileInputRef.current?.click()}
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 cursor-pointer transition-colors"
          >
            <div className="text-4xl mb-4">🎥</div>
            <p className="text-gray-600 mb-2">点击选择视频文件</p>
            <p className="text-sm text-gray-500">支持 MP4, AVI, MOV 等格式，最大500MB</p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={handleFileSelect}
            className="hidden"
          />
        </div>
      ) : (
        // 上传进度界面
        <div className="space-y-4">
          {/* 视频预览 */}
          <div className="relative">
            {thumbnailUrl ? (
              <img 
                src={thumbnailUrl} 
                alt="视频缩略图" 
                className="w-full h-32 object-cover rounded-lg"
              />
            ) : previewUrl ? (
              <video 
                src={previewUrl} 
                className="w-full h-32 object-cover rounded-lg"
                controls={false}
                muted
              />
            ) : (
              <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                <span className="text-gray-500">视频预览</span>
              </div>
            )}
          </div>

          {/* 文件信息 */}
          <div className="text-sm text-gray-600">
            <p><strong>文件名:</strong> {selectedFile.name}</p>
            <p><strong>大小:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
            <p><strong>类型:</strong> {selectedFile.type}</p>
          </div>

          {/* 进度条 */}
          {progress && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">{progress.message}</span>
                <span className="text-gray-600">{progress.percentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full transition-all duration-300 ${
                    progress.stage === 'error' ? 'bg-red-500' : 
                    progress.stage === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${progress.percentage}%` }}
                ></div>
              </div>
              
              {/* 阶段指示器 */}
              <div className="flex justify-between text-xs text-gray-500">
                <span className={progress.stage === 'uploading' ? 'text-blue-600 font-medium' : ''}>
                  上传
                </span>
                <span className={progress.stage === 'processing' ? 'text-blue-600 font-medium' : ''}>
                  转码
                </span>
                <span className={progress.stage === 'generating_thumbnail' ? 'text-blue-600 font-medium' : ''}>
                  缩略图
                </span>
                <span className={progress.stage === 'completed' ? 'text-green-600 font-medium' : ''}>
                  完成
                </span>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-3">
            {!progress ? (
              <>
                <button
                  onClick={startUpload}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                  开始上传
                </button>
                <button
                  onClick={resetUpload}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  重选
                </button>
              </>
            ) : progress.stage === 'completed' ? (
              <button
                onClick={resetUpload}
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors"
              >
                上传更多
              </button>
            ) : progress.stage === 'error' ? (
              <>
                <button
                  onClick={startUpload}
                  className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                >
                  重试
                </button>
                <button
                  onClick={resetUpload}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  重选
                </button>
              </>
            ) : (
              <button
                onClick={cancelUpload}
                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors"
              >
                取消上传
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoUploadProgress;