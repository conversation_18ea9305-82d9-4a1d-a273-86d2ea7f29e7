@echo off
title Newzora Deployment
color 0A

echo ==========================================
echo    🚀 Newzora 完整部署脚本
echo ==========================================
echo.

echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    echo 💡 访问 https://nodejs.org/ 下载安装
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
node --version

echo.
echo 🔍 检查项目结构...
if not exist "Backend" (
    echo ❌ Backend 目录不存在
    pause
    exit /b 1
)

if not exist "Frontend" (
    echo ❌ Frontend 目录不存在
    pause
    exit /b 1
)

echo ✅ 项目结构完整

echo.
echo 📦 检查并更新依赖...

:: 检查根目录依赖
if not exist "node_modules" (
    echo 安装根目录依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 根目录依赖安装失败
        pause
        exit /b 1
    )
) else (
    echo ✅ 根目录依赖已存在
)

:: 检查后端依赖
if not exist "Backend\node_modules" (
    echo 安装后端依赖...
    cd Backend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ 后端依赖已存在
)

:: 检查前端依赖
if not exist "Frontend\node_modules" (
    echo 安装前端依赖...
    cd Frontend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..
        pause
        exit /b 1
    )
    cd ..
) else (
    echo ✅ 前端依赖已存在
)

echo.
echo 🗄️ 检查数据库配置...
if exist "Backend\.env" (
    echo ✅ 环境配置文件存在
) else (
    echo ❌ 环境配置文件不存在
    pause
    exit /b 1
)

echo.
echo 🚀 启动服务...

:: 启动后端服务
echo 启动后端服务 (端口 5000)...
start "Newzora Backend" cmd /k "cd /d %cd%\Backend && node server.js"

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 启动前端服务
echo 启动前端服务 (端口 3000)...
start "Newzora Frontend" cmd /k "cd /d %cd%\Frontend && npm run dev"

echo.
echo ⏳ 等待服务完全启动...
timeout /t 15 /nobreak >nul

echo.
echo 🔍 验证服务状态...

:: 检查端口是否被占用（表示服务正在运行）
netstat -an | find ":5000" >nul
if %errorlevel% == 0 (
    echo ✅ 后端服务 (端口 5000) 正在运行
) else (
    echo ⚠️ 后端服务可能未启动
)

netstat -an | find ":3000" >nul
if %errorlevel% == 0 (
    echo ✅ 前端服务 (端口 3000) 正在运行
) else (
    echo ⚠️ 前端服务可能未启动
)

echo.
echo ==========================================
echo    🎉 Newzora 部署完成！
echo ==========================================
echo.
echo 🌐 访问地址:
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:5000
echo    健康检查: http://localhost:5000/api/health
echo.
echo 💡 提示:
echo    - 两个命令行窗口已打开运行前后端服务
echo    - 请等待 1-2 分钟让服务完全启动
echo    - 如果页面无法访问，请检查命令行窗口的错误信息
echo    - 关闭命令行窗口将停止对应的服务
echo.
echo 📝 如果遇到问题:
echo    1. 检查 Node.js 版本 (需要 18+)
echo    2. 检查端口 3000 和 5000 是否被占用
echo    3. 检查网络连接
echo    4. 查看命令行窗口的错误信息
echo.

:: 自动打开浏览器
echo 🌐 正在打开浏览器...
timeout /t 3 /nobreak >nul
start http://localhost:3000

echo.
echo 按任意键退出部署脚本...
pause >nul
