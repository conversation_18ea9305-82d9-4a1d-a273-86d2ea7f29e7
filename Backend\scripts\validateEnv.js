#!/usr/bin/env node

/**
 * 生产环境配置验证脚本
 * 用于验证生产环境的环境变量配置是否完整和安全
 */

require('dotenv').config();

// 简单的颜色输出函数
const colors = {
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  orange: (text) => `\x1b[38;5;208m${text}\x1b[0m`,
  gray: (text) => `\x1b[90m${text}\x1b[0m`,
  bold: (text) => `\x1b[1m${text}\x1b[0m`,
};

// 必需的环境变量
const REQUIRED_VARS = [
  'NODE_ENV',
  'PORT',
  'DB_HOST',
  'DB_NAME',
  'DB_USER',
  'DB_PASSWORD',
  'JWT_SECRET',
  'SESSION_SECRET',
  'FRONTEND_URL',
  'EMAIL_HOST',
  'EMAIL_USER',
  'EMAIL_PASS',
  'VAPID_PUBLIC_KEY',
  'VAPID_PRIVATE_KEY',
];

// 生产环境额外要求的变量
const PRODUCTION_VARS = [
  'REDIS_URL',
  'SSL_CERT_PATH',
  'SSL_KEY_PATH',
  'SENTRY_DSN',
  'CLOUDFLARE_API_TOKEN',
];

// 安全检查的环境变量
const SECURITY_VARS = [
  {
    name: 'JWT_SECRET',
    minLength: 32,
    pattern: /^[A-Za-z0-9+/=]{32,}$/,
    description: 'JWT密钥必须至少32字符，建议使用base64编码的随机字符串',
  },
  {
    name: 'SESSION_SECRET',
    minLength: 32,
    pattern: /^[A-Za-z0-9+/=]{32,}$/,
    description: 'Session密钥必须至少32字符，建议使用base64编码的随机字符串',
  },
  {
    name: 'DB_PASSWORD',
    minLength: 12,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{12,}$/,
    description: '数据库密码必须至少12字符，包含大小写字母、数字和特殊字符',
  },
];

// Default values that should not be used
const INSECURE_DEFAULTS = [
  'your_jwt_secret_key_here',
  'your_production_db_password',
  '<EMAIL>',
  'your-gmail-app-password',
  'CHANGE_THIS_TO_A_VERY_LONG_RANDOM_STRING',
  'your-production-google-client-id',
  'your-production-facebook-app-id',
];

console.log(colors.blue(colors.bold('\n🔍 OneNews Production Environment Configuration Validation\n')));

let hasErrors = false;
let hasWarnings = false;

// Check required environment variables
console.log(colors.yellow('📋 Checking required environment variables...'));
REQUIRED_VARS.forEach((varName) => {
  const value = process.env[varName];
  if (!value) {
    console.log(colors.red(`❌ Missing required environment variable: ${varName}`));
    hasErrors = true;
  } else {
    console.log(colors.green(`✅ ${varName}: Configured`));
  }
});

// 安全性检查
console.log(colors.yellow('\n🔐 安全性检查...'));
SECURITY_VARS.forEach(({ name, minLength, description }) => {
  const value = process.env[name];
  if (value) {
    if (value.length < minLength) {
      console.log(colors.red(`❌ ${name}: ${description}`));
      hasErrors = true;
    } else {
      console.log(colors.green(`✅ ${name}: 长度符合要求`));
    }
  }
});

// 检查不安全的默认值
console.log(colors.yellow('\n⚠️  检查不安全的默认值...'));
Object.keys(process.env).forEach((key) => {
  const value = process.env[key];
  INSECURE_DEFAULTS.forEach((defaultValue) => {
    if (value && value.includes(defaultValue)) {
      console.log(colors.red(`❌ ${key}: 仍在使用不安全的默认值`));
      hasErrors = true;
    }
  });
});

// 环境特定检查
if (process.env.NODE_ENV === 'production') {
  console.log(colors.yellow('\n🏭 生产环境特定检查...'));

  // HTTPS检查
  if (process.env.FRONTEND_URL && !process.env.FRONTEND_URL.startsWith('https://')) {
    console.log(colors.orange('⚠️  FRONTEND_URL 应该使用 HTTPS'));
    hasWarnings = true;
  } else {
    console.log(colors.green('✅ FRONTEND_URL: 使用 HTTPS'));
  }

  // Session安全检查
  if (process.env.SESSION_SECURE !== 'true') {
    console.log(colors.orange('⚠️  SESSION_SECURE 应该设置为 true'));
    hasWarnings = true;
  } else {
    console.log(colors.green('✅ SESSION_SECURE: 已启用'));
  }

  // 数据库SSL检查
  if (!process.env.DATABASE_URL || !process.env.DATABASE_URL.includes('sslmode=require')) {
    console.log(colors.orange('⚠️  建议为数据库连接启用SSL'));
    hasWarnings = true;
  }
}

// 可选但推荐的配置
console.log(colors.yellow('\n💡 可选但推荐的配置...'));
const OPTIONAL_VARS = [
  'REDIS_URL',
  'SENTRY_DSN',
  'AWS_ACCESS_KEY_ID',
  'GOOGLE_CLIENT_ID',
  'FACEBOOK_APP_ID',
];

OPTIONAL_VARS.forEach((varName) => {
  if (process.env[varName]) {
    console.log(colors.green(`✅ ${varName}: 已配置`));
  } else {
    console.log(colors.gray(`ℹ️  ${varName}: 未配置（可选）`));
  }
});

// 配置建议
console.log(colors.yellow('\n📝 配置建议:'));
console.log('1. 使用密钥管理服务（如 AWS Secrets Manager）存储敏感信息');
console.log('2. 定期轮换密钥和密码');
console.log('3. 使用强随机密码生成器');
console.log('4. 启用数据库连接加密');
console.log('5. 配置监控和告警');

// 总结
console.log(colors.blue(colors.bold('\n📊 验证结果:')));
if (hasErrors) {
  console.log(colors.red(colors.bold('❌ 发现严重问题，请修复后再部署到生产环境！')));
  process.exit(1);
} else if (hasWarnings) {
  console.log(colors.orange(colors.bold('⚠️  配置基本正确，但有一些建议改进的地方')));
  process.exit(0);
} else {
  console.log(colors.green(colors.bold('✅ 配置验证通过，可以部署到生产环境！')));
  process.exit(0);
}
