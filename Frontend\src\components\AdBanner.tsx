'use client';

import { useState, useEffect } from 'react';
import { X, ExternalLink } from 'lucide-react';

interface AdData {
  id: number;
  type: 'banner' | 'video' | 'native' | 'popup';
  title: string;
  description: string;
  imageUrl: string;
  linkUrl: string;
  sponsor: string;
  position: 'top' | 'sidebar' | 'content' | 'bottom';
}

interface AdBannerProps {
  position: 'top' | 'sidebar' | 'content' | 'bottom';
  className?: string;
}

export default function AdBanner({ position, className = '' }: AdBannerProps) {
  const [ad, setAd] = useState<AdData | null>(null);
  const [isVisible, setIsVisible] = useState(true);
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    loadAd();
  }, [position]);

  const loadAd = async () => {
    try {
      setIsLoading(true);
      
      // AI生成的模拟广告数据
      const aiGeneratedAds: AdData[][] = [
        // 顶部横幅广告
        [
          {
            id: 1,
            type: 'banner',
            title: '🚀 AI写作助手 - 提升创作效率300%',
            description: '智能文案生成，SEO优化，多语言支持',
            imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=728&h=90&fit=crop',
            linkUrl: 'https://example.com/ai-writer',
            sponsor: 'WriteGenius AI',
            position: 'top'
          },
          {
            id: 11,
            type: 'banner', 
            title: '📱 新一代社交媒体管理工具',
            description: '一键发布到所有平台，智能排程，数据分析',
            imageUrl: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=728&h=90&fit=crop',
            linkUrl: 'https://example.com/social-manager',
            sponsor: 'SocialHub Pro',
            position: 'top'
          }
        ],
        // 侧边栏广告
        [
          {
            id: 2,
            type: 'banner',
            title: '💡 创意设计课程限时优惠',
            description: '从零基础到专业设计师，30天掌握Figma、PS、AI',
            imageUrl: 'https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=300&h=250&fit=crop',
            linkUrl: 'https://example.com/design-course',
            sponsor: 'DesignMaster',
            position: 'sidebar'
          },
          {
            id: 12,
            type: 'banner',
            title: '🎯 数字营销实战训练营',
            description: '7天学会短视频营销，直播带货，私域运营',
            imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=250&fit=crop',
            linkUrl: 'https://example.com/marketing-bootcamp',
            sponsor: 'MarketingPro',
            position: 'sidebar'
          }
        ],
        // 内容区广告
        [
          {
            id: 3,
            type: 'native',
            title: '推荐：2024年最值得关注的10个AI工具',
            description: '这些AI工具正在改变内容创作行业，早用早受益！包含ChatGPT替代品、图像生成器、视频编辑助手等',
            imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=200&fit=crop',
            linkUrl: 'https://example.com/ai-tools-2024',
            sponsor: 'TechReview',
            position: 'content'
          },
          {
            id: 13,
            type: 'native',
            title: '独家：成功创作者的5个赚钱秘诀',
            description: '月入10万+的创作者都在用这些方法！包含内容变现、粉丝运营、品牌合作等实战经验分享',
            imageUrl: 'https://images.unsplash.com/photo-1559526324-4b87b5e36e44?w=400&h=200&fit=crop',
            linkUrl: 'https://example.com/creator-secrets',
            sponsor: 'CreatorWealth',
            position: 'content'
          }
        ],
        // 底部广告
        [
          {
            id: 4,
            type: 'banner',
            title: '☁️ 企业级云存储 - 首月免费',
            description: '无限容量，99.9%可靠性，全球CDN加速',
            imageUrl: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=970&h=90&fit=crop',
            linkUrl: 'https://example.com/cloud-storage',
            sponsor: 'CloudVault',
            position: 'bottom'
          },
          {
            id: 14,
            type: 'banner',
            title: '🔒 网络安全防护套件 - 7天试用',
            description: '保护您的数字资产，防病毒+VPN+密码管理',
            imageUrl: 'https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=970&h=90&fit=crop',
            linkUrl: 'https://example.com/security-suite',
            sponsor: 'SecureShield',
            position: 'bottom'
          }
        ]
      ];

      // 根据位置选择对应的广告组
      const positionIndex = ['top', 'sidebar', 'content', 'bottom'].indexOf(position);
      const adsForPosition = aiGeneratedAds[positionIndex] || aiGeneratedAds[0];
      
      // 随机选择一个广告
      const randomAd = adsForPosition[Math.floor(Math.random() * adsForPosition.length)];
      
      setAd(randomAd);
    } catch (error) {
      console.error('Failed to load ad:', error);
      setAd(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdClick = () => {
    if (ad) {
      // Track ad click
      console.log('Ad clicked:', ad.id);
      window.open(ad.linkUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const handleClose = () => {
    setIsVisible(false);
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-gray-200 rounded-lg ${getPositionStyles(position)} ${className}`}>
        <div className="h-full bg-gray-300 rounded-lg"></div>
      </div>
    );
  }

  if (!ad || !isVisible) {
    return null;
  }

  return (
    <div className={`relative group ${getPositionStyles(position)} ${className}`}>
      {/* Ad Label */}
      <div className="absolute top-2 left-2 z-10">
        <span className="bg-gray-800 text-white text-xs px-2 py-1 rounded">
          Sponsored
        </span>
      </div>

      {/* Close Button */}
      <button
        onClick={handleClose}
        className="absolute top-2 right-2 z-10 p-1 bg-gray-800 bg-opacity-50 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
      >
        <X className="h-3 w-3" />
      </button>

      {/* Ad Content */}
      <div
        onClick={handleAdClick}
        className="cursor-pointer h-full bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
      >
        {position === 'content' ? (
          // Native ad format
          <div className="p-4">
            <div className="flex space-x-4">
              <div className="w-24 h-24 flex-shrink-0">
                <img
                  src={ad.imageUrl}
                  alt={ad.title}
                  className="w-24 h-24 object-cover rounded-lg"
                  loading="lazy"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 mb-1">{ad.title}</h3>
                <p className="text-sm text-gray-600 mb-2">{ad.description}</p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500">by {ad.sponsor}</span>
                  <ExternalLink className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </div>
          </div>
        ) : (
          // Banner ad format
          <div className="relative h-full">
            <img
              src={ad.imageUrl}
              alt={ad.title}
              className="w-full h-full object-cover"
              loading="lazy"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent flex items-end">
              <div className="p-4 text-white">
                <h3 className="font-semibold mb-1">{ad.title}</h3>
                <p className="text-sm opacity-90">{ad.description}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function getPositionStyles(position: string): string {
  switch (position) {
    case 'top':
      return 'w-full h-24 mb-4';
    case 'sidebar':
      return 'w-full h-64 mb-6';
    case 'content':
      return 'w-full mb-6';
    case 'bottom':
      return 'w-full h-20 mt-4';
    default:
      return 'w-full h-32';
  }
}

