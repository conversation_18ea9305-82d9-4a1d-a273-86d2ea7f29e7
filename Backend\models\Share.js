const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Share = sequelize.define(
  'Share',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true, // Allow anonymous shares
      references: {
        model: 'users',
        key: 'id',
      },
    },
    articleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'articles',
        key: 'id',
      },
    },
    platform: {
      type: DataTypes.ENUM(
        'facebook',
        'twitter',
        'linkedin',
        'whatsapp',
        'telegram',
        'email',
        'copy_link',
        'other'
      ),
      allowNull: false,
    },
    ipAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    referrer: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    tableName: 'shares',
    timestamps: true,
    indexes: [
      {
        fields: ['userId'],
      },
      {
        fields: ['articleId'],
      },
      {
        fields: ['platform'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['articleId', 'platform'],
      },
    ],
  }
);

module.exports = Share;
