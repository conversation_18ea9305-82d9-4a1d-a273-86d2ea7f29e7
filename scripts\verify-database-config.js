#!/usr/bin/env node

/**
 * Newzora 数据库配置验证脚本
 * 验证主站前台和后台管理系统的数据库配置是否正确
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查配置文件
function checkConfigFile(filePath, expectedValues, description) {
  log(`\n🔍 检查 ${description}`, 'cyan');
  
  if (!fs.existsSync(filePath)) {
    log(`❌ 文件不存在: ${filePath}`, 'red');
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  let allValid = true;
  
  for (const [key, expectedValue] of Object.entries(expectedValues)) {
    if (content.includes(`${key}=${expectedValue}`)) {
      log(`✅ ${key} 配置正确`, 'green');
    } else {
      log(`❌ ${key} 配置错误或缺失`, 'red');
      allValid = false;
    }
  }
  
  return allValid;
}

// 检查文件是否存在
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description} 存在`, 'green');
    return true;
  } else {
    log(`❌ ${description} 不存在: ${filePath}`, 'red');
    return false;
  }
}

// 主验证函数
function verifyConfiguration() {
  log('🚀 开始验证 Newzora 数据库配置', 'bright');
  
  let allValid = true;
  
  // 检查主站前台配置
  log('\n=== 主站前台配置检查 ===', 'yellow');
  
  const webEnvPath = path.join(__dirname, '../Backend/.env');
  const webEnvExpected = {
    'DB_NAME': 'PostgreSQL-newzora_web',
    'DB_PASSWORD': 'wasd080980!'
  };
  allValid &= checkConfigFile(webEnvPath, webEnvExpected, '主站前台环境变量');
  
  const webDbConfigPath = path.join(__dirname, '../Backend/config/database.js');
  allValid &= checkFileExists(webDbConfigPath, '主站前台数据库配置文件');
  
  const webInitScriptPath = path.join(__dirname, '../Backend/scripts/init-db.sql');
  allValid &= checkFileExists(webInitScriptPath, '主站前台初始化脚本');
  
  // 检查后台管理系统配置
  log('\n=== 后台管理系统配置检查 ===', 'yellow');
  
  const adminEnvPath = path.join(__dirname, '../NewzoraAdmin/Backend/.env');
  const adminEnvExpected = {
    'DB_NAME': 'PostgreSQL-newzora_admin',
    'DB_PASSWORD': 'QWasd080980!'
  };
  allValid &= checkConfigFile(adminEnvPath, adminEnvExpected, '后台管理系统环境变量');
  
  const adminDbConfigPath = path.join(__dirname, '../NewzoraAdmin/Backend/config/database.js');
  allValid &= checkFileExists(adminDbConfigPath, '后台管理系统数据库配置文件');
  
  const adminInitScriptPath = path.join(__dirname, '../NewzoraAdmin/Backend/scripts/init-db.sql');
  allValid &= checkFileExists(adminInitScriptPath, '后台管理系统初始化脚本');
  
  const adminNodeInitPath = path.join(__dirname, '../NewzoraAdmin/Backend/scripts/initDatabase.js');
  allValid &= checkFileExists(adminNodeInitPath, '后台管理系统Node.js初始化脚本');
  
  // 检查Docker配置
  log('\n=== Docker 配置检查 ===', 'yellow');
  
  const dockerComposePath = path.join(__dirname, '../deployment/docker/docker-compose.yml');
  allValid &= checkFileExists(dockerComposePath, 'Docker Compose 配置文件');
  
  if (fs.existsSync(dockerComposePath)) {
    const dockerContent = fs.readFileSync(dockerComposePath, 'utf8');
    
    if (dockerContent.includes('postgres-web:') && dockerContent.includes('postgres-admin:')) {
      log('✅ Docker 包含两个数据库服务', 'green');
    } else {
      log('❌ Docker 配置缺少数据库服务', 'red');
      allValid = false;
    }
    
    if (dockerContent.includes('PostgreSQL-newzora_web') && dockerContent.includes('PostgreSQL-newzora_admin')) {
      log('✅ Docker 数据库名称配置正确', 'green');
    } else {
      log('❌ Docker 数据库名称配置错误', 'red');
      allValid = false;
    }
  }
  
  // 检查管理工具
  log('\n=== 管理工具检查 ===', 'yellow');
  
  const webDbManagerPath = path.join(__dirname, 'web-database-manager.js');
  allValid &= checkFileExists(webDbManagerPath, '主站数据库管理工具');
  
  const adminDbManagerPath = path.join(__dirname, '../NewzoraAdmin/scripts/database-manager.js');
  allValid &= checkFileExists(adminDbManagerPath, '后台管理数据库管理工具');
  
  const backupDirPath = path.join(__dirname, '../backups');
  allValid &= checkFileExists(backupDirPath, '备份目录');
  
  const configDocPath = path.join(__dirname, '../DATABASE_CONFIGURATION.md');
  allValid &= checkFileExists(configDocPath, '数据库配置文档');
  
  // 输出结果
  log('\n' + '='.repeat(50), 'cyan');
  if (allValid) {
    log('🎉 所有配置检查通过！数据库配置正确。', 'green');
    log('\n📋 下一步操作:', 'cyan');
    log('1. 运行: node scripts/database-manager.js setup', 'yellow');
    log('2. 启动应用: npm run dev', 'yellow');
  } else {
    log('❌ 配置检查失败！请修复上述问题。', 'red');
    log('\n📋 建议操作:', 'cyan');
    log('1. 检查环境变量文件是否正确配置', 'yellow');
    log('2. 确认所有必需文件都存在', 'yellow');
    log('3. 重新运行此验证脚本', 'yellow');
  }
  log('='.repeat(50), 'cyan');
  
  return allValid;
}

// 如果直接运行此脚本
if (require.main === module) {
  const isValid = verifyConfiguration();
  process.exit(isValid ? 0 : 1);
}

module.exports = { verifyConfiguration };