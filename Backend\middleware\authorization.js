/**
 * 增强的授权检查中间件
 */

const { logger, logSecurityEvent } = require('../config/logger');
const { sanitizeLogInput } = require('../utils/logSanitizer');

/**
 * 检查资源访问权限
 */
const checkResourceAccess = (resourceType, action = 'read') => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        logSecurityEvent('UNAUTHORIZED_ACCESS_ATTEMPT', {
          resourceType,
          action,
          path: req.path,
          method: req.method
        }, req);

        return res.status(401).json({
          success: false,
          message: 'Authentication required',
          code: 'AUTH_REQUIRED'
        });
      }

      // 管理员拥有所有权限
      if (req.user.role === 'admin') {
        return next();
      }

      // 根据资源类型和操作检查权限
      const hasPermission = await checkPermission(req.user, resourceType, action, req);

      if (!hasPermission) {
        logSecurityEvent('INSUFFICIENT_PERMISSIONS', {
          userId: req.user.id,
          userRole: req.user.role,
          resourceType,
          action,
          path: req.path
        }, req);

        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions',
          code: 'INSUFFICIENT_PERMISSIONS'
        });
      }

      next();
    } catch (error) {
      logger.error('Authorization check error', {
        error: error.message,
        resourceType,
        action,
        userId: req.user?.id
      });

      return res.status(500).json({
        success: false,
        message: 'Authorization error',
        code: 'AUTH_ERROR'
      });
    }
  };
};

/**
 * 检查用户权限
 */
const checkPermission = async (user, resourceType, action, req) => {
  switch (resourceType) {
    case 'article':
      return checkArticlePermission(user, action, req);
    case 'comment':
      return checkCommentPermission(user, action, req);
    case 'user':
      return checkUserPermission(user, action, req);
    case 'admin':
      return user.role === 'admin';
    case 'moderator':
      return ['admin', 'moderator'].includes(user.role);
    default:
      return false;
  }
};

/**
 * 检查文章权限
 */
const checkArticlePermission = async (user, action, req) => {
  switch (action) {
    case 'create':
      return user.isActive && !user.isAccountLocked();
    case 'read':
      return true; // 所有用户都可以读取公开文章
    case 'update':
    case 'delete':
      // 需要检查文章所有权
      if (req.params.id) {
        const Article = require('../models/Article');
        const article = await Article.findByPk(req.params.id);
        return article && (article.authorId === user.id || user.role === 'admin');
      }
      return false;
    default:
      return false;
  }
};

/**
 * 检查评论权限
 */
const checkCommentPermission = async (user, action, req) => {
  switch (action) {
    case 'create':
      return user.isActive && !user.isAccountLocked();
    case 'read':
      return true;
    case 'update':
    case 'delete':
      if (req.params.id) {
        const Comment = require('../models/Comment');
        const comment = await Comment.findByPk(req.params.id);
        return comment && (comment.userId === user.id || user.role === 'admin');
      }
      return false;
    default:
      return false;
  }
};

/**
 * 检查用户权限
 */
const checkUserPermission = async (user, action, req) => {
  switch (action) {
    case 'read':
      return true; // 所有用户都可以查看公开用户信息
    case 'update':
      // 只能更新自己的信息或管理员
      return req.params.id ? 
        (parseInt(req.params.id) === user.id || user.role === 'admin') : 
        true;
    case 'delete':
      return user.role === 'admin';
    default:
      return false;
  }
};

/**
 * 检查API速率限制权限
 */
const checkRateLimit = (req, res, next) => {
  // 管理员和版主不受速率限制
  if (req.user && ['admin', 'moderator'].includes(req.user.role)) {
    return next();
  }

  // 应用标准速率限制
  next();
};

/**
 * 检查账户状态
 */
const checkAccountStatus = (req, res, next) => {
  if (!req.user) {
    return next();
  }

  if (!req.user.isActive) {
    logSecurityEvent('INACTIVE_ACCOUNT_ACCESS', {
      userId: req.user.id,
      path: req.path
    }, req);

    return res.status(403).json({
      success: false,
      message: 'Account is deactivated',
      code: 'ACCOUNT_DEACTIVATED'
    });
  }

  if (req.user.isAccountLocked && req.user.isAccountLocked()) {
    logSecurityEvent('LOCKED_ACCOUNT_ACCESS', {
      userId: req.user.id,
      path: req.path
    }, req);

    return res.status(403).json({
      success: false,
      message: 'Account is temporarily locked',
      code: 'ACCOUNT_LOCKED'
    });
  }

  next();
};

module.exports = {
  checkResourceAccess,
  checkPermission,
  checkRateLimit,
  checkAccountStatus
};