'use client';

import { useState } from 'react';
import { 
  CreditCard, 
  DollarSign, 
  Shield, 
  Zap, 
  CheckCircle, 
  Clock, 
  Star,
  Info,
  ArrowRight,
  Wallet,
  Globe
} from 'lucide-react';

interface PaymentMethod {
  name: string;
  fee: number;
  time: string;
  icon: any;
  description: string;
  popular: boolean;
  security: string;
}

interface PaymentMethodSelectorProps {
  paymentMethods: Record<string, PaymentMethod>;
  selectedMethod: string;
  onMethodSelect: (method: string) => void;
  amount?: string;
}

export default function PaymentMethodSelector({ 
  paymentMethods, 
  selectedMethod, 
  onMethodSelect,
  amount 
}: PaymentMethodSelectorProps) {
  const [hoveredMethod, setHoveredMethod] = useState<string | null>(null);

  const calculateFee = (method: PaymentMethod) => {
    if (!amount) return 0;
    return (parseFloat(amount) * method.fee / 100).toFixed(2);
  };

  const getMethodColor = (key: string) => {
    const colors = {
      bank_card: 'from-blue-500 to-indigo-600',
      paypal: 'from-purple-500 to-pink-600',
      crypto_btc: 'from-orange-500 to-yellow-600',
      crypto_usdt: 'from-green-500 to-emerald-600'
    };
    return colors[key as keyof typeof colors] || 'from-gray-500 to-gray-600';
  };

  const getSecurityLevel = (security: string) => {
    const levels = {
      'High': { color: 'text-blue-600', bg: 'bg-blue-100' },
      'Very High': { color: 'text-green-600', bg: 'bg-green-100' }
    };
    return levels[security as keyof typeof levels] || { color: 'text-gray-600', bg: 'bg-gray-100' };
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-900 mb-2">Choose Payment Method</h3>
        <p className="text-gray-600">Select how you'd like to receive your funds</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {Object.entries(paymentMethods).map(([key, method]) => {
          const Icon = method.icon;
          const isSelected = selectedMethod === key;
          const isHovered = hoveredMethod === key;
          const securityStyle = getSecurityLevel(method.security);
          
          return (
            <div
              key={key}
              className={`relative cursor-pointer transition-all duration-300 transform ${
                isSelected ? 'scale-105' : isHovered ? 'scale-102' : ''
              }`}
              onMouseEnter={() => setHoveredMethod(key)}
              onMouseLeave={() => setHoveredMethod(null)}
              onClick={() => onMethodSelect(key)}
            >
              <div className={`relative overflow-hidden border-2 rounded-2xl p-6 transition-all duration-300 ${
                isSelected
                  ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl'
                  : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-lg'
              }`}>
                {/* Popular Badge */}
                {method.popular && (
                  <div className="absolute -top-2 -right-2 z-10">
                    <div className="bg-gradient-to-r from-orange-400 to-red-500 text-white text-xs px-3 py-1 rounded-full font-bold shadow-lg">
                      <Star className="inline h-3 w-3 mr-1" />
                      Popular
                    </div>
                  </div>
                )}

                {/* Selection Indicator */}
                {isSelected && (
                  <div className="absolute top-4 right-4">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  </div>
                )}

                {/* Method Icon and Name */}
                <div className="flex items-center mb-4">
                  <div className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br ${getMethodColor(key)} shadow-lg`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-gray-900">{method.name}</h4>
                    <p className="text-sm text-gray-600">{method.description}</p>
                  </div>
                </div>

                {/* Method Details */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      <span>Processing Time</span>
                    </div>
                    <span className="font-semibold text-gray-900">{method.time}</span>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm text-gray-600">
                      <DollarSign className="h-4 w-4 mr-2" />
                      <span>Fee</span>
                    </div>
                    <div className="text-right">
                      <span className="font-semibold text-gray-900">{method.fee}%</span>
                      {amount && (
                        <div className="text-xs text-gray-500">
                          ≈ ${calculateFee(method)}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-sm text-gray-600">
                      <Shield className="h-4 w-4 mr-2" />
                      <span>Security</span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${securityStyle.bg} ${securityStyle.color}`}>
                      {method.security}
                    </span>
                  </div>
                </div>

                {/* Additional Info */}
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    {method.popular && (
                      <div className="flex items-center text-xs text-orange-600">
                        <Star className="h-3 w-3 mr-1" />
                        Recommended
                      </div>
                    )}
                    
                    {isSelected && (
                      <div className="flex items-center text-xs text-blue-600 font-medium">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Selected
                      </div>
                    )}
                  </div>
                </div>

                {/* Hover Effect Overlay */}
                {isHovered && !isSelected && (
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 rounded-2xl pointer-events-none" />
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Method Comparison */}
      {selectedMethod && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
          <div className="flex items-center mb-4">
            <Info className="h-5 w-5 text-blue-600 mr-2" />
            <h4 className="font-semibold text-blue-900">Selected Method Details</h4>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="text-center">
              <div className="text-blue-600 font-semibold">Processing Time</div>
              <div className="text-gray-700">{paymentMethods[selectedMethod].time}</div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-semibold">Fee Rate</div>
              <div className="text-gray-700">{paymentMethods[selectedMethod].fee}%</div>
            </div>
            <div className="text-center">
              <div className="text-blue-600 font-semibold">Security Level</div>
              <div className="text-gray-700">{paymentMethods[selectedMethod].security}</div>
            </div>
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className="text-center text-sm text-gray-500">
        <p>All payment methods are secured with industry-standard encryption</p>
      </div>
    </div>
  );
}
