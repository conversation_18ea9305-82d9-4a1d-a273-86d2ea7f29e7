'use client';

import React, { useState, useEffect } from 'react';
import { Globe } from 'lucide-react';

type Language = 'en' | 'zh';

interface LanguageSwitcherProps {
  className?: string;
}

const translations = {
  zh: {
    '搜索用户、文章、评论...': '搜索用户、文章、评论...',
    'Newzora Admin': 'Newzora 管理后台',
    '仪表板': '仪表板',
    '用户管理': '用户管理',
    '内容管理': '内容管理',
    '收益管理': '收益管理',
    '数据分析': '数据分析',
    '系统设置': '系统设置',
    '退出登录': '退出登录'
  },
  en: {
    '搜索用户、文章、评论...': 'Search users, articles, comments...',
    'Newzora Admin': 'Newzora Admin',
    '仪表板': 'Dashboard',
    '用户管理': 'User Management',
    '内容管理': 'Content Management',
    '收益管理': 'Monetization',
    '数据分析': 'Analytics',
    '系统设置': 'Settings',
    '退出登录': 'Logout'
  }
};

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ className = '' }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    // Get language setting from localStorage, default to English for international users
    const savedLanguage = localStorage.getItem('admin_language') as Language;

    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'zh')) {
      setCurrentLanguage(savedLanguage);
    } else {
      // Default to English for admin interface
      setCurrentLanguage('en');
      localStorage.setItem('admin_language', 'en');
    }
  }, []);

  const handleLanguageChange = (language: Language) => {
    setCurrentLanguage(language);
    localStorage.setItem('admin_language', language);
    setIsOpen(false);
    
    // Trigger custom event to notify other components that language has changed
    window.dispatchEvent(new CustomEvent('languageChange', { detail: language }));

    // Update all translatable text on the page
    updatePageTexts(language);
  };

  const updatePageTexts = (language: Language) => {
    // Get all elements that need translation
    const translatableElements = document.querySelectorAll('[data-translate]');

    translatableElements.forEach(element => {
      const key = element.getAttribute('data-translate') || element.textContent;
      if (key && translations[language][key]) {
        element.textContent = translations[language][key];
      }
    });

    // Special handling for placeholders
    const placeholderElements = document.querySelectorAll('[data-translate-placeholder]');
    placeholderElements.forEach(element => {
      const key = element.getAttribute('data-translate-placeholder');
      if (key && translations[language][key]) {
        (element as HTMLInputElement).placeholder = translations[language][key];
      }
    });
  };

  const languages = [
    { code: 'zh' as Language, name: 'Chinese', flag: '🇨🇳' },
    { code: 'en' as Language, name: 'English', flag: '🇺🇸' },
  ];

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 text-gray-600 hover:text-gray-900 focus:outline-none"
      >
        <Globe size={18} />
        <span className="text-sm font-medium">
          {languages.find(lang => lang.code === currentLanguage)?.flag}
        </span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`block w-full text-left px-4 py-2 text-sm ${
                currentLanguage === language.code
                  ? 'bg-blue-50 text-blue-700'
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
            >
              <span className="mr-2">{language.flag}</span>
              {language.name}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;