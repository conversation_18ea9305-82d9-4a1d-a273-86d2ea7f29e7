'use client';

import { useState, useRef } from 'react';
import {
  LinkIcon,
  PhotoIcon,
  CodeBracketIcon,
  ChatBubbleLeftIcon as QuoteIcon,
  Bars3BottomLeftIcon as MenuIcon,
} from '@heroicons/react/24/outline';

interface RichTextEditorProps {
  value: string;
  onChange: (value: string, html: string) => void;
  placeholder?: string;
  className?: string;
}

export default function RichTextEditor({
  value,
  onChange,
  placeholder = '开始写作...',
  className = '',
}: RichTextEditorProps) {
  const [showHeadings, setShowHeadings] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const editorRef = useRef<HTMLDivElement>(null);

  const insertText = (before: string, after: string = '') => {
    if (!textareaRef.current) return;

    const textarea = textareaRef.current;
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);

    onChange(newText, convertToHtml(newText));
    textarea.focus();
    textarea.setSelectionRange(start + before.length, end + before.length);
  };

  const formatActions = {
    bold: () => insertText('**', '**'),
    italic: () => insertText('*', '*'),
    underline: () => insertText('<u>', '</u>'),
    bulletList: () => insertText('\\n- '),
    quote: () => insertText('\\n> '),
    code: () => insertText('`', '`'),
    link: () => {
      const url = prompt('请输入链接地址：');
      if (url) {
        const text = prompt('请输入链接文字：') || url;
        insertText(`[${text}](${url})`);
      }
    },
    image: () => {
      const url = prompt('请输入图片地址：');
      if (url) {
        const alt = prompt('请输入图片描述：') || '图片';
        insertText(`![${alt}](${url})`);
      }
    },
    heading: (level: number) => {
      insertText('\\n' + '#'.repeat(level) + ' ');
      setShowHeadings(false);
    },
  };

  const toolbarItems = [
    { icon: MenuIcon, action: () => setShowHeadings(!showHeadings), label: '标题' },
    { icon: PhotoIcon, action: formatActions.image, label: '插入图片' },
    { icon: LinkIcon, action: formatActions.link, label: '插入链接' },
    { icon: CodeBracketIcon, action: formatActions.code, label: '代码片段' },
    { icon: QuoteIcon, action: formatActions.quote, label: '引用文字' },
  ];

  const convertToHtml = (markdown: string): string => {
    let html = markdown;
    
    // 基础转换规则
    const rules = [
      { pattern: /^###### (.*$)/gim, replace: '<h6 class="text-base">$1</h6>' },
      { pattern: /^##### (.*$)/gim, replace: '<h5 class="text-lg">$1</h5>' },
      { pattern: /^#### (.*$)/gim, replace: '<h4 class="text-xl">$1</h4>' },
      { pattern: /^### (.*$)/gim, replace: '<h3 class="text-2xl">$1</h3>' },
      { pattern: /^## (.*$)/gim, replace: '<h2 class="text-3xl">$1</h2>' },
      { pattern: /^# (.*$)/gim, replace: '<h1 class="text-4xl">$1</h1>' },
      { pattern: /\*\*(.*)\*\*/gim, replace: '<strong>$1</strong>' },
      { pattern: /\*(.*)\*/gim, replace: '<em>$1</em>' },
      { pattern: /<u>(.*)<\/u>/gim, replace: '<u>$1</u>' },
      { pattern: /`(.*)`/gim, replace: '<code class="bg-gray-100 px-1 rounded">$1</code>' },
      { pattern: /\[([^\]]*)\]\(([^\)]*)\)/gim, replace: '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">$1</a>' },
      { pattern: /!\[([^\]]*)\]\(([^\)]*)\)/gim, replace: '<img src="$2" alt="$1" class="max-w-full h-auto rounded my-2" loading="lazy" />' },
      { pattern: /^> (.*$)/gim, replace: '<blockquote class="border-l-4 border-gray-300 pl-4 my-4 text-gray-600">$1</blockquote>' },
      { pattern: /^\- (.*$)/gim, replace: '<li class="ml-4">$1</li>' },
      { pattern: /\n/gim, replace: '<br>' },
    ];

    rules.forEach(({ pattern, replace }) => {
      html = html.replace(pattern, replace);
    });

    return html;
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* 工具栏 */}
      <div className="flex items-center gap-2 p-3 border-b border-gray-200 bg-gray-50">
        {toolbarItems.map((item, index) => (
          <button
            key={index}
            onClick={item.action}
            className="p-2 rounded-md hover:bg-white hover:shadow-sm transition-all text-gray-600 hover:text-gray-900"
            title={item.label}
          >
            <item.icon className="w-5 h-5" />
          </button>
        ))}

        {/* 分隔线 */}
        <div className="h-6 w-px bg-gray-300 mx-2" />
        
        <div className="flex-1" />

        <div className="text-sm text-gray-500">
          Markdown
        </div>
      </div>

      {/* 标题选择下拉菜单 */}
      {showHeadings && (
        <div className="absolute mt-1 ml-3 bg-white border rounded-lg shadow-lg z-10 w-48">
          {[1, 2, 3, 4, 5, 6].map((level) => (
            <button
              key={level}
              onClick={() => formatActions.heading(level)}
              className="block w-full text-left px-4 py-2 text-sm hover:bg-gray-50"
            >
              {`标题 ${level}`}
            </button>
          ))}
        </div>
      )}

      {/* 编辑区域 */}
      <div ref={editorRef} className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value, convertToHtml(e.target.value))}
          placeholder={placeholder}
          className="w-full min-h-[400px] p-4 text-base leading-relaxed resize-none focus:outline-none focus:ring-1 focus:ring-blue-500 transition-shadow"
        />
      </div>

      {/* 状态栏 */}
      <div className="flex items-center justify-between px-4 py-2 border-t border-gray-200 bg-gray-50">
        <div className="text-sm text-gray-500">
          {value.trim().length} 字符
        </div>
      </div>
    </div>
  );
}
