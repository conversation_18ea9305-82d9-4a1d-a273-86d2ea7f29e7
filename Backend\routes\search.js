const express = require('express');
const router = express.Router();
const { Article, User, Tag } = require('../models');
const { Op } = require('sequelize');
const { logger } = require('../config/logger');

// Advanced search endpoint
router.get('/', async (req, res) => {
  try {
    const {
      q: query = '',
      type = 'all',
      category = 'all',
      author = '',
      dateRange = 'all',
      sortBy = 'relevance',
      minViews = 0,
      tags = '',
      page = 1,
      limit = 20
    } = req.query;

    // Build where conditions
    const whereConditions = {
      published: true,
    };

    // Full-text search conditions
    if (query) {
      whereConditions[Op.or] = [
        { title: { [Op.iLike]: `%${query}%` } },
        { description: { [Op.iLike]: `%${query}%` } },
        { content: { [Op.iLike]: `%${query}%` } },
        { tags: { [Op.contains]: [query.toLowerCase()] } }
      ];
    }

    // Content type filter
    if (type !== 'all') {
      whereConditions.type = type.slice(0, -1); // Remove 's' from 'articles', 'videos', etc.
    }

    // Category filter
    if (category !== 'all') {
      whereConditions.category = { [Op.iLike]: category };
    }

    // Author filter
    if (author) {
      whereConditions.author = { [Op.iLike]: `%${author}%` };
    }

    // Minimum views filter
    if (minViews > 0) {
      whereConditions.views = { [Op.gte]: parseInt(minViews) };
    }

    // Tags filter
    if (tags) {
      const tagArray = tags.split(',').filter(Boolean);
      if (tagArray.length > 0) {
        whereConditions.tags = { [Op.overlap]: tagArray };
      }
    }

    // Date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate;

      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
      }

      if (startDate) {
        whereConditions.createdAt = { [Op.gte]: startDate };
      }
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Determine sort order
    let order;
    switch (sortBy) {
      case 'date':
        order = [['createdAt', 'DESC']];
        break;
      case 'views':
        order = [['views', 'DESC']];
        break;
      case 'likes':
        order = [['likes', 'DESC']];
        break;
      case 'relevance':
      default:
        // For relevance, we'll use a combination of factors
        if (query) {
          // PostgreSQL full-text search ranking (simplified)
          order = [
            ['views', 'DESC'], // Secondary sort by views
            ['createdAt', 'DESC'] // Tertiary sort by date
          ];
        } else {
          order = [['views', 'DESC'], ['createdAt', 'DESC']];
        }
        break;
    }

    // Execute search query
    const { count, rows: articles } = await Article.findAndCountAll({
      where: whereConditions,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: order,
      attributes: [
        'id', 'title', 'description', 'content', 'category', 'image',
        'author', 'readTime', 'tags', 'views', 'likes', 'featured',
        'published', 'createdAt', 'updatedAt'
      ]
    });

    // Calculate relevance scores for better ranking (if query provided)
    let rankedArticles = articles;
    if (query && sortBy === 'relevance') {
      rankedArticles = articles.map(article => {
        let score = 0;
        const lowerQuery = query.toLowerCase();
        const articleData = article.toJSON();

        // Title match (highest weight)
        if (articleData.title.toLowerCase().includes(lowerQuery)) {
          score += 10;
        }

        // Description match (medium weight)
        if (articleData.description && articleData.description.toLowerCase().includes(lowerQuery)) {
          score += 5;
        }

        // Content match (lower weight)
        if (articleData.content && articleData.content.toLowerCase().includes(lowerQuery)) {
          score += 3;
        }

        // Tags match (lowest weight)
        if (articleData.tags && articleData.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
          score += 2;
        }

        // Author match (very low weight)
        if (articleData.author && articleData.author.toLowerCase().includes(lowerQuery)) {
          score += 1;
        }

        return { ...articleData, relevanceScore: score };
      }).sort((a, b) => {
        if (a.relevanceScore !== b.relevanceScore) {
          return b.relevanceScore - a.relevanceScore;
        }
        // If relevance scores are equal, sort by views
        return b.views - a.views;
      });
    }

    // Search suggestions (if no results found)
    let suggestions = [];
    if (count === 0 && query) {
      // Get popular categories and tags for suggestions
      const popularCategories = await Article.findAll({
        attributes: ['category'],
        where: { published: true },
        group: ['category'],
        order: [[Article.sequelize.fn('COUNT', Article.sequelize.col('category')), 'DESC']],
        limit: 5,
        raw: true
      });

      suggestions = popularCategories.map(item => ({
        type: 'category',
        value: item.category,
        label: `Browse ${item.category} articles`
      }));
    }

    // Response
    res.json({
      success: true,
      data: {
        articles: rankedArticles,
        pagination: {
          total: count,
          totalPages: Math.ceil(count / limit),
          currentPage: parseInt(page),
          limit: parseInt(limit),
          hasNext: offset + parseInt(limit) < count,
          hasPrev: parseInt(page) > 1
        },
        filters: {
          query,
          type,
          category,
          author,
          dateRange,
          sortBy,
          minViews: parseInt(minViews),
          tags: tags ? tags.split(',').filter(Boolean) : []
        },
        suggestions,
        searchTime: Date.now() // For performance tracking
      }
    });

  } catch (error) {
    logger.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Search suggestions endpoint
router.get('/suggestions', async (req, res) => {
  try {
    const { q: query = '', limit = 5 } = req.query;

    if (!query || query.length < 2) {
      return res.json({
        success: true,
        data: {
          suggestions: []
        }
      });
    }

    // Get title suggestions
    const titleSuggestions = await Article.findAll({
      attributes: ['title'],
      where: {
        published: true,
        title: { [Op.iLike]: `%${query}%` }
      },
      limit: parseInt(limit),
      order: [['views', 'DESC']],
      raw: true
    });

    // Get tag suggestions
    const tagSuggestions = await Article.findAll({
      attributes: ['tags'],
      where: {
        published: true,
        tags: { [Op.contains]: [query.toLowerCase()] }
      },
      limit: parseInt(limit),
      raw: true
    });

    // Process and combine suggestions
    const suggestions = [
      ...titleSuggestions.map(item => ({
        type: 'title',
        value: item.title,
        label: item.title
      })),
      ...tagSuggestions.flatMap(item => 
        item.tags
          .filter(tag => tag.toLowerCase().includes(query.toLowerCase()))
          .map(tag => ({
            type: 'tag',
            value: tag,
            label: `#${tag}`
          }))
      )
    ].slice(0, parseInt(limit));

    res.json({
      success: true,
      data: {
        suggestions: suggestions
      }
    });

  } catch (error) {
    logger.error('Search suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get search suggestions',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Popular searches endpoint
router.get('/popular', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    // Get popular categories
    const popularCategories = await Article.findAll({
      attributes: [
        'category',
        [Article.sequelize.fn('COUNT', Article.sequelize.col('id')), 'count'],
        [Article.sequelize.fn('SUM', Article.sequelize.col('views')), 'totalViews']
      ],
      where: { published: true },
      group: ['category'],
      order: [[Article.sequelize.fn('SUM', Article.sequelize.col('views')), 'DESC']],
      limit: parseInt(limit),
      raw: true
    });

    // Get trending tags
    const trendingTags = await Article.findAll({
      attributes: ['tags'],
      where: {
        published: true,
        createdAt: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      },
      order: [['views', 'DESC']],
      limit: 50,
      raw: true
    });

    // Process trending tags
    const tagCounts = {};
    trendingTags.forEach(article => {
      if (article.tags) {
        article.tags.forEach(tag => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      }
    });

    const topTags = Object.entries(tagCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, parseInt(limit))
      .map(([tag, count]) => ({ tag, count }));

    res.json({
      success: true,
      data: {
        popularCategories: popularCategories.map(item => ({
          category: item.category,
          articleCount: parseInt(item.count),
          totalViews: parseInt(item.totalViews || 0)
        })),
        trendingTags: topTags
      }
    });

  } catch (error) {
    logger.error('Popular searches error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular searches',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

module.exports = router;