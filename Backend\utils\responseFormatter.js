/**
 * 统一的API响应格式化工具
 * 确保所有API端点返回一致的响应格式
 */

/**
 * 成功响应格式
 * @param {Object} data - 响应数据
 * @param {string} message - 成功消息
 * @param {Object} meta - 元数据（分页、统计等）
 * @returns {Object} 格式化的成功响应
 */
const successResponse = (data = null, message = 'Success', meta = null) => {
  const response = {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };

  if (meta) {
    response.meta = meta;
  }

  return response;
};

/**
 * 错误响应格式
 * @param {string} message - 错误消息
 * @param {string|number} code - 错误代码
 * @param {Array} errors - 详细错误信息（验证错误等）
 * @param {Object} debug - 调试信息（仅开发环境）
 * @returns {Object} 格式化的错误响应
 */
const errorResponse = (message = 'An error occurred', code = null, errors = null, debug = null) => {
  const response = {
    success: false,
    message,
    timestamp: new Date().toISOString()
  };

  if (code) {
    response.code = code;
  }

  if (errors && Array.isArray(errors) && errors.length > 0) {
    response.errors = errors;
  }

  // 仅在开发环境包含调试信息
  if (debug && process.env.NODE_ENV === 'development') {
    response.debug = debug;
  }

  return response;
};

/**
 * 认证相关的成功响应
 * @param {Object} user - 用户信息
 * @param {string} token - JWT token
 * @param {string} message - 成功消息
 * @returns {Object} 格式化的认证成功响应
 */
const authSuccessResponse = (user, token, message = 'Authentication successful') => {
  return successResponse(
    {
      user: user.getPublicProfile ? user.getPublicProfile() : user,
      token
    },
    message
  );
};

/**
 * 分页响应格式
 * @param {Array} items - 数据项
 * @param {number} total - 总数
 * @param {number} page - 当前页
 * @param {number} limit - 每页数量
 * @param {string} message - 消息
 * @returns {Object} 格式化的分页响应
 */
const paginatedResponse = (items, total, page, limit, message = 'Data retrieved successfully') => {
  const totalPages = Math.ceil(total / limit);
  
  return successResponse(
    items,
    message,
    {
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  );
};

/**
 * 验证错误响应
 * @param {Array} validationErrors - express-validator错误数组
 * @returns {Object} 格式化的验证错误响应
 */
const validationErrorResponse = (validationErrors) => {
  const errors = validationErrors.map(error => ({
    field: error.path || error.param,
    message: error.msg,
    value: error.value
  }));

  return errorResponse(
    'Validation failed',
    'VALIDATION_ERROR',
    errors
  );
};

/**
 * 认证错误响应
 * @param {string} message - 错误消息
 * @param {string} code - 错误代码
 * @returns {Object} 格式化的认证错误响应
 */
const authErrorResponse = (message = 'Authentication failed', code = 'AUTH_ERROR') => {
  return errorResponse(message, code);
};

/**
 * Permission error response
 * @param {string} message - Error message
 * @returns {Object} Formatted permission error response
 */
const forbiddenResponse = (message = 'Access denied') => {
  return errorResponse(message, 'FORBIDDEN');
};

/**
 * Resource not found response
 * @param {string} resource - Resource name
 * @returns {Object} Formatted 404 response
 */
const notFoundResponse = (resource = 'Resource') => {
  return errorResponse(`${resource} not found`, 'NOT_FOUND');
};

/**
 * Server error response
 * @param {Error} error - Error object
 * @returns {Object} Formatted server error response
 */
const serverErrorResponse = (error) => {
  const debug = process.env.NODE_ENV === 'development' ? {
    stack: error.stack,
    name: error.name
  } : null;

  return errorResponse(
    'Internal server error',
    'SERVER_ERROR',
    null,
    debug
  );
};

/**
 * 速率限制错误响应
 * @param {string} message - 错误消息
 * @returns {Object} 格式化的速率限制错误响应
 */
const rateLimitResponse = (message = 'Too many requests') => {
  return errorResponse(message, 'RATE_LIMIT');
};

module.exports = {
  successResponse,
  errorResponse,
  authSuccessResponse,
  paginatedResponse,
  validationErrorResponse,
  authErrorResponse,
  forbiddenResponse,
  notFoundResponse,
  serverErrorResponse,
  rateLimitResponse
};
