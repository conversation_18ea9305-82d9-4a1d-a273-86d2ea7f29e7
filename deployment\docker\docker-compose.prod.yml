version: '3.8'

# <PERSON><PERSON><PERSON> 生产环境 Docker Compose 配置
# 包含负载均衡、SSL终端、监控、日志聚合等生产级功能

services:
  # Nginx 反向代理和负载均衡
  nginx:
    image: nginx:alpine
    container_name: onenews-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./config/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - frontend-1
      - frontend-2
      - backend-1
      - backend-2
    restart: unless-stopped
    networks:
      - onenews-network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务 - 实例1
  frontend-1:
    build:
      context: ./Frontend
      dockerfile: Dockerfile.prod
    container_name: onenews-frontend-1
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://api.onenews.com
      - PORT=3000
    volumes:
      - ./logs/frontend:/app/logs
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - backend-1
      - backend-2

  # 前端服务 - 实例2
  frontend-2:
    build:
      context: ./Frontend
      dockerfile: Dockerfile.prod
    container_name: onenews-frontend-2
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://api.onenews.com
      - PORT=3000
    volumes:
      - ./logs/frontend:/app/logs
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - backend-1
      - backend-2

  # 后端服务 - 实例1
  backend-1:
    build:
      context: ./Backend
      dockerfile: Dockerfile.prod
    container_name: onenews-backend-1
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - REDIS_URL=redis://redis:6379
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASS=${EMAIL_PASS}
      - FRONTEND_URL=https://onenews.com
    volumes:
      - ./uploads:/app/uploads
      - ./logs/backend:/app/logs
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - postgres
      - redis

  # 后端服务 - 实例2
  backend-2:
    build:
      context: ./Backend
      dockerfile: Dockerfile.prod
    container_name: onenews-backend-2
    environment:
      - NODE_ENV=production
      - PORT=5000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - SESSION_SECRET=${SESSION_SECRET}
      - REDIS_URL=redis://redis:6379
      - EMAIL_HOST=${EMAIL_HOST}
      - EMAIL_PORT=${EMAIL_PORT}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASS=${EMAIL_PASS}
      - FRONTEND_URL=https://onenews.com
    volumes:
      - ./uploads:/app/uploads
      - ./logs/backend:/app/logs
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - postgres
      - redis

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: onenews-postgres
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./config/postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./logs/postgres:/var/log/postgresql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - onenews-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: onenews-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/etc/redis/redis.conf
      - ./logs/redis:/var/log/redis
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - onenews-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    container_name: onenews-prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./config/prometheus:/etc/prometheus
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - onenews-network

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    container_name: onenews-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - prometheus

  # ELK Stack - Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: onenews-elasticsearch
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped
    networks:
      - onenews-network

  # ELK Stack - Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: onenews-logstash
    volumes:
      - ./config/logstash:/usr/share/logstash/pipeline
      - ./logs:/var/log/onenews
    ports:
      - "5044:5044"
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - elasticsearch

  # ELK Stack - Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: onenews-kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    restart: unless-stopped
    networks:
      - onenews-network
    depends_on:
      - elasticsearch

# 网络配置
networks:
  onenews-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
