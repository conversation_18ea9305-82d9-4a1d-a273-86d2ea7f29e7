import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import FollowButton from '../FollowButton';
import { AuthProvider } from '@/contexts/AuthContext';

// Mock the AuthContext
const mockAuthContext = {
  user: { id: 1, username: 'testuser', email: '<EMAIL>', role: 'user' },
  token: 'mock-token',
  isAuthenticated: true,
  login: jest.fn(),
  register: jest.fn(),
  logout: jest.fn(),
  error: '',
  setError: jest.fn(),
  clearError: jest.fn(),
  updateUser: jest.fn(),
  resetPassword: jest.fn(),
  isLoading: false,
};

jest.mock('@/contexts/AuthContext', () => ({
  ...jest.requireActual('@/contexts/AuthContext'),
  useAuth: () => mockAuthContext,
}));

// Mock the Toast component
jest.mock('@/components/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn(),
  }),
}));

// Mock fetch
global.fetch = jest.fn();

const renderFollowButton = (props = {}) => {
  const defaultProps = {
    userId: 2,
    onFollowChange: jest.fn(),
    ...props,
  };

  return render(
    <AuthProvider>
      <FollowButton {...defaultProps} />
    </AuthProvider>
  );
};

describe('FollowButton', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('Authentication Requirements', () => {
    it('should not render for unauthenticated users', () => {
      mockAuthContext.isAuthenticated = false;
      mockAuthContext.user = null;

      renderFollowButton();

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should not render for own profile', () => {
      renderFollowButton({ userId: 1 }); // Same as current user ID

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('should render for other users when authenticated', () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        });

      renderFollowButton();

      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('Follow Status Check', () => {
    it('should check follow status on mount', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        });

      renderFollowButton();

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('http://localhost:5000/api/follows/check/2', {
          headers: {
            Authorization: 'Bearer mock-token',
          },
        });
      });
    });

    it('should fetch follower count on mount', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        });

      renderFollowButton();

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('http://localhost:5000/api/follows/2/followers?limit=1');
      });
    });
  });

  describe('Follow/Unfollow Actions', () => {
    it('should show "Follow" button when not following', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        });

      renderFollowButton();

      await waitFor(() => {
        expect(screen.getByText('Follow')).toBeInTheDocument();
      });
    });

    it('should show "Following" button when already following', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: true } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        });

      renderFollowButton();

      await waitFor(() => {
        expect(screen.getByText('Following')).toBeInTheDocument();
      });
    });

    it('should handle follow action', async () => {
      const user = userEvent.setup();
      const mockOnFollowChange = jest.fn();

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        });

      renderFollowButton({ onFollowChange: mockOnFollowChange });

      await waitFor(() => {
        expect(screen.getByText('Follow')).toBeInTheDocument();
      });

      const followButton = screen.getByText('Follow');

      await act(async () => {
        await user.click(followButton);
      });

      expect(fetch).toHaveBeenCalledWith('http://localhost:5000/api/follows', {
        method: 'POST',
        headers: {
          Authorization: 'Bearer mock-token',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ followingId: 2 }),
      });

      expect(mockOnFollowChange).toHaveBeenCalledWith(true, 11);
    });

    it('should handle unfollow action', async () => {
      const user = userEvent.setup();
      const mockOnFollowChange = jest.fn();

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: true } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        });

      renderFollowButton({ onFollowChange: mockOnFollowChange });

      await waitFor(() => {
        expect(screen.getByText('Following')).toBeInTheDocument();
      });

      const unfollowButton = screen.getByText('Following');

      await act(async () => {
        await user.click(unfollowButton);
      });

      expect(fetch).toHaveBeenCalledWith('http://localhost:5000/api/follows/2', {
        method: 'DELETE',
        headers: {
          Authorization: 'Bearer mock-token',
          'Content-Type': 'application/json',
        },
      });

      expect(mockOnFollowChange).toHaveBeenCalledWith(false, 9);
    });

    it('should show loading state during follow action', async () => {
      const user = userEvent.setup();

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        })
        .mockImplementation(() => new Promise(() => {})); // Never resolves

      renderFollowButton();

      await waitFor(() => {
        expect(screen.getByText('Follow')).toBeInTheDocument();
      });

      const followButton = screen.getByText('Follow');

      await act(async () => {
        await user.click(followButton);
      });

      expect(screen.getByText('Following...')).toBeInTheDocument();
      expect(followButton).toBeDisabled();
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const user = userEvent.setup();

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        })
        .mockRejectedValueOnce(new Error('Network error'));

      renderFollowButton();

      await waitFor(() => {
        expect(screen.getByText('Follow')).toBeInTheDocument();
      });

      const followButton = screen.getByText('Follow');

      await act(async () => {
        await user.click(followButton);
      });

      // Should still show Follow button after error
      await waitFor(() => {
        expect(screen.getByText('Follow')).toBeInTheDocument();
      });
    });
  });

  describe('Follower Count Display', () => {
    it('should display follower count', async () => {
      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 42 } } }),
        });

      renderFollowButton();

      await waitFor(() => {
        expect(screen.getByText('42')).toBeInTheDocument();
      });
    });

    it('should update follower count after follow action', async () => {
      const user = userEvent.setup();

      (fetch as jest.Mock)
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { isFollowing: false } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true, data: { pagination: { total: 10 } } }),
        })
        .mockResolvedValueOnce({
          ok: true,
          json: async () => ({ success: true }),
        });

      renderFollowButton();

      await waitFor(() => {
        expect(screen.getByText('10')).toBeInTheDocument();
      });

      const followButton = screen.getByText('Follow');

      await act(async () => {
        await user.click(followButton);
      });

      await waitFor(() => {
        expect(screen.getByText('11')).toBeInTheDocument();
      });
    });
  });
});
