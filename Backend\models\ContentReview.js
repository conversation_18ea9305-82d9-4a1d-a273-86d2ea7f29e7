const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ContentReview = sequelize.define(
  'ContentReview',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    contentType: {
      type: DataTypes.ENUM('article', 'draft', 'comment', 'media_file', 'user_profile'),
      allowNull: false,
      comment: '审核内容类型',
    },
    contentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: '内容ID',
    },
    submitterId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: '提交审核的用户ID',
    },
    reviewerId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: '审核员ID',
    },
    status: {
      type: DataTypes.ENUM(
        'pending',
        'in_review',
        'approved',
        'rejected',
        'needs_revision',
        'escalated'
      ),
      allowNull: false,
      defaultValue: 'pending',
      comment: '审核状态',
    },
    priority: {
      type: DataTypes.ENUM('low', 'normal', 'high', 'urgent'),
      allowNull: false,
      defaultValue: 'normal',
      comment: '审核优先级',
    },
    aiReviewResult: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'AI审核结果',
    },
    aiScore: {
      type: DataTypes.DECIMAL(3, 2),
      allowNull: true,
      comment: 'AI审核评分 (0.00-1.00)',
    },
    aiFlags: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      comment: 'AI标记的问题类型',
    },
    humanReviewNotes: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '人工审核备注',
    },
    rejectionReason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '拒绝原因',
    },
    revisionSuggestions: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '修改建议',
    },
    flaggedContent: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: '被标记的具体内容片段',
    },
    reviewCategories: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
      comment: '审核分类 [fake_news, hate_speech, violence, adult_content, spam, etc.]',
    },
    riskLevel: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'critical'),
      allowNull: false,
      defaultValue: 'low',
      comment: '风险等级',
    },
    autoApproved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: '是否自动通过',
    },
    reviewStartedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '开始审核时间',
    },
    reviewCompletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: '完成审核时间',
    },
    escalationReason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '升级原因',
    },
    metadata: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: {},
      comment: '额外元数据',
    },
  },
  {
    tableName: 'content_reviews',
    timestamps: true,
    indexes: [
      {
        fields: ['contentType', 'contentId'],
      },
      {
        fields: ['submitterId'],
      },
      {
        fields: ['reviewerId'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['priority'],
      },
      {
        fields: ['riskLevel'],
      },
      {
        fields: ['autoApproved'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['reviewStartedAt'],
      },
      {
        fields: ['reviewCompletedAt'],
      },
      {
        fields: ['status', 'priority'],
      },
      {
        fields: ['reviewerId', 'status'],
      },
    ],
  }
);

// 实例方法
ContentReview.prototype.startReview = async function (reviewerId) {
  this.reviewerId = reviewerId;
  this.status = 'in_review';
  this.reviewStartedAt = new Date();
  await this.save();
};

ContentReview.prototype.approve = async function (reviewerId, notes = null) {
  this.reviewerId = reviewerId;
  this.status = 'approved';
  this.reviewCompletedAt = new Date();
  if (notes) {
    this.humanReviewNotes = notes;
  }
  await this.save();
};

ContentReview.prototype.reject = async function (reviewerId, reason, suggestions = null) {
  this.reviewerId = reviewerId;
  this.status = 'rejected';
  this.reviewCompletedAt = new Date();
  this.rejectionReason = reason;
  if (suggestions) {
    this.revisionSuggestions = suggestions;
  }
  await this.save();
};

ContentReview.prototype.escalate = async function (reason) {
  this.status = 'escalated';
  this.priority = 'high';
  this.escalationReason = reason;
  await this.save();
};

ContentReview.prototype.getReviewDuration = function () {
  if (!this.reviewStartedAt || !this.reviewCompletedAt) {
    return null;
  }
  return Math.round((this.reviewCompletedAt - this.reviewStartedAt) / (1000 * 60)); // 分钟
};

// 类方法
ContentReview.getPendingReviews = async function (options = {}) {
  const { priority = null, contentType = null, riskLevel = null, page = 1, limit = 20 } = options;

  const whereClause = {
    status: ['pending', 'in_review'],
  };

  if (priority) whereClause.priority = priority;
  if (contentType) whereClause.contentType = contentType;
  if (riskLevel) whereClause.riskLevel = riskLevel;

  return await this.findAndCountAll({
    where: whereClause,
    order: [
      ['priority', 'DESC'],
      ['createdAt', 'ASC'],
    ],
    limit,
    offset: (page - 1) * limit,
    include: [
      {
        model: sequelize.models.User,
        as: 'submitter',
        attributes: ['id', 'username', 'email'],
      },
      {
        model: sequelize.models.User,
        as: 'reviewer',
        attributes: ['id', 'username', 'email'],
        required: false,
      },
    ],
  });
};

ContentReview.getReviewerStats = async function (reviewerId, days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  const stats = await this.findAll({
    where: {
      reviewerId,
      reviewCompletedAt: {
        [sequelize.Sequelize.Op.gte]: startDate,
      },
    },
    attributes: [
      'status',
      [sequelize.fn('COUNT', '*'), 'count'],
      [
        sequelize.fn(
          'AVG',
          sequelize.literal('EXTRACT(EPOCH FROM ("reviewCompletedAt" - "reviewStartedAt"))/60')
        ),
        'avgReviewTime',
      ],
    ],
    group: ['status'],
    raw: true,
  });

  return stats;
};

module.exports = ContentReview;
