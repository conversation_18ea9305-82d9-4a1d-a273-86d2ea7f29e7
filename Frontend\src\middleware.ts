import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// 这些路径不需要认证检查
const publicPaths = ['/auth/login', '/auth/register', '/auth/callback', '/auth/forgot-password', '/auth/reset-password'];
const HOME_PATH = '/';
const LOGIN_PATH = '/auth/login';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 检查Supabase认证token
  const supabaseToken = request.cookies.get('sb-wdpprzeflzlardkmncfk-auth-token')?.value;
  
  // 重定向旧的登录路径到新的认证路径
  const redirectMap: { [key: string]: string } = {
    '/login': '/auth/login',
    '/register': '/auth/register',
    '/simple-login': '/auth/login',
    '/simple-register': '/auth/register',
    '/forgot-password': '/auth/forgot-password'
  };

  if (redirectMap[pathname]) {
    return NextResponse.redirect(new URL(redirectMap[pathname], request.url));
  }
  
  // 检查是否为公开路径
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path));
  
  // 只对明确需要认证的路径进行强制检查
  const strictProtectedPaths = ['/admin'];
  const needsStrictAuth = strictProtectedPaths.some(path => pathname.startsWith(path));
  
  // 处理严格保护的路径
  if (needsStrictAuth && !supabaseToken) {
    const loginUrl = new URL(LOGIN_PATH, request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }
  
  // 已登录用户访问登录页面时重定向到首页
  if (supabaseToken && isPublicPath && !request.nextUrl.searchParams.has('redirect')) {
    return NextResponse.redirect(new URL(HOME_PATH, request.url));
  }
  
  // 其他所有路径直接放行，让页面组件自己处理认证检查
  return NextResponse.next();
}

export const config = {
  matcher: [
    // 只匹配需要的路径，排除静态资源和API路径
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)'
  ],
};
