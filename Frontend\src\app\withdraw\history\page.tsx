'use client';

import { useState, useEffect } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useData } from '@/contexts/DataContext';
import { useRouter } from 'next/navigation';

interface WithdrawalRequest {
  id: number;
  amount: number;
  netAmount: number;
  withdrawalMethod: string;
  status: string;
  country: string;
  taxAmount: number;
  feeAmount: number;
  scheduledPaymentDate: string;
  createdAt: string;
}

export default function WithdrawHistoryPage() {
  const { user, isLoading } = useSimpleAuth();
  const { withdrawalRequests } = useData();
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!isLoading && !user) {
      router.push('/auth/login');
      return;
    }
    
    if (user) {
      fetchHistory();
    }
    
    // 监听storage变化，实时更新数据
    const handleStorageChange = () => {
      fetchHistory();
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [user, isLoading, router]);

  const fetchHistory = async () => {
    try {
      // 从localStorage获取提现记录（模拟实时数据）
      const savedRequests = localStorage.getItem('withdrawalRequests');
      let mockData: WithdrawalRequest[] = [];
      
      if (savedRequests) {
        mockData = JSON.parse(savedRequests);
      } else {
        // 默认数据
        mockData = [
          {
            id: 1,
            amount: 500.00,
            netAmount: 365.00,
            withdrawalMethod: 'bank_card',
            status: 'completed',
            country: 'US',
            taxAmount: 120.00,
            feeAmount: 15.00,
            scheduledPaymentDate: '2024-05-15',
            createdAt: '2024-05-10T10:30:00Z'
          },
          {
            id: 2,
            amount: 300.00,
            netAmount: 219.00,
            withdrawalMethod: 'paypal',
            status: 'processing',
            country: 'US',
            taxAmount: 72.00,
            feeAmount: 9.00,
            scheduledPaymentDate: '2024-05-22',
            createdAt: '2024-05-18T14:20:00Z'
          }
        ];
        localStorage.setItem('withdrawalRequests', JSON.stringify(mockData));
      }
      
      setRequests(mockData.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()));
    } catch (error) {
      console.error('Failed to fetch withdrawal history:', error);
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const getStatusText = (status: string) => {
    const statusMap = {
      pending: 'Pending',
      verified: 'Verified',
      processing: 'Processing',
      completed: 'Completed',
      rejected: 'Rejected',
    };
    return statusMap[status as keyof typeof statusMap] || status;
  };

  const getStatusColor = (status: string) => {
    const colorMap = {
      pending: 'text-yellow-600 bg-yellow-100',
      verified: 'text-blue-600 bg-blue-100',
      processing: 'text-purple-600 bg-purple-100',
      completed: 'text-green-600 bg-green-100',
      rejected: 'text-red-600 bg-red-100',
    };
    return colorMap[status as keyof typeof colorMap] || 'text-gray-600 bg-gray-100';
  };

  const getMethodText = (method: string) => {
    const methodMap = {
      bank_card: 'Bank Card',
      paypal: 'PayPal',
      crypto_btc: 'Bitcoin',
      crypto_usdt: 'USDT',
    };
    return methodMap[method as keyof typeof methodMap] || method;
  };

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Withdrawal History</h1>
        <button
          onClick={() => router.push('/withdraw')}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          New Withdrawal
        </button>
      </div>

      {withdrawalRequests.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">💰</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Withdrawal Records</h3>
          <p className="text-gray-600 mb-4">You haven't made any withdrawals yet</p>
          <button
            onClick={() => router.push('/withdraw')}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Withdraw Now
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {withdrawalRequests.map((request) => (
            <div key={request.id} className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-lg font-semibold">
                    Withdrawal Request #{request.id}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {new Date(request.createdAt).toLocaleString()}
                  </p>
                </div>
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(request.status)}`}>
                  {getStatusText(request.status)}
                </span>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <p className="text-gray-600 text-sm">Request Amount</p>
                  <p className="font-semibold">${request.amount}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm">Net Amount</p>
                  <p className="font-semibold text-green-600">${request.netAmount}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm">Payment Method</p>
                  <p className="font-semibold">{getMethodText(request.withdrawalMethod)}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm">Country</p>
                  <p className="font-semibold">{request.country}</p>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded text-sm">
                <div className="flex justify-between mb-1">
                  <span>Tax:</span>
                  <span className="text-red-600">-${request.taxAmount}</span>
                </div>
                <div className="flex justify-between mb-1">
                  <span>Processing Fee:</span>
                  <span className="text-red-600">-${request.feeAmount}</span>
                </div>
                {request.scheduledPaymentDate && (
                  <div className="flex justify-between text-xs text-gray-500 mt-2">
                    <span>Estimated Processing:</span>
                    <span>{new Date(request.scheduledPaymentDate).toLocaleDateString()} (Wednesday)</span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}