# OneNews GitHub部署脚本
# 使用方法: .\deploy-to-github.ps1

Write-Host "🚀 OneNews GitHub部署脚本" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# 检查Git是否已安装
try {
    $null = git --version
    Write-Host "✅ Git已安装" -ForegroundColor Green
}
catch {
    Write-Host "❌ 错误: 未找到Git，请先安装Git" -ForegroundColor Red
    exit 1
}

# 检查是否在正确的目录
if (-not (Test-Path "package.json") -and -not (Test-Path "README.md")) {
    Write-Host "❌ 错误: 请在OneNews项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

Write-Host "📁 当前目录: $(Get-Location)" -ForegroundColor Yellow

# 检查是否已经是Git仓库
if (-not (Test-Path ".git")) {
    Write-Host "🔧 初始化Git仓库..." -ForegroundColor Yellow
    git init
    Write-Host "✅ Git仓库初始化完成" -ForegroundColor Green
}
else {
    Write-Host "✅ Git仓库已存在" -ForegroundColor Green
}

# 添加远程仓库
$remoteUrl = "https://github.com/Jacken22/OneNews.git"
Write-Host "🔗 添加远程仓库: $remoteUrl" -ForegroundColor Yellow

try {
    # 检查是否已有origin远程仓库
    $existingRemote = git remote get-url origin 2>$null
    if ($existingRemote) {
        Write-Host "⚠️  远程仓库已存在: $existingRemote" -ForegroundColor Yellow
        $response = Read-Host "是否要更新远程仓库URL? (y/N)"
        if ($response -eq "y" -or $response -eq "Y") {
            git remote set-url origin $remoteUrl
            Write-Host "✅ 远程仓库URL已更新" -ForegroundColor Green
        }
    }
    else {
        git remote add origin $remoteUrl
        Write-Host "✅ 远程仓库已添加" -ForegroundColor Green
    }
}
catch {
    Write-Host "⚠️  添加远程仓库时出现问题，继续执行..." -ForegroundColor Yellow
}

# 检查环境变量文件
Write-Host "🔍 检查环境变量文件..." -ForegroundColor Yellow
if (-not (Test-Path "Backend\.env.example")) {
    Write-Host "⚠️  Backend\.env.example 不存在，建议创建此文件" -ForegroundColor Yellow
}
if (-not (Test-Path "Frontend\.env.example")) {
    Write-Host "⚠️  Frontend\.env.example 不存在，建议创建此文件" -ForegroundColor Yellow
}

# 添加所有文件到Git
Write-Host "📦 添加文件到Git..." -ForegroundColor Yellow
git add .

# 检查是否有文件被添加
$status = git status --porcelain
if (-not $status) {
    Write-Host "ℹ️  没有新的更改需要提交" -ForegroundColor Blue
}
else {
    Write-Host "📝 准备提交的文件:" -ForegroundColor Yellow
    git status --short
}

# 提交更改
$commitMessage = Read-Host "请输入提交信息 (默认: 'Initial commit - OneNews project')"
if (-not $commitMessage) {
    $commitMessage = "Initial commit - OneNews project"
}

Write-Host "💾 提交更改..." -ForegroundColor Yellow
try {
    git commit -m "$commitMessage"
    Write-Host "✅ 提交成功" -ForegroundColor Green
}
catch {
    Write-Host "ℹ️  没有新的更改需要提交" -ForegroundColor Blue
}

# 推送到GitHub
Write-Host "🚀 推送到GitHub..." -ForegroundColor Yellow
Write-Host "⚠️  如果这是第一次推送，可能需要输入GitHub用户名和密码/token" -ForegroundColor Yellow

try {
    # 尝试推送到main分支
    git branch -M main
    git push -u origin main
    Write-Host "✅ 成功推送到GitHub!" -ForegroundColor Green
    Write-Host "🌐 项目地址: https://github.com/Jacken22/OneNews" -ForegroundColor Cyan
}
catch {
    Write-Host "❌ 推送失败，可能的原因:" -ForegroundColor Red
    Write-Host "   1. 网络连接问题" -ForegroundColor Red
    Write-Host "   2. GitHub认证问题" -ForegroundColor Red
    Write-Host "   3. 仓库权限问题" -ForegroundColor Red
    Write-Host "   4. 仓库已存在且有冲突" -ForegroundColor Red
    Write-Host "" -ForegroundColor Red
    Write-Host "💡 解决方案:" -ForegroundColor Yellow
    Write-Host "   1. 检查网络连接" -ForegroundColor Yellow
    Write-Host "   2. 确保GitHub仓库存在且有写入权限" -ForegroundColor Yellow
    Write-Host "   3. 配置Git认证: git config --global user.name 'Your Name'" -ForegroundColor Yellow
    Write-Host "   4. 配置Git邮箱: git config --global user.email '<EMAIL>'" -ForegroundColor Yellow
    Write-Host "   5. 如果仓库已存在，先执行: git pull origin main --allow-unrelated-histories" -ForegroundColor Yellow
}

Write-Host "" -ForegroundColor White
Write-Host "📋 部署完成检查清单:" -ForegroundColor Green
Write-Host "✅ Git仓库已初始化" -ForegroundColor Green
Write-Host "✅ 远程仓库已配置" -ForegroundColor Green
Write-Host "✅ .gitignore文件已配置" -ForegroundColor Green
Write-Host "✅ 环境变量模板已创建" -ForegroundColor Green
Write-Host "✅ 代码已提交到本地仓库" -ForegroundColor Green

Write-Host "" -ForegroundColor White
Write-Host "🔗 GitHub仓库地址: https://github.com/Jacken22/OneNews" -ForegroundColor Cyan
Write-Host "📖 项目文档: https://github.com/Jacken22/OneNews/blob/main/README.md" -ForegroundColor Cyan

Write-Host "" -ForegroundColor White
Write-Host "🎉 部署脚本执行完成!" -ForegroundColor Green
