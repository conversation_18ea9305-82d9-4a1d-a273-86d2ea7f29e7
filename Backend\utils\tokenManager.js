/**
 * Token管理工具
 * 统一管理JWT、重置密码token、邮箱验证token等
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { SECURITY_CONFIG } = require('../config/security');

class TokenManager {
  /**
   * 生成JWT访问令牌
   * @param {Object} user - 用户对象
   * @returns {string} JWT token
   */
  static generateAccessToken(user) {
    const payload = {
      userId: user.id,
      username: user.username,
      role: user.role,
      iss: SECURITY_CONFIG.JWT.ISSUER,
      aud: SECURITY_CONFIG.JWT.AUDIENCE,
      iat: Math.floor(Date.now() / 1000),
      sessionId: crypto.randomBytes(16).toString('hex')
    };

    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: SECURITY_CONFIG.JWT.ACCESS_TOKEN_EXPIRES,
      algorithm: SECURITY_CONFIG.JWT.ALGORITHM
    });
  }

  /**
   * 生成刷新令牌
   * @param {Object} user - 用户对象
   * @returns {string} Refresh token
   */
  static generateRefreshToken(user) {
    const payload = {
      userId: user.id,
      type: 'refresh',
      iss: SECURITY_CONFIG.JWT.ISSUER,
      aud: SECURITY_CONFIG.JWT.AUDIENCE,
      iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: SECURITY_CONFIG.JWT.REFRESH_TOKEN_EXPIRES,
      algorithm: SECURITY_CONFIG.JWT.ALGORITHM
    });
  }

  /**
   * 验证JWT令牌
   * @param {string} token - JWT token
   * @returns {Object} 解码后的payload
   */
  static verifyToken(token) {
    return jwt.verify(token, process.env.JWT_SECRET, {
      algorithms: [SECURITY_CONFIG.JWT.ALGORITHM],
      issuer: SECURITY_CONFIG.JWT.ISSUER,
      audience: SECURITY_CONFIG.JWT.AUDIENCE
    });
  }

  /**
   * 生成密码重置令牌
   * @returns {Object} { token, expires }
   */
  static generatePasswordResetToken() {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + SECURITY_CONFIG.TOKENS.PASSWORD_RESET_EXPIRES);
    
    return { token, expires };
  }

  /**
   * 生成邮箱验证令牌
   * @returns {Object} { token, expires }
   */
  static generateEmailVerificationToken() {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + SECURITY_CONFIG.TOKENS.EMAIL_VERIFICATION_EXPIRES);
    
    return { token, expires };
  }

  /**
   * 验证密码重置令牌是否有效
   * @param {string} token - 重置令牌
   * @param {Date} expires - 过期时间
   * @returns {boolean} 是否有效
   */
  static isPasswordResetTokenValid(token, expires) {
    if (!token || !expires) {
      return false;
    }
    
    return new Date() < new Date(expires);
  }

  /**
   * 验证邮箱验证令牌是否有效
   * @param {string} token - 验证令牌
   * @param {Date} expires - 过期时间
   * @returns {boolean} 是否有效
   */
  static isEmailVerificationTokenValid(token, expires) {
    if (!token || !expires) {
      return false;
    }
    
    return new Date() < new Date(expires);
  }

  /**
   * 生成CSRF令牌
   * @returns {string} CSRF token
   */
  static generateCSRFToken() {
    return crypto.randomBytes(SECURITY_CONFIG.TOKENS.CSRF_TOKEN_LENGTH).toString('hex');
  }

  /**
   * 从JWT中提取用户信息
   * @param {string} token - JWT token
   * @returns {Object|null} 用户信息
   */
  static extractUserFromToken(token) {
    try {
      const decoded = this.verifyToken(token);
      return {
        id: decoded.userId,
        username: decoded.username,
        role: decoded.role,
        sessionId: decoded.sessionId
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * 检查令牌是否即将过期
   * @param {string} token - JWT token
   * @param {number} thresholdMinutes - 阈值（分钟）
   * @returns {boolean} 是否即将过期
   */
  static isTokenExpiringSoon(token, thresholdMinutes = 30) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        return true;
      }
      
      const expirationTime = decoded.exp * 1000; // 转换为毫秒
      const thresholdTime = Date.now() + (thresholdMinutes * 60 * 1000);
      
      return expirationTime < thresholdTime;
    } catch (error) {
      return true;
    }
  }

  /**
   * 获取令牌剩余有效时间
   * @param {string} token - JWT token
   * @returns {number} 剩余时间（秒），-1表示无效
   */
  static getTokenRemainingTime(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) {
        return -1;
      }
      
      const remainingTime = decoded.exp - Math.floor(Date.now() / 1000);
      return Math.max(0, remainingTime);
    } catch (error) {
      return -1;
    }
  }

  /**
   * 生成API密钥
   * @param {string} prefix - 前缀
   * @returns {string} API key
   */
  static generateAPIKey(prefix = 'nz') {
    const randomPart = crypto.randomBytes(32).toString('hex');
    return `${prefix}_${randomPart}`;
  }

  /**
   * 令牌黑名单管理（简单实现，生产环境建议使用Redis）
   */
  static tokenBlacklist = new Set();

  /**
   * 将令牌加入黑名单
   * @param {string} token - JWT token
   */
  static blacklistToken(token) {
    this.tokenBlacklist.add(token);
    
    // 定期清理过期的黑名单令牌
    setTimeout(() => {
      this.tokenBlacklist.delete(token);
    }, SECURITY_CONFIG.JWT.ACCESS_TOKEN_EXPIRES);
  }

  /**
   * 检查令牌是否在黑名单中
   * @param {string} token - JWT token
   * @returns {boolean} 是否在黑名单中
   */
  static isTokenBlacklisted(token) {
    return this.tokenBlacklist.has(token);
  }

  /**
   * 清理过期的黑名单令牌
   */
  static cleanupBlacklist() {
    // 这里可以实现更复杂的清理逻辑
    // 在生产环境中，建议使用Redis的TTL功能
  }
}

module.exports = TokenManager;
