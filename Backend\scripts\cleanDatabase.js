const { sequelize } = require('../config/database');

/**
 * 清理数据库 - 删除所有用户相关数据
 */

async function cleanDatabase() {
  try {
    console.log('🧹 开始清理数据库...');
    console.log('==========================================');

    // 删除所有用户数据
    await sequelize.query('DELETE FROM users WHERE 1=1');
    console.log('✅ 已清理 users 表');

    // 重置自增ID
    await sequelize.query('ALTER SEQUENCE users_id_seq RESTART WITH 1');
    console.log('✅ 已重置用户ID序列');

    // 清理其他相关表（如果存在）
    const tables = ['sessions', 'user_tokens', 'password_resets'];
    
    for (const table of tables) {
      try {
        await sequelize.query(`DELETE FROM ${table} WHERE 1=1`);
        console.log(`✅ 已清理 ${table} 表`);
      } catch (error) {
        console.log(`⚠️  表 ${table} 不存在或已清理`);
      }
    }

    console.log('\n🎉 数据库清理完成！');
    console.log('==========================================');

  } catch (error) {
    console.error('❌ 数据库清理失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanDatabase()
    .then(() => {
      console.log('✅ 清理脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 清理脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { cleanDatabase };
