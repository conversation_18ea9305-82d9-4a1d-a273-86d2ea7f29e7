'use client';

import React, { useState } from 'react';
import { Save, Mail, Send, TestTube, CheckCircle, AlertCircle } from 'lucide-react';

interface EmailSettings {
  smtpHost: string;
  smtpPort: number;
  smtpUsername: string;
  smtpPassword: string;
  smtpEncryption: 'none' | 'tls' | 'ssl';
  fromEmail: string;
  fromName: string;
  replyToEmail: string;
  enableEmailQueue: boolean;
  maxRetries: number;
  retryDelay: number;
  enableEmailLogs: boolean;
  emailProvider: 'smtp' | 'sendgrid' | 'mailgun' | 'ses';
  sendgridApiKey: string;
  mailgunApiKey: string;
  mailgunDomain: string;
  sesAccessKey: string;
  sesSecretKey: string;
  sesRegion: string;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  enabled: boolean;
}

const EmailSettingsPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [settings, setSettings] = useState<EmailSettings>({
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    smtpEncryption: 'tls',
    fromEmail: '<EMAIL>',
    fromName: 'Newzora',
    replyToEmail: '<EMAIL>',
    enableEmailQueue: true,
    maxRetries: 3,
    retryDelay: 5,
    enableEmailLogs: true,
    emailProvider: 'smtp',
    sendgridApiKey: '',
    mailgunApiKey: '',
    mailgunDomain: '',
    sesAccessKey: '',
    sesSecretKey: '',
    sesRegion: 'us-east-1'
  });

  const [emailTemplates] = useState<EmailTemplate[]>([
    { id: 'welcome', name: 'Welcome Email', subject: 'Welcome to Newzora!', enabled: true },
    { id: 'verification', name: 'Email Verification', subject: 'Verify your email address', enabled: true },
    { id: 'password-reset', name: 'Password Reset', subject: 'Reset your password', enabled: true },
    { id: 'notification', name: 'Notification Email', subject: 'You have new notifications', enabled: true },
    { id: 'newsletter', name: 'Newsletter', subject: 'Weekly Newsletter', enabled: false }
  ]);

  const handleInputChange = (field: keyof EmailSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSave = async () => {
    setLoading(true);
    try {
      // Mock API call - in real app, this would save to backend
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Email settings saved successfully!');
    } catch (error) {
      console.error('Failed to save settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleTestEmail = async () => {
    setTestLoading(true);
    setTestResult(null);
    
    try {
      // Mock test email - in real app, this would send a test email
      await new Promise(resolve => setTimeout(resolve, 2000));
      const success = Math.random() > 0.3; // 70% success rate for demo
      
      setTestResult({
        success,
        message: success 
          ? 'Test email sent successfully! Check your inbox.'
          : 'Failed to send test email. Please check your settings.'
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: 'Error occurred while sending test email.'
      });
    } finally {
      setTestLoading(false);
    }
  };

  const renderProviderSettings = () => {
    switch (settings.emailProvider) {
      case 'smtp':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Host
                </label>
                <input
                  type="text"
                  value={settings.smtpHost}
                  onChange={(e) => handleInputChange('smtpHost', e.target.value)}
                  className="form-input"
                  placeholder="smtp.gmail.com"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  SMTP Port
                </label>
                <input
                  type="number"
                  value={settings.smtpPort}
                  onChange={(e) => handleInputChange('smtpPort', parseInt(e.target.value))}
                  className="form-input"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Username
                </label>
                <input
                  type="text"
                  value={settings.smtpUsername}
                  onChange={(e) => handleInputChange('smtpUsername', e.target.value)}
                  className="form-input"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Password
                </label>
                <input
                  type="password"
                  value={settings.smtpPassword}
                  onChange={(e) => handleInputChange('smtpPassword', e.target.value)}
                  className="form-input"
                  placeholder="••••••••"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Encryption
              </label>
              <select
                value={settings.smtpEncryption}
                onChange={(e) => handleInputChange('smtpEncryption', e.target.value)}
                className="form-select"
              >
                <option value="none">None</option>
                <option value="tls">TLS</option>
                <option value="ssl">SSL</option>
              </select>
            </div>
          </div>
        );
        
      case 'sendgrid':
        return (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              SendGrid API Key
            </label>
            <input
              type="password"
              value={settings.sendgridApiKey}
              onChange={(e) => handleInputChange('sendgridApiKey', e.target.value)}
              className="form-input"
              placeholder="SG.••••••••"
            />
          </div>
        );
        
      case 'mailgun':
        return (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mailgun API Key
              </label>
              <input
                type="password"
                value={settings.mailgunApiKey}
                onChange={(e) => handleInputChange('mailgunApiKey', e.target.value)}
                className="form-input"
                placeholder="key-••••••••"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Mailgun Domain
              </label>
              <input
                type="text"
                value={settings.mailgunDomain}
                onChange={(e) => handleInputChange('mailgunDomain', e.target.value)}
                className="form-input"
                placeholder="mg.yourdomain.com"
              />
            </div>
          </div>
        );
        
      case 'ses':
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  AWS Access Key
                </label>
                <input
                  type="password"
                  value={settings.sesAccessKey}
                  onChange={(e) => handleInputChange('sesAccessKey', e.target.value)}
                  className="form-input"
                  placeholder="AKIA••••••••"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  AWS Secret Key
                </label>
                <input
                  type="password"
                  value={settings.sesSecretKey}
                  onChange={(e) => handleInputChange('sesSecretKey', e.target.value)}
                  className="form-input"
                  placeholder="••••••••"
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                AWS Region
              </label>
              <select
                value={settings.sesRegion}
                onChange={(e) => handleInputChange('sesRegion', e.target.value)}
                className="form-select"
              >
                <option value="us-east-1">US East (N. Virginia)</option>
                <option value="us-west-2">US West (Oregon)</option>
                <option value="eu-west-1">Europe (Ireland)</option>
                <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
              </select>
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Email Settings</h1>
          <p className="text-gray-600 mt-1">Configure email delivery and templates</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleTestEmail}
            disabled={testLoading}
            className="btn-secondary flex items-center"
          >
            {testLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
            ) : (
              <TestTube className="w-4 h-4 mr-2" />
            )}
            Test Email
          </button>
          <button
            onClick={handleSave}
            disabled={loading}
            className="btn-primary flex items-center"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            Save Changes
          </button>
        </div>
      </div>

      {/* Test Result */}
      {testResult && (
        <div className={`p-4 rounded-lg flex items-center ${
          testResult.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
        }`}>
          {testResult.success ? (
            <CheckCircle className="w-5 h-5 mr-2" />
          ) : (
            <AlertCircle className="w-5 h-5 mr-2" />
          )}
          {testResult.message}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Settings */}
        <div className="lg:col-span-2 space-y-6">
          {/* Email Provider */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Mail className="w-5 h-5 mr-2" />
              Email Provider
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Provider
                </label>
                <select
                  value={settings.emailProvider}
                  onChange={(e) => handleInputChange('emailProvider', e.target.value)}
                  className="form-select"
                >
                  <option value="smtp">SMTP</option>
                  <option value="sendgrid">SendGrid</option>
                  <option value="mailgun">Mailgun</option>
                  <option value="ses">Amazon SES</option>
                </select>
              </div>
              
              {renderProviderSettings()}
            </div>
          </div>

          {/* Sender Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <Send className="w-5 h-5 mr-2" />
              Sender Information
            </h2>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Email
                  </label>
                  <input
                    type="email"
                    value={settings.fromEmail}
                    onChange={(e) => handleInputChange('fromEmail', e.target.value)}
                    className="form-input"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    From Name
                  </label>
                  <input
                    type="text"
                    value={settings.fromName}
                    onChange={(e) => handleInputChange('fromName', e.target.value)}
                    className="form-input"
                    placeholder="Your Site Name"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reply-To Email
                </label>
                <input
                  type="email"
                  value={settings.replyToEmail}
                  onChange={(e) => handleInputChange('replyToEmail', e.target.value)}
                  className="form-input"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          {/* Queue Settings */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">
              Queue & Retry Settings
            </h2>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableEmailQueue"
                  checked={settings.enableEmailQueue}
                  onChange={(e) => handleInputChange('enableEmailQueue', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="enableEmailQueue" className="ml-2 text-sm text-gray-700">
                  Enable email queue for better performance
                </label>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Retries
                  </label>
                  <input
                    type="number"
                    value={settings.maxRetries}
                    onChange={(e) => handleInputChange('maxRetries', parseInt(e.target.value))}
                    className="form-input"
                    min="0"
                    max="10"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Retry Delay (minutes)
                  </label>
                  <input
                    type="number"
                    value={settings.retryDelay}
                    onChange={(e) => handleInputChange('retryDelay', parseInt(e.target.value))}
                    className="form-input"
                    min="1"
                    max="60"
                  />
                </div>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="enableEmailLogs"
                  checked={settings.enableEmailLogs}
                  onChange={(e) => handleInputChange('enableEmailLogs', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="enableEmailLogs" className="ml-2 text-sm text-gray-700">
                  Enable email delivery logs
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Email Templates */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Email Templates</h3>
            <div className="space-y-3">
              {emailTemplates.map((template) => (
                <div key={template.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">{template.name}</p>
                    <p className="text-sm text-gray-600">{template.subject}</p>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${
                    template.enabled ? 'bg-green-500' : 'bg-gray-300'
                  }`}></div>
                </div>
              ))}
            </div>
            <button className="w-full mt-4 btn-secondary text-sm">
              Manage Templates
            </button>
          </div>

          {/* Quick Stats */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Email Statistics</h3>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Sent Today</span>
                <span className="font-medium">247</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Delivery Rate</span>
                <span className="font-medium text-green-600">98.5%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Queue Size</span>
                <span className="font-medium">12</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Failed</span>
                <span className="font-medium text-red-600">3</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmailSettingsPage;
