import { createClient } from '@supabase/supabase-js';

// 从环境变量获取Supabase配置
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';

// 验证环境变量
if (!supabaseUrl || supabaseUrl === 'https://placeholder.supabase.co') {
  console.warn('Using placeholder Supabase configuration. Please configure your .env.local file.');
}

const realSupabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
});

// 导出 Supabase 客户端
export const supabase = realSupabase;

// 类型定义
export interface User {
  id: string;
  email: string;
  username?: string;
  display_name?: string;
  avatar_url?: string;
  role?: 'user' | 'admin' | 'moderator';
  created_at: string;
  updated_at?: string;
  user_metadata?: {
    username?: string;
    display_name?: string;
  };
}

export interface AuthResponse {
  user: User | null;
  session: any;
  error: any;
}

// 认证相关函数
export const authService = {
  // 用户注册
  async signUp(email: string, password: string, username: string, displayName?: string) {
    const { data, error } = await realSupabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          username,
          display_name: displayName || username,
        },
        emailRedirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/callback`,
      },
    });
    return { data, error };
  },

  // 用户登录
  async signIn(email: string, password: string) {
    const { data, error } = await realSupabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  },

  // 用户登出
  async signOut() {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  // 获取当前用户
  async getCurrentUser() {
    const {
      data: { user },
      error,
    } = await realSupabase.auth.getUser();
    return { user, error };
  },

  // 获取当前会话
  async getCurrentSession() {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();
    return { session, error };
  },

  // 重置密码
  async resetPassword(email: string) {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/reset-password`,
    });

    return { data, error };
  },

  // 更新密码
  async updatePassword(password: string) {
    const { data, error } = await supabase.auth.updateUser({
      password,
    });

    return { data, error };
  },

  // Google 社交登录
  async signInWithGoogle() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/callback`,
      },
    });

    return { data, error };
  },

  // Facebook 社交登录
  async signInWithFacebook() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'facebook',
      options: {
        redirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/callback`,
      },
    });

    return { data, error };
  },

  // Twitter 社交登录
  async signInWithTwitter() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'twitter',
      options: {
        redirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/callback`,
      },
    });

    return { data, error };
  },

  // Apple 社交登录
  async signInWithApple() {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'apple',
      options: {
        redirectTo: `${typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_FRONTEND_URL}/auth/callback`,
      },
    });

    return { data, error };
  },
};

// 监听认证状态变化
export const onAuthStateChange = (callback: (event: string, session: any) => void) => {
  return supabase.auth.onAuthStateChange(callback);
};
