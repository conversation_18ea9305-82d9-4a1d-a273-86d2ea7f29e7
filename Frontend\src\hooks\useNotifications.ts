'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { socketService } from '@/services/socketService';

export interface Notification {
  id: string;
  type:
    | 'like'
    | 'comment'
    | 'follow'
    | 'system'
    | 'article_published'
    | 'article_approved'
    | 'security';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  user?: {
    name: string;
    avatar: string;
  };
  data?: {
    articleId?: string;
    articleTitle?: string;
    commentId?: string;
    userId?: string;
  };
}

export function useNotifications() {
  const { user, isAuthenticated } = useSimpleAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 获取通知列表 - 暂时禁用，因为SimpleAuth不提供token
  const fetchNotifications = useCallback(async () => {
    if (!isAuthenticated) return;

    // TODO: 当有完整的认证系统时，重新启用此功能
    console.log('📝 通知功能暂时禁用 - 等待完整认证系统');
    return;

    // 暂时禁用API调用
    setIsLoading(false);
  }, [isAuthenticated]);

  // 标记单个通知为已读 - 暂时禁用
  const markAsRead = useCallback(
    async (notificationId: string) => {
      if (!isAuthenticated) return;

      // TODO: 当有完整的认证系统时，重新启用此功能
      console.log('📝 标记通知已读功能暂时禁用');
    },
    [isAuthenticated]
  );

  // 标记所有通知为已读 - 暂时禁用
  const markAllAsRead = useCallback(async () => {
    if (!isAuthenticated) return;

    // TODO: 当有完整的认证系统时，重新启用此功能
    console.log('📝 标记所有通知已读功能暂时禁用');
  }, [isAuthenticated]);

  // 初始化Socket连接和事件监听 - 暂时禁用
  useEffect(() => {
    if (!isAuthenticated) {
      setIsConnected(false);
      return;
    }

    // TODO: 当有完整的认证系统时，重新启用Socket连接
    console.log('📝 Socket连接功能暂时禁用');
    setIsConnected(false);
  }, [isAuthenticated]);

  // 获取初始通知数据 - 暂时禁用
  useEffect(() => {
    if (isAuthenticated) {
      // TODO: 当有完整的认证系统时，重新启用
      console.log('📝 获取通知数据功能暂时禁用');
    } else {
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [isAuthenticated]);

  return {
    notifications,
    unreadCount,
    isConnected,
    isLoading,
    markAsRead,
    markAllAsRead,
    fetchNotifications,
  };
}
