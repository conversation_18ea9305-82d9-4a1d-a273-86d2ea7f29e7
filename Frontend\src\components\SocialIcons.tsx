import React from 'react';

export function Google(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
      <path
        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
        fill="#4285F4"
      />
      <path
        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
        fill="#34A853"
      />
      <path
        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
        fill="#FBBC05"
      />
      <path
        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
        fill="#EA4335"
      />
    </svg>
  );
}

export function Facebook(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
      <path
        d="M22 12c0-5.52-4.48-10-10-10S2 6.48 2 12c0 4.84 3.44 8.87 8 9.8V15H8v-3h2V9.5C10 7.57 11.57 6 13.5 6H16v3h-2c-.55 0-1 .45-1 1v2h3v3h-3v6.95c5.05-.5 9-4.76 9-9.95z"
        fill="#1877F2"
      />
    </svg>
  );
}

export function Twitter(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
      <path
        d="M22.46 6.57c-.8.36-1.65.6-2.55.71.92-.55 1.63-1.42 1.96-2.45-.86.51-1.82.88-2.83 1.08-.82-.87-1.98-1.42-3.27-1.42-2.47 0-4.48 2-4.48 4.47 0 .35.04.7.11 1.03-3.72-.19-7.02-1.97-9.23-4.68-.39.67-.61 1.45-.61 2.28 0 1.55.79 2.92 2 3.72-.74-.02-1.43-.23-2.04-.57v.06c0 2.17 1.54 3.97 3.59 4.38-.37.1-.77.15-1.18.15-.29 0-.57-.03-.85-.08.57 1.78 2.24 3.08 4.22 3.12-1.55 1.22-3.5 1.94-5.62 1.94-.36 0-.72-.02-1.08-.06 1.99 1.27 4.35 2.01 6.89 2.01 8.27 0 12.8-6.85 12.8-12.8 0-.2 0-.39-.01-.59.88-.63 1.64-1.43 2.25-2.34z"
        fill="#1DA1F2"
      />
    </svg>
  );
}