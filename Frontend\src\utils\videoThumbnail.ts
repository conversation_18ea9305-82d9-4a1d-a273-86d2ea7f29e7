/**
 * 视频缩略图生成工具
 */

export interface ThumbnailOptions {
  width?: number;
  height?: number;
  quality?: number;
  timeOffset?: number; // 秒
}

/**
 * 从视频文件生成缩略图
 */
export function generateVideoThumbnail(
  videoFile: File,
  options: ThumbnailOptions = {}
): Promise<string> {
  return new Promise((resolve, reject) => {
    const { width = 800, height = 450, quality = 0.8, timeOffset = 1 } = options;

    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    video.addEventListener('loadedmetadata', () => {
      // 设置画布尺寸
      canvas.width = width;
      canvas.height = height;

      // 设置视频时间点
      const seekTime = Math.min(timeOffset, video.duration - 0.1);
      video.currentTime = seekTime;
    });

    video.addEventListener('seeked', () => {
      try {
        // 绘制视频帧到画布
        ctx.drawImage(video, 0, 0, width, height);

        // 转换为base64
        const thumbnail = canvas.toDataURL('image/jpeg', quality);

        // 清理资源
        video.src = '';
        URL.revokeObjectURL(video.src);

        resolve(thumbnail);
      } catch (error) {
        reject(error);
      }
    });

    video.addEventListener('error', (error) => {
      reject(error);
    });

    // 设置视频源
    video.src = URL.createObjectURL(videoFile);
    video.load();
  });
}

/**
 * 从视频生成多个缩略图（用于选择最佳帧）
 */
export function generateMultipleThumbnails(
  videoFile: File,
  count: number = 5,
  options: ThumbnailOptions = {}
): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');
    const thumbnails: string[] = [];
    let currentIndex = 0;

    video.addEventListener('loadedmetadata', () => {
      const duration = video.duration;
      const interval = duration / (count + 1);

      generateNextThumbnail();

      function generateNextThumbnail() {
        if (currentIndex >= count) {
          // 清理资源
          video.src = '';
          URL.revokeObjectURL(video.src);
          resolve(thumbnails);
          return;
        }

        const timeOffset = interval * (currentIndex + 1);
        video.currentTime = timeOffset;
      }
    });

    video.addEventListener('seeked', () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        const { width = 800, height = 450, quality = 0.8 } = options;
        canvas.width = width;
        canvas.height = height;

        ctx.drawImage(video, 0, 0, width, height);
        const thumbnail = canvas.toDataURL('image/jpeg', quality);

        thumbnails.push(thumbnail);
        currentIndex++;

        // 生成下一个缩略图
        setTimeout(() => {
          if (currentIndex < count) {
            const duration = video.duration;
            const interval = duration / (count + 1);
            const timeOffset = interval * (currentIndex + 1);
            video.currentTime = timeOffset;
          } else {
            // 完成
            video.src = '';
            URL.revokeObjectURL(video.src);
            resolve(thumbnails);
          }
        }, 100);
      } catch (error) {
        reject(error);
      }
    });

    video.addEventListener('error', (error) => {
      reject(error);
    });

    // 设置视频源
    video.src = URL.createObjectURL(videoFile);
    video.load();
  });
}

/**
 * 获取视频基本信息
 */
export function getVideoInfo(videoFile: File): Promise<{
  duration: number;
  width: number;
  height: number;
  size: number;
}> {
  return new Promise((resolve, reject) => {
    const video = document.createElement('video');

    video.addEventListener('loadedmetadata', () => {
      const info = {
        duration: video.duration,
        width: video.videoWidth,
        height: video.videoHeight,
        size: videoFile.size,
      };

      // 清理资源
      video.src = '';
      URL.revokeObjectURL(video.src);

      resolve(info);
    });

    video.addEventListener('error', (error) => {
      reject(error);
    });

    // 设置视频源
    video.src = URL.createObjectURL(videoFile);
    video.load();
  });
}

/**
 * 检查视频文件是否有效
 */
export function validateVideoFile(file: File): Promise<boolean> {
  return new Promise((resolve) => {
    const video = document.createElement('video');

    video.addEventListener('loadedmetadata', () => {
      const isValid = video.duration > 0 && video.videoWidth > 0 && video.videoHeight > 0;

      // 清理资源
      video.src = '';
      URL.revokeObjectURL(video.src);

      resolve(isValid);
    });

    video.addEventListener('error', () => {
      resolve(false);
    });

    // 设置视频源
    video.src = URL.createObjectURL(file);
    video.load();
  });
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化视频时长
 */
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 获取视频质量标签
 */
export function getQualityLabel(width: number, height: number): string {
  if (height >= 4320) return '8K';
  if (height >= 2160) return '4K';
  if (height >= 1440) return '2K';
  if (height >= 1080) return '1080p';
  if (height >= 720) return '720p';
  if (height >= 480) return '480p';
  return '360p';
}
