const { DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const { sequelize } = require('../config/database');

/**
 * 简化的用户模型
 * 只包含基本的登录注册功能
 */
const SimpleUser = sequelize.define(
  'SimpleUser',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    username: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 50],
      },
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
      set(value) {
        this.setDataValue('email', value.toLowerCase().trim());
      },
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    role: {
      type: DataTypes.ENUM('user', 'admin'),
      defaultValue: 'user',
      allowNull: false,
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false,
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'simple_users',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['email'],
      },
      {
        unique: true,
        fields: ['username'],
      },
    ],
  }
);

// 实例方法：比较密码
SimpleUser.prototype.comparePassword = async function (password) {
  try {
    if (!this.password_hash) {
      return false;
    }
    return await bcrypt.compare(password, this.password_hash);
  } catch (error) {
    console.error('Password comparison error:', error);
    return false;
  }
};

// 静态方法：创建用户
SimpleUser.createUser = async function (userData) {
  try {
    const { username, email, password, role = 'user' } = userData;
    
    // 哈希密码
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    // 创建用户
    const user = await this.create({
      username,
      email,
      password_hash,
      role,
      isActive: true,
    });
    
    return user;
  } catch (error) {
    console.error('User creation error:', error);
    throw error;
  }
};

// 静态方法：通过邮箱或用户名查找用户
SimpleUser.findByEmailOrUsername = async function (identifier) {
  try {
    return await this.findOne({
      where: {
        [sequelize.Sequelize.Op.or]: [
          { email: identifier.toLowerCase() },
          { username: identifier },
        ],
      },
    });
  } catch (error) {
    console.error('User lookup error:', error);
    throw error;
  }
};

// 静态方法：验证登录
SimpleUser.authenticate = async function (identifier, password) {
  try {
    const user = await this.findByEmailOrUsername(identifier);
    
    if (!user) {
      return { success: false, message: 'User not found' };
    }
    
    if (!user.isActive) {
      return { success: false, message: 'Account is disabled' };
    }
    
    const isValidPassword = await user.comparePassword(password);
    
    if (!isValidPassword) {
      return { success: false, message: 'Invalid password' };
    }
    
    // 更新最后登录时间
    await user.update({ lastLogin: new Date() });
    
    return { 
      success: true, 
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role,
      }
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return { success: false, message: 'Authentication failed' };
  }
};

module.exports = SimpleUser;
