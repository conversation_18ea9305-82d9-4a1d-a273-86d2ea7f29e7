@echo off
echo ========================================
echo 🚀 Newzora 平台自动部署脚本
echo ========================================

:: 设置颜色
color 0A

echo.
echo 🔍 检查部署环境...
echo.

:: 检查Node.js
echo 检查 Node.js...
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 已安装
    node --version
) else (
    echo ❌ Node.js 未安装或不在PATH中
    goto :install_nodejs
)

:: 检查npm
echo.
echo 检查 npm...
npm --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npm 已安装
    npm --version
) else (
    echo ❌ npm 未安装
    goto :error
)

:: 检查Docker
echo.
echo 检查 Docker...
docker --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Docker 已安装
    docker --version
    goto :docker_deploy
) else (
    echo ⚠️ Docker 未安装，使用本地环境部署
    goto :local_deploy
)

:docker_deploy
echo.
echo 🐳 使用Docker部署...
echo.

:: 检查docker-compose文件
if not exist "config\docker-compose.yml" (
    echo ❌ Docker Compose文件不存在
    goto :local_deploy
)

:: 停止现有容器
echo 停止现有容器...
cd config
docker-compose down >nul 2>&1

:: 启动服务
echo 🔨 构建并启动服务...
docker-compose up -d --build
if %errorlevel% == 0 (
    echo ✅ Docker服务启动成功
    cd ..
    goto :verify_deployment
) else (
    echo ❌ Docker部署失败，尝试本地部署
    cd ..
    goto :local_deploy
)

:local_deploy
echo.
echo 🔧 使用本地环境部署...
echo.

:: 检查PostgreSQL服务
echo 检查PostgreSQL服务...
sc query postgresql-x64-16 >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ PostgreSQL服务存在
    sc query postgresql-x64-16 | find "RUNNING" >nul
    if %errorlevel% == 0 (
        echo ✅ PostgreSQL正在运行
    ) else (
        echo 🔄 启动PostgreSQL服务...
        net start postgresql-x64-16 >nul 2>&1
        if %errorlevel% == 0 (
            echo ✅ PostgreSQL启动成功
        ) else (
            echo ⚠️ PostgreSQL启动失败，继续部署
        )
    )
) else (
    echo ⚠️ PostgreSQL服务未找到，继续部署
)

:: 安装依赖
echo.
echo 📦 安装项目依赖...

:: 检查根目录package.json
if exist "package.json" (
    echo 安装根目录依赖...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 根目录依赖安装失败
        goto :error
    )
)

:: 安装后端依赖
if exist "Backend\package.json" (
    echo 安装后端依赖...
    cd Backend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 后端依赖安装失败
        cd ..
        goto :error
    )
    cd ..
)

:: 安装前端依赖
if exist "Frontend\package.json" (
    echo 安装前端依赖...
    cd Frontend
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        cd ..
        goto :error
    )
    cd ..
)

echo ✅ 依赖安装完成

:: 数据库初始化
echo.
echo 🗄️ 初始化数据库...
cd Backend
npm run migrate >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 数据库迁移成功
) else (
    echo ⚠️ 数据库迁移失败，继续部署
)

npm run seed >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ 种子数据初始化成功
) else (
    echo ⚠️ 种子数据初始化失败，继续部署
)
cd ..

:: 启动服务
echo.
echo 🚀 启动开发服务...

:: 检查是否有统一启动脚本
if exist "package.json" (
    echo 使用统一启动脚本...
    start /b npm run dev
    echo ✅ 服务启动中...
) else (
    :: 分别启动前后端
    echo 分别启动前后端服务...
    
    :: 启动后端
    cd Backend
    start /b npm start
    cd ..
    
    :: 启动前端
    cd Frontend
    start /b npm run dev
    cd ..
    
    echo ✅ 前后端服务启动中...
)

:verify_deployment
echo.
echo ⏳ 等待服务启动...
timeout /t 30 /nobreak >nul

echo.
echo 🔍 验证部署状态...

:: 检查前端服务
echo 检查前端服务 (http://localhost:3000)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5 -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host '✅ 前端服务正常' } else { Write-Host '❌ 前端服务异常' } } catch { Write-Host '❌ 前端服务不可访问' }" 2>nul

:: 检查后端服务
echo 检查后端服务 (http://localhost:5000)...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:5000/api/health' -TimeoutSec 5 -UseBasicParsing; if ($response.StatusCode -eq 200) { Write-Host '✅ 后端API正常' } else { Write-Host '❌ 后端API异常' } } catch { Write-Host '❌ 后端API不可访问' }" 2>nul

echo.
echo ========================================
echo 🎉 Newzora 平台部署完成！
echo ========================================
echo.
echo 🌐 访问地址:
echo    前端应用: http://localhost:3000
echo    后端API:  http://localhost:5000
echo    健康检查: http://localhost:5000/api/health
echo.
echo 🔧 管理命令:
echo    查看状态: manage.ps1 status
echo    停止服务: manage.ps1 stop
echo    重启服务: manage.ps1 restart
echo.
echo 📝 如果服务未正常启动，请等待几分钟后再次检查
echo 💡 或手动运行: npm run dev
echo.
goto :end

:install_nodejs
echo.
echo ❌ 需要安装 Node.js
echo.
echo 💡 请按以下步骤安装:
echo    1. 访问 https://nodejs.org/
echo    2. 下载并安装 LTS 版本
echo    3. 重新运行此脚本
echo.
goto :end

:error
echo.
echo ❌ 部署失败
echo.
echo 💡 建议:
echo    1. 检查网络连接
echo    2. 确保有足够的磁盘空间
echo    3. 检查防火墙设置
echo    4. 尝试手动安装依赖: npm install
echo.
goto :end

:end
echo 按任意键退出...
pause >nul
