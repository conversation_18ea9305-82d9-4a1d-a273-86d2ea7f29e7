'use client';

import { useState, useEffect, useRef } from 'react';

interface SearchBarProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export default function SearchBar({ searchQuery, onSearchChange }: SearchBarProps) {
  const [isFocused, setIsFocused] = useState(false);
  const isMountedRef = useRef(true);

  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isMountedRef.current) return;
    // 这里可以添加实际的搜索提交逻辑
  };

  const handleClearSearch = () => {
    if (!isMountedRef.current) return;
    onSearchChange('');
  };

  return (
    <div className="relative max-w-4xl mx-auto mb-12">
      <form onSubmit={handleSubmit} className="relative">
        <div
          className={`relative flex items-center transition-all duration-200 ${
            isFocused ? 'transform scale-[1.02]' : ''
          }`}
        >
          <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
            <svg
              className="h-6 w-6 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>

          <input
            type="text"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            onFocus={() => {
              if (isMountedRef.current) setIsFocused(true);
            }}
            onBlur={() => {
              setTimeout(() => {
                if (isMountedRef.current) setIsFocused(false);
              }, 200);
            }}
            placeholder="Search for articles or videos"
            className="block w-full pl-16 pr-6 py-6 text-xl border-2 border-gray-200 rounded-2xl bg-gray-50 focus:bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 placeholder-gray-500"
          />

          {searchQuery && (
            <button
              type="button"
              onClick={() => onSearchChange('')}
              className="absolute inset-y-0 right-0 pr-6 flex items-center"
            >
              <svg
                className="h-6 w-6 text-gray-400 hover:text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          )}
        </div>
      </form>

      {/* Search suggestions */}
      {searchQuery && isFocused && (
        <div 
          className="absolute top-full left-0 right-0 mt-3 bg-white rounded-xl shadow-lg border border-gray-200 z-10"
          onMouseDown={(e) => e.preventDefault()} // 防止blur事件过早触发
        >
          <div className="p-4 text-sm text-gray-600">
            Press Enter to search for &quot;{searchQuery}&quot;
          </div>
        </div>
      )}
    </div>
  );
}