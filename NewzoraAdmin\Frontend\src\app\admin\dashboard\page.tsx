'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Users, FileText, MessageCircle, Activity } from 'lucide-react';
import StatsCard from '@/components/admin/dashboard/StatsCard';
import TrendChart from '@/components/admin/dashboard/TrendChart';
import QuickActions from '@/components/admin/dashboard/QuickActions';
import RecentActivities from '@/components/admin/dashboard/RecentActivities';
import SafeDatePicker, { DateRange } from '@/components/admin/common/SafeDatePicker';
import { dashboardService } from '@/services/dashboardService';
import { DashboardStats } from '@/types/admin';
import { useDataSync } from '@/contexts/DataSyncContext';

const DashboardPage: React.FC = () => {
  const router = useRouter();
  const { stats: globalStats } = useDataSync();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  
  // User growth trend data
  const [userGrowthData, setUserGrowthData] = useState([
    { name: 'Mon', users: 400 },
    { name: 'Tue', users: 600 },
    { name: 'Wed', users: 300 },
    { name: 'Thu', users: 800 },
    { name: 'Fri', users: 500 },
    { name: 'Sat', users: 900 },
    { name: 'Sun', users: 400 },
  ]);
  
  // Content publishing trend data
  const [contentPublishData, setContentPublishData] = useState([
    { name: 'Mon', articles: 12 },
    { name: 'Tue', articles: 19 },
    { name: 'Wed', articles: 8 },
    { name: 'Thu', articles: 15 },
    { name: 'Fri', articles: 12 },
    { name: 'Sat', articles: 20 },
    { name: 'Sun', articles: 18 },
  ]);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await dashboardService.getStats();
        setStats(data);
      } catch (error) {
        console.error('Failed to fetch statistics:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);

    // Fetch updated stats based on date range
    fetchStatsForDateRange(dateRange);

    // Generate different sample data based on time range
    switch (range) {
      case '30d':
        setUserGrowthData([
          { name: 'Week 1', users: 2100 },
          { name: 'Week 2', users: 2400 },
          { name: 'Week 3', users: 1900 },
          { name: 'Week 4', users: 2800 },
        ]);

        setContentPublishData([
          { name: 'Week 1', articles: 65 },
          { name: 'Week 2', articles: 78 },
          { name: 'Week 3', articles: 52 },
          { name: 'Week 4', articles: 95 },
        ]);
        break;
      case '90d':
        setUserGrowthData([
          { name: 'Month 1', users: 8200 },
          { name: 'Month 2', users: 9500 },
          { name: 'Month 3', users: 11200 },
        ]);

        setContentPublishData([
          { name: 'Month 1', articles: 240 },
          { name: 'Month 2', articles: 280 },
          { name: 'Month 3', articles: 320 },
        ]);
        break;
      default: // 7d
        setUserGrowthData([
          { name: 'Mon', users: 400 },
          { name: 'Tue', users: 600 },
          { name: 'Wed', users: 300 },
          { name: 'Thu', users: 800 },
          { name: 'Fri', users: 500 },
          { name: 'Sat', users: 900 },
          { name: 'Sun', users: 400 },
        ]);

        setContentPublishData([
          { name: 'Mon', articles: 12 },
          { name: 'Tue', articles: 19 },
          { name: 'Wed', articles: 8 },
          { name: 'Thu', articles: 15 },
          { name: 'Fri', articles: 12 },
          { name: 'Sat', articles: 20 },
          { name: 'Sun', articles: 18 },
        ]);
    }
  };

  const fetchStatsForDateRange = async (dateRange: DateRange) => {
    try {
      setLoading(true);

      // 使用更新后的服务获取基于日期范围的统计数据
      const statsData = await dashboardService.getStats({
        dateRange,
        timeRange
      });

      setStats(statsData);
    } catch (error) {
      console.error('Failed to fetch stats for date range:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始数据加载
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);
        const filters: any = { timeRange };
        if (currentDateRange) {
          filters.dateRange = currentDateRange;
        }
        const statsData = await dashboardService.getStats(filters);
        setStats(statsData);
      } catch (error) {
        console.error('Failed to fetch initial dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [timeRange, currentDateRange]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <div className="flex items-center space-x-2">
          <SafeDatePicker
            value={currentDateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            className="w-64"
          />
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="Total Users"
          value={globalStats.totalUsers}
          change={{ value: 12, type: 'increase' }}
          icon={<Users />}
          color="blue"
          onClick={() => router.push('/admin/users')}
        />
        <StatsCard
          title="Total Content"
          value={globalStats.totalContent}
          change={{ value: 8, type: 'increase' }}
          icon={<FileText />}
          color="green"
          onClick={() => router.push('/admin/content')}
        />
        <StatsCard
          title="Total Comments"
          value={globalStats.totalComments}
          change={{ value: 5, type: 'decrease' }}
          icon={<MessageCircle />}
          color="yellow"
          onClick={() => router.push('/admin/analytics/content')}
        />
        <StatsCard
          title="Active Users"
          value={globalStats.activeUsers}
          change={{ value: 15, type: 'increase' }}
          icon={<Activity />}
          color="red"
          onClick={() => router.push('/admin/analytics/users')}
        />
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TrendChart 
          title="User Growth Trend" 
          data={userGrowthData} 
          dataKey="users" 
          color="#3b82f6" 
        />
        <TrendChart 
          title="Content Publishing Trend" 
          data={contentPublishData} 
          dataKey="articles" 
          color="#10b981" 
        />
      </div>

      {/* 快速操作和最新活动 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <QuickActions />
        
        <RecentActivities />
      </div>
    </div>
  );
};

export default DashboardPage;