const { Sequelize } = require('sequelize');
require('dotenv').config();

// 根据环境获取数据库配置
const getEnvironmentConfig = () => {
  const baseConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    database: process.env.DB_NAME || 'PostgreSQL-newzora_web',
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD,
    dialect: 'postgres',
  };

  // 根据环境设置不同的配置
  switch (process.env.NODE_ENV) {
    case 'production':
      return {
        ...baseConfig,
        logging: (sql, timing) => {
          if (timing > 1000) {
            // 只记录慢查询 (>1s)
            console.warn(`[SLOW QUERY] ${timing}ms: ${sql}`);
          }
        },
        benchmark: true,
        pool: {
          max: parseInt(process.env.DB_POOL_MAX) || 20,
          min: parseInt(process.env.DB_POOL_MIN) || 5,
          acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 60000,
          idle: parseInt(process.env.DB_POOL_IDLE) || 10000,
          evict: parseInt(process.env.DB_POOL_EVICT) || 1000,
        },
        dialectOptions: {
          ssl:
            process.env.DB_SSL === 'true'
              ? {
                  require: true,
                  rejectUnauthorized: false,
                }
              : false,
          statement_timeout: 60000,
          charset: 'utf8',
          collate: 'utf8_general_ci',
          query_timeout: 60000,
          application_name: 'Newzora',
        },
        retry: {
          max: 3,
          match: [
            /ETIMEDOUT/,
            /EHOSTUNREACH/,
            /ECONNRESET/,
            /ECONNREFUSED/,
            /ETIMEDOUT/,
            /ESOCKETTIMEDOUT/,
            /EHOSTUNREACH/,
            /EPIPE/,
            /EAI_AGAIN/,
            /SequelizeConnectionError/,
            /SequelizeConnectionRefusedError/,
            /SequelizeHostNotFoundError/,
            /SequelizeHostNotReachableError/,
            /SequelizeInvalidConnectionError/,
            /SequelizeConnectionTimedOutError/,
          ],
        },
      };

    case 'test':
      return {
        ...baseConfig,
        logging: false,
        pool: {
          max: 5,
          min: 1,
          acquire: 30000,
          idle: 10000,
        },
      };

    case 'development':
    default:
      return {
        ...baseConfig,
        logging: console.log,
        benchmark: true,
        pool: {
          max: 10,
          min: 2,
          acquire: 30000,
          idle: 10000,
        },
        dialectOptions: {
          charset: 'utf8',
          collate: 'utf8_general_ci',
        },
      };
  }
};

// 创建 Sequelize 实例
const dbConfig = getEnvironmentConfig();
const sequelize = new Sequelize(dbConfig);

// 增强的连接测试函数
const testConnection = async () => {
  try {
    await sequelize.authenticate();
    console.log('✅ PostgreSQL connection has been established successfully.');

    // 输出连接池状态
    const poolStats = {
      total: sequelize.connectionManager.pool.size,
      used: sequelize.connectionManager.pool.used,
      waiting: sequelize.connectionManager.pool.pending,
    };
    console.log('📊 Connection pool status:', poolStats);

    // 测试查询性能
    const startTime = Date.now();
    await sequelize.query('SELECT NOW() as current_time');
    const queryTime = Date.now() - startTime;
    console.log(`⚡ Query response time: ${queryTime}ms`);

    return true;
  } catch (error) {
    console.error('❌ Unable to connect to the database:', error.message);
    return false;
  }
};

// 健康检查函数
const healthCheck = async () => {
  try {
    const startTime = Date.now();
    const result = await sequelize.query('SELECT NOW() as current_time, version() as version');
    const responseTime = Date.now() - startTime;

    return {
      status: 'healthy',
      responseTime,
      timestamp: result[0][0].current_time,
      version: result[0][0].version,
      pool: {
        total: sequelize.connectionManager.pool.size,
        used: sequelize.connectionManager.pool.used,
        waiting: sequelize.connectionManager.pool.pending,
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString(),
    };
  }
};

// 优雅关闭数据库连接
const gracefulShutdown = async () => {
  try {
    console.log('🔄 Closing database connections...');
    await sequelize.close();
    console.log('✅ Database connections closed successfully.');
  } catch (error) {
    console.error('❌ Error closing database connections:', error.message);
  }
};

// 数据库性能统计
const getPerformanceStats = async () => {
  try {
    const queries = [
      // 连接统计
      `SELECT count(*) as total_connections,
              count(*) FILTER (WHERE state = 'active') as active_connections,
              count(*) FILTER (WHERE state = 'idle') as idle_connections
       FROM pg_stat_activity
       WHERE datname = '${dbConfig.database}'`,

      // 数据库大小
      `SELECT pg_size_pretty(pg_database_size('${dbConfig.database}')) as database_size`,

      // 表统计
      `SELECT schemaname, relname as tablename, n_tup_ins, n_tup_upd, n_tup_del, n_live_tup
       FROM pg_stat_user_tables
       ORDER BY n_live_tup DESC
       LIMIT 10`,
    ];

    const [connections, dbSize, tableStats] = await Promise.all(
      queries.map((query) => sequelize.query(query, { type: Sequelize.QueryTypes.SELECT }))
    );

    return {
      connections: connections[0],
      database_size: dbSize[0].database_size,
      top_tables: tableStats,
    };
  } catch (error) {
    console.error('获取性能统计失败:', error.message);
    return null;
  }
};

module.exports = {
  sequelize,
  testConnection,
  healthCheck,
  gracefulShutdown,
  getPerformanceStats,
};
