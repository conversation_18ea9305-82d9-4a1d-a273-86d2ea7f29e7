import axios, { AxiosError, AxiosResponse } from 'axios';

// Enhanced error handling interface
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

// Create axios instance with enhanced configuration
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001',
  timeout: 15000, // Increased timeout for better reliability
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - add authentication token and enhanced logging
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add request ID for tracking
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Enhanced request logging
    console.log('API Request:', {
      requestId: config.headers['X-Request-ID'],
      url: config.url,
      method: config.method?.toUpperCase(),
      baseURL: config.baseURL,
      fullUrl: `${config.baseURL}${config.url}`,
      data: config.data,
      timestamp: new Date().toISOString()
    });

    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor - enhanced error handling and logging
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Enhanced response logging
    console.log('API Response:', {
      requestId: response.config.headers['X-Request-ID'],
      url: response.config.url,
      status: response.status,
      statusText: response.statusText,
      data: response.data,
      timestamp: new Date().toISOString(),
      responseTime: response.headers['x-response-time'] || 'unknown'
    });

    return response;
  },
  (error: AxiosError) => {
    const apiError: ApiError = {
      message: 'An error occurred',
      status: error.response?.status,
      details: error.response?.data
    };

    // Enhanced error logging
    console.error('API Error:', {
      requestId: error.config?.headers?.['X-Request-ID'],
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      response: error.response?.data,
      timestamp: new Date().toISOString()
    });

    // Handle specific error cases
    switch (error.response?.status) {
      case 401:
        apiError.message = 'Authentication required';
        apiError.code = 'UNAUTHORIZED';
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_user');
        window.dispatchEvent(new CustomEvent('unauthorized'));
        break;

      case 403:
        apiError.message = 'Access forbidden';
        apiError.code = 'FORBIDDEN';
        break;

      case 404:
        apiError.message = 'Resource not found';
        apiError.code = 'NOT_FOUND';
        break;

      case 422:
        apiError.message = 'Validation error';
        apiError.code = 'VALIDATION_ERROR';
        break;

      case 429:
        apiError.message = 'Too many requests';
        apiError.code = 'RATE_LIMITED';
        break;

      case 500:
        apiError.message = 'Internal server error';
        apiError.code = 'SERVER_ERROR';
        break;

      case 502:
      case 503:
      case 504:
        apiError.message = 'Service unavailable';
        apiError.code = 'SERVICE_UNAVAILABLE';
        break;

      default:
        if (error.code === 'ECONNABORTED') {
          apiError.message = 'Request timeout';
          apiError.code = 'TIMEOUT';
        } else if (error.code === 'ERR_NETWORK') {
          apiError.message = 'Network error';
          apiError.code = 'NETWORK_ERROR';
        } else {
          apiError.message = error.response?.data?.message || error.message || 'Unknown error';
          apiError.code = 'UNKNOWN_ERROR';
        }
    }

    // Dispatch error event for global error handling
    window.dispatchEvent(new CustomEvent('apiError', {
      detail: apiError
    }));

    return Promise.reject(apiError);
  }
);

export default api;