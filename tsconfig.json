{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "paths": {"@/*": ["./Frontend/src/*"], "@/components/*": ["./Frontend/src/components/*"], "@/lib/*": ["./Frontend/src/lib/*"], "@/types/*": ["./Frontend/src/types/*"], "@/utils/*": ["./Frontend/src/utils/*"]}}, "include": ["Frontend/**/*.ts", "Frontend/**/*.tsx", "Backend/**/*.ts", "Backend/**/*.js"], "exclude": ["node_modules", "Frontend/.next", "Frontend/out", "Backend/dist", "Backend/build"]}