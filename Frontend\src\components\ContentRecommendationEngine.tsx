'use client';

import { useState, useEffect } from 'react';

interface ContentAnalysis {
  id: number;
  title: string;
  type: 'article' | 'video' | 'audio';
  duplicateScore: number;
  qualityScore: number;
  engagementScore: number;
  revenueScore: number;
  suspiciousPatterns: string[];
  recommendations: string[];
  status: 'approved' | 'flagged' | 'under_review';
}

interface RecommendationEngineProps {
  userId?: number;
  contentId?: number;
}

export default function ContentRecommendationEngine({ userId, contentId }: RecommendationEngineProps) {
  const [analyses, setAnalyses] = useState<ContentAnalysis[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContentAnalyses();
  }, [userId, contentId]);

  const loadContentAnalyses = async () => {
    try {
      setLoading(true);
      
      const mockAnalyses: ContentAnalysis[] = [
        {
          id: 1,
          title: 'AI技术发展趋势分析',
          type: 'article',
          duplicateScore: 12,
          qualityScore: 94,
          engagementScore: 87,
          revenueScore: 92,
          suspiciousPatterns: [],
          recommendations: [
            '内容质量优秀，建议增加更多技术细节',
            '可以添加实际案例提升可读性',
            '建议制作配套视频内容'
          ],
          status: 'approved'
        },
        {
          id: 2,
          title: 'Web开发最佳实践',
          type: 'video',
          duplicateScore: 78,
          qualityScore: 45,
          engagementScore: 23,
          revenueScore: 15,
          suspiciousPatterns: [
            '内容与其他作品高度相似',
            '观看时长异常短',
            '评论质量低'
          ],
          recommendations: [
            '检测到重复内容，建议重新创作',
            '提升内容原创性',
            '改善视频制作质量'
          ],
          status: 'flagged'
        }
      ];

      setTimeout(() => {
        setAnalyses(mockAnalyses);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('加载内容分析失败:', error);
      setLoading(false);
    }
  };

  const getScoreColor = (score: number, isReverse = false) => {
    if (isReverse) {
      if (score < 30) return 'text-green-600';
      if (score < 70) return 'text-yellow-600';
      return 'text-red-600';
    } else {
      if (score >= 80) return 'text-green-600';
      if (score >= 60) return 'text-yellow-600';
      return 'text-red-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'flagged': return 'bg-red-100 text-red-800 border-red-200';
      case 'under_review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[...Array(2)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">📊 内容质量分析</h3>
      
      <div className="space-y-6">
        {analyses.map((analysis) => (
          <div key={analysis.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h4 className="font-medium text-gray-900">{analysis.title}</h4>
                <p className="text-sm text-gray-500">{analysis.type}</p>
              </div>
              <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getStatusColor(analysis.status)}`}>
                {analysis.status}
              </span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">重复度</p>
                <p className={`text-lg font-bold ${getScoreColor(analysis.duplicateScore, true)}`}>
                  {analysis.duplicateScore}%
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">质量分</p>
                <p className={`text-lg font-bold ${getScoreColor(analysis.qualityScore)}`}>
                  {analysis.qualityScore}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">互动分</p>
                <p className={`text-lg font-bold ${getScoreColor(analysis.engagementScore)}`}>
                  {analysis.engagementScore}
                </p>
              </div>
              <div className="text-center">
                <p className="text-sm text-gray-600">收益分</p>
                <p className={`text-lg font-bold ${getScoreColor(analysis.revenueScore)}`}>
                  {analysis.revenueScore}
                </p>
              </div>
            </div>

            {analysis.suspiciousPatterns.length > 0 && (
              <div className="mb-4">
                <h5 className="text-sm font-medium text-red-800 mb-2">⚠️ 可疑模式</h5>
                <div className="flex flex-wrap gap-2">
                  {analysis.suspiciousPatterns.map((pattern, index) => (
                    <span key={index} className="px-2 py-1 bg-red-50 text-red-700 text-xs rounded-full border border-red-200">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div>
              <h5 className="text-sm font-medium text-blue-800 mb-2">💡 优化建议</h5>
              <ul className="text-sm text-blue-700 space-y-1">
                {analysis.recommendations.map((rec, index) => (
                  <li key={index}>• {rec}</li>
                ))}
              </ul>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}