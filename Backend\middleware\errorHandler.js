/**
 * Unified error handling middleware
 * Handles all uncaught errors and returns unified format responses
 */

const {
  errorResponse,
  serverErrorResponse,
  validationErrorResponse,
  authErrorResponse,
  forbiddenResponse,
  notFoundResponse,
  rateLimitResponse
} = require('../utils/responseFormatter');

const errorHandler = (err, req, res, next) => {
  console.error('Global error handler:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString()
  });

  // If response has already been sent, pass to default error handler
  if (res.headersSent) {
    return next(err);
  }

  // JWT相关错误
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json(authErrorResponse('Invalid token', 'INVALID_TOKEN'));
  }

  if (err.name === 'TokenExpiredError') {
    return res.status(401).json(authErrorResponse('Token expired', 'TOKEN_EXPIRED'));
  }

  // Authentication error
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json(authErrorResponse('Authentication failed', 'AUTH_ERROR'));
  }

  // Sequelize validation error
  if (err.name === 'SequelizeValidationError') {
    const errors = err.errors.map(error => ({
      field: error.path,
      message: error.message,
      value: error.value
    }));
    return res.status(400).json(validationErrorResponse(errors));
  }

  // Sequelize unique constraint error
  if (err.name === 'SequelizeUniqueConstraintError') {
    const field = err.errors[0]?.path || 'field';
    const message = `${field} already exists`;
    return res.status(409).json(errorResponse(message, 'DUPLICATE_ENTRY'));
  }

  // Database connection error
  if (err.name === 'SequelizeConnectionError' || err.code === 'ECONNREFUSED' || err.code === 'ENOTFOUND') {
    return res.status(503).json(errorResponse('Service temporarily unavailable', 'DATABASE_ERROR'));
  }

  // Timeout error
  if (err.code === 'ETIMEDOUT') {
    return res.status(408).json(errorResponse('Request timeout', 'TIMEOUT_ERROR'));
  }

  // Syntax error (usually JSON parsing error)
  if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    return res.status(400).json(errorResponse('Invalid JSON format', 'INVALID_JSON'));
  }

  // Rate limit error
  if (err.status === 429) {
    return res.status(429).json(rateLimitResponse(err.message));
  }

  // Permission error
  if (err.status === 403) {
    return res.status(403).json(forbiddenResponse(err.message));
  }

  // 404 error
  if (err.status === 404) {
    return res.status(404).json(notFoundResponse(err.message));
  }

  // Default server error
  res.status(err.status || 500).json(serverErrorResponse(err));
};

/**
 * Async error wrapper
 * Automatically catch errors in async functions
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Validation error handler
 * Handle express-validator validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const { validationResult } = require('express-validator');
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    return res.status(400).json(validationErrorResponse(errors.array()));
  }

  next();
};

module.exports = {
  errorHandler,
  asyncHandler,
  handleValidationErrors
};