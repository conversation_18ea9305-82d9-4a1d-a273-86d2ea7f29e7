'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import { useData } from '@/contexts/DataContext';
import { useToast } from '@/components/Toast';
import { useNotifications } from '@/contexts/NotificationContext';
import EmojiPicker from '@/components/EmojiPicker';
import socketService from '@/services/socketService';

interface Comment {
  id: number;
  author: {
    id?: number;
    name: string;
    avatar: string;
    username?: string;
  };
  content: string;
  createdAt: string;
  likes: number;
  isLiked?: boolean;
  replies?: Comment[];
  parentId?: number;
}

interface CommentSectionProps {
  workId: number;
  workType: 'article' | 'video' | 'audio';
  initialComments?: Comment[];
  currentUser?: {
    name: string;
    avatar: string;
  };
}

export default function CommentSection({
  workId,
  workType,
  initialComments = [],
  currentUser = {
    name: 'You',
    avatar:
      'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
  },
}: CommentSectionProps) {
  const { isAuthenticated, user, session, isLoading: authLoading } = useSimpleAuth();
  const { addComment } = useData();
  // 获取认证token
  const token = session?.access_token || 'mock-token-for-development';
  const toast = useToast();
  const { addNotification } = useNotifications();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [emojiPickerFor, setEmojiPickerFor] = useState<'main' | number>('main');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'mostLiked'>('newest');
  const router = useRouter();

  useEffect(() => {
    fetchComments();
    
    // 设置Socket.IO监听器用于实时评论更新
    if (isAuthenticated && session?.access_token) {
      // 连接Socket.IO
      socketService.connect(session.access_token).catch(console.error);
      
      // 监听新评论
      const handleNewComment = (data: any) => {
        if (data.workId === workId && data.workType === workType) {
          setComments(prev => [data.comment, ...prev]);
          // 添加通知
          addNotification({
            type: 'info',
            title: 'New Comment',
            message: `${data.comment.author.name} commented on this ${workType}`
          });
        }
      };
      
      // 监听评论点赞更新
      const handleCommentLikeUpdate = (data: any) => {
        if (data.workId === workId && data.workType === workType) {
          setComments(prev => prev.map(comment => {
            if (comment.id === data.commentId) {
              return {
                ...comment,
                likes: data.likes,
                isLiked: data.isLiked
              };
            }
            // 检查回复
            if (comment.replies) {
              return {
                ...comment,
                replies: comment.replies.map(reply => 
                  reply.id === data.commentId 
                    ? { ...reply, likes: data.likes, isLiked: data.isLiked }
                    : reply
                )
              };
            }
            return comment;
          }));
        }
      };
      
      // 监听新回复
      const handleNewReply = (data: any) => {
        if (data.workId === workId && data.workType === workType) {
          setComments(prev => prev.map(comment => 
            comment.id === data.parentId 
              ? { ...comment, replies: [...(comment.replies || []), data.reply] }
              : comment
          ));
        }
      };
      
      socketService.on('new_comment', handleNewComment);
      socketService.on('comment_like_updated', handleCommentLikeUpdate);
      socketService.on('new_reply', handleNewReply);
      
      // 清理监听器
      return () => {
        socketService.off('new_comment', handleNewComment);
        socketService.off('comment_like_updated', handleCommentLikeUpdate);
        socketService.off('new_reply', handleNewReply);
      };
    }
  }, [workId, workType, isAuthenticated, session]);

  const fetchComments = async () => {
    try {
      setLoading(true);

      // 首先尝试从API获取评论
      try {
        const response = await fetch(`http://localhost:5000/api/comments/${workType}/${workId}`);
        if (response.ok) {
          const data = await response.json();
          setComments(data);
          return;
        }
      } catch (apiError) {
        console.log('API不可用，使用模拟数据');
      }

      // 如果API不可用，使用模拟数据
      const mockComments: Comment[] = [
        {
          id: 1,
          author: {
            id: 1,
            name: 'Alice Johnson',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=48&h=48&fit=crop&crop=face',
            username: 'alice_j'
          },
          content: 'This is a really insightful article! Thanks for sharing your thoughts on this topic.',
          createdAt: '2 hours ago',
          likes: 12,
          isLiked: false,
          replies: [
            {
              id: 11,
              author: {
                id: 4,
                name: 'David Wilson',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face',
                username: 'david_w'
              },
              content: 'I totally agree! 👍',
              createdAt: '1 hour ago',
              likes: 3,
              isLiked: false,
              parentId: 1
            }
          ]
        },
        {
          id: 2,
          author: {
            id: 2,
            name: 'Bob Smith',
            avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face',
            username: 'bob_smith'
          },
          content: 'I have a different perspective on this. Would love to discuss further!',
          createdAt: '4 hours ago',
          likes: 8,
          isLiked: true
        },
        {
          id: 3,
          author: {
            id: 3,
            name: 'Carol Davis',
            avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=48&h=48&fit=crop&crop=face',
            username: 'carol_d'
          },
          content: 'Great points made here. This really helped me understand the topic better.',
          createdAt: '6 hours ago',
          likes: 15,
          isLiked: false
        }
      ];

      setComments(mockComments);
    } catch (error) {
      console.error('Error fetching comments:', error);
      setComments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;

    // Check if user is authenticated
    if (!isAuthenticated) {
      toast.warning('Please log in first', 'You need to log in to post comments');
      return;
    }

    setIsSubmitting(true);

    try {
      // 尝试API调用
      try {
        const response = await fetch('http://localhost:5000/api/comments', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            workId: workId,
            workType: workType,
            content: newComment.trim(),
          }),
        });

        if (response.ok) {
          const newCommentData = await response.json();
          setComments((prev) => [newCommentData, ...prev]);
          setNewComment('');
          toast.success('Comment posted successfully!');
          return;
        }
      } catch (apiError) {
        console.log('API不可用，使用模拟提交');
      }

      // 如果API不可用，创建模拟评论
      const mockNewComment: Comment = {
        id: Date.now(), // 使用时间戳作为临时ID
        author: {
          id: user?.id || 999,
          name: user?.username || 'Current User',
          avatar: user?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.username || 'User')}&background=6366f1&color=fff&size=48`,
          username: user?.username || 'current_user'
        },
        content: newComment.trim(),
        createdAt: 'just now',
        likes: 0,
        isLiked: false
      };

      setComments((prev) => [mockNewComment, ...prev]);
      setNewComment('');
      toast.success('Comment posted successfully!');

      // 更新DataContext中的评论数
      addComment(workId, workType);

      // 通过Socket.IO广播新评论
      if (socketService.isSocketConnected()) {
        socketService.emit('comment_posted', {
          workId: workId,
          workType: workType,
          comment: mockNewComment
        });
      }

      // 添加通知
      addNotification({
        type: 'success',
        title: 'Comment Posted',
        message: 'Your comment has been posted successfully!'
      });
    } catch (error) {
      console.error('Error posting comment:', error);
      toast.error('Failed to post comment', 'Please try again later');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleLikeComment = async (commentId: number) => {
    // Check if user is authenticated
    if (!isAuthenticated) {
      toast.warning('Please log in first', 'You need to log in to like comments');
      return;
    }

    try {
      // 尝试API调用
      try {
        const response = await fetch(`http://localhost:5000/api/comments/${commentId}/like`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          setComments((prev) =>
            prev.map((comment) =>
              comment.id === commentId
                ? {
                    ...comment,
                    isLiked: !comment.isLiked,
                    likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
                  }
                : comment
            )
          );
          return;
        }
      } catch (apiError) {
        console.log('API不可用，使用本地状态更新');
      }

      // 如果API不可用，直接更新本地状态
      setComments((prev) =>
        prev.map((comment) =>
          comment.id === commentId
            ? {
                ...comment,
                isLiked: !comment.isLiked,
                likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
              }
            : comment
        )
      );

      // 显示成功提示和通知
      const comment = comments.find(c => c.id === commentId);
      if (comment) {
        const isLiking = !comment.isLiked;
        const newLikes = isLiking ? comment.likes + 1 : comment.likes - 1;
        
        toast.success(isLiking ? 'Liked comment' : 'Removed like');

        // 通过Socket.IO广播点赞更新
        if (socketService.isSocketConnected()) {
          socketService.emit('comment_like_updated', {
            workId: workId,
            workType: workType,
            commentId: commentId,
            likes: newLikes,
            isLiked: isLiking
          });
        }

        // 添加通知
        addNotification({
          type: 'info',
          title: isLiking ? 'Comment Liked' : 'Like Removed',
          message: isLiking ? `You liked ${comment.author.name}'s comment` : `You removed your like from ${comment.author.name}'s comment`
        });
      }
    } catch (error) {
      console.error('Error liking comment:', error);
      toast.error('Failed to like comment', 'Please try again later');
    }
  };

  const handleUserClick = (author: Comment['author'], e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    
    console.log('User clicked:', author);
    
    if (author.username) {
      console.log('Navigating to:', `/profile/${author.username}`);
      router.push(`/profile/${author.username}`);
    } else {
      console.log('No username available for user:', author.name);
      toast.info('User profile not available');
    }
  };

  const handleReply = async (parentId: number) => {
    if (!replyContent.trim() || isSubmitting) return;
    
    if (!isAuthenticated) {
      toast.warning('Please log in first', 'You need to log in to reply to comments');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const mockReply: Comment = {
        id: Date.now(),
        author: {
          id: user?.id || 999,
          name: user?.username || 'Current User',
          avatar: user?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.username || 'User')}&background=6366f1&color=fff&size=48`,
          username: user?.username || 'current_user'
        },
        content: replyContent.trim(),
        createdAt: 'just now',
        likes: 0,
        isLiked: false,
        parentId
      };

      setComments(prev => prev.map(comment => 
        comment.id === parentId 
          ? { ...comment, replies: [...(comment.replies || []), mockReply] }
          : comment
      ));
      
      // 通过Socket.IO广播新回复
      if (socketService.isSocketConnected()) {
        socketService.emit('reply_posted', {
          workId: workId,
          workType: workType,
          parentId: parentId,
          reply: mockReply
        });
      }
      
      setReplyContent('');
      setReplyingTo(null);
      toast.success('Reply posted successfully!');
    } catch (error) {
      console.error('Error posting reply:', error);
      toast.error('Failed to post reply', 'Please try again later');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    if (emojiPickerFor === 'main') {
      setNewComment(prev => prev + emoji);
    } else {
      setReplyContent(prev => prev + emoji);
    }
  };

  const renderContent = (content: string) => {
    // 检查是否包含GIF markdown格式
    const gifRegex = /!\[([^\]]+)\]\(([^)]+)\)/g;
    const parts = content.split(gifRegex);
    
    if (parts.length === 1) {
      return content;
    }
    
    const elements = [];
    for (let i = 0; i < parts.length; i += 3) {
      if (parts[i]) {
        elements.push(parts[i]);
      }
      if (parts[i + 1] && parts[i + 2]) {
        elements.push(
          <img
            key={i}
            src={parts[i + 2]}
            alt={parts[i + 1]}
            className="inline-block max-w-32 h-auto rounded-lg mx-1 my-1"
            style={{ maxHeight: '120px' }}
          />
        );
      }
    }
    
    return elements;
  };

  // 排序评论
  const sortComments = (comments: Comment[]) => {
    const sorted = [...comments];
    switch (sortBy) {
      case 'oldest':
        return sorted.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
      case 'mostLiked':
        return sorted.sort((a, b) => b.likes - a.likes);
      case 'newest':
      default:
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    }
  };

  const sortedComments = sortComments(comments);

  return (
    <section className="mt-12">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl font-bold text-gray-900">Comments ({comments.length})</h2>
        
        {/* 排序选项 */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Sort by:</span>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as 'newest' | 'oldest' | 'mostLiked')}
            className="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="newest">Newest</option>
            <option value="mostLiked">Most Liked</option>
            <option value="oldest">Oldest</option>
          </select>
        </div>
      </div>

      {/* Comment Input */}
      <div className="mb-8">
        {authLoading ? (
          <div className="flex space-x-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
            <div className="w-12 h-12 rounded-full bg-gray-300 animate-pulse flex-shrink-0"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-300 rounded animate-pulse mb-2"></div>
              <div className="h-8 bg-gray-300 rounded animate-pulse"></div>
            </div>
          </div>
        ) : isAuthenticated ? (
          <form onSubmit={handleSubmitComment} className="flex space-x-4">
            <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
              {user?.avatar || user?.avatar_url ? (
                <img
                  src={user.avatar || user.avatar_url}
                  alt={user.username || user.name || 'User'}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    const userName = user?.username || user?.name || 'User';
                    const fallbackSrc = `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&background=6366f1&color=fff&size=48`;
                    if (e.target instanceof HTMLImageElement) {
                      e.target.src = fallbackSrc;
                    }
                  }}
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  <span className="text-white font-medium text-lg">
                    {(user?.username || user?.name || 'U').charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
            </div>
            <div className="flex-1">
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder="Add a comment..."
                className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/40 focus:bg-white transition-all duration-200 hover:bg-gray-100/50 resize-none"
                rows={3}
              />
              <div className="flex justify-between items-center mt-3">
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => {
                      setEmojiPickerFor('main');
                      setShowEmojiPicker(!showEmojiPicker);
                    }}
                    className="px-3 py-2 text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    😀
                  </button>
                  {showEmojiPicker && emojiPickerFor === 'main' && (
                    <EmojiPicker
                      onEmojiSelect={handleEmojiSelect}
                      isOpen={showEmojiPicker}
                      onClose={() => setShowEmojiPicker(false)}
                    />
                  )}
                </div>
                <button
                  type="submit"
                  disabled={!newComment.trim() || isSubmitting}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  {isSubmitting ? 'Posting...' : 'Post Comment'}
                </button>
              </div>
            </div>
          </form>
        ) : (
          <div className="flex space-x-4 p-4 bg-gray-50 rounded-xl border border-gray-200">
            <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
              <span className="text-gray-500 text-lg">👤</span>
            </div>
            <div className="flex-1">
              <p className="text-gray-600 mb-3">Please log in to post comments</p>
              <button
                onClick={() => toast.info('Please log in to comment')}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
              >
                Log In to Comment
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Comments List */}
      <div className="space-y-8">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-500 mt-2">Loading comments...</p>
          </div>
        ) : comments.length === 0 ? (
          <div className="text-center py-12 text-gray-500">
            <p>No comments yet. Be the first to share your thoughts!</p>
          </div>
        ) : (
          sortedComments.map((comment) => (
            <div key={comment.id} className="flex space-x-4">
              <div
                className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-500/30 transition-all duration-200"
                onClick={(e) => handleUserClick(comment.author, e)}
                title="Click to view profile"
              >
                <img
                  src={comment.author.avatar}
                  alt={comment.author.name}
                  className="object-cover w-full h-full"
                  onError={(e) => {
                    // 使用React状态管理替代直接DOM操作
                    const fallbackSrc = `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.author.name)}&background=6366f1&color=fff&size=48`;
                    if (e.target instanceof HTMLImageElement) {
                      e.target.src = fallbackSrc;
                    }
                  }}
                />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h4
                    className="font-semibold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                    onClick={(e) => handleUserClick(comment.author, e)}
                    title="Click to view profile"
                  >
                    {comment.author.name}
                  </h4>
                  {comment.author.username && (
                    <span className="text-gray-500 text-sm">@{comment.author.username}</span>
                  )}
                  <span className="text-gray-500 text-sm">{comment.createdAt}</span>
                </div>
                <div className="text-gray-700 leading-relaxed mb-3">{renderContent(comment.content)}</div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => handleLikeComment(comment.id)}
                    className={`flex items-center space-x-1 text-sm transition-colors duration-200 ${
                      comment.isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
                    }`}
                    title={isAuthenticated ? 'Like this comment' : 'Please log in to like'}
                  >
                    <svg
                      className="w-4 h-4"
                      fill={comment.isLiked ? 'currentColor' : 'none'}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                      />
                    </svg>
                    <span>{comment.likes}</span>
                  </button>
                  <button
                    className="text-sm text-gray-500 hover:text-blue-500 transition-colors duration-200"
                    onClick={() => {
                      if (!isAuthenticated) {
                        toast.warning(
                          'Please log in first',
                          'You need to log in to reply to comments'
                        );
                        return;
                      }
                      setReplyingTo(replyingTo === comment.id ? null : comment.id);
                    }}
                    title={isAuthenticated ? 'Reply to this comment' : 'Please log in to reply'}
                  >
                    Reply
                  </button>
                </div>
                
                {/* Reply Form */}
                {replyingTo === comment.id && isAuthenticated && (
                  <div className="mt-4 ml-8">
                    <div className="flex space-x-3">
                      <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0">
                        <img
                          src={user?.avatar || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.username || 'User')}&background=6366f1&color=fff&size=32`}
                          alt="Your avatar"
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1">
                        <textarea
                          value={replyContent}
                          onChange={(e) => setReplyContent(e.target.value)}
                          placeholder={`Reply to ${comment.author.name}...`}
                          className="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg text-sm resize-none"
                          rows={2}
                        />
                        <div className="flex justify-between items-center mt-2">
                          <div className="relative">
                            <button
                              type="button"
                              onClick={() => {
                                setEmojiPickerFor(comment.id);
                                setShowEmojiPicker(!showEmojiPicker);
                              }}
                              className="px-2 py-1 text-gray-500 hover:text-gray-700 transition-colors text-sm"
                            >
                              😀
                            </button>
                            {showEmojiPicker && emojiPickerFor === comment.id && (
                              <EmojiPicker
                                onEmojiSelect={handleEmojiSelect}
                                isOpen={showEmojiPicker}
                                onClose={() => setShowEmojiPicker(false)}
                              />
                            )}
                          </div>
                          <div className="space-x-2">
                            <button
                              onClick={() => {
                                setReplyingTo(null);
                                setReplyContent('');
                              }}
                              className="px-3 py-1 text-sm text-gray-500 hover:text-gray-700"
                            >
                              Cancel
                            </button>
                            <button
                              onClick={() => handleReply(comment.id)}
                              disabled={!replyContent.trim() || isSubmitting}
                              className="px-4 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 disabled:bg-gray-300"
                            >
                              Reply
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                
                {/* Replies */}
                {comment.replies && comment.replies.length > 0 && (
                  <div className="mt-4 ml-8 space-y-4">
                    {comment.replies.map((reply) => (
                      <div key={reply.id} className="flex space-x-3">
                        <div
                          className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 cursor-pointer hover:ring-2 hover:ring-blue-500/30 transition-all duration-200"
                          onClick={(e) => handleUserClick(reply.author, e)}
                        >
                          <img
                            src={reply.author.avatar}
                            alt={reply.author.name}
                            className="object-cover w-full h-full"
                          />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-1">
                            <h5
                              className="text-sm font-medium text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                              onClick={(e) => handleUserClick(reply.author, e)}
                            >
                              {reply.author.name}
                            </h5>
                            <span className="text-gray-500 text-xs">{reply.createdAt}</span>
                          </div>
                          <div className="text-sm text-gray-700 leading-relaxed mb-2">{renderContent(reply.content)}</div>
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={() => handleLikeComment(reply.id)}
                              className={`flex items-center space-x-1 text-xs transition-colors duration-200 ${
                                reply.isLiked ? 'text-red-500' : 'text-gray-500 hover:text-red-500'
                              }`}
                            >
                              <svg className="w-3 h-3" fill={reply.isLiked ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                              </svg>
                              <span>{reply.likes}</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>


    </section>
  );
}