name: OneNews CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: onenews_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: Backend/package-lock.json
    
    - name: Install Backend Dependencies
      run: |
        cd Backend
        npm ci
    
    - name: Create Backend Environment File
      run: |
        cd Backend
        cat > .env << EOF
        NODE_ENV=test
        PORT=5000
        DB_HOST=localhost
        DB_PORT=5432
        DB_NAME=onenews_test
        DB_USER=postgres
        DB_PASSWORD=postgres
        JWT_SECRET=test_jwt_secret
        JWT_EXPIRES_IN=7d
        EOF
    
    - name: Run Backend Tests
      run: |
        cd Backend
        npm test || echo "No tests configured yet"

  test-frontend:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: Frontend/package-lock.json
    
    - name: Install Frontend Dependencies
      run: |
        cd Frontend
        npm ci
    
    - name: Create Frontend Environment File
      run: |
        cd Frontend
        echo "NEXT_PUBLIC_API_URL=http://localhost:5000/api" > .env.local
    
    - name: Lint Frontend
      run: |
        cd Frontend
        npm run lint || echo "Linting completed with warnings"
    
    - name: Build Frontend
      run: |
        cd Frontend
        npm run build
    
    - name: Run Frontend Tests
      run: |
        cd Frontend
        npm test || echo "No tests configured yet"

  build-docker:
    runs-on: ubuntu-latest
    needs: [test-backend, test-frontend]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Backend Docker Image
      run: |
        cd Backend
        docker build -t onenews-backend:latest .
    
    - name: Build Frontend Docker Image
      run: |
        cd Frontend
        docker build -t onenews-frontend:latest .
    
    - name: Test Docker Compose
      run: |
        docker-compose -f config/docker-compose.yml config
