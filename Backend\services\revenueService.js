const { UserBalance, Article, UserBehavior, ReadingStats } = require('../models');
const { Op } = require('sequelize');

class RevenueService {
  constructor() {
    // 收益计算配置
    this.config = {
      // 基础收益率 (每1000次浏览)
      baseRPM: 10.0,
      
      // 不同内容类型的收益倍数
      contentTypeMultipliers: {
        article: 1.0,
        video: 1.5,
        audio: 1.2,
        image: 0.8
      },
      
      // 互动收益 (每次互动的收益)
      interactionRewards: {
        like: 0.05,
        comment: 0.10,
        share: 0.15,
        bookmark: 0.08
      },
      
      // 质量分数影响收益的倍数
      qualityMultipliers: {
        excellent: 1.5,  // 90-100分
        good: 1.2,       // 80-89分
        average: 1.0,    // 70-79分
        poor: 0.8        // 60-69分
      },
      
      // 用户等级收益倍数
      userLevelMultipliers: {
        bronze: 1.0,
        silver: 1.1,
        gold: 1.2,
        platinum: 1.3,
        diamond: 1.5
      },
      
      // 最低提现金额
      minimumWithdrawal: 50.0,
      
      // 平台抽成比例
      platformFee: 0.1 // 10%
    };
  }

  /**
   * 计算文章收益
   * @param {Object} article - 文章对象
   * @param {Object} stats - 统计数据
   * @param {Object} user - 用户对象
   * @returns {Object} 收益详情
   */
  async calculateArticleRevenue(article, stats, user) {
    try {
      // 基础浏览收益
      const viewRevenue = this.calculateViewRevenue(
        stats.views, 
        article.contentType || 'article',
        article.qualityScore || 75
      );

      // 互动收益
      const interactionRevenue = this.calculateInteractionRevenue(stats);

      // 用户等级加成
      const userMultiplier = this.config.userLevelMultipliers[user.level || 'bronze'];

      // 总收益（扣除平台费用前）
      const grossRevenue = (viewRevenue + interactionRevenue) * userMultiplier;
      
      // 平台费用
      const platformFee = grossRevenue * this.config.platformFee;
      
      // 净收益
      const netRevenue = grossRevenue - platformFee;

      return {
        articleId: article.id,
        grossRevenue: parseFloat(grossRevenue.toFixed(4)),
        platformFee: parseFloat(platformFee.toFixed(4)),
        netRevenue: parseFloat(netRevenue.toFixed(4)),
        breakdown: {
          viewRevenue: parseFloat(viewRevenue.toFixed(4)),
          interactionRevenue: parseFloat(interactionRevenue.toFixed(4)),
          userMultiplier,
          qualityScore: article.qualityScore || 75
        },
        calculatedAt: new Date()
      };
    } catch (error) {
      console.error('计算文章收益错误:', error);
      throw error;
    }
  }

  /**
   * 计算浏览收益
   */
  calculateViewRevenue(views, contentType, qualityScore) {
    const baseRevenue = (views / 1000) * this.config.baseRPM;
    const contentMultiplier = this.config.contentTypeMultipliers[contentType] || 1.0;
    const qualityMultiplier = this.getQualityMultiplier(qualityScore);
    
    return baseRevenue * contentMultiplier * qualityMultiplier;
  }

  /**
   * 计算互动收益
   */
  calculateInteractionRevenue(stats) {
    let total = 0;
    
    total += (stats.likes || 0) * this.config.interactionRewards.like;
    total += (stats.comments || 0) * this.config.interactionRewards.comment;
    total += (stats.shares || 0) * this.config.interactionRewards.share;
    total += (stats.bookmarks || 0) * this.config.interactionRewards.bookmark;
    
    return total;
  }

  /**
   * 根据质量分数获取倍数
   */
  getQualityMultiplier(score) {
    if (score >= 90) return this.config.qualityMultipliers.excellent;
    if (score >= 80) return this.config.qualityMultipliers.good;
    if (score >= 70) return this.config.qualityMultipliers.average;
    return this.config.qualityMultipliers.poor;
  }

  /**
   * 批量计算用户所有文章收益
   */
  async calculateUserTotalRevenue(userId, startDate, endDate) {
    try {
      // 获取用户文章
      const articles = await Article.findAll({
        where: {
          authorId: userId,
          createdAt: {
            [Op.between]: [startDate, endDate]
          }
        }
      });

      // 获取用户信息
      const user = await User.findByPk(userId);
      if (!user) {
        throw new Error('用户不存在');
      }

      let totalRevenue = 0;
      const articleRevenues = [];

      for (const article of articles) {
        // 获取文章统计数据
        const stats = await this.getArticleStats(article.id, startDate, endDate);
        
        // 计算收益
        const revenue = await this.calculateArticleRevenue(article, stats, user);
        
        totalRevenue += revenue.netRevenue;
        articleRevenues.push(revenue);
      }

      return {
        userId,
        period: { startDate, endDate },
        totalRevenue: parseFloat(totalRevenue.toFixed(4)),
        articleCount: articles.length,
        articles: articleRevenues,
        calculatedAt: new Date()
      };
    } catch (error) {
      console.error('计算用户总收益错误:', error);
      throw error;
    }
  }

  /**
   * 获取文章统计数据
   */
  async getArticleStats(articleId, startDate, endDate) {
    try {
      // 从阅读统计表获取浏览数据
      const readingStats = await ReadingStats.findAll({
        where: {
          articleId,
          createdAt: {
            [Op.between]: [startDate, endDate]
          }
        }
      });

      // 从用户行为表获取互动数据
      const interactions = await UserBehavior.findAll({
        where: {
          targetType: 'article',
          targetId: articleId,
          createdAt: {
            [Op.between]: [startDate, endDate]
          }
        }
      });

      // 统计数据
      const stats = {
        views: readingStats.length,
        likes: interactions.filter(i => i.actionType === 'like').length,
        comments: interactions.filter(i => i.actionType === 'comment').length,
        shares: interactions.filter(i => i.actionType === 'share').length,
        bookmarks: interactions.filter(i => i.actionType === 'bookmark').length
      };

      return stats;
    } catch (error) {
      console.error('获取文章统计错误:', error);
      return { views: 0, likes: 0, comments: 0, shares: 0, bookmarks: 0 };
    }
  }

  /**
   * 更新用户余额
   */
  async updateUserBalance(userId, amount, type = 'revenue', description = '') {
    try {
      let userBalance = await UserBalance.findOne({ where: { userId } });
      
      if (!userBalance) {
        userBalance = await UserBalance.create({
          userId,
          totalEarnings: 0,
          availableBalance: 0,
          pendingBalance: 0,
          totalWithdrawn: 0
        });
      }

      // 更新余额
      if (type === 'revenue') {
        userBalance.totalEarnings += amount;
        userBalance.availableBalance += amount;
      } else if (type === 'withdrawal') {
        userBalance.availableBalance -= amount;
        userBalance.totalWithdrawn += amount;
      }

      await userBalance.save();

      // 记录余额变动
      await this.recordBalanceTransaction(userId, amount, type, description);

      return userBalance;
    } catch (error) {
      console.error('更新用户余额错误:', error);
      throw error;
    }
  }

  /**
   * 记录余额变动
   */
  async recordBalanceTransaction(userId, amount, type, description) {
    // 这里可以创建一个余额变动记录表来记录所有交易
    console.log(`余额变动记录: 用户${userId}, 金额${amount}, 类型${type}, 描述${description}`);
  }

  /**
   * 检查是否可以提现
   */
  async canWithdraw(userId, amount) {
    try {
      const userBalance = await UserBalance.findOne({ where: { userId } });
      
      if (!userBalance) {
        return { canWithdraw: false, reason: '余额不足' };
      }

      if (amount < this.config.minimumWithdrawal) {
        return { 
          canWithdraw: false, 
          reason: `最低提现金额为 ¥${this.config.minimumWithdrawal}` 
        };
      }

      if (userBalance.availableBalance < amount) {
        return { canWithdraw: false, reason: '可用余额不足' };
      }

      return { canWithdraw: true };
    } catch (error) {
      console.error('检查提现条件错误:', error);
      return { canWithdraw: false, reason: '系统错误' };
    }
  }

  /**
   * 获取收益统计
   */
  async getRevenueStats(userId, period = 'month') {
    try {
      const now = new Date();
      let startDate, endDate = now;

      switch (period) {
        case 'day':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case 'year':
          startDate = new Date(now.getFullYear(), 0, 1);
          break;
        default:
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      }

      const revenueData = await this.calculateUserTotalRevenue(userId, startDate, endDate);
      const userBalance = await UserBalance.findOne({ where: { userId } });

      return {
        period: {
          type: period,
          startDate,
          endDate,
          revenue: revenueData.totalRevenue
        },
        balance: userBalance ? {
          total: userBalance.totalEarnings,
          available: userBalance.availableBalance,
          pending: userBalance.pendingBalance,
          withdrawn: userBalance.totalWithdrawn
        } : null,
        rpm: revenueData.totalRevenue > 0 ? 
          (revenueData.totalRevenue / Math.max(1, revenueData.articles.reduce((sum, a) => sum + a.breakdown.viewRevenue, 0)) * 1000).toFixed(2) : 
          '0.00'
      };
    } catch (error) {
      console.error('获取收益统计错误:', error);
      throw error;
    }
  }
}

module.exports = new RevenueService();