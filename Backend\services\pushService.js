const webpush = require('web-push');
const PushSubscription = require('../models/PushSubscription');
const User = require('../models/User');

class PushService {
  constructor() {
    this.initializeWebPush();
  }

  /**
   * 初始化Web Push配置
   */
  initializeWebPush() {
    // 设置VAPID密钥
    const vapidKeys = {
      publicKey: process.env.VAPID_PUBLIC_KEY || this.generateVapidKeys().publicKey,
      privateKey: process.env.VAPID_PRIVATE_KEY || this.generateVapidKeys().privateKey,
    };

    webpush.setVapidDetails(
      process.env.VAPID_SUBJECT || 'mailto:<EMAIL>',
      vapidKeys.publicKey,
      vapidKeys.privateKey
    );

    this.vapidPublicKey = vapidKeys.publicKey;
    console.log('🔑 Web Push initialized with VAPID keys');
  }

  /**
   * 生成VAPID密钥对（仅用于开发环境）
   */
  generateVapidKeys() {
    try {
      const vapidKeys = webpush.generateVAPIDKeys();
      console.log('🔑 Generated VAPID keys for development:');
      console.log('   Public Key:', vapidKeys.publicKey);
      console.log('   Private Key:', vapidKeys.privateKey);
      console.log('⚠️  Please add these to your .env file for production use');
      return vapidKeys;
    } catch (error) {
      console.error('Error generating VAPID keys:', error);
      // 返回默认密钥（仅用于测试）
      return {
        publicKey:
          'BEl62iUYgUivxIkv69yViEuiBIa40HI0DLLuxN4O5DEdFkE5qMaokQ7vfSKaO2V5tdkBAiB3jUfHeMQisGaPZs4',
        privateKey: 'UUxI4O8-FbRouAevSmBQ6o18hgE4nSG3qwvJTfKc-ls',
      };
    }
  }

  /**
   * 获取VAPID公钥
   */
  getVapidPublicKey() {
    return this.vapidPublicKey;
  }

  /**
   * 保存用户的推送订阅
   */
  async saveSubscription(userId, subscription) {
    try {
      // 检查是否已存在相同的订阅
      const existingSubscription = await PushSubscription.findOne({
        where: {
          userId: userId,
          endpoint: subscription.endpoint,
        },
      });

      if (existingSubscription) {
        // 更新现有订阅
        await existingSubscription.update({
          p256dh: subscription.keys.p256dh,
          auth: subscription.keys.auth,
          isActive: true,
          lastUsed: new Date(),
        });
        console.log(`🔔 Updated push subscription for user ${userId}`);
        return existingSubscription;
      } else {
        // 创建新订阅
        const newSubscription = await PushSubscription.create({
          userId: userId,
          endpoint: subscription.endpoint,
          p256dh: subscription.keys.p256dh,
          auth: subscription.keys.auth,
          isActive: true,
          lastUsed: new Date(),
        });
        console.log(`🔔 Created new push subscription for user ${userId}`);
        return newSubscription;
      }
    } catch (error) {
      console.error('Error saving push subscription:', error);
      throw error;
    }
  }

  /**
   * 删除用户的推送订阅
   */
  async removeSubscription(userId, endpoint) {
    try {
      const result = await PushSubscription.destroy({
        where: {
          userId: userId,
          endpoint: endpoint,
        },
      });

      if (result > 0) {
        console.log(`🔔 Removed push subscription for user ${userId}`);
        return true;
      } else {
        console.log(`🔔 No push subscription found for user ${userId} with endpoint ${endpoint}`);
        return false;
      }
    } catch (error) {
      console.error('Error removing push subscription:', error);
      throw error;
    }
  }

  /**
   * 发送推送通知给单个用户
   */
  async sendPushNotification(userId, notification) {
    try {
      // 获取用户的所有活跃推送订阅
      const subscriptions = await PushSubscription.findAll({
        where: {
          userId: userId,
          isActive: true,
        },
      });

      if (subscriptions.length === 0) {
        console.log(`🔔 No active push subscriptions for user ${userId}`);
        return false;
      }

      // 准备推送消息
      const payload = JSON.stringify({
        title: notification.title,
        body: notification.content,
        icon: notification.imageUrl || '/icons/notification-icon.png',
        badge: '/icons/badge-icon.png',
        tag: `notification-${notification.id}`,
        data: {
          notificationId: notification.id,
          actionUrl: notification.actionUrl,
          timestamp: new Date().toISOString(),
          ...notification.data,
        },
        actions: notification.actionUrl
          ? [
              {
                action: 'view',
                title: '查看详情',
                icon: '/icons/view-icon.png',
              },
              {
                action: 'dismiss',
                title: '忽略',
                icon: '/icons/dismiss-icon.png',
              },
            ]
          : [],
      });

      const options = {
        TTL: 24 * 60 * 60, // 24小时过期
        urgency: this.getUrgencyLevel(notification.priority),
        headers: {
          Topic: 'OneNews-Notifications',
        },
      };

      // 发送到所有订阅
      const results = [];
      for (const subscription of subscriptions) {
        try {
          const pushSubscription = {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: subscription.p256dh,
              auth: subscription.auth,
            },
          };

          const result = await webpush.sendNotification(pushSubscription, payload, options);

          // 更新最后使用时间
          await subscription.update({ lastUsed: new Date() });

          results.push({ success: true, subscriptionId: subscription.id });
          console.log(`🔔 Push notification sent to subscription ${subscription.id}`);
        } catch (error) {
          console.error(`Error sending push to subscription ${subscription.id}:`, error);

          // 如果订阅无效，标记为不活跃
          if (error.statusCode === 410 || error.statusCode === 404) {
            await subscription.update({ isActive: false });
            console.log(`🔔 Marked subscription ${subscription.id} as inactive`);
          }

          results.push({ success: false, subscriptionId: subscription.id, error: error.message });
        }
      }

      const successCount = results.filter((r) => r.success).length;
      console.log(
        `🔔 Push notification sent to ${successCount}/${results.length} subscriptions for user ${userId}`
      );

      return successCount > 0;
    } catch (error) {
      console.error('Error sending push notification:', error);
      return false;
    }
  }

  /**
   * 批量发送推送通知
   */
  async sendBulkPushNotifications(notifications) {
    const results = [];

    for (const notification of notifications) {
      try {
        const success = await this.sendPushNotification(notification.userId, notification);
        results.push({
          notificationId: notification.id,
          userId: notification.userId,
          success: success,
        });
      } catch (error) {
        console.error(`Error sending bulk push notification ${notification.id}:`, error);
        results.push({
          notificationId: notification.id,
          userId: notification.userId,
          success: false,
          error: error.message,
        });
      }
    }

    const successCount = results.filter((r) => r.success).length;
    console.log(`🔔 Bulk push notifications: ${successCount}/${results.length} successful`);

    return results;
  }

  /**
   * 发送推送通知给所有用户
   */
  async sendBroadcastPushNotification(notification) {
    try {
      // 获取所有活跃的推送订阅
      const subscriptions = await PushSubscription.findAll({
        where: { isActive: true },
      });

      if (subscriptions.length === 0) {
        console.log('🔔 No active push subscriptions for broadcast');
        return [];
      }

      console.log(`🔔 Broadcasting push notification to ${subscriptions.length} subscriptions`);

      // 准备推送消息
      const payload = JSON.stringify({
        title: notification.title,
        body: notification.content,
        icon: notification.imageUrl || '/icons/notification-icon.png',
        badge: '/icons/badge-icon.png',
        tag: `broadcast-${Date.now()}`,
        data: {
          actionUrl: notification.actionUrl,
          timestamp: new Date().toISOString(),
          ...notification.data,
        },
      });

      const options = {
        TTL: 24 * 60 * 60,
        urgency: 'normal',
        headers: {
          Topic: 'OneNews-Broadcast',
        },
      };

      // 批量发送
      const results = [];
      const batchSize = 100; // 每批处理100个订阅

      for (let i = 0; i < subscriptions.length; i += batchSize) {
        const batch = subscriptions.slice(i, i + batchSize);
        const batchPromises = batch.map(async (subscription) => {
          try {
            const pushSubscription = {
              endpoint: subscription.endpoint,
              keys: {
                p256dh: subscription.p256dh,
                auth: subscription.auth,
              },
            };

            await webpush.sendNotification(pushSubscription, payload, options);
            await subscription.update({ lastUsed: new Date() });

            return { success: true, subscriptionId: subscription.id, userId: subscription.userId };
          } catch (error) {
            if (error.statusCode === 410 || error.statusCode === 404) {
              await subscription.update({ isActive: false });
            }
            return {
              success: false,
              subscriptionId: subscription.id,
              userId: subscription.userId,
              error: error.message,
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);

        // 短暂延迟避免过载
        if (i + batchSize < subscriptions.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      const successCount = results.filter((r) => r.success).length;
      console.log(
        `🔔 Broadcast push notification sent to ${successCount}/${results.length} subscriptions`
      );

      return results;
    } catch (error) {
      console.error('Error sending broadcast push notification:', error);
      return [];
    }
  }

  /**
   * 获取紧急程度级别
   */
  getUrgencyLevel(priority) {
    switch (priority) {
      case 'urgent':
        return 'high';
      case 'high':
        return 'high';
      case 'normal':
        return 'normal';
      case 'low':
        return 'low';
      default:
        return 'normal';
    }
  }

  /**
   * 清理无效的推送订阅
   */
  async cleanupInactiveSubscriptions() {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const result = await PushSubscription.destroy({
        where: {
          isActive: false,
          updatedAt: {
            [require('sequelize').Op.lt]: thirtyDaysAgo,
          },
        },
      });

      console.log(`🔔 Cleaned up ${result} inactive push subscriptions`);
      return result;
    } catch (error) {
      console.error('Error cleaning up inactive subscriptions:', error);
      return 0;
    }
  }

  /**
   * 获取用户的推送订阅统计
   */
  async getUserSubscriptionStats(userId) {
    try {
      const total = await PushSubscription.count({
        where: { userId: userId },
      });

      const active = await PushSubscription.count({
        where: { userId: userId, isActive: true },
      });

      const inactive = total - active;

      return {
        total: total,
        active: active,
        inactive: inactive,
      };
    } catch (error) {
      console.error('Error getting user subscription stats:', error);
      return { total: 0, active: 0, inactive: 0 };
    }
  }
}

module.exports = new PushService();
