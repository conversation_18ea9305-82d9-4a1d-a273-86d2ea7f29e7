# OneNews Nginx 主配置文件
# 高性能反向代理和负载均衡配置

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 优化工作连接数
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 缓冲区设置
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # 超时设置
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;
    
    # 连接限制
    limit_conn_zone $binary_remote_addr zone=addr:5m;
    limit_req_zone $binary_remote_addr zone=req:10m rate=10r/s;
    
    # 上游服务器定义
    upstream backend {
        least_conn;
        server backend:5000 max_fails=3 fail_timeout=30s;
        # 可以添加更多后端实例进行负载均衡
        # server backend2:5000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    upstream frontend {
        least_conn;
        server frontend:3000 max_fails=3 fail_timeout=30s;
        # 可以添加更多前端实例进行负载均衡
        # server frontend2:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}

# 流量控制（可选）
stream {
    # 可以在这里添加TCP/UDP负载均衡配置
}
