# Newzora 项目根目录整理与优化规则（rule-root-directory.md）

---

## 1. 目录结构与文件规范

- 根目录只保留必要的顶层文件和配置目录，避免杂乱。
- 必须包含且规范以下文件和文件夹：

  | 文件/文件夹          | 说明及要求                                  |
  | -------------------- | ------------------------------------------ |
  | `package.json`       | 明确依赖，包含标准脚本（dev, build, lint）|
  | `tsconfig.json`      | 启用严格模式，保证类型安全                  |
  | `next.config.js`     | Next.js 配置，明确 App Router 相关设置      |
  | `tailwind.config.js` | Tailwind CSS 配置文件                        |
  | `.eslintrc.json`     | 代码风格与语法检查规则                      |
  | `.prettierrc`        | 代码格式化规则                              |
  | `.gitignore`         | 忽略 node_modules、日志、环境变量等          |
  | `.env`, `.env.example`| 环境变量，`.env` 不上传 Git，`.env.example` 为示范|
  | `README.md`          | 项目说明，包含架构、启动、目录介绍          |
  | `public/`            | 静态资源目录                                |
  | `app/`               | Next.js 页面路由入口                        |
  | `components/`        | 可复用 UI 组件                              |
  | `lib/`               | API 和工具函数封装                          |
  | `contexts/`          | React Context 管理状态                      |
  | `types/`             | 全局 TS 类型声明                            |
  | `config/`            | 配置文件，如环境区分常量等                   |

- 避免在根目录出现业务代码、调试文件、临时文件或编译产物。

---

## 2. 依赖与脚本管理

- `package.json` 中依赖需严格区分 `dependencies` 和 `devDependencies`。
- 使用统一包管理工具（npm 或 pnpm），避免混用造成版本不一致。
- 必须提供标准化脚本：
  - `dev`：开发服务器
  - `build`：生产构建
  - `start`：生产启动
  - `lint`：代码检查
  - `typecheck`：类型检查
- 依赖安装前，需检查项目已有依赖，避免重复安装。

---

## 3. 代码质量保障

- 根目录配置 ESLint 和 Prettier，保证统一代码风格。
- 启用 TypeScript 严格模式，杜绝 `any` 类型。
- 配置 Git 钩子（husky + lint-staged），自动检测代码格式和类型。
- 所有代码必须符合 Newzora 代码生成规则（完整、注释、错误处理等）。

---

## 4. 环境变量管理

- 仅根目录存放 `.env` 系列文件。
- `.env` 文件应加入 `.gitignore`，防止敏感信息泄露。
- 提供 `.env.example` 供团队成员参考配置。
- 代码中不得硬编码敏感信息。

---

## 5. 文档规范

- `README.md` 必须包含：
  - 项目技术栈说明
  - 启动和构建指令
  - 项目目录结构说明
  - 代码规范简述
- 可选提供 `CONTRIBUTING.md` 规范团队协作。

---

## 6. 自动化自检与维护

- 定期使用 `depcheck` 等工具清理无用依赖。
- 定期用 `tsc` 和 ESLint 检查类型和代码质量。
- 删除或重命名文件时必须全局搜索引用，避免破坏功能。
- 任何根目录变更都应保证项目能够正常启动和构建。

---

## 7. 兼容性与稳定性

- 根目录所有配置和脚本必须兼容主要开发和部署环境（Node 18+，Vercel、Supabase 等）。
- 避免出现与代码生成规则冲突的配置或文件。
- 根目录文件和目录命名采用统一命名规则（小写+短横线，配置文件除外）。

---

## 8. 自检 Checklist

```markdown
✅ Newzora 根目录规范自检：

- [ ] 仅包含必要顶层文件和目录，无冗余杂乱文件
- [ ] 关键配置文件齐全且符合规范（tsconfig, next.config, tailwind.config 等）
- [ ] package.json 脚本和依赖分类正确，版本统一
- [ ] 环境变量配置合理，.env 不上传 Git
- [ ] ESLint 和 Prettier 配置生效，代码风格统一
- [ ] README 文档完整，含项目结构和启动说明
- [ ] 使用 Git 钩子自动检查代码质量
- [ ] 根目录变更前已确认无影响项目运行的依赖和引用
- [ ] 所有文件和目录命名统一且规范