'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  createdAt: string;
  lastUsed?: string;
  status: 'active' | 'inactive';
}

export default function DeveloperPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');

  useEffect(() => {
    // 模拟获取API密钥列表
    setApiKeys([
      {
        id: '1',
        name: '测试应用',
        key: 'nz_test_1234567890abcdef',
        createdAt: '2024-12-01',
        lastUsed: '2024-12-15',
        status: 'active'
      }
    ]);
  }, []);

  const handleCreateApiKey = () => {
    if (!newKeyName.trim()) return;
    
    const newKey: ApiKey = {
      id: Date.now().toString(),
      name: newKeyName,
      key: `nz_${Math.random().toString(36).substring(2, 15)}`,
      createdAt: new Date().toISOString().split('T')[0],
      status: 'active'
    };
    
    setApiKeys([...apiKeys, newKey]);
    setNewKeyName('');
    setShowCreateForm(false);
  };

  const handleRevokeKey = (keyId: string) => {
    setApiKeys(apiKeys.map(key => 
      key.id === keyId ? { ...key, status: 'inactive' as const } : key
    ));
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-4">开发者中心</h1>
        <p className="text-gray-600">
          管理您的API密钥，集成Newzora平台功能到您的应用中。
        </p>
      </div>

      {/* 快速开始 */}
      <Card className="p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-4">快速开始</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-blue-600 font-bold">1</span>
            </div>
            <h3 className="font-semibold mb-2">创建API密钥</h3>
            <p className="text-sm text-gray-600">
              生成您的API密钥以访问Newzora API
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-blue-600 font-bold">2</span>
            </div>
            <h3 className="font-semibold mb-2">阅读文档</h3>
            <p className="text-sm text-gray-600">
              查看API文档了解如何使用
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-blue-600 font-bold">3</span>
            </div>
            <h3 className="font-semibold mb-2">开始集成</h3>
            <p className="text-sm text-gray-600">
              在您的应用中集成API功能
            </p>
          </div>
        </div>
      </Card>

      {/* API密钥管理 */}
      <Card className="p-6 mb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold">API 密钥</h2>
          <Button onClick={() => setShowCreateForm(true)}>
            创建新密钥
          </Button>
        </div>

        {showCreateForm && (
          <div className="bg-gray-50 p-4 rounded-lg mb-6">
            <h3 className="font-semibold mb-3">创建新的API密钥</h3>
            <div className="flex gap-3">
              <Input
                placeholder="输入应用名称"
                value={newKeyName}
                onChange={(e) => setNewKeyName(e.target.value)}
                className="flex-1"
              />
              <Button onClick={handleCreateApiKey}>创建</Button>
              <Button 
                variant="outline" 
                onClick={() => setShowCreateForm(false)}
              >
                取消
              </Button>
            </div>
          </div>
        )}

        <div className="space-y-4">
          {apiKeys.map((apiKey) => (
            <div key={apiKey.id} className="border rounded-lg p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-semibold">{apiKey.name}</h3>
                  <p className="text-sm text-gray-500">
                    创建于 {apiKey.createdAt}
                    {apiKey.lastUsed && ` • 最后使用 ${apiKey.lastUsed}`}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    apiKey.status === 'active' 
                      ? 'bg-green-100 text-green-700' 
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {apiKey.status === 'active' ? '活跃' : '已停用'}
                  </span>
                  {apiKey.status === 'active' && (
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => handleRevokeKey(apiKey.id)}
                    >
                      撤销
                    </Button>
                  )}
                </div>
              </div>
              <div className="bg-gray-100 p-3 rounded font-mono text-sm">
                {apiKey.key}
              </div>
            </div>
          ))}
        </div>
      </Card>

      {/* 使用统计 */}
      <Card className="p-6 mb-8">
        <h2 className="text-2xl font-semibold mb-4">使用统计</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">1,234</div>
            <div className="text-sm text-gray-600">本月请求</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">98.5%</div>
            <div className="text-sm text-gray-600">成功率</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">45ms</div>
            <div className="text-sm text-gray-600">平均响应时间</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">5,000</div>
            <div className="text-sm text-gray-600">月度限额</div>
          </div>
        </div>
      </Card>

      {/* 资源链接 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="font-semibold mb-3">开发资源</h3>
          <div className="space-y-2">
            <a href="/api-docs" className="block text-blue-600 hover:underline">
              API 文档
            </a>
            <a href="#" className="block text-blue-600 hover:underline">
              SDK 下载
            </a>
            <a href="#" className="block text-blue-600 hover:underline">
              示例代码
            </a>
            <a href="#" className="block text-blue-600 hover:underline">
              Postman 集合
            </a>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="font-semibold mb-3">支持与帮助</h3>
          <div className="space-y-2">
            <a href="#" className="block text-blue-600 hover:underline">
              开发者论坛
            </a>
            <a href="#" className="block text-blue-600 hover:underline">
              技术支持
            </a>
            <a href="#" className="block text-blue-600 hover:underline">
              状态页面
            </a>
            <a href="#" className="block text-blue-600 hover:underline">
              更新日志
            </a>
          </div>
        </Card>
      </div>
    </div>
  );
}