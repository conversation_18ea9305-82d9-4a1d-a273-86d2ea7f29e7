const express = require('express');
const router = express.Router();
const {
  UserBehavior,
  ReadingStats,
  SearchLog,
  UserProfile,
  Article,
  User,
} = require('../models');
const { Op } = require('sequelize');
const { sequelize } = require('../config/database');

// Middleware to extract device info from user agent
const extractDeviceInfo = (userAgent) => {
  if (!userAgent) return { deviceType: null, browserName: null, osName: null };

  const ua = userAgent.toLowerCase();
  let deviceType = 'desktop';
  let browserName = 'unknown';
  let osName = 'unknown';

  // Device detection
  if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(ua)) {
    deviceType = 'mobile';
  } else if (/tablet|ipad/i.test(ua)) {
    deviceType = 'tablet';
  }

  // Browser detection
  if (ua.includes('chrome')) browserName = 'Chrome';
  else if (ua.includes('firefox')) browserName = 'Firefox';
  else if (ua.includes('safari')) browserName = 'Safari';
  else if (ua.includes('edge')) browserName = 'Edge';
  else if (ua.includes('opera')) browserName = 'Opera';

  // OS detection
  if (ua.includes('windows')) osName = 'Windows';
  else if (ua.includes('mac')) osName = 'macOS';
  else if (ua.includes('linux')) osName = 'Linux';
  else if (ua.includes('android')) osName = 'Android';
  else if (ua.includes('ios')) osName = 'iOS';

  return { deviceType, browserName, osName };
};

// Track user behavior event
router.post('/behavior', async (req, res) => {
  try {
    const {
      userId,
      sessionId,
      eventType,
      targetType,
      targetId,
      url,
      referrer,
      duration,
      scrollDepth,
      metadata,
      screenResolution,
    } = req.body;

    // Validate required fields
    if (!sessionId || !eventType) {
      return res.status(400).json({
        message: 'Session ID and event type are required',
      });
    }

    const userAgent = req.headers['user-agent'];
    const ipAddress = req.ip || req.connection.remoteAddress;
    const deviceInfo = extractDeviceInfo(userAgent);

    const behavior = await UserBehavior.create({
      userId: userId || null,
      sessionId,
      eventType,
      targetType,
      targetId,
      url,
      referrer,
      userAgent,
      ipAddress,
      duration,
      scrollDepth,
      metadata: metadata || {},
      screenResolution,
      ...deviceInfo,
    });

    res.status(201).json({
      success: true,
      data: behavior,
    });
  } catch (error) {
    console.error('Error tracking behavior:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track behavior',
    });
  }
});

// Batch track multiple behavior events
router.post('/behavior/batch', async (req, res) => {
  try {
    const { events } = req.body;

    if (!Array.isArray(events) || events.length === 0) {
      return res.status(400).json({
        message: 'Events array is required',
      });
    }

    const userAgent = req.headers['user-agent'];
    const ipAddress = req.ip || req.connection.remoteAddress;
    const deviceInfo = extractDeviceInfo(userAgent);

    const behaviors = events.map((event) => ({
      ...event,
      userAgent,
      ipAddress,
      ...deviceInfo,
      metadata: event.metadata || {},
    }));

    const createdBehaviors = await UserBehavior.bulkCreate(behaviors);

    res.status(201).json({
      success: true,
      data: createdBehaviors,
      count: createdBehaviors.length,
    });
  } catch (error) {
    console.error('Error batch tracking behaviors:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to batch track behaviors',
    });
  }
});

// Get user behavior analytics
router.get('/behavior/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { days = 30, eventType } = req.query;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const whereClause = {
      userId: parseInt(userId),
      createdAt: {
        [Op.gte]: startDate,
      },
    };

    if (eventType) {
      whereClause.eventType = eventType;
    }

    // Get behavior summary
    const summary = await UserBehavior.findAll({
      attributes: [
        'eventType',
        [sequelize.fn('COUNT', '*'), 'count'],
        [sequelize.fn('AVG', sequelize.col('duration')), 'avgDuration'],
        [sequelize.fn('SUM', sequelize.col('duration')), 'totalDuration'],
      ],
      where: whereClause,
      group: ['eventType'],
      order: [[sequelize.literal('count'), 'DESC']],
    });

    // Get daily activity
    const dailyActivity = await UserBehavior.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
        [sequelize.fn('COUNT', '*'), 'events'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('sessionId'))), 'sessions'],
      ],
      where: whereClause,
      group: [sequelize.fn('DATE', sequelize.col('createdAt'))],
      order: [[sequelize.fn('DATE', sequelize.col('createdAt')), 'ASC']],
    });

    // Get device usage
    const deviceUsage = await UserBehavior.findAll({
      attributes: ['deviceType', [sequelize.fn('COUNT', '*'), 'count']],
      where: whereClause,
      group: ['deviceType'],
      order: [[sequelize.literal('count'), 'DESC']],
    });

    res.json({
      success: true,
      data: {
        summary,
        dailyActivity,
        deviceUsage,
        period: `${days} days`,
      },
    });
  } catch (error) {
    console.error('Error getting user behavior analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get behavior analytics',
    });
  }
});

// Get popular content based on behavior
router.get('/behavior/popular-content', async (req, res) => {
  try {
    const { days = 7, limit = 10, eventType = 'article_view' } = req.query;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const popularContent = await UserBehavior.findAll({
      attributes: [
        'targetId',
        [sequelize.fn('COUNT', '*'), 'interactions'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('userId'))), 'uniqueUsers'],
        [sequelize.fn('AVG', sequelize.col('duration')), 'avgDuration'],
      ],
      where: {
        eventType,
        targetType: 'article',
        targetId: { [Op.not]: null },
        createdAt: {
          [Op.gte]: startDate,
        },
      },
      group: ['targetId'],
      order: [[sequelize.literal('interactions'), 'DESC']],
      limit: parseInt(limit),
    });

    // Get article details for popular content
    const articleIds = popularContent.map((item) => item.targetId);
    const articles = await Article.findAll({
      where: { id: { [Op.in]: articleIds } },
      attributes: ['id', 'title', 'category', 'author', 'views', 'likes'],
    });

    const articlesMap = articles.reduce((acc, article) => {
      acc[article.id] = article;
      return acc;
    }, {});

    const result = popularContent.map((item) => ({
      ...item.toJSON(),
      article: articlesMap[item.targetId] || null,
    }));

    res.json({
      success: true,
      data: result,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting popular content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular content',
    });
  }
});

// Get behavior trends
router.get('/behavior/trends', async (req, res) => {
  try {
    const { days = 30, eventType } = req.query;

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const whereClause = {
      createdAt: {
        [Op.gte]: startDate,
      },
    };

    if (eventType) {
      whereClause.eventType = eventType;
    }

    const trends = await UserBehavior.findAll({
      attributes: [
        [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
        'eventType',
        [sequelize.fn('COUNT', '*'), 'count'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('userId'))), 'uniqueUsers'],
        [
          sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('sessionId'))),
          'uniqueSessions',
        ],
      ],
      where: whereClause,
      group: [sequelize.fn('DATE', sequelize.col('createdAt')), 'eventType'],
      order: [
        [sequelize.fn('DATE', sequelize.col('createdAt')), 'ASC'],
        ['eventType', 'ASC'],
      ],
    });

    res.json({
      success: true,
      data: trends,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting behavior trends:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get behavior trends',
    });
  }
});

// Track reading session
router.post('/reading', async (req, res) => {
  try {
    const {
      articleId,
      userId,
      sessionId,
      startTime,
      endTime,
      readingDuration,
      totalTimeOnPage,
      scrollDepth,
      readingProgress,
      isCompleted,
      exitType,
      interactions,
    } = req.body;

    // Validate required fields
    if (!articleId || !sessionId || !startTime) {
      return res.status(400).json({
        message: 'Article ID, session ID, and start time are required',
      });
    }

    const userAgent = req.headers['user-agent'];
    const ipAddress = req.ip || req.connection.remoteAddress;
    const deviceInfo = extractDeviceInfo(userAgent);

    const readingStats = await ReadingStats.create({
      articleId: parseInt(articleId),
      userId: userId || null,
      sessionId,
      startTime: new Date(startTime),
      endTime: endTime ? new Date(endTime) : null,
      readingDuration: readingDuration || 0,
      totalTimeOnPage: totalTimeOnPage || 0,
      scrollDepth: scrollDepth || 0,
      readingProgress: readingProgress || 0,
      isCompleted: isCompleted || false,
      exitType,
      referrer: req.headers.referer,
      userAgent,
      ipAddress,
      interactions: interactions || {},
      deviceType: deviceInfo.deviceType,
    });

    // Calculate engagement score
    readingStats.calculateEngagementScore();
    await readingStats.save();

    res.status(201).json({
      success: true,
      data: readingStats,
    });
  } catch (error) {
    console.error('Error tracking reading stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track reading stats',
    });
  }
});

// Update reading session
router.put('/reading/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const readingStats = await ReadingStats.findByPk(id);
    if (!readingStats) {
      return res.status(404).json({
        success: false,
        message: 'Reading session not found',
      });
    }

    await readingStats.update(updateData);

    // Recalculate engagement score
    readingStats.calculateEngagementScore();
    await readingStats.save();

    res.json({
      success: true,
      data: readingStats,
    });
  } catch (error) {
    console.error('Error updating reading stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update reading stats',
    });
  }
});

// Get article reading statistics
router.get('/reading/article/:articleId', async (req, res) => {
  try {
    const { articleId } = req.params;
    const { days = 30 } = req.query;

    const stats = await ReadingStats.getArticleStats(parseInt(articleId), parseInt(days));

    // Get reading patterns
    const patterns = await ReadingStats.findAll({
      attributes: [
        [sequelize.fn('EXTRACT', sequelize.literal('HOUR FROM "startTime"')), 'hour'],
        [sequelize.fn('COUNT', '*'), 'reads'],
        [sequelize.fn('AVG', sequelize.col('readingDuration')), 'avgDuration'],
      ],
      where: {
        articleId: parseInt(articleId),
        createdAt: {
          [Op.gte]: new Date(Date.now() - parseInt(days) * 24 * 60 * 60 * 1000),
        },
      },
      group: [sequelize.fn('EXTRACT', sequelize.literal('HOUR FROM "startTime"'))],
      order: [[sequelize.fn('EXTRACT', sequelize.literal('HOUR FROM "startTime"')), 'ASC']],
    });

    res.json({
      success: true,
      data: {
        stats: stats || {},
        readingPatterns: patterns,
        period: `${days} days`,
      },
    });
  } catch (error) {
    console.error('Error getting article reading stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get article reading stats',
    });
  }
});

// Track search query
router.post('/search', async (req, res) => {
  try {
    const { userId, sessionId, query, category, filters, resultsCount, searchType } = req.body;

    // Validate required fields
    if (!sessionId || !query) {
      return res.status(400).json({
        message: 'Session ID and query are required',
      });
    }

    const userAgent = req.headers['user-agent'];
    const ipAddress = req.ip || req.connection.remoteAddress;
    const deviceInfo = extractDeviceInfo(userAgent);

    // Normalize query for analysis
    const normalizedQuery = query.toLowerCase().trim().replace(/\s+/g, ' ');

    const searchLog = await SearchLog.create({
      userId: userId || null,
      sessionId,
      query,
      normalizedQuery,
      category,
      filters: filters || {},
      resultsCount: resultsCount || 0,
      searchType: searchType || 'general',
      userAgent,
      ipAddress,
      referrer: req.headers.referer,
      deviceType: deviceInfo.deviceType,
    });

    res.status(201).json({
      success: true,
      data: searchLog,
    });
  } catch (error) {
    console.error('Error tracking search:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track search',
    });
  }
});

// Update search with click data
router.put('/search/:id/click', async (req, res) => {
  try {
    const { id } = req.params;
    const { clickedResults, clickPosition, searchDuration } = req.body;

    const searchLog = await SearchLog.findByPk(id);
    if (!searchLog) {
      return res.status(404).json({
        success: false,
        message: 'Search log not found',
      });
    }

    await searchLog.update({
      clickedResults: clickedResults || [],
      clickPosition,
      hasClicked: clickedResults && clickedResults.length > 0,
      searchDuration,
    });

    // Mark as successful based on behavior
    searchLog.markAsSuccessful();
    await searchLog.save();

    res.json({
      success: true,
      data: searchLog,
    });
  } catch (error) {
    console.error('Error updating search click:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update search click',
    });
  }
});

// Get popular search queries
router.get('/search/popular', async (req, res) => {
  try {
    const { days = 30, limit = 20 } = req.query;

    const popularQueries = await SearchLog.getPopularQueries(parseInt(days), parseInt(limit));

    res.json({
      success: true,
      data: popularQueries,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting popular searches:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular searches',
    });
  }
});

// Get search trends
router.get('/search/trends', async (req, res) => {
  try {
    const { days = 30 } = req.query;

    const trends = await SearchLog.getSearchTrends(parseInt(days));

    res.json({
      success: true,
      data: trends,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting search trends:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get search trends',
    });
  }
});

// Get failed searches for optimization
router.get('/search/failed', async (req, res) => {
  try {
    const { days = 7, limit = 50 } = req.query;

    const failedSearches = await SearchLog.getFailedSearches(parseInt(days), parseInt(limit));

    res.json({
      success: true,
      data: failedSearches,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting failed searches:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get failed searches',
    });
  }
});

// Get top articles by reading metrics
router.get('/reading/top-articles', async (req, res) => {
  try {
    const { days = 7, limit = 10, orderBy = 'reads' } = req.query;

    const topArticles = await ReadingStats.getTopArticles(parseInt(days), parseInt(limit), orderBy);

    // Get article details
    const articleIds = topArticles.map((item) => item.articleId);
    const articles = await Article.findAll({
      where: { id: { [Op.in]: articleIds } },
      attributes: ['id', 'title', 'category', 'author', 'views', 'likes', 'createdAt'],
    });

    const articlesMap = articles.reduce((acc, article) => {
      acc[article.id] = article;
      return acc;
    }, {});

    const result = topArticles.map((item) => ({
      ...item.toJSON(),
      article: articlesMap[item.articleId] || null,
    }));

    res.json({
      success: true,
      data: result,
      period: `${days} days`,
      orderBy,
    });
  } catch (error) {
    console.error('Error getting top articles:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get top articles',
    });
  }
});

// Get reading completion rates by category
router.get('/reading/completion-rates', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const completionRates = await sequelize.query(
      `
      SELECT
        a.category,
        COUNT(rs.*) as total_reads,
        COUNT(CASE WHEN rs."isCompleted" = true THEN 1 END) as completed_reads,
        ROUND(
          COUNT(CASE WHEN rs."isCompleted" = true THEN 1 END) * 100.0 / COUNT(rs.*),
          2
        ) as completion_rate,
        AVG(rs."readingDuration") as avg_duration,
        AVG(rs."scrollDepth") as avg_scroll_depth
      FROM reading_stats rs
      JOIN articles a ON rs."articleId" = a.id
      WHERE rs."createdAt" >= :startDate
      GROUP BY a.category
      ORDER BY completion_rate DESC
    `,
      {
        replacements: { startDate },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    res.json({
      success: true,
      data: completionRates,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting completion rates:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get completion rates',
    });
  }
});

// Get reading patterns by time of day
router.get('/reading/time-patterns', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const timePatterns = await sequelize.query(
      `
      SELECT
        EXTRACT(HOUR FROM "startTime") as hour,
        COUNT(*) as reads,
        AVG("readingDuration") as avg_duration,
        AVG("engagementScore") as avg_engagement,
        COUNT(CASE WHEN "isCompleted" = true THEN 1 END) as completed_reads
      FROM reading_stats
      WHERE "createdAt" >= :startDate
      GROUP BY EXTRACT(HOUR FROM "startTime")
      ORDER BY hour
    `,
      {
        replacements: { startDate },
        type: sequelize.QueryTypes.SELECT,
      }
    );

    res.json({
      success: true,
      data: timePatterns,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting time patterns:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get time patterns',
    });
  }
});

// Get device-based reading analytics
router.get('/reading/device-analytics', async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    const deviceAnalytics = await ReadingStats.findAll({
      attributes: [
        'deviceType',
        [sequelize.fn('COUNT', '*'), 'totalReads'],
        [sequelize.fn('AVG', sequelize.col('readingDuration')), 'avgDuration'],
        [sequelize.fn('AVG', sequelize.col('scrollDepth')), 'avgScrollDepth'],
        [sequelize.fn('AVG', sequelize.col('engagementScore')), 'avgEngagement'],
        [
          sequelize.literal(
            'ROUND(COUNT(CASE WHEN "isCompleted" = true THEN 1 END) * 100.0 / COUNT(*), 2)'
          ),
          'completionRate',
        ],
      ],
      where: {
        createdAt: {
          [Op.gte]: startDate,
        },
      },
      group: ['deviceType'],
      order: [[sequelize.fn('COUNT', '*'), 'DESC']],
    });

    res.json({
      success: true,
      data: deviceAnalytics,
      period: `${days} days`,
    });
  } catch (error) {
    console.error('Error getting device analytics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get device analytics',
    });
  }
});

module.exports = router;
