'use client';

import React, { useState, useEffect } from 'react';
import { Calendar, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import DateRangeSelector, { DateRange } from '@/components/admin/common/DateRangeSelector';
import { 
  filterByDateRange, 
  getPredefinedDateRanges, 
  validateDateRange,
  getDateRangeBoundaries,
  isDateInRange,
  formatDateRange
} from '@/utils/dateFilters';

interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  details?: string;
}

interface MockData {
  id: string;
  title: string;
  createdAt: string;
  publishedAt: string;
}

const TestDateFiltersPage: React.FC = () => {
  const [selectedRange, setSelectedRange] = useState('7d');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [mockData, setMockData] = useState<MockData[]>([]);
  const [filteredData, setFilteredData] = useState<MockData[]>([]);

  // Generate mock data for testing
  useEffect(() => {
    const generateMockData = (): MockData[] => {
      const data: MockData[] = [];
      const now = new Date();
      
      // Generate data for different time periods
      for (let i = 0; i < 100; i++) {
        const daysAgo = Math.floor(Math.random() * 365); // Random day in the past year
        const createdDate = new Date(now);
        createdDate.setDate(now.getDate() - daysAgo);
        
        const publishedDate = new Date(createdDate);
        publishedDate.setHours(publishedDate.getHours() + Math.floor(Math.random() * 24));
        
        data.push({
          id: `item-${i}`,
          title: `Test Item ${i}`,
          createdAt: createdDate.toISOString(),
          publishedAt: publishedDate.toISOString()
        });
      }
      
      return data.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    };

    setMockData(generateMockData());
  }, []);

  // Run comprehensive tests
  const runTests = () => {
    const results: TestResult[] = [];
    const boundaries = getDateRangeBoundaries();

    // Test 1: Basic date range validation
    try {
      const validRange = validateDateRange(new Date('2024-01-01'), new Date('2024-01-31'));
      results.push({
        name: 'Date Range Validation - Valid Range',
        passed: validRange.isValid,
        message: validRange.isValid ? 'Valid date range accepted' : validRange.error || 'Unknown error'
      });
    } catch (error) {
      results.push({
        name: 'Date Range Validation - Valid Range',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    // Test 2: Invalid date range (start after end)
    try {
      const invalidRange = validateDateRange(new Date('2024-01-31'), new Date('2024-01-01'));
      results.push({
        name: 'Date Range Validation - Invalid Range',
        passed: !invalidRange.isValid,
        message: !invalidRange.isValid ? 'Invalid date range correctly rejected' : 'Should have rejected invalid range'
      });
    } catch (error) {
      results.push({
        name: 'Date Range Validation - Invalid Range',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    // Test 3: Today boundary test
    try {
      const today = new Date();
      const todayStart = boundaries.startOfToday;
      const todayEnd = boundaries.endOfToday;
      
      const isInRange = isDateInRange(today, todayStart, todayEnd);
      results.push({
        name: 'Today Boundary Test',
        passed: isInRange,
        message: isInRange ? 'Current time correctly identified as today' : 'Current time not in today range',
        details: `Today: ${today.toLocaleString()}, Range: ${todayStart.toLocaleString()} - ${todayEnd.toLocaleString()}`
      });
    } catch (error) {
      results.push({
        name: 'Today Boundary Test',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    // Test 4: Month boundary test
    try {
      const monthStart = boundaries.startOfMonth;
      const monthEnd = boundaries.endOfMonth;
      const firstDayOfMonth = new Date(monthStart);
      const lastDayOfMonth = new Date(monthEnd);
      
      const firstDayInRange = isDateInRange(firstDayOfMonth, monthStart, monthEnd);
      const lastDayInRange = isDateInRange(lastDayOfMonth, monthStart, monthEnd);
      
      results.push({
        name: 'Month Boundary Test',
        passed: firstDayInRange && lastDayInRange,
        message: firstDayInRange && lastDayInRange ? 'Month boundaries working correctly' : 'Month boundary test failed',
        details: `First day in range: ${firstDayInRange}, Last day in range: ${lastDayInRange}`
      });
    } catch (error) {
      results.push({
        name: 'Month Boundary Test',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    // Test 5: Data filtering test
    if (mockData.length > 0 && currentDateRange) {
      try {
        const filtered = filterByDateRange(mockData, currentDateRange, { dateField: 'createdAt' });
        const allInRange = filtered.every(item => 
          isDateInRange(new Date(item.createdAt), currentDateRange.startDate, currentDateRange.endDate)
        );
        
        results.push({
          name: 'Data Filtering Test',
          passed: allInRange,
          message: allInRange ? 'All filtered data within date range' : 'Some filtered data outside date range',
          details: `Filtered ${filtered.length} items from ${mockData.length} total`
        });
      } catch (error) {
        results.push({
          name: 'Data Filtering Test',
          passed: false,
          message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
        });
      }
    }

    // Test 6: Edge case - Same day range
    try {
      const sameDay = new Date();
      const sameDayRange = validateDateRange(sameDay, sameDay);
      results.push({
        name: 'Same Day Range Test',
        passed: sameDayRange.isValid,
        message: sameDayRange.isValid ? 'Same day range accepted' : sameDayRange.error || 'Same day range rejected'
      });
    } catch (error) {
      results.push({
        name: 'Same Day Range Test',
        passed: false,
        message: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    }

    setTestResults(results);
  };

  const handleDateRangeChange = (range: string, dateRange: DateRange) => {
    setSelectedRange(range);
    setCurrentDateRange(dateRange);
    
    if (mockData.length > 0) {
      const filtered = filterByDateRange(mockData, dateRange, { dateField: 'createdAt' });
      setFilteredData(filtered);
    }
  };

  useEffect(() => {
    if (mockData.length > 0) {
      runTests();
    }
  }, [mockData, currentDateRange]);

  const passedTests = testResults.filter(test => test.passed).length;
  const totalTests = testResults.length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Date Filter Testing</h1>
          <p className="text-gray-600 mt-2">Comprehensive testing of date range selection and filtering functionality</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className={`px-3 py-2 rounded-lg ${
            passedTests === totalTests ? 'bg-green-100 text-green-800' : 
            passedTests > totalTests / 2 ? 'bg-yellow-100 text-yellow-800' : 
            'bg-red-100 text-red-800'
          }`}>
            {passedTests}/{totalTests} Tests Passed
          </div>
          <button
            onClick={runTests}
            className="btn-primary"
          >
            Run Tests
          </button>
        </div>
      </div>

      {/* Date Range Selector Test */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Date Range Selector</h2>
        <div className="flex items-center space-x-4 mb-4">
          <DateRangeSelector
            value={selectedRange}
            onChange={handleDateRangeChange}
            options={getPredefinedDateRanges()}
            showIcon={true}
          />
          {currentDateRange && (
            <div className="text-sm text-gray-600">
              Selected: {formatDateRange(currentDateRange.startDate, currentDateRange.endDate)}
            </div>
          )}
        </div>
        
        {filteredData.length > 0 && (
          <div className="text-sm text-gray-600">
            Showing {filteredData.length} items out of {mockData.length} total
          </div>
        )}
      </div>

      {/* Test Results */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
        <div className="space-y-3">
          {testResults.map((test, index) => (
            <div
              key={index}
              className={`p-4 rounded-lg border ${
                test.passed 
                  ? 'border-green-200 bg-green-50' 
                  : 'border-red-200 bg-red-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                {test.passed ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{test.name}</h3>
                  <p className="text-sm text-gray-600">{test.message}</p>
                  {test.details && (
                    <p className="text-xs text-gray-500 mt-1">{test.details}</p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Sample Data Preview */}
      {filteredData.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Filtered Data Sample</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created At
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Published At
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredData.slice(0, 10).map((item) => (
                  <tr key={item.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {item.title}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(item.createdAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(item.publishedAt).toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            {filteredData.length > 10 && (
              <div className="text-center py-4 text-sm text-gray-500">
                ... and {filteredData.length - 10} more items
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TestDateFiltersPage;
