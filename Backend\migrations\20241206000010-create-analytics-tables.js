'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create user_behaviors table
    await queryInterface.createTable('user_behaviors', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      sessionId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      eventType: {
        type: Sequelize.ENUM(
          'page_view',
          'article_view',
          'article_read',
          'click',
          'scroll',
          'search',
          'like',
          'share',
          'comment',
          'follow',
          'login',
          'logout',
          'register'
        ),
        allowNull: false,
      },
      targetType: {
        type: Sequelize.ENUM('article', 'user', 'page', 'search', 'comment'),
        allowNull: true,
      },
      targetId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      url: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      referrer: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      ipAddress: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      deviceType: {
        type: Sequelize.ENUM('desktop', 'mobile', 'tablet'),
        allowNull: true,
      },
      browserName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      osName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      screenResolution: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      duration: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      scrollDepth: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      location: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Create reading_stats table
    await queryInterface.createTable('reading_stats', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      articleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'articles',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      sessionId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      startTime: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      endTime: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      readingDuration: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      totalTimeOnPage: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      scrollDepth: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      readingProgress: {
        type: Sequelize.FLOAT,
        allowNull: false,
        defaultValue: 0,
      },
      isCompleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      exitType: {
        type: Sequelize.ENUM('completed', 'bounced', 'navigated', 'closed'),
        allowNull: true,
      },
      deviceType: {
        type: Sequelize.ENUM('desktop', 'mobile', 'tablet'),
        allowNull: true,
      },
      referrer: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      ipAddress: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      location: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      engagementScore: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      interactions: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Create search_logs table
    await queryInterface.createTable('search_logs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      sessionId: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      query: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      normalizedQuery: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      category: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      filters: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      resultsCount: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      clickedResults: {
        type: Sequelize.ARRAY(Sequelize.INTEGER),
        defaultValue: [],
      },
      clickPosition: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      hasClicked: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      searchDuration: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      deviceType: {
        type: Sequelize.ENUM('desktop', 'mobile', 'tablet'),
        allowNull: true,
      },
      userAgent: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      ipAddress: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      referrer: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      location: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      searchType: {
        type: Sequelize.ENUM('general', 'autocomplete', 'filter', 'advanced'),
        defaultValue: 'general',
      },
      isSuccessful: {
        type: Sequelize.BOOLEAN,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Create user_profiles table
    await queryInterface.createTable('user_profiles', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        unique: true,
        references: {
          model: 'users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      totalPageViews: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalReadingTime: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalArticlesRead: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      totalSearches: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      lastActiveAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      activeDays: {
        type: Sequelize.INTEGER,
        defaultValue: 0,
      },
      avgSessionDuration: {
        type: Sequelize.FLOAT,
        defaultValue: 0,
      },
      engagementScore: {
        type: Sequelize.FLOAT,
        defaultValue: 0,
      },
      preferredCategories: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      readingPatterns: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      topKeywords: {
        type: Sequelize.ARRAY(Sequelize.STRING),
        defaultValue: [],
      },
      preferredDevice: {
        type: Sequelize.ENUM('desktop', 'mobile', 'tablet'),
        allowNull: true,
      },
      deviceUsage: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      socialActivity: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      recommendationPreferences: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      contentAffinityScores: {
        type: Sequelize.JSONB,
        defaultValue: {},
      },
      userSegment: {
        type: Sequelize.ENUM(
          'new_user',
          'casual_reader',
          'active_reader',
          'power_user',
          'content_creator',
          'inactive'
        ),
        defaultValue: 'new_user',
      },
      contentQualityScore: {
        type: Sequelize.FLOAT,
        defaultValue: 0,
      },
      trustScore: {
        type: Sequelize.FLOAT,
        defaultValue: 50,
      },
      lastUpdatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      profileVersion: {
        type: Sequelize.INTEGER,
        defaultValue: 1,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user_profiles');
    await queryInterface.dropTable('search_logs');
    await queryInterface.dropTable('reading_stats');
    await queryInterface.dropTable('user_behaviors');
  },
};
