'use client';

import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

// Dynamic import component, disable SSR
export function createDynamicComponent<T extends {}>(
  importFunc: () => Promise<{ default: ComponentType<T> }>,
  fallback?: ComponentType<T>
) {
  return dynamic(importFunc, {
    ssr: false,
    loading: () => {
      if (fallback) {
        const FallbackComponent = fallback;
        return <FallbackComponent {...({} as T)} />;
      }
      return <div>Loading...</div>;
    },
  });
}

// Create a generic client-side rendering wrapper
export const ClientRenderWrapper = ({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) => {
  return dynamic(() => Promise.resolve(() => <>{children}</>), {
    ssr: false,
    loading: () => <>{fallback}</>,
  });
};
