#!/bin/bash

# OneNews 数据库备份脚本
# 支持完整备份、增量备份和恢复功能

set -e  # 遇到错误立即退出

# 配置变量
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-onenews}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD}"

# 备份目录配置
BACKUP_DIR="${BACKUP_DIR:-./backups}"
BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
LOG_FILE="${BACKUP_DIR}/backup.log"

# 创建备份目录
mkdir -p "$BACKUP_DIR"
mkdir -p "$BACKUP_DIR/full"
mkdir -p "$BACKUP_DIR/incremental"
mkdir -p "$BACKUP_DIR/logs"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log "ERROR: $1"
    exit 1
}

# 检查依赖
check_dependencies() {
    log "检查依赖工具..."
    
    if ! command -v pg_dump &> /dev/null; then
        error_exit "pg_dump 未安装，请安装 PostgreSQL 客户端工具"
    fi
    
    if ! command -v pg_restore &> /dev/null; then
        error_exit "pg_restore 未安装，请安装 PostgreSQL 客户端工具"
    fi
    
    if [ -z "$DB_PASSWORD" ]; then
        error_exit "数据库密码未设置，请设置 DB_PASSWORD 环境变量"
    fi
    
    log "依赖检查完成"
}

# 测试数据库连接
test_connection() {
    log "测试数据库连接..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if ! pg_isready -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" &> /dev/null; then
        error_exit "无法连接到数据库 $DB_HOST:$DB_PORT/$DB_NAME"
    fi
    
    log "数据库连接正常"
}

# 完整备份
full_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_file="$BACKUP_DIR/full/onenews_full_$timestamp.sql"
    local backup_file_compressed="$backup_file.gz"
    
    log "开始完整备份..."
    log "备份文件: $backup_file_compressed"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行备份
    if pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --verbose \
        --no-password \
        --format=custom \
        --compress=9 \
        --file="$backup_file" \
        2>> "$LOG_FILE"; then
        
        # 压缩备份文件
        gzip "$backup_file"
        
        # 获取备份文件大小
        local file_size=$(du -h "$backup_file_compressed" | cut -f1)
        
        log "完整备份成功完成"
        log "备份文件大小: $file_size"
        log "备份文件路径: $backup_file_compressed"
        
        # 验证备份文件
        if [ -f "$backup_file_compressed" ] && [ -s "$backup_file_compressed" ]; then
            log "备份文件验证成功"
        else
            error_exit "备份文件验证失败"
        fi
        
    else
        error_exit "完整备份失败"
    fi
}

# 增量备份 (基于 WAL 日志)
incremental_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local backup_dir="$BACKUP_DIR/incremental/onenews_incremental_$timestamp"
    
    log "开始增量备份..."
    log "备份目录: $backup_dir"
    
    mkdir -p "$backup_dir"
    
    export PGPASSWORD="$DB_PASSWORD"
    
    # 执行基础备份
    if pg_basebackup \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -D "$backup_dir" \
        --format=tar \
        --gzip \
        --progress \
        --verbose \
        --write-recovery-conf \
        2>> "$LOG_FILE"; then
        
        # 获取备份大小
        local backup_size=$(du -sh "$backup_dir" | cut -f1)
        
        log "增量备份成功完成"
        log "备份大小: $backup_size"
        log "备份路径: $backup_dir"
        
    else
        error_exit "增量备份失败"
    fi
}

# 数据库结构备份
schema_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local schema_file="$BACKUP_DIR/onenews_schema_$timestamp.sql"
    
    log "开始数据库结构备份..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --schema-only \
        --verbose \
        --no-password \
        --file="$schema_file" \
        2>> "$LOG_FILE"; then
        
        gzip "$schema_file"
        log "数据库结构备份完成: $schema_file.gz"
    else
        error_exit "数据库结构备份失败"
    fi
}

# 数据备份 (仅数据，不包含结构)
data_backup() {
    local timestamp=$(date '+%Y%m%d_%H%M%S')
    local data_file="$BACKUP_DIR/onenews_data_$timestamp.sql"
    
    log "开始数据备份..."
    
    export PGPASSWORD="$DB_PASSWORD"
    
    if pg_dump \
        -h "$DB_HOST" \
        -p "$DB_PORT" \
        -U "$DB_USER" \
        -d "$DB_NAME" \
        --data-only \
        --verbose \
        --no-password \
        --file="$data_file" \
        2>> "$LOG_FILE"; then
        
        gzip "$data_file"
        log "数据备份完成: $data_file.gz"
    else
        error_exit "数据备份失败"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log "清理 $BACKUP_RETENTION_DAYS 天前的旧备份..."
    
    # 清理完整备份
    find "$BACKUP_DIR/full" -name "*.sql.gz" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    
    # 清理增量备份
    find "$BACKUP_DIR/incremental" -type d -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
    
    # 清理结构和数据备份
    find "$BACKUP_DIR" -name "onenews_schema_*.sql.gz" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    find "$BACKUP_DIR" -name "onenews_data_*.sql.gz" -mtime +$BACKUP_RETENTION_DAYS -delete 2>/dev/null || true
    
    log "旧备份清理完成"
}

# 备份状态报告
backup_report() {
    log "生成备份状态报告..."
    
    local report_file="$BACKUP_DIR/backup_report_$(date '+%Y%m%d').txt"
    
    {
        echo "OneNews 数据库备份报告"
        echo "生成时间: $(date)"
        echo "================================"
        echo ""
        
        echo "完整备份文件:"
        ls -lh "$BACKUP_DIR/full/" 2>/dev/null | tail -10 || echo "无完整备份文件"
        echo ""
        
        echo "增量备份目录:"
        ls -lh "$BACKUP_DIR/incremental/" 2>/dev/null | tail -10 || echo "无增量备份目录"
        echo ""
        
        echo "备份目录总大小:"
        du -sh "$BACKUP_DIR" 2>/dev/null || echo "无法计算大小"
        echo ""
        
        echo "磁盘空间使用情况:"
        df -h "$BACKUP_DIR" 2>/dev/null || echo "无法获取磁盘信息"
        
    } > "$report_file"
    
    log "备份报告已生成: $report_file"
}

# 主函数
main() {
    local backup_type="${1:-full}"
    
    log "开始 OneNews 数据库备份 (类型: $backup_type)"
    
    # 检查依赖和连接
    check_dependencies
    test_connection
    
    # 根据类型执行备份
    case "$backup_type" in
        "full")
            full_backup
            ;;
        "incremental")
            incremental_backup
            ;;
        "schema")
            schema_backup
            ;;
        "data")
            data_backup
            ;;
        "all")
            full_backup
            schema_backup
            data_backup
            ;;
        *)
            error_exit "未知的备份类型: $backup_type. 支持的类型: full, incremental, schema, data, all"
            ;;
    esac
    
    # 清理旧备份
    cleanup_old_backups
    
    # 生成报告
    backup_report
    
    log "数据库备份任务完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
OneNews 数据库备份脚本

用法: $0 [backup_type]

备份类型:
  full        完整备份 (默认)
  incremental 增量备份
  schema      仅结构备份
  data        仅数据备份
  all         所有类型备份

环境变量:
  DB_HOST                数据库主机 (默认: localhost)
  DB_PORT                数据库端口 (默认: 5432)
  DB_NAME                数据库名称 (默认: onenews)
  DB_USER                数据库用户 (默认: postgres)
  DB_PASSWORD            数据库密码 (必需)
  BACKUP_DIR             备份目录 (默认: ./backups)
  BACKUP_RETENTION_DAYS  备份保留天数 (默认: 30)

示例:
  $0 full                # 完整备份
  $0 incremental         # 增量备份
  $0 all                 # 所有类型备份

EOF
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

main "$@"
