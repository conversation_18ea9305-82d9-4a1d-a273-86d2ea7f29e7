'use client';

import React, { useState } from 'react';
import { Calendar, CheckCircle, Info, Clock, Globe } from 'lucide-react';
import EnhancedDatePicker, { DateRange } from '@/components/admin/common/EnhancedDatePicker';

const TestCalendarImprovementsPage: React.FC = () => {
  const [selectedRange, setSelectedRange] = useState<DateRange | null>(null);
  const [testResults, setTestResults] = useState<Array<{
    feature: string;
    result: string;
    timestamp: string;
    daysDiff?: number;
  }>>([]);

  const handleDateRangeChange = (dateRange: DateRange) => {
    setSelectedRange(dateRange);
    
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    ) + 1;
    
    setTestResults(prev => [...prev, {
      feature: 'Date Range Selection',
      result: `Selected ${daysDiff} days from ${dateRange.startDate.toLocaleDateString('en-US')} to ${dateRange.endDate.toLocaleDateString('en-US')}`,
      timestamp: new Date().toLocaleString('en-US'),
      daysDiff
    }].slice(-10));
  };

  const testFeatures = [
    {
      name: 'Calendar Interface',
      description: 'Visual calendar with clickable dates',
      status: 'completed',
      details: 'Users can click on calendar dates to select start and end dates'
    },
    {
      name: 'Year/Month Picker',
      description: 'Dropdown selectors for year and month navigation',
      status: 'completed',
      details: 'Click on the month/year header to access quick navigation'
    },
    {
      name: 'Days Counter',
      description: 'Automatic calculation and display of selected days',
      status: 'completed',
      details: 'Shows total days in selected range at the top of calendar'
    },
    {
      name: 'English Interface',
      description: 'All text and labels in English',
      status: 'completed',
      details: 'Week headers, month names, and all UI text in English'
    },
    {
      name: 'Quick Filters Removed',
      description: 'Removed preset quick selection buttons',
      status: 'completed',
      details: 'Simplified interface focuses on calendar selection only'
    },
    {
      name: 'Content Type Support',
      description: 'Content management supports articles, videos, and audio',
      status: 'completed',
      details: 'Added type filtering and visual indicators for different content types'
    },
    {
      name: 'Analytics Charts',
      description: 'Added missing charts to user and content analytics',
      status: 'completed',
      details: 'Line charts for user growth, bar charts for activity and content publishing'
    }
  ];

  const implementedPages = [
    {
      name: 'Dashboard',
      path: '/admin/dashboard',
      status: 'updated',
      changes: ['Calendar picker', 'English interface']
    },
    {
      name: 'Content Management',
      path: '/admin/content',
      status: 'enhanced',
      changes: ['Calendar picker', 'Audio/Video support', 'Type filtering']
    },
    {
      name: 'Analytics Overview',
      path: '/admin/analytics',
      status: 'updated',
      changes: ['Calendar picker', 'English interface']
    },
    {
      name: 'User Analytics',
      path: '/admin/analytics/users',
      status: 'enhanced',
      changes: ['Calendar picker', 'Growth charts', 'Activity charts']
    },
    {
      name: 'Content Analytics',
      path: '/admin/analytics/content',
      status: 'enhanced',
      changes: ['Calendar picker', 'Publishing charts', 'Category charts']
    },
    {
      name: 'Monetization',
      path: '/admin/monetization',
      status: 'updated',
      changes: ['Calendar picker', 'English interface']
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Calendar Improvements Testing</h1>
          <p className="text-gray-600 mt-2">Test the enhanced calendar picker with year/month selection and English interface</p>
        </div>
        <div className="flex items-center space-x-2">
          <Globe className="w-6 h-6 text-green-600" />
          <span className="text-sm text-green-600">English Interface</span>
        </div>
      </div>

      {/* Enhanced Calendar Demo */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Enhanced Calendar Picker</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">Select Date Range</h3>
            <EnhancedDatePicker
              value={selectedRange}
              onChange={handleDateRangeChange}
              placeholder="Click to select date range"
              className="w-full"
            />
            
            {selectedRange && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">Selected Range</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p><strong>Start:</strong> {selectedRange.startDate.toLocaleDateString('en-US', { 
                    weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' 
                  })}</p>
                  <p><strong>End:</strong> {selectedRange.endDate.toLocaleDateString('en-US', { 
                    weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' 
                  })}</p>
                  <p><strong>Duration:</strong> {Math.ceil(
                    (selectedRange.endDate.getTime() - selectedRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
                  ) + 1} days</p>
                </div>
              </div>
            )}
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">New Features</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Year/Month Navigation</p>
                  <p className="text-sm text-gray-600">Click on month/year header to quickly navigate</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Days Counter</p>
                  <p className="text-sm text-gray-600">Automatic calculation of selected days</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">English Interface</p>
                  <p className="text-sm text-gray-600">All text and labels in English</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium text-gray-900">Simplified Design</p>
                  <p className="text-sm text-gray-600">Removed quick filters for cleaner interface</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Implementation Status */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Implementation Status</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {testFeatures.map((feature, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{feature.name}</h3>
                  <p className="text-sm text-gray-600 mt-1">{feature.description}</p>
                  <p className="text-xs text-green-600 mt-2">{feature.details}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Updated Pages */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Updated Pages</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {implementedPages.map((page, index) => (
            <div key={index} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-medium text-gray-900">{page.name}</h3>
                <span className={`px-2 py-1 text-xs rounded-full ${
                  page.status === 'enhanced' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                }`}>
                  {page.status}
                </span>
              </div>
              <p className="text-sm text-gray-600 mb-2">{page.path}</p>
              <div className="space-y-1">
                {page.changes.map((change, changeIndex) => (
                  <div key={changeIndex} className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
                    <span className="text-xs text-gray-600">{change}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Test Results */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
        {testResults.length > 0 ? (
          <div className="space-y-3">
            {testResults.slice().reverse().map((result, index) => (
              <div key={index} className={`p-3 rounded-lg border ${index === 0 ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'}`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <p className="font-medium text-gray-900">{result.feature}</p>
                    <p className="text-sm text-gray-600 mt-1">{result.result}</p>
                  </div>
                  <div className="text-xs text-gray-500 ml-4">
                    <Clock className="w-3 h-3 inline mr-1" />
                    {result.timestamp}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No test results yet. Use the calendar picker above to start testing.</p>
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-3">How to Test</h3>
        <div className="space-y-2 text-sm text-gray-700">
          <p>• <strong>Calendar Selection:</strong> Click on dates in the calendar to select start and end dates</p>
          <p>• <strong>Year/Month Navigation:</strong> Click on the month/year header to access dropdown selectors</p>
          <p>• <strong>Days Counter:</strong> Notice the automatic calculation of selected days</p>
          <p>• <strong>English Interface:</strong> All text is now in English (Sun, Mon, Tue, etc.)</p>
          <p>• <strong>Range Selection:</strong> Click start date first, then click end date to complete the range</p>
          <p>• <strong>Clear Selection:</strong> Use the "Clear" button to reset the selection</p>
        </div>
      </div>
    </div>
  );
};

export default TestCalendarImprovementsPage;
