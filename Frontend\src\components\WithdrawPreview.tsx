'use client';

import { 
  CheckCircle, 
  Clock, 
  DollarSign, 
  Shield, 
  Info,
  TrendingDown,
  TrendingUp,
  Calendar,
  ArrowRight,
  AlertTriangle
} from 'lucide-react';

interface WithdrawalPreview {
  amount: number;
  taxRate: number;
  serviceFeeRate: number;
  methodFeeRate: number;
  taxAmount: number;
  serviceFeeAmount: number;
  methodFeeAmount: number;
  totalFeeAmount: number;
  netAmount: number;
  scheduledPaymentDate: string;
  estimatedArrival: string;
}

interface WithdrawPreviewProps {
  preview: WithdrawalPreview;
  paymentMethod: string;
  paymentMethodName: string;
  country: string;
  countryName: string;
  onConfirm: () => void;
  onEdit: () => void;
  isLoading?: boolean;
}

export default function WithdrawPreview({ 
  preview, 
  paymentMethod, 
  paymentMethodName,
  country,
  countryName,
  onConfirm, 
  onEdit,
  isLoading = false
}: WithdrawPreviewProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const feePercentage = ((preview.totalFeeAmount / preview.amount) * 100).toFixed(1);

  return (
    <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-8">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
          <CheckCircle className="h-8 w-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Withdrawal Summary</h2>
        <p className="text-gray-600">Review your withdrawal details before confirming</p>
      </div>

      {/* Main Amount Display */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 mb-8">
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-2">You'll Receive</p>
          <p className="text-4xl font-bold text-green-600 mb-2">${preview.netAmount}</p>
          <p className="text-sm text-gray-500">
            From ${preview.amount} withdrawal ({feePercentage}% total fees)
          </p>
        </div>
      </div>

      {/* Breakdown Section */}
      <div className="space-y-6 mb-8">
        {/* Original Amount */}
        <div className="flex justify-between items-center py-3 border-b border-gray-200">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
              <DollarSign className="h-4 w-4 text-blue-600" />
            </div>
            <span className="font-medium text-gray-900">Withdrawal Amount</span>
          </div>
          <span className="text-xl font-bold text-gray-900">${preview.amount}</span>
        </div>

        {/* Fees Breakdown */}
        <div className="bg-gray-50 rounded-xl p-4 space-y-3">
          <h4 className="font-semibold text-gray-900 flex items-center">
            <TrendingDown className="h-4 w-4 mr-2 text-red-500" />
            Fee Breakdown
          </h4>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Tax ({(preview.taxRate * 100).toFixed(1)}% - {countryName})</span>
              <span className="text-red-600 font-medium">-${preview.taxAmount}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Service Fee ({(preview.serviceFeeRate * 100).toFixed(1)}%)</span>
              <span className="text-orange-600 font-medium">-${preview.serviceFeeAmount}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-gray-600">Payment Method Fee ({(preview.methodFeeRate * 100).toFixed(1)}%)</span>
              <span className="text-red-600 font-medium">-${preview.methodFeeAmount}</span>
            </div>
            
            <div className="border-t border-gray-300 pt-2 mt-2">
              <div className="flex justify-between items-center font-medium">
                <span className="text-gray-700">Total Fees</span>
                <span className="text-red-600">-${preview.totalFeeAmount}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Net Amount */}
        <div className="flex justify-between items-center py-3 border-t border-gray-200">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
            <span className="font-bold text-gray-900">Final Amount</span>
          </div>
          <span className="text-2xl font-bold text-green-600">${preview.netAmount}</span>
        </div>
      </div>

      {/* Payment Details */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8">
        <h4 className="font-semibold text-blue-900 mb-4 flex items-center">
          <Info className="h-4 w-4 mr-2" />
          Payment Details
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-blue-700 font-medium">Payment Method</p>
            <p className="text-blue-900">{paymentMethodName}</p>
          </div>
          
          <div>
            <p className="text-blue-700 font-medium">Processing Time</p>
            <p className="text-blue-900">{preview.estimatedArrival}</p>
          </div>
          
          <div>
            <p className="text-blue-700 font-medium">Scheduled Date</p>
            <p className="text-blue-900 flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              {formatDate(preview.scheduledPaymentDate)}
            </p>
          </div>
          
          <div>
            <p className="text-blue-700 font-medium">Country/Region</p>
            <p className="text-blue-900">{countryName}</p>
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-8">
        <div className="flex items-start">
          <Shield className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
          <div className="text-sm">
            <p className="font-medium text-yellow-800 mb-1">Security Notice</p>
            <p className="text-yellow-700">
              This withdrawal will require two-factor authentication. 
              Make sure you have access to your registered email address.
            </p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex space-x-4">
        <button
          onClick={onEdit}
          disabled={isLoading}
          className="flex-1 bg-gray-200 text-gray-700 py-4 rounded-xl hover:bg-gray-300 transition-all font-semibold disabled:opacity-50"
        >
          Edit Details
        </button>
        
        <button
          onClick={onConfirm}
          disabled={isLoading}
          className={`flex-2 py-4 px-6 rounded-xl font-semibold transition-all duration-300 flex items-center justify-center ${
            isLoading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 shadow-lg hover:shadow-xl transform hover:scale-105'
          }`}
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent mr-2"></div>
              Processing...
            </>
          ) : (
            <>
              Confirm Withdrawal
              <ArrowRight className="h-5 w-5 ml-2" />
            </>
          )}
        </button>
      </div>

      {/* Disclaimer */}
      <div className="mt-6 text-xs text-gray-500 text-center">
        <p>
          By confirming this withdrawal, you agree to our terms of service and fee structure. 
          Processing times may vary based on your payment method and local banking hours.
        </p>
      </div>
    </div>
  );
}
