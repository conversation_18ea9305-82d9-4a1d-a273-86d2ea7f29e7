'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

// 数据统计接口
interface DataStats {
  totalUsers: number;
  totalContent: number;
  totalComments: number;
  totalRevenue: number;
  activeUsers: number;
  publishedContent: number;
  pendingContent: number;
  lastUpdated: string;
}

// 数据同步上下文接口
interface DataSyncContextType {
  stats: DataStats;
  updateStats: (newStats: Partial<DataStats>) => void;
  incrementStat: (key: keyof DataStats, value: number) => void;
  decrementStat: (key: keyof DataStats, value: number) => void;
  refreshStats: () => Promise<void>;
  isLoading: boolean;
}

// 默认统计数据
const defaultStats: DataStats = {
  totalUsers: 15420,
  totalContent: 2340,
  totalComments: 8920,
  totalRevenue: 125600,
  activeUsers: 8950,
  publishedContent: 1890,
  pendingContent: 450,
  lastUpdated: new Date().toISOString()
};

// 创建上下文
const DataSyncContext = createContext<DataSyncContextType | undefined>(undefined);

// 数据同步提供者组件
export const DataSyncProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [stats, setStats] = useState<DataStats>(defaultStats);
  const [isLoading, setIsLoading] = useState(false);

  // 更新统计数据
  const updateStats = useCallback((newStats: Partial<DataStats>) => {
    setStats(prev => ({
      ...prev,
      ...newStats,
      lastUpdated: new Date().toISOString()
    }));
  }, []);

  // 增加统计数据
  const incrementStat = useCallback((key: keyof DataStats, value: number) => {
    setStats(prev => ({
      ...prev,
      [key]: typeof prev[key] === 'number' ? (prev[key] as number) + value : prev[key],
      lastUpdated: new Date().toISOString()
    }));
  }, []);

  // 减少统计数据
  const decrementStat = useCallback((key: keyof DataStats, value: number) => {
    setStats(prev => ({
      ...prev,
      [key]: typeof prev[key] === 'number' ? Math.max(0, (prev[key] as number) - value) : prev[key],
      lastUpdated: new Date().toISOString()
    }));
  }, []);

  // 刷新统计数据
  const refreshStats = useCallback(async () => {
    setIsLoading(true);
    try {
      // 模拟API调用 - 实际项目中应该从API获取最新数据
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用实际的API来获取最新统计数据
      // const response = await fetch('/api/admin/stats');
      // const newStats = await response.json();
      // setStats(newStats);
      
      // 模拟数据更新
      setStats(prev => ({
        ...prev,
        lastUpdated: new Date().toISOString()
      }));
    } catch (error) {
      console.error('Failed to refresh stats:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const value: DataSyncContextType = {
    stats,
    updateStats,
    incrementStat,
    decrementStat,
    refreshStats,
    isLoading
  };

  return (
    <DataSyncContext.Provider value={value}>
      {children}
    </DataSyncContext.Provider>
  );
};

// 使用数据同步上下文的Hook
export const useDataSync = (): DataSyncContextType => {
  const context = useContext(DataSyncContext);
  if (context === undefined) {
    throw new Error('useDataSync must be used within a DataSyncProvider');
  }
  return context;
};

// 数据操作Hook - 提供常用的数据操作方法
export const useDataOperations = () => {
  const { incrementStat, decrementStat, updateStats } = useDataSync();

  return {
    // 用户操作
    addUser: () => incrementStat('totalUsers', 1),
    removeUsers: (count: number) => decrementStat('totalUsers', count),
    
    // 内容操作
    addContent: () => {
      incrementStat('totalContent', 1);
      incrementStat('pendingContent', 1);
    },
    removeContent: (count: number) => {
      decrementStat('totalContent', count);
      decrementStat('publishedContent', Math.min(count, 1)); // 假设删除的内容中有已发布的
    },
    publishContent: (count: number = 1) => {
      decrementStat('pendingContent', count);
      incrementStat('publishedContent', count);
    },
    
    // 评论操作
    addComment: () => incrementStat('totalComments', 1),
    removeComments: (count: number) => decrementStat('totalComments', count),
    
    // 收入操作
    addRevenue: (amount: number) => incrementStat('totalRevenue', amount),
    
    // 批量更新
    batchUpdate: (updates: Partial<DataStats>) => updateStats(updates)
  };
};

export default DataSyncContext;
