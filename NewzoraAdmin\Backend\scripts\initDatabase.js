const { sequelize } = require('../config/database');
const { User, SystemSetting } = require('../models');
const bcrypt = require('bcryptjs');

// 初始化数据库
const initDatabase = async () => {
  try {
    console.log('🔄 开始初始化数据库...');
    
    // 强制同步数据库（会删除现有表）
    await sequelize.sync({ force: true });
    console.log('✅ 数据库表结构创建完成');
    
    // 创建默认超级管理员
    const adminPassword = await bcrypt.hash('admin123456', 12);
    const superAdmin = await User.create({
      username: 'admin',
      email: '<EMAIL>',
      password_hash: adminPassword,
      display_name: '超级管理员',
      role: 'super_admin',
      isActive: true,
      isEmailVerified: true,
      bio: '系统默认超级管理员账户'
    });
    console.log('✅ 超级管理员账户创建完成');
    
    // 创建测试管理员
    const testAdminPassword = await bcrypt.hash('test123456', 12);
    const testAdmin = await User.create({
      username: 'testadmin',
      email: '<EMAIL>',
      password_hash: testAdminPassword,
      display_name: '测试管理员',
      role: 'admin',
      isActive: true,
      isEmailVerified: true,
      bio: '测试管理员账户'
    });
    console.log('✅ 测试管理员账户创建完成');
    
    // 创建测试审核员
    const moderatorPassword = await bcrypt.hash('mod123456', 12);
    const moderator = await User.create({
      username: 'moderator',
      email: '<EMAIL>',
      password_hash: moderatorPassword,
      display_name: '审核员',
      role: 'moderator',
      isActive: true,
      isEmailVerified: true,
      bio: '测试审核员账户'
    });
    console.log('✅ 测试审核员账户创建完成');
    
    // 创建一些测试用户
    const testUsers = [
      {
        username: 'user1',
        email: '<EMAIL>',
        password_hash: await bcrypt.hash('user123456', 12),
        display_name: '测试用户1',
        role: 'user',
        isActive: true,
        isEmailVerified: true
      },
      {
        username: 'user2',
        email: '<EMAIL>',
        password_hash: await bcrypt.hash('user123456', 12),
        display_name: '测试用户2',
        role: 'user',
        isActive: true,
        isEmailVerified: true
      },
      {
        username: 'user3',
        email: '<EMAIL>',
        password_hash: await bcrypt.hash('user123456', 12),
        display_name: '测试用户3',
        role: 'user',
        isActive: false,
        isEmailVerified: false
      }
    ];
    
    await User.bulkCreate(testUsers);
    console.log('✅ 测试用户创建完成');
    
    // 创建系统默认设置
    const defaultSettings = [
      {
        key: 'site_name',
        value: 'Newzora',
        description: '网站名称',
        category: 'basic',
        is_public: true
      },
      {
        key: 'site_description',
        value: '现代化内容管理平台',
        description: '网站描述',
        category: 'basic',
        is_public: true
      },
      {
        key: 'admin_email',
        value: '<EMAIL>',
        description: '管理员邮箱',
        category: 'basic',
        is_public: false
      },
      {
        key: 'max_login_attempts',
        value: 5,
        description: '最大登录尝试次数',
        category: 'security',
        is_public: false
      },
      {
        key: 'session_timeout',
        value: 3600,
        description: '会话超时时间（秒）',
        category: 'security',
        is_public: false
      }
    ];
    
    await SystemSetting.bulkCreate(defaultSettings);
    console.log('✅ 系统默认设置创建完成');
    
    console.log('\n🎉 数据库初始化完成！');
    console.log('\n📋 默认账户信息:');
    console.log('超级管理员: <EMAIL> / admin123456');
    console.log('测试管理员: <EMAIL> / test123456');
    console.log('审核员: <EMAIL> / mod123456');
    console.log('测试用户: <EMAIL> / user123456');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
};

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase()
    .then(() => {
      console.log('✅ 初始化完成，正在退出...');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 初始化失败:', error);
      process.exit(1);
    });
}

module.exports = { initDatabase };