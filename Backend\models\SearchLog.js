const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const SearchLog = sequelize.define(
  'SearchLog',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true, // Allow anonymous search
      references: {
        model: 'users',
        key: 'id',
      },
    },
    sessionId: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '会话ID，用于跟踪匿名用户',
    },
    query: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '搜索查询词',
    },
    normalizedQuery: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: '标准化后的查询词（小写、去空格等）',
    },
    category: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '搜索分类筛选',
    },
    filters: {
      type: DataTypes.JSONB,
      defaultValue: {},
      comment: '搜索过滤条件',
    },
    resultsCount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '搜索结果数量',
    },
    clickedResults: {
      type: DataTypes.ARRAY(DataTypes.INTEGER),
      defaultValue: [],
      comment: '点击的结果ID列表',
    },
    clickPosition: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '首次点击结果的位置',
    },
    hasClicked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: '是否点击了搜索结果',
    },
    searchDuration: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: '搜索页面停留时间（秒）',
    },
    deviceType: {
      type: DataTypes.ENUM('desktop', 'mobile', 'tablet'),
      allowNull: true,
    },
    userAgent: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    ipAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    referrer: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '搜索来源页面',
    },
    location: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: '地理位置信息',
    },
    searchType: {
      type: DataTypes.ENUM('general', 'autocomplete', 'filter', 'advanced'),
      defaultValue: 'general',
      comment: '搜索类型',
    },
    isSuccessful: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      comment: '搜索是否成功（基于用户行为判断）',
    },
  },
  {
    tableName: 'search_logs',
    timestamps: true,
    indexes: [
      {
        fields: ['userId'],
      },
      {
        fields: ['sessionId'],
      },
      {
        fields: ['query'],
      },
      {
        fields: ['normalizedQuery'],
      },
      {
        fields: ['category'],
      },
      {
        fields: ['hasClicked'],
      },
      {
        fields: ['isSuccessful'],
      },
      {
        fields: ['createdAt'],
      },
      {
        fields: ['deviceType'],
      },
      {
        fields: ['searchType'],
      },
      {
        fields: ['normalizedQuery', 'createdAt'],
      },
    ],
  }
);

// 实例方法
SearchLog.prototype.calculateClickThroughRate = function () {
  return this.resultsCount > 0 ? (this.hasClicked ? 1 : 0) : 0;
};

SearchLog.prototype.markAsSuccessful = function () {
  // 基于多个因素判断搜索是否成功
  const hasResults = this.resultsCount > 0;
  const hasClicked = this.hasClicked;
  const reasonableDuration = this.searchDuration && this.searchDuration > 5; // 至少停留5秒

  this.isSuccessful = hasResults && (hasClicked || reasonableDuration);
  return this.isSuccessful;
};

// 类方法
SearchLog.getPopularQueries = async function (days = 30, limit = 20) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return await this.findAll({
    attributes: [
      'normalizedQuery',
      [sequelize.fn('COUNT', '*'), 'searchCount'],
      [sequelize.fn('AVG', sequelize.col('resultsCount')), 'avgResults'],
      [
        sequelize.literal(
          'ROUND(AVG(CASE WHEN "hasClicked" = true THEN 1.0 ELSE 0.0 END) * 100, 2)'
        ),
        'clickThroughRate',
      ],
      [
        sequelize.literal(
          'ROUND(AVG(CASE WHEN "isSuccessful" = true THEN 1.0 ELSE 0.0 END) * 100, 2)'
        ),
        'successRate',
      ],
    ],
    where: {
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate,
      },
    },
    group: ['normalizedQuery'],
    having: sequelize.where(sequelize.fn('COUNT', '*'), '>', 1), // At least searched 2 times
    order: [[sequelize.literal('searchCount'), 'DESC']],
    limit,
  });
};

SearchLog.getSearchTrends = async function (days = 30) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return await this.findAll({
    attributes: [
      [sequelize.fn('DATE', sequelize.col('createdAt')), 'date'],
      [sequelize.fn('COUNT', '*'), 'totalSearches'],
      [
        sequelize.fn('COUNT', sequelize.where(sequelize.col('hasClicked'), true)),
        'clickedSearches',
      ],
      [
        sequelize.fn('COUNT', sequelize.where(sequelize.col('isSuccessful'), true)),
        'successfulSearches',
      ],
      [
        sequelize.fn('COUNT', sequelize.where(sequelize.col('resultsCount'), 0)),
        'noResultSearches',
      ],
    ],
    where: {
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate,
      },
    },
    group: [sequelize.fn('DATE', sequelize.col('createdAt'))],
    order: [[sequelize.fn('DATE', sequelize.col('createdAt')), 'ASC']],
  });
};

SearchLog.getFailedSearches = async function (days = 7, limit = 50) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);

  return await this.findAll({
    attributes: [
      'normalizedQuery',
      [sequelize.fn('COUNT', '*'), 'failureCount'],
      [sequelize.fn('AVG', sequelize.col('resultsCount')), 'avgResults'],
    ],
    where: {
      isSuccessful: false,
      createdAt: {
        [sequelize.Sequelize.Op.gte]: startDate,
      },
    },
    group: ['normalizedQuery'],
    order: [[sequelize.literal('failureCount'), 'DESC']],
    limit,
  });
};

module.exports = SearchLog;
