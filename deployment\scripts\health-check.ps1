# Newzora Health Check Script

param(
    [string]$Environment = "development",
    [switch]$Detailed,
    [switch]$Json,
    [int]$Timeout = 10
)

# Health check results
$healthResults = @{
    timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    environment = $Environment
    overall = "unknown"
    services = @{}
}

# Colors for output
function Write-Success { Write-Host $args -ForegroundColor Green }
function Write-Warning { Write-Host $args -ForegroundColor Yellow }
function Write-Error { Write-Host $args -ForegroundColor Red }
function Write-Info { Write-Host $args -ForegroundColor Cyan }

# Set URLs based on environment
$frontendUrl = if ($Environment -eq "production") { "https://newzora.com" } else { "http://localhost:3000" }
$backendUrl = if ($Environment -eq "production") { "https://api.newzora.com" } else { "http://localhost:5000" }

# Check service health
function Test-ServiceHealth {
    param(
        [string]$Name,
        [string]$Url,
        [string]$ExpectedContent = $null
    )
    
    $result = @{
        name = $Name
        url = $Url
        status = "unknown"
        responseTime = 0
        statusCode = 0
        error = $null
    }
    
    try {
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $Timeout -UseBasicParsing -ErrorAction Stop
        $stopwatch.Stop()
        
        $result.responseTime = $stopwatch.ElapsedMilliseconds
        $result.statusCode = $response.StatusCode
        
        if ($response.StatusCode -eq 200) {
            if ($ExpectedContent -and $response.Content -notlike "*$ExpectedContent*") {
                $result.status = "warning"
                $result.error = "Unexpected content"
            } else {
                $result.status = "healthy"
            }
        } else {
            $result.status = "unhealthy"
            $result.error = "HTTP $($response.StatusCode)"
        }
    }
    catch {
        $result.status = "unhealthy"
        $result.error = $_.Exception.Message
    }
    
    return $result
}

# Check database connectivity (for development)
function Test-DatabaseHealth {
    if ($Environment -eq "development") {
        try {
            $result = Test-ServiceHealth -Name "Database" -Url "$backendUrl/api/health/db"
            return $result
        }
        catch {
            return @{
                name = "Database"
                status = "unhealthy"
                error = "Cannot connect to health endpoint"
            }
        }
    }
    return $null
}

# Check Redis connectivity (for development)
function Test-RedisHealth {
    if ($Environment -eq "development") {
        try {
            $result = Test-ServiceHealth -Name "Redis" -Url "$backendUrl/api/health/redis"
            return $result
        }
        catch {
            return @{
                name = "Redis"
                status = "unhealthy"
                error = "Cannot connect to health endpoint"
            }
        }
    }
    return $null
}

# Main health check
Write-Info "🔍 Newzora Health Check - $Environment Environment"
Write-Info "=================================================="

# Check Frontend
Write-Info "Checking Frontend..."
$frontendHealth = Test-ServiceHealth -Name "Frontend" -Url $frontendUrl -ExpectedContent "Newzora"
$healthResults.services.frontend = $frontendHealth

if ($frontendHealth.status -eq "healthy") {
    Write-Success "✅ Frontend: Healthy ($($frontendHealth.responseTime)ms)"
} elseif ($frontendHealth.status -eq "warning") {
    Write-Warning "⚠️ Frontend: Warning - $($frontendHealth.error) ($($frontendHealth.responseTime)ms)"
} else {
    Write-Error "❌ Frontend: Unhealthy - $($frontendHealth.error)"
}

# Check Backend API
Write-Info "Checking Backend API..."
$backendHealth = Test-ServiceHealth -Name "Backend" -Url "$backendUrl/api/health"
$healthResults.services.backend = $backendHealth

if ($backendHealth.status -eq "healthy") {
    Write-Success "✅ Backend API: Healthy ($($backendHealth.responseTime)ms)"
} elseif ($backendHealth.status -eq "warning") {
    Write-Warning "⚠️ Backend API: Warning - $($backendHealth.error) ($($backendHealth.responseTime)ms)"
} else {
    Write-Error "❌ Backend API: Unhealthy - $($backendHealth.error)"
}

# Check Database (development only)
if ($Environment -eq "development") {
    Write-Info "Checking Database..."
    $dbHealth = Test-DatabaseHealth
    if ($dbHealth) {
        $healthResults.services.database = $dbHealth
        
        if ($dbHealth.status -eq "healthy") {
            Write-Success "✅ Database: Healthy"
        } else {
            Write-Error "❌ Database: Unhealthy - $($dbHealth.error)"
        }
    }
    
    # Check Redis (development only)
    Write-Info "Checking Redis..."
    $redisHealth = Test-RedisHealth
    if ($redisHealth) {
        $healthResults.services.redis = $redisHealth
        
        if ($redisHealth.status -eq "healthy") {
            Write-Success "✅ Redis: Healthy"
        } else {
            Write-Error "❌ Redis: Unhealthy - $($redisHealth.error)"
        }
    }
}

# Determine overall health
$unhealthyServices = $healthResults.services.Values | Where-Object { $_.status -eq "unhealthy" }
$warningServices = $healthResults.services.Values | Where-Object { $_.status -eq "warning" }

if ($unhealthyServices.Count -eq 0) {
    if ($warningServices.Count -eq 0) {
        $healthResults.overall = "healthy"
        Write-Success "`n🎉 Overall Status: HEALTHY"
    } else {
        $healthResults.overall = "warning"
        Write-Warning "`n⚠️ Overall Status: WARNING"
    }
} else {
    $healthResults.overall = "unhealthy"
    Write-Error "`n❌ Overall Status: UNHEALTHY"
}

# Detailed output
if ($Detailed) {
    Write-Info "`n📊 Detailed Health Report:"
    Write-Info "=========================="
    
    foreach ($service in $healthResults.services.Values) {
        Write-Info "`n🔧 $($service.name):"
        Write-Info "   Status: $($service.status)"
        if ($service.url) { Write-Info "   URL: $($service.url)" }
        if ($service.responseTime) { Write-Info "   Response Time: $($service.responseTime)ms" }
        if ($service.statusCode) { Write-Info "   HTTP Status: $($service.statusCode)" }
        if ($service.error) { Write-Info "   Error: $($service.error)" }
    }
}

# JSON output
if ($Json) {
    Write-Info "`n📄 JSON Output:"
    $healthResults | ConvertTo-Json -Depth 3
}

# Exit with appropriate code
if ($healthResults.overall -eq "unhealthy") {
    exit 1
} elseif ($healthResults.overall -eq "warning") {
    exit 2
} else {
    exit 0
}
