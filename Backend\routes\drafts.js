const express = require('express');
const router = express.Router();
const { Draft, Tag, UserTag } = require('../models');
const { authenticateToken } = require('../middleware/auth');
const { Op } = require('sequelize');

// 获取用户草稿列表
router.get(
  '/',
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('status')
      .optional()
      .isIn(['draft', 'auto_saved', 'ready_for_review', 'under_review', 'approved', 'rejected']),
    query('search').optional().isLength({ min: 1, max: 100 }),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const {
        page = 1,
        limit = 20,
        status,
        search,
        orderBy = 'updatedAt',
        orderDirection = 'DESC',
      } = req.query;

      const whereClause = {
        authorId: req.user.id,
        isDeleted: false,
      };

      if (status) {
        whereClause.status = status;
      }

      if (search) {
        whereClause[Op.or] = [
          { title: { [Op.iLike]: `%${search}%` } },
          { content: { [Op.iLike]: `%${search}%` } },
        ];
      }

      const { count, rows: drafts } = await Draft.findAndCountAll({
        where: whereClause,
        order: [[orderBy, orderDirection]],
        limit: parseInt(limit),
        offset: (parseInt(page) - 1) * parseInt(limit),
        include: [
          {
            model: User,
            as: 'author',
            attributes: ['id', 'username', 'email', 'avatar'],
          },
          {
            model: Article,
            as: 'publishedArticle',
            attributes: ['id', 'title', 'slug', 'publishedAt'],
            required: false,
          },
        ],
      });

      res.json({
        success: true,
        data: {
          drafts,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: count,
            pages: Math.ceil(count / parseInt(limit)),
          },
        },
      });
    } catch (error) {
      console.error('Error fetching drafts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 获取单个草稿详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const draft = await Draft.findOne({
      where: {
        id,
        authorId: req.user.id,
        isDeleted: false,
      },
      include: [
        {
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'email', 'avatar'],
        },
        {
          model: Draft,
          as: 'parentDraft',
          attributes: ['id', 'title', 'version'],
          required: false,
        },
        {
          model: Draft,
          as: 'versions',
          attributes: ['id', 'title', 'version', 'createdAt', 'status'],
          required: false,
          order: [['version', 'DESC']],
        },
      ],
    });

    if (!draft) {
      return res.status(404).json({
        success: false,
        message: 'Draft not found',
      });
    }

    res.json({
      success: true,
      data: { draft },
    });
  } catch (error) {
    console.error('Error fetching draft:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// 创建新草稿
router.post(
  '/',
  authenticateToken,
  [
    body('title').optional().isLength({ min: 1, max: 500 }),
    body('content').optional().isLength({ max: 50000 }),
    body('contentHtml').optional().isLength({ max: 100000 }),
    body('excerpt').optional().isLength({ max: 1000 }),
    body('category')
      .optional()
      .isIn([
        'technology',
        'business',
        'health',
        'sports',
        'entertainment',
        'politics',
        'science',
        'lifestyle',
      ]),
    body('tags').optional().isArray({ max: 10 }),
    body('featuredImage').optional().isURL(),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const {
        title = '未命名草稿',
        content = '',
        contentHtml = '',
        excerpt,
        category,
        tags = [],
        featuredImage,
        parentDraftId,
      } = req.body;

      // 计算版本号
      let version = 1;
      if (parentDraftId) {
        const parentDraft = await Draft.findByPk(parentDraftId);
        if (parentDraft && parentDraft.authorId === req.user.id) {
          version = parentDraft.version + 1;
        } else {
          return res.status(400).json({
            success: false,
            message: 'Invalid parent draft',
          });
        }
      }

      const draft = await Draft.create({
        title,
        content,
        contentHtml,
        excerpt: excerpt || (content ? content.substring(0, 200) + '...' : ''),
        category,
        tags,
        featuredImage,
        authorId: req.user.id,
        version,
        parentDraftId,
        status: 'draft',
        lastAutoSaveAt: new Date(),
      });

      // 计算阅读时间
      draft.calculateReadingTime();
      await draft.save();

      res.status(201).json({
        success: true,
        data: { draft },
        message: 'Draft created successfully',
      });
    } catch (error) {
      console.error('Error creating draft:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 更新草稿
router.put(
  '/:id',
  authenticateToken,
  [
    body('title').optional().isLength({ min: 1, max: 500 }),
    body('content').optional().isLength({ max: 50000 }),
    body('contentHtml').optional().isLength({ max: 100000 }),
    body('excerpt').optional().isLength({ max: 1000 }),
    body('category')
      .optional()
      .isIn([
        'technology',
        'business',
        'health',
        'sports',
        'entertainment',
        'politics',
        'science',
        'lifestyle',
      ]),
    body('tags').optional().isArray({ max: 10 }),
    body('featuredImage').optional().isURL(),
    body('status').optional().isIn(['draft', 'ready_for_review']),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { id } = req.params;
      const updateData = req.body;

      const draft = await Draft.findOne({
        where: {
          id,
          authorId: req.user.id,
          isDeleted: false,
        },
      });

      if (!draft) {
        return res.status(404).json({
          success: false,
          message: 'Draft not found',
        });
      }

      // 更新字段
      Object.keys(updateData).forEach((key) => {
        if (updateData[key] !== undefined) {
          draft[key] = updateData[key];
        }
      });

      // 自动生成摘要
      if (updateData.content && !updateData.excerpt) {
        draft.excerpt = draft.generateExcerpt();
      }

      // 重新计算阅读时间
      if (updateData.content) {
        draft.calculateReadingTime();
      }

      draft.lastAutoSaveAt = new Date();
      await draft.save();

      res.json({
        success: true,
        data: { draft },
        message: 'Draft updated successfully',
      });
    } catch (error) {
      console.error('Error updating draft:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 自动保存草稿
router.post(
  '/:id/autosave',
  authenticateToken,
  [
    body('content').optional().isLength({ max: 50000 }),
    body('contentHtml').optional().isLength({ max: 100000 }),
    body('title').optional().isLength({ min: 1, max: 500 }),
  ],
  async (req, res) => {
    try {
      const { id } = req.params;
      const { content, contentHtml, title } = req.body;

      const draft = await Draft.findOne({
        where: {
          id,
          authorId: req.user.id,
          isDeleted: false,
        },
      });

      if (!draft) {
        return res.status(404).json({
          success: false,
          message: 'Draft not found',
        });
      }

      // 只更新内容相关字段
      if (content !== undefined) draft.content = content;
      if (contentHtml !== undefined) draft.contentHtml = contentHtml;
      if (title !== undefined) draft.title = title;

      draft.status = 'auto_saved';
      draft.lastAutoSaveAt = new Date();

      if (content) {
        draft.calculateReadingTime();
        draft.excerpt = draft.generateExcerpt();
      }

      await draft.save();

      res.json({
        success: true,
        data: {
          id: draft.id,
          lastAutoSaveAt: draft.lastAutoSaveAt,
          wordCount: draft.wordCount,
          readingTime: draft.readingTime,
        },
        message: 'Draft auto-saved successfully',
      });
    } catch (error) {
      console.error('Error auto-saving draft:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
);

// 删除草稿（软删除）
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const draft = await Draft.findOne({
      where: {
        id,
        authorId: req.user.id,
        isDeleted: false,
      },
    });

    if (!draft) {
      return res.status(404).json({
        success: false,
        message: 'Draft not found',
      });
    }

    draft.isDeleted = true;
    draft.deletedAt = new Date();
    await draft.save();

    res.json({
      success: true,
      message: 'Draft deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting draft:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// 恢复已删除的草稿
router.post('/:id/restore', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const draft = await Draft.findOne({
      where: {
        id,
        authorId: req.user.id,
        isDeleted: true,
      },
    });

    if (!draft) {
      return res.status(404).json({
        success: false,
        message: 'Deleted draft not found',
      });
    }

    draft.isDeleted = false;
    draft.deletedAt = null;
    await draft.save();

    res.json({
      success: true,
      data: { draft },
      message: 'Draft restored successfully',
    });
  } catch (error) {
    console.error('Error restoring draft:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// 复制草稿
router.post('/:id/duplicate', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const originalDraft = await Draft.findOne({
      where: {
        id,
        authorId: req.user.id,
        isDeleted: false,
      },
    });

    if (!originalDraft) {
      return res.status(404).json({
        success: false,
        message: 'Draft not found',
      });
    }

    const duplicatedDraft = await Draft.create({
      title: `${originalDraft.title} (副本)`,
      content: originalDraft.content,
      contentHtml: originalDraft.contentHtml,
      excerpt: originalDraft.excerpt,
      category: originalDraft.category,
      tags: originalDraft.tags,
      featuredImage: originalDraft.featuredImage,
      authorId: req.user.id,
      version: 1,
      status: 'draft',
      wordCount: originalDraft.wordCount,
      readingTime: originalDraft.readingTime,
      lastAutoSaveAt: new Date(),
    });

    res.status(201).json({
      success: true,
      data: { draft: duplicatedDraft },
      message: 'Draft duplicated successfully',
    });
  } catch (error) {
    console.error('Error duplicating draft:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// 提交草稿审核
router.post('/:id/submit-review', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const draft = await Draft.findOne({
      where: {
        id,
        authorId: req.user.id,
        isDeleted: false,
      },
    });

    if (!draft) {
      return res.status(404).json({
        success: false,
        message: 'Draft not found',
      });
    }

    if (draft.status !== 'draft' && draft.status !== 'auto_saved') {
      return res.status(400).json({
        success: false,
        message: 'Draft is not in a state that can be submitted for review',
      });
    }

    draft.status = 'ready_for_review';
    await draft.save();

    // TODO: 创建审核记录
    // const ContentReview = require('../models/ContentReview');
    // await ContentReview.create({
    //   contentType: 'draft',
    //   contentId: draft.id,
    //   submitterId: req.user.id,
    //   status: 'pending'
    // });

    res.json({
      success: true,
      data: { draft },
      message: 'Draft submitted for review successfully',
    });
  } catch (error) {
    console.error('Error submitting draft for review:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

// 获取草稿统计信息
router.get('/stats/overview', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = await Draft.findAll({
      where: {
        authorId: userId,
        isDeleted: false,
      },
      attributes: ['status', [Draft.sequelize.fn('COUNT', '*'), 'count']],
      group: ['status'],
      raw: true,
    });

    const totalDrafts = await Draft.count({
      where: {
        authorId: userId,
        isDeleted: false,
      },
    });

    const recentDrafts = await Draft.count({
      where: {
        authorId: userId,
        isDeleted: false,
        createdAt: {
          [Op.gte]: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 最近7天
        },
      },
    });

    res.json({
      success: true,
      data: {
        statusBreakdown: stats,
        totalDrafts,
        recentDrafts,
      },
    });
  } catch (error) {
    console.error('Error fetching draft stats:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
    });
  }
});

module.exports = router;


