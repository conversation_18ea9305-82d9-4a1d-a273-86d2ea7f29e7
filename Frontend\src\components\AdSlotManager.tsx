'use client';

import React, { useState } from 'react';
import { AdSlot, AdCampaign } from '@/types';

interface AdSlotManagerProps {
  adSlots: AdSlot[];
  campaigns: AdCampaign[];
  onSaveSlot: (slot: AdSlot) => void;
  onDeleteSlot: (slotId: number) => void;
}

export default function AdSlotManager({ adSlots, campaigns, onSaveSlot, onDeleteSlot }: AdSlotManagerProps) {
  const [editingSlot, setEditingSlot] = useState<AdSlot | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleCreateSlot = () => {
    setEditingSlot({
      id: 0,
      name: '',
      description: '',
      width: 300,
      height: 250,
      position: 'sidebar',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    setIsModalOpen(true);
  };

  const handleEditSlot = (slot: AdSlot) => {
    setEditingSlot({ ...slot });
    setIsModalOpen(true);
  };

  const handleSaveSlot = () => {
    if (editingSlot) {
      onSaveSlot({
        ...editingSlot,
        updatedAt: new Date().toISOString(),
      });
      setIsModalOpen(false);
      setEditingSlot(null);
    }
  };

  const handleDeleteSlot = (slotId: number) => {
    if (confirm('确定要删除这个广告位吗？')) {
      onDeleteSlot(slotId);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">广告位管理</h2>
        <button
          onClick={handleCreateSlot}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          新建广告位
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {adSlots.map((slot) => (
          <div key={slot.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start mb-3">
              <h3 className="font-semibold text-gray-900">{slot.name}</h3>
              <div className="flex space-x-2">
                <button
                  onClick={() => handleEditSlot(slot)}
                  className="text-blue-600 hover:text-blue-800"
                >
                  编辑
                </button>
                <button
                  onClick={() => handleDeleteSlot(slot.id)}
                  className="text-red-600 hover:text-red-800"
                >
                  删除
                </button>
              </div>
            </div>
            
            <p className="text-gray-600 text-sm mb-3">{slot.description}</p>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">尺寸:</span>
                <span>{slot.width} × {slot.height}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">位置:</span>
                <span className="capitalize">{slot.position}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">状态:</span>
                <span className={slot.isActive ? 'text-green-600' : 'text-red-600'}>
                  {slot.isActive ? '启用' : '停用'}
                </span>
              </div>
            </div>
            
            <div className="mt-4 pt-3 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                相关广告活动: {campaigns.filter(c => c.status === 'active').length} 个
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 编辑/创建模态框 */}
      {isModalOpen && editingSlot && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg w-full max-w-md">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {editingSlot.id ? '编辑广告位' : '新建广告位'}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    广告位名称
                  </label>
                  <input
                    type="text"
                    value={editingSlot.name}
                    onChange={(e) => setEditingSlot({...editingSlot, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <textarea
                    value={editingSlot.description}
                    onChange={(e) => setEditingSlot({...editingSlot, description: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      宽度 (px)
                    </label>
                    <input
                      type="number"
                      value={editingSlot.width}
                      onChange={(e) => setEditingSlot({...editingSlot, width: parseInt(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      高度 (px)
                    </label>
                    <input
                      type="number"
                      value={editingSlot.height}
                      onChange={(e) => setEditingSlot({...editingSlot, height: parseInt(e.target.value) || 0})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    位置
                  </label>
                  <select
                    value={editingSlot.position}
                    onChange={(e) => setEditingSlot({...editingSlot, position: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="header">页头</option>
                    <option value="sidebar">侧边栏</option>
                    <option value="content_top">内容上方</option>
                    <option value="content_bottom">内容下方</option>
                    <option value="footer">页脚</option>
                  </select>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={editingSlot.isActive}
                    onChange={(e) => setEditingSlot({...editingSlot, isActive: e.target.checked})}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    启用广告位
                  </label>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 px-6 py-3 rounded-b-lg flex justify-end space-x-3">
              <button
                onClick={() => setIsModalOpen(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-100"
              >
                取消
              </button>
              <button
                onClick={handleSaveSlot}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                保存
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}