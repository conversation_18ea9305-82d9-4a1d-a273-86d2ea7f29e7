/**
 * DOM operation utility functions - prevent React DOM errors
 */

/**
 * Safely remove DOM nodes, prevent removeChild errors
 */
export function safeRemoveChild(parent: Node, child: Node): boolean {
  try {
    if (parent && child && parent.contains(child)) {
      parent.removeChild(child);
      return true;
    }
  } catch (error) {
    console.warn('Safe removeChild failed:', error);
  }
  return false;
}

/**
 * Safely add DOM nodes
 */
export function safeAppendChild(parent: Node, child: Node): boolean {
  try {
    if (parent && child && !parent.contains(child)) {
      parent.appendChild(child);
      return true;
    }
  } catch (error) {
    console.warn('Safe appendChild failed:', error);
  }
  return false;
}

/**
 * Check if node is still in DOM
 */
export function isNodeInDOM(node: Node): boolean {
  return document.contains(node);
}

/**
 * Safely clean up timers
 */
export function safeTimeout(callback: () => void, delay: number): () => void {
  const timeoutId = setTimeout(callback, delay);
  return () => {
    try {
      clearTimeout(timeoutId);
    } catch (error) {
      console.warn('Safe timeout cleanup failed:', error);
    }
  };
}

/**
 * Debounce function - prevent frequent DOM operations
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function - limit DOM operation frequency
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}