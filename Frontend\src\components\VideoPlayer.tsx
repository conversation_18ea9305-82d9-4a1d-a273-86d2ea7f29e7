'use client';

import { useState, useRef, useEffect } from 'react';

interface VideoSource {
  quality: string;
  url: string;
  label: string;
}

interface VideoPlayerProps {
  videoUrl?: string;
  src?: string;
  sources?: VideoSource[];
  title: string;
  thumbnail?: string;
  poster?: string;
  autoPlay?: boolean;
  controls?: boolean;
  className?: string;
  enablePictureInPicture?: boolean;
  subtitles?: Array<{id: string, language: string, label: string, url: string, isDefault: boolean}>;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onPictureInPictureChange?: (isPip: boolean) => void;
}

export default function VideoPlayer({
  videoUrl,
  src,
  sources,
  title,
  thumbnail,
  poster,
  autoPlay = false,
  controls = true,
  className = '',
  enablePictureInPicture = false,
  subtitles = [],
  onTimeUpdate,
  onPlay,
  onPause,
  onEnded,
  onPictureInPictureChange
}: VideoPlayerProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentQuality, setCurrentQuality] = useState<string>('1080p');
  const [showQualityMenu, setShowQualityMenu] = useState(false);
  const [displayMode, setDisplayMode] = useState<'small' | 'medium' | 'fullscreen'>('medium');
  const [isPictureInPicture, setIsPictureInPicture] = useState(false);
  const [showDanmaku, setShowDanmaku] = useState(true);
  const [danmakuList, setDanmakuList] = useState<Array<{id: number, text: string, time: number, color: string}>>([]);
  const [showSubtitles, setShowSubtitles] = useState(true);
  const [currentSubtitleLang, setCurrentSubtitleLang] = useState('en');
  const [showSubtitleMenu, setShowSubtitleMenu] = useState(false);
  const [subtitleTracks, setSubtitleTracks] = useState<Array<{id: string, language: string, label: string, url: string, isDefault: boolean}>>([]);
  const [currentSubtitle, setCurrentSubtitle] = useState<string>('');

  // 使用src或videoUrl
  const videoSrc = src || videoUrl;
  const videoPoster = poster || thumbnail;
  
  // 初始化字幕
  useEffect(() => {
    if (subtitles.length > 0) {
      setSubtitleTracks(subtitles);
      const defaultSub = subtitles.find(sub => sub.isDefault) || subtitles[0];
      setCurrentSubtitleLang(defaultSub.language);
    }
  }, [subtitles]);
  
  // 模拟字幕数据
  const mockSubtitles = [
    { id: 'en', language: 'en', label: 'English', url: '', isDefault: true },
    { id: 'zh', language: 'zh', label: '中文', url: '', isDefault: false },
    { id: 'es', language: 'es', label: 'Español', url: '', isDefault: false },
    { id: 'fr', language: 'fr', label: 'Français', url: '', isDefault: false },
    { id: 'de', language: 'de', label: 'Deutsch', url: '', isDefault: false },
    { id: 'ja', language: 'ja', label: '日本語', url: '', isDefault: false },
    { id: 'ko', language: 'ko', label: '한국어', url: '', isDefault: false },
    { id: 'ar', language: 'ar', label: 'العربية', url: '', isDefault: false },
    { id: 'ru', language: 'ru', label: 'Русский', url: '', isDefault: false },
    { id: 'pt', language: 'pt', label: 'Português', url: '', isDefault: false }
  ];
  
  // 模拟字幕内容
  const getSubtitleText = (time: number, lang: string) => {
    const subtitleTexts: Record<string, string[]> = {
      en: ['Welcome to our video tutorial', 'Today we will learn about...', 'This is an important concept'],
      zh: ['欢迎观看我们的视频教程', '今天我们将学习关于...', '这是一个重要的概念'],
      es: ['Bienvenido a nuestro tutorial en video', 'Hoy aprenderemos sobre...', 'Este es un concepto importante'],
      fr: ['Bienvenue dans notre tutoriel vidéo', 'Aujourd\'hui nous allons apprendre...', 'C\'est un concept important'],
      de: ['Willkommen zu unserem Video-Tutorial', 'Heute lernen wir über...', 'Das ist ein wichtiges Konzept'],
      ja: ['ビデオチュートリアルへようこそ', '今日は...について学びます', 'これは重要な概念です'],
      ko: ['비디오 튜토리얼에 오신 것을 환영합니다', '오늘 우리는...에 대해 배울 것입니다', '이것은 중요한 개념입니다'],
      ar: ['مرحبا بكم في البرنامج التعليمي', 'اليوم سوف نتعلم عن...', 'هذا مفهوم مهم'],
      ru: ['Добро пожаловать в наш видеоурок', 'Сегодня мы изучим...', 'Это важная концепция'],
      pt: ['Bem-vindos ao nosso tutorial em vídeo', 'Hoje vamos aprender sobre...', 'Este é um conceito importante']
    };
    
    const texts = subtitleTexts[lang] || subtitleTexts.en;
    const index = Math.floor(time / 10) % texts.length;
    return texts[index];
  };
  
  // 更新字幕显示
  useEffect(() => {
    if (showSubtitles && currentTime > 0) {
      setCurrentSubtitle(getSubtitleText(currentTime, currentSubtitleLang));
    } else {
      setCurrentSubtitle('');
    }
  }, [currentTime, currentSubtitleLang, showSubtitles]);
  
  // 切换字幕语言
  const handleSubtitleLanguageChange = (lang: string) => {
    setCurrentSubtitleLang(lang);
    setShowSubtitleMenu(false);
  };
  
  // Default video sources if not provided
  const defaultSources: VideoSource[] = [
    { quality: '480p', url: videoSrc || '', label: '480p' },
    { quality: '720p', url: videoSrc || '', label: '720p HD' },
    { quality: '1080p', url: videoSrc || '', label: '1080p Full HD' },
    { quality: '1440p', url: videoSrc || '', label: '1440p 2K' },
    { quality: '2160p', url: videoSrc || '', label: '2160p 4K' },
    { quality: '4320p', url: videoSrc || '', label: '4320p 8K' }
  ];
  
  const videoSources = sources || defaultSources;
  
  // Get current video source based on quality
  const getCurrentVideoSrc = () => {
    const source = videoSources.find(s => s.quality === currentQuality);
    return source?.url || videoSrc;
  };
  
  // Handle quality change
  const handleQualityChange = (quality: string) => {
    if (!videoRef.current) return;
    
    const currentTime = videoRef.current.currentTime;
    const wasPlaying = !videoRef.current.paused;
    
    setCurrentQuality(quality);
    setShowQualityMenu(false);
    
    // Force video source update
    const newSource = videoSources.find(s => s.quality === quality);
    if (newSource && newSource.url) {
      videoRef.current.src = newSource.url;
      videoRef.current.load();
      
      // Restore playback position after load
      const handleLoadedData = () => {
        if (videoRef.current) {
          videoRef.current.currentTime = currentTime;
          if (wasPlaying) {
            videoRef.current.play();
          }
          videoRef.current.removeEventListener('loadeddata', handleLoadedData);
        }
      };
      
      videoRef.current.addEventListener('loadeddata', handleLoadedData);
    }
  };
  
  // Handle display mode change
  const handleDisplayModeChange = (mode: 'small' | 'medium' | 'fullscreen') => {
    setDisplayMode(mode);
    if (mode === 'fullscreen' && videoRef.current) {
      videoRef.current.requestFullscreen?.();
    }
  };
  
  // Handle picture-in-picture
  const handlePictureInPicture = async () => {
    if (!videoRef.current || !enablePictureInPicture) return;
    
    try {
      if (isPictureInPicture) {
        await document.exitPictureInPicture?.();
      } else {
        await videoRef.current.requestPictureInPicture?.();
      }
      const newPipState = !isPictureInPicture;
      setIsPictureInPicture(newPipState);
      onPictureInPictureChange?.(newPipState);
    } catch (error) {
      console.error('Picture-in-picture error:', error);
    }
  };
  
  // Get container class based on display mode
  const getContainerClass = () => {
    switch (displayMode) {
      case 'small':
        return 'w-80 h-48';
      case 'fullscreen':
        return 'w-full h-full fixed inset-0 z-50 bg-black';
      default:
        return className;
    }
  };

  // 播放/暂停
  const togglePlay = () => {
    if (!videoRef.current) return;

    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
  };

  // 音量控制
  const toggleMute = () => {
    if (!videoRef.current) return;

    const newMuted = !isMuted;
    setIsMuted(newMuted);
    videoRef.current.muted = newMuted;
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!videoRef.current) return;

    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    videoRef.current.volume = newVolume;

    if (newVolume === 0) {
      setIsMuted(true);
      videoRef.current.muted = true;
    } else if (isMuted) {
      setIsMuted(false);
      videoRef.current.muted = false;
    }
  };

  // 进度控制
  const handleProgressClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!videoRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const width = rect.width;
    const percentage = clickX / width;
    const newTime = percentage * duration;

    videoRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // 视频事件处理
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
      setIsLoading(false);
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const current = videoRef.current.currentTime;
      setCurrentTime(current);
      onTimeUpdate?.(current, duration);
    }
  };

  const handlePlay = () => {
    setIsPlaying(true);
    onPlay?.();
  };

  const handlePause = () => {
    setIsPlaying(false);
    onPause?.();
  };

  const handleEnded = () => {
    setIsPlaying(false);
    onEnded?.();
  };

  const handleError = () => {
    setError('Video temporarily unavailable');
    setIsLoading(false);
  };

  // 格式化时间
  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  // 如果没有视频源，显示占位符
  if (!videoSrc) {
    return (
      <div className={`relative w-full h-96 rounded-2xl overflow-hidden bg-gradient-to-br from-orange-100 via-orange-200 to-orange-300 ${className}`}>
        <div className="absolute inset-0">
          <div className="absolute top-20 left-20 w-16 h-16 bg-orange-300 rounded-full opacity-70"></div>
          <div className="absolute top-12 right-32 w-48 h-48 bg-orange-400 rounded-full opacity-50"></div>
          <div className="absolute bottom-8 right-8 w-64 h-64 bg-orange-600 rounded-full opacity-40"></div>
          <div className="absolute bottom-16 left-16 w-32 h-32 bg-orange-500 rounded-full opacity-60"></div>
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <button className="w-20 h-20 bg-black bg-opacity-60 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-all duration-300 transform hover:scale-105">
            <svg className="w-8 h-8 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`relative bg-black rounded-lg overflow-hidden ${getContainerClass()}`}
      onMouseEnter={() => setShowControls(true)}
      onMouseLeave={() => !isPlaying || setShowControls(true)}
      onMouseMove={() => setShowControls(true)}
    >
      <video
        ref={videoRef}
        src={getCurrentVideoSrc()}
        poster={videoPoster}
        className="w-full h-full object-contain"
        autoPlay={autoPlay}
        onLoadedMetadata={handleLoadedMetadata}
        onTimeUpdate={handleTimeUpdate}
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
        onError={handleError}
        onClick={togglePlay}
      />

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-800 to-gray-900">
          <div className="text-white text-center p-8">
            <div className="text-6xl mb-4">🎬</div>
            <p className="text-xl mb-2">Video Preview</p>
            <p className="text-sm text-gray-300 mb-4">{error}</p>
            <p className="text-xs text-gray-400">This is a demo video player</p>
          </div>
        </div>
      )}

      {!isPlaying && !isLoading && !error && (
        <div className="absolute inset-0 flex items-center justify-center">
          <button
            onClick={togglePlay}
            className="w-20 h-20 bg-black bg-opacity-50 rounded-full flex items-center justify-center hover:bg-opacity-70 transition-all duration-200"
          >
            <svg className="w-10 h-10 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </button>
        </div>
      )}

      {showControls && !error && controls && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-4">
          <div
            className="w-full h-2 bg-gray-600 rounded-full cursor-pointer mb-4"
            onClick={handleProgressClick}
          >
            <div
              className="h-full bg-blue-500 rounded-full transition-all duration-100"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={togglePlay}
                className="text-white hover:text-blue-400 transition-colors"
              >
                {isPlaying ? (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                  </svg>
                ) : (
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                )}
              </button>

              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="text-white hover:text-blue-400 transition-colors"
                >
                  {isMuted || volume === 0 ? (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.586 15H4a1 1 0 01-1-1v-4a1 1 0 011-1h1.586l4.707-4.707C10.923 3.663 12 4.109 12 5v14c0 .891-1.077 1.337-1.707.707L5.586 15z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2" />
                    </svg>
                  ) : (
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12H5a1 1 0 01-1-1V9a1 1 0 011-1h4l5-5v16l-5-5z" />
                    </svg>
                  )}
                </button>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={isMuted ? 0 : volume}
                  onChange={handleVolumeChange}
                  className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                />
              </div>

              <div className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Subtitles Toggle */}
              <button
                onClick={() => setShowSubtitles(!showSubtitles)}
                className={`transition-colors ${
                  showSubtitles ? 'text-blue-400' : 'text-white hover:text-blue-400'
                }`}
                title="Toggle subtitles"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v2M7 4h10v16H7V4z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 8h6M9 12h6M9 16h4" />
                </svg>
              </button>
              
              {/* Subtitle Language Selector */}
              <div className="relative">
                <button
                  onClick={() => setShowSubtitleMenu(!showSubtitleMenu)}
                  className="text-white hover:text-blue-400 transition-colors flex items-center space-x-1"
                  title="Subtitle language"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                  </svg>
                  <span className="text-sm uppercase">{currentSubtitleLang}</span>
                </button>
                
                {showSubtitleMenu && (
                  <div className="absolute bottom-full right-0 mb-2 bg-black bg-opacity-90 rounded-lg py-2 min-w-[140px] max-h-48 overflow-y-auto">
                    <div className="px-3 py-1 text-xs text-gray-400 border-b border-gray-600 mb-1">Subtitles</div>
                    <button
                      onClick={() => {
                        setShowSubtitles(false);
                        setShowSubtitleMenu(false);
                      }}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-white hover:bg-opacity-20 transition-colors ${
                        !showSubtitles ? 'text-blue-400' : 'text-white'
                      }`}
                    >
                      Off
                    </button>
                    {mockSubtitles.map((subtitle) => (
                      <button
                        key={subtitle.id}
                        onClick={() => {
                          handleSubtitleLanguageChange(subtitle.language);
                          setShowSubtitles(true);
                        }}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-white hover:bg-opacity-20 transition-colors ${
                          currentSubtitleLang === subtitle.language && showSubtitles ? 'text-blue-400' : 'text-white'
                        }`}
                      >
                        {subtitle.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Danmaku Toggle */}
              <button
                onClick={() => setShowDanmaku(!showDanmaku)}
                className={`transition-colors ${
                  showDanmaku ? 'text-blue-400' : 'text-white hover:text-blue-400'
                }`}
                title="Toggle danmaku"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-1l-4 4z" />
                </svg>
              </button>
              
              {/* Display Mode Selector */}
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleDisplayModeChange('small')}
                  className={`p-1 rounded transition-colors ${
                    displayMode === 'small' ? 'text-blue-400' : 'text-white hover:text-blue-400'
                  }`}
                  title="Small screen"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="4" width="18" height="12" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
                  </svg>
                </button>
                <button
                  onClick={() => handleDisplayModeChange('medium')}
                  className={`p-1 rounded transition-colors ${
                    displayMode === 'medium' ? 'text-blue-400' : 'text-white hover:text-blue-400'
                  }`}
                  title="Medium screen"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <rect x="2" y="3" width="20" height="14" rx="2" stroke="currentColor" strokeWidth="2" fill="none"/>
                  </svg>
                </button>
                <button
                  onClick={() => handleDisplayModeChange('fullscreen')}
                  className={`p-1 rounded transition-colors ${
                    displayMode === 'fullscreen' ? 'text-blue-400' : 'text-white hover:text-blue-400'
                  }`}
                  title="Fullscreen"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                </button>
              </div>
              
              {/* Picture-in-Picture */}
              {enablePictureInPicture && (
                <button
                  onClick={handlePictureInPicture}
                  className={`transition-colors ${
                    isPictureInPicture ? 'text-blue-400' : 'text-white hover:text-blue-400'
                  }`}
                  title="Picture-in-picture"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v2m-4 0h4m-4 0V1a1 1 0 00-1-1H8a1 1 0 00-1 1v3m0 0H3a1 1 0 00-1 1v12a1 1 0 001 1h14a1 1 0 001-1V8a1 1 0 00-1-1h-4" />
                  </svg>
                </button>
              )}
              
              {/* Quality Selector */}
              <div className="relative">
                <button
                  onClick={() => setShowQualityMenu(!showQualityMenu)}
                  className="text-white hover:text-blue-400 transition-colors flex items-center space-x-1"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  <span className="text-sm">{currentQuality}</span>
                </button>
                
                {showQualityMenu && (
                  <div className="absolute bottom-full right-0 mb-2 bg-black bg-opacity-90 rounded-lg py-2 min-w-[120px]">
                    {videoSources.map((source) => (
                      <button
                        key={source.quality}
                        onClick={() => handleQualityChange(source.quality)}
                        className={`block w-full text-left px-4 py-2 text-sm hover:bg-white hover:bg-opacity-20 transition-colors ${
                          currentQuality === source.quality ? 'text-blue-400' : 'text-white'
                        }`}
                      >
                        {source.label}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Subtitles Overlay */}
      {showSubtitles && currentSubtitle && (
        <div className="absolute bottom-16 left-0 right-0 flex justify-center pointer-events-none">
          <div className="bg-black bg-opacity-80 text-white px-4 py-2 rounded-lg text-center max-w-4xl mx-4">
            <p className="text-lg font-medium leading-relaxed">{currentSubtitle}</p>
          </div>
        </div>
      )}
      
      {/* Danmaku Overlay */}
      {showDanmaku && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {danmakuList.map((danmaku) => (
            <div
              key={danmaku.id}
              className="absolute text-white text-sm font-medium px-2 py-1 bg-black bg-opacity-60 rounded animate-danmaku"
              style={{
                color: danmaku.color,
                top: `${Math.random() * 70 + 10}%`,
                right: '-200px',
                animationDuration: '10s',
                animationDelay: `${danmaku.time}s`
              }}
            >
              {danmaku.text}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}