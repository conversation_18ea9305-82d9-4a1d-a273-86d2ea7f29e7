/**
 * 性能压力测试套件
 */

const request = require('supertest');
const { performance } = require('perf_hooks');

// 模拟应用用于性能测试
const createPerfTestApp = () => {
  const express = require('express');
  const app = express();
  
  app.use(express.json());
  
  // 快速响应端点
  app.get('/api/fast', (req, res) => {
    res.json({ message: 'Fast response', timestamp: Date.now() });
  });
  
  // 慢响应端点（模拟数据库查询）
  app.get('/api/slow', async (req, res) => {
    await new Promise(resolve => setTimeout(resolve, 100)); // 100ms延迟
    res.json({ message: 'Slow response', timestamp: Date.now() });
  });
  
  // CPU密集型端点
  app.get('/api/cpu-intensive', (req, res) => {
    let result = 0;
    for (let i = 0; i < 1000000; i++) {
      result += Math.sqrt(i);
    }
    res.json({ message: 'CPU intensive task completed', result });
  });
  
  // 内存密集型端点
  app.get('/api/memory-intensive', (req, res) => {
    const largeArray = new Array(100000).fill(0).map((_, i) => ({
      id: i,
      data: `Item ${i}`,
      timestamp: Date.now()
    }));
    res.json({ message: 'Memory intensive task completed', count: largeArray.length });
  });
  
  return app;
};

describe('Performance Tests', () => {
  let app;

  beforeAll(() => {
    app = createPerfTestApp();
  });

  describe('Response Time Tests', () => {
    test('should respond quickly to simple requests', async () => {
      const startTime = performance.now();
      
      const response = await request(app).get('/api/fast');
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(100); // 应该在100ms内响应
    });

    test('should handle slow requests within acceptable time', async () => {
      const startTime = performance.now();
      
      const response = await request(app).get('/api/slow');
      
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(500); // 应该在500ms内响应
    });
  });

  describe('Concurrent Request Tests', () => {
    test('should handle multiple concurrent requests', async () => {
      const concurrentRequests = 50;
      const requests = [];
      
      const startTime = performance.now();
      
      for (let i = 0; i < concurrentRequests; i++) {
        requests.push(request(app).get('/api/fast'));
      }
      
      const responses = await Promise.all(requests);
      
      const endTime = performance.now();
      const totalTime = endTime - startTime;
      
      // 所有请求都应该成功
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // 并发处理应该比串行处理快
      expect(totalTime).toBeLessThan(concurrentRequests * 50); // 假设每个请求50ms
      
      console.log(`Processed ${concurrentRequests} concurrent requests in ${totalTime.toFixed(2)}ms`);
    });

    test('should maintain performance under load', async () => {
      const loadTestRequests = 100;
      const batchSize = 10;
      const batches = Math.ceil(loadTestRequests / batchSize);
      
      const allResponseTimes = [];
      
      for (let batch = 0; batch < batches; batch++) {
        const batchRequests = [];
        
        for (let i = 0; i < batchSize; i++) {
          const startTime = performance.now();
          batchRequests.push(
            request(app)
              .get('/api/fast')
              .then(response => {
                const endTime = performance.now();
                return {
                  response,
                  responseTime: endTime - startTime
                };
              })
          );
        }
        
        const batchResults = await Promise.all(batchRequests);
        
        batchResults.forEach(result => {
          expect(result.response.status).toBe(200);
          allResponseTimes.push(result.responseTime);
        });
      }
      
      // 计算统计信息
      const avgResponseTime = allResponseTimes.reduce((a, b) => a + b, 0) / allResponseTimes.length;
      const maxResponseTime = Math.max(...allResponseTimes);
      const minResponseTime = Math.min(...allResponseTimes);
      
      console.log(`Load test results for ${loadTestRequests} requests:`);
      console.log(`Average response time: ${avgResponseTime.toFixed(2)}ms`);
      console.log(`Max response time: ${maxResponseTime.toFixed(2)}ms`);
      console.log(`Min response time: ${minResponseTime.toFixed(2)}ms`);
      
      // 性能断言
      expect(avgResponseTime).toBeLessThan(200); // 平均响应时间应小于200ms
      expect(maxResponseTime).toBeLessThan(1000); // 最大响应时间应小于1秒
    });
  });

  describe('Resource Usage Tests', () => {
    test('should handle CPU intensive tasks efficiently', async () => {
      const startMemory = process.memoryUsage();
      const startTime = performance.now();
      
      const response = await request(app).get('/api/cpu-intensive');
      
      const endTime = performance.now();
      const endMemory = process.memoryUsage();
      
      const executionTime = endTime - startTime;
      const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
      
      expect(response.status).toBe(200);
      expect(executionTime).toBeLessThan(5000); // 应该在5秒内完成
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // 内存增长应小于50MB
      
      console.log(`CPU intensive task completed in ${executionTime.toFixed(2)}ms`);
      console.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });

    test('should manage memory efficiently', async () => {
      const startMemory = process.memoryUsage();
      
      const response = await request(app).get('/api/memory-intensive');
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const endMemory = process.memoryUsage();
      const memoryIncrease = endMemory.heapUsed - startMemory.heapUsed;
      
      expect(response.status).toBe(200);
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 内存增长应小于100MB
      
      console.log(`Memory intensive task memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
    });
  });

  describe('Stress Tests', () => {
    test('should survive stress test', async () => {
      const stressTestDuration = 10000; // 10秒
      const requestInterval = 10; // 每10ms一个请求
      const maxConcurrentRequests = 20;
      
      let activeRequests = 0;
      let completedRequests = 0;
      let failedRequests = 0;
      
      const startTime = Date.now();
      
      const stressTest = new Promise((resolve) => {
        const interval = setInterval(async () => {
          if (Date.now() - startTime >= stressTestDuration) {
            clearInterval(interval);
            resolve();
            return;
          }
          
          if (activeRequests < maxConcurrentRequests) {
            activeRequests++;
            
            request(app)
              .get('/api/fast')
              .then(response => {
                if (response.status === 200) {
                  completedRequests++;
                } else {
                  failedRequests++;
                }
              })
              .catch(() => {
                failedRequests++;
              })
              .finally(() => {
                activeRequests--;
              });
          }
        }, requestInterval);
      });
      
      await stressTest;
      
      // 等待所有活跃请求完成
      while (activeRequests > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      const totalRequests = completedRequests + failedRequests;
      const successRate = (completedRequests / totalRequests) * 100;
      
      console.log(`Stress test results:`);
      console.log(`Total requests: ${totalRequests}`);
      console.log(`Completed requests: ${completedRequests}`);
      console.log(`Failed requests: ${failedRequests}`);
      console.log(`Success rate: ${successRate.toFixed(2)}%`);
      
      // 成功率应该很高
      expect(successRate).toBeGreaterThan(95);
      expect(totalRequests).toBeGreaterThan(100); // 应该处理了足够多的请求
    });
  });

  describe('Memory Leak Tests', () => {
    test('should not have memory leaks', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行多次请求
      for (let i = 0; i < 100; i++) {
        await request(app).get('/api/fast');
      }
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc();
        global.gc(); // 运行两次确保清理
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`Memory increase after 100 requests: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
      
      // 内存增长应该很小
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 小于10MB
    });
  });

  describe('Database Performance Tests', () => {
    test('should handle database queries efficiently', async () => {
      // 这个测试需要实际的数据库连接
      // 这里只是一个示例结构
      
      const queryCount = 50;
      const queries = [];
      
      for (let i = 0; i < queryCount; i++) {
        queries.push(
          request(app)
            .get('/api/slow') // 模拟数据库查询
            .then(response => {
              expect(response.status).toBe(200);
              return response;
            })
        );
      }
      
      const startTime = performance.now();
      await Promise.all(queries);
      const endTime = performance.now();
      
      const totalTime = endTime - startTime;
      const avgTimePerQuery = totalTime / queryCount;
      
      console.log(`${queryCount} database queries completed in ${totalTime.toFixed(2)}ms`);
      console.log(`Average time per query: ${avgTimePerQuery.toFixed(2)}ms`);
      
      expect(avgTimePerQuery).toBeLessThan(500); // 每个查询应该在500ms内完成
    });
  });
});