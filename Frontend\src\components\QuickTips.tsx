'use client';

import React from 'react';

interface QuickTipsProps {
  className?: string;
}

const tips = [
  {
    id: 1,
    title: 'Write Engaging Headlines',
    description: 'Use numbers, questions, or power words to grab attention',
    icon: '✍️'
  },
  {
    id: 2,
    title: 'Add Rich Media',
    description: 'Include images, videos, or audio to enhance your content',
    icon: '🎬'
  },
  {
    id: 3,
    title: 'Use Proper Formatting',
    description: 'Break up text with headings, lists, and paragraphs',
    icon: '📝'
  },
  {
    id: 4,
    title: 'Engage Your Audience',
    description: 'Ask questions and encourage comments from readers',
    icon: '💬'
  }
];

const QuickTips: React.FC<QuickTipsProps> = ({ className = '' }) => {
  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-sm font-semibold text-gray-800 flex items-center">
          <svg className="w-4 h-4 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          Quick Tips
        </h3>
      </div>
      <div className="p-4 space-y-3">
        {tips.map((tip) => (
          <div key={tip.id} className="flex items-start space-x-3 p-2 rounded-md hover:bg-gray-50">
            <span className="text-lg">{tip.icon}</span>
            <div className="flex-1">
              <h4 className="text-sm font-medium text-gray-800">{tip.title}</h4>
              <p className="text-xs text-gray-600 mt-1">{tip.description}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default QuickTips;