'use client';

import React, { useState } from 'react';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import {
  ShieldCheckIcon,
  EyeIcon,
  EyeSlashIcon,
  GlobeAltIcon,
  UserGroupIcon,
  LockClosedIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

interface PrivacySettings {
  profileVisibility: 'public' | 'followers' | 'private';
  showEmail: boolean;
  showFollowers: boolean;
  showFollowing: boolean;
  allowMessages: 'everyone' | 'followers' | 'none';
  allowComments: 'everyone' | 'followers' | 'none';
  showActivity: boolean;
  dataCollection: boolean;
  marketingEmails: boolean;
  pushNotifications: boolean;
}

export default function PrivacySettingsPage() {
  const { user } = useSimpleAuth();
  const [isLoading, setIsLoading] = useState(false);

  const [settings, setSettings] = useState<PrivacySettings>({
    profileVisibility: 'public',
    showEmail: false,
    showFollowers: true,
    showFollowing: true,
    allowMessages: 'followers',
    allowComments: 'everyone',
    showActivity: true,
    dataCollection: true,
    marketingEmails: false,
    pushNotifications: true,
  });

  const handleSettingChange = (key: keyof PrivacySettings, value: any) => {
    setSettings((prev) => ({ ...prev, [key]: value }));
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));
      toast.success('Privacy settings updated successfully!');
    } catch (error) {
      toast.error('Failed to update privacy settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDataExport = async () => {
    setIsLoading(true);
    try {
      // 模拟数据导出
      await new Promise((resolve) => setTimeout(resolve, 2000));
      toast.success('Data export request submitted. You will receive an email when ready.');
    } catch (error) {
      toast.error('Failed to request data export');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ShieldCheckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Sign in required</h2>
          <p className="text-gray-600">You need to be logged in to access privacy settings.</p>
        </div>
      </div>
    );
  }

  const ToggleSwitch = ({
    enabled,
    onChange,
    disabled = false,
  }: {
    enabled: boolean;
    onChange: (enabled: boolean) => void;
    disabled?: boolean;
  }) => (
    <button
      type="button"
      onClick={() => !disabled && onChange(!enabled)}
      disabled={disabled}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        enabled ? 'bg-blue-600' : 'bg-gray-200'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
          enabled ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  );

  const RadioGroup = ({
    options,
    value,
    onChange,
    name,
  }: {
    options: { value: string; label: string; description?: string; icon?: React.ReactNode }[];
    value: string;
    onChange: (value: string) => void;
    name: string;
  }) => (
    <div className="space-y-3">
      {options.map((option) => (
        <label key={option.value} className="flex items-start cursor-pointer">
          <input
            type="radio"
            name={name}
            value={option.value}
            checked={value === option.value}
            onChange={(e) => onChange(e.target.value)}
            className="mt-1 h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
          />
          <div className="ml-3">
            <div className="flex items-center">
              {option.icon && <span className="mr-2">{option.icon}</span>}
              <span className="font-medium text-gray-900">{option.label}</span>
            </div>
            {option.description && <p className="text-sm text-gray-600">{option.description}</p>}
          </div>
        </label>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Privacy Settings</h1>
          <p className="text-gray-600 mt-1">
            Control who can see your information and how we use your data
          </p>
        </div>

        <div className="space-y-8">
          {/* Profile Visibility */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <EyeIcon className="h-5 w-5 mr-2" />
                Profile Visibility
              </h2>
            </div>

            <div className="p-6">
              <div className="mb-6">
                <h3 className="font-medium text-gray-900 mb-4">Who can see your profile?</h3>
                <RadioGroup
                  name="profileVisibility"
                  value={settings.profileVisibility}
                  onChange={(value) => handleSettingChange('profileVisibility', value)}
                  options={[
                    {
                      value: 'public',
                      label: 'Public',
                      description: 'Anyone can see your profile and content',
                      icon: <GlobeAltIcon className="h-4 w-4 text-green-500" />,
                    },
                    {
                      value: 'followers',
                      label: 'Followers Only',
                      description: 'Only your followers can see your profile',
                      icon: <UserGroupIcon className="h-4 w-4 text-blue-500" />,
                    },
                    {
                      value: 'private',
                      label: 'Private',
                      description: 'Only you can see your profile',
                      icon: <LockClosedIcon className="h-4 w-4 text-red-500" />,
                    },
                  ]}
                />
              </div>

              <div className="space-y-4 border-t border-gray-200 pt-6">
                <h3 className="font-medium text-gray-900">Profile Information</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Show email address</h4>
                      <p className="text-sm text-gray-600">
                        Display your email on your public profile
                      </p>
                    </div>
                    <ToggleSwitch
                      enabled={settings.showEmail}
                      onChange={(enabled) => handleSettingChange('showEmail', enabled)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Show followers count</h4>
                      <p className="text-sm text-gray-600">Display how many people follow you</p>
                    </div>
                    <ToggleSwitch
                      enabled={settings.showFollowers}
                      onChange={(enabled) => handleSettingChange('showFollowers', enabled)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Show following count</h4>
                      <p className="text-sm text-gray-600">Display how many people you follow</p>
                    </div>
                    <ToggleSwitch
                      enabled={settings.showFollowing}
                      onChange={(enabled) => handleSettingChange('showFollowing', enabled)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">Show activity status</h4>
                      <p className="text-sm text-gray-600">
                        Let others see when you were last active
                      </p>
                    </div>
                    <ToggleSwitch
                      enabled={settings.showActivity}
                      onChange={(enabled) => handleSettingChange('showActivity', enabled)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Interaction Settings */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <UserGroupIcon className="h-5 w-5 mr-2" />
                Interaction Settings
              </h2>
            </div>

            <div className="p-6 space-y-6">
              <div>
                <h3 className="font-medium text-gray-900 mb-4">Who can send you messages?</h3>
                <RadioGroup
                  name="allowMessages"
                  value={settings.allowMessages}
                  onChange={(value) => handleSettingChange('allowMessages', value)}
                  options={[
                    {
                      value: 'everyone',
                      label: 'Everyone',
                      description: 'Anyone can send you messages',
                    },
                    {
                      value: 'followers',
                      label: 'Followers Only',
                      description: 'Only your followers can message you',
                    },
                    { value: 'none', label: 'No One', description: 'Disable direct messages' },
                  ]}
                />
              </div>

              <div>
                <h3 className="font-medium text-gray-900 mb-4">Who can comment on your posts?</h3>
                <RadioGroup
                  name="allowComments"
                  value={settings.allowComments}
                  onChange={(value) => handleSettingChange('allowComments', value)}
                  options={[
                    {
                      value: 'everyone',
                      label: 'Everyone',
                      description: 'Anyone can comment on your posts',
                    },
                    {
                      value: 'followers',
                      label: 'Followers Only',
                      description: 'Only your followers can comment',
                    },
                    {
                      value: 'none',
                      label: 'No One',
                      description: 'Disable comments on your posts',
                    },
                  ]}
                />
              </div>
            </div>
          </div>

          {/* Data & Privacy */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <ShieldCheckIcon className="h-5 w-5 mr-2" />
                Data & Privacy
              </h2>
            </div>

            <div className="p-6 space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Data collection for personalization</h4>
                  <p className="text-sm text-gray-600">
                    Allow us to collect data to improve your experience
                  </p>
                </div>
                <ToggleSwitch
                  enabled={settings.dataCollection}
                  onChange={(enabled) => handleSettingChange('dataCollection', enabled)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Marketing emails</h4>
                  <p className="text-sm text-gray-600">
                    Receive emails about new features and updates
                  </p>
                </div>
                <ToggleSwitch
                  enabled={settings.marketingEmails}
                  onChange={(enabled) => handleSettingChange('marketingEmails', enabled)}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Push notifications</h4>
                  <p className="text-sm text-gray-600">
                    Receive push notifications on your devices
                  </p>
                </div>
                <ToggleSwitch
                  enabled={settings.pushNotifications}
                  onChange={(enabled) => handleSettingChange('pushNotifications', enabled)}
                />
              </div>
            </div>
          </div>

          {/* Data Export */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <DocumentArrowDownIcon className="h-5 w-5 mr-2" />
                Data Export
              </h2>
            </div>

            <div className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900">Download your data</h3>
                  <p className="text-sm text-gray-600">
                    Get a copy of all your data including posts, comments, and profile information
                  </p>
                </div>
                <button
                  onClick={handleDataExport}
                  disabled={isLoading}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 font-medium"
                >
                  {isLoading ? 'Processing...' : 'Request Export'}
                </button>
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <button
              onClick={handleSaveSettings}
              disabled={isLoading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors duration-200 font-medium"
            >
              {isLoading ? 'Saving...' : 'Save Privacy Settings'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
