'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import ModernArticleEditor from '@/components/ModernArticleEditor';
import { useSimpleAuth } from '@/contexts/SimpleAuthContext';
import ErrorAlert from '@/components/ui/ErrorAlert';
import LoadingState from '@/components/ui/LoadingState';

export default function CreateArticlePage() {
  const router = useRouter();
  const { user, isAuthenticated } = useSimpleAuth();
  const [category, setCategory] = useState('technology');
  const [tags, setTags] = useState('');
  const [isPublishing, setIsPublishing] = useState(false);
  const [error, setError] = useState('');
  const [publishSuccess, setPublishSuccess] = useState(false);

  // 如果用户未登录，重定向到登录页
  if (!isAuthenticated) {
    router.push('/auth/login');
    return null;
  }

  const categories = [
    { value: 'technology', label: 'Technology' },
    { value: 'finance', label: 'Finance' },
    { value: 'entertainment', label: 'Entertainment' },
    { value: 'travel', label: 'Travel' },
    { value: 'lifestyle', label: 'Lifestyle' },
    { value: 'food', label: 'Food' },
    { value: 'sports', label: 'Sports' },
    { value: 'health', label: 'Health' },
    { value: 'science', label: 'Science' },
    { value: 'business', label: 'Business' },
  ];

  const handleSave = async (articleData: { title: string; content: string; excerpt: string }) => {
    if (!articleData.title.trim() || !articleData.content.trim()) {
      throw new Error('Please fill in both title and content');
    }

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/articles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          title: articleData.title,
          content: articleData.content,
          excerpt: articleData.excerpt,
          category,
          tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
          status: 'draft',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save article');
      }

      return await response.json();
    } catch (err) {
      console.error('Save error:', err);
      throw err;
    }
  };

  const handlePublish = async (articleData: { title: string; content: string; excerpt: string }) => {
    if (!articleData.title.trim() || !articleData.content.trim()) {
      setError('Please fill in both title and content');
      return;
    }

    setIsPublishing(true);
    setError('');

    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/articles`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          title: articleData.title,
          content: articleData.content,
          excerpt: articleData.excerpt,
          category,
          tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
          status: 'published',
        }),
      });

      if (response.ok) {
        const article = await response.json();
        setPublishSuccess(true);
        setTimeout(() => {
          router.push(`/article/${article.id}`);
        }, 1500);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to publish article');
      }
    } catch (err) {
      setError('Network error. Please try again.');
    } finally {
      setIsPublishing(false);
    }
  };

  const handlePreview = (articleData: { title: string; content: string }) => {
    // 在新窗口中预览文章
    const previewWindow = window.open('', '_blank');
    if (previewWindow) {
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${articleData.title}</title>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
            h1 { color: #1f2937; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; }
            .content { margin-top: 20px; }
            .preview-badge { background: #fef3c7; color: #92400e; padding: 8px 16px; border-radius: 6px; font-size: 14px; margin-bottom: 20px; display: inline-block; }
          </style>
        </head>
        <body>
          <div class="preview-badge">📝 Article Preview</div>
          <h1>${articleData.title}</h1>
          <div class="content">${articleData.content}</div>
        </body>
        </html>
      `);
      previewWindow.document.close();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="max-w-4xl mx-auto px-6 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Article</h1>
          <p className="text-gray-600">Share your thoughts and insights with the world</p>
        </div>

        {/* 错误提示 */}
        <ErrorAlert 
          error={error}
          onDismiss={() => setError('')}
          className="mb-6"
        />

        {/* 文章设置 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Article Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 分类选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category
              </label>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map((cat) => (
                  <option key={cat.value} value={cat.value}>
                    {cat.label}
                  </option>
                ))}
              </select>
            </div>

            {/* 标签输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags (comma separated)
              </label>
              <input
                type="text"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                placeholder="technology, programming, tutorial"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>

        {/* 文章编辑器 */}
        <ModernArticleEditor
          onSave={handleSave}
          onPreview={handlePreview}
          autoSave={true}
          className="mb-6"
        />

        {/* 发布控制面板 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-600">
              Ready to share your article with the world?
            </div>
            
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              
              <button
                type="button"
                onClick={() => {
                  // 这里需要从编辑器获取内容
                  // 暂时使用空数据，实际应该从编辑器组件获取
                  handlePublish({ title: '', content: '', excerpt: '' });
                }}
                disabled={isPublishing}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
              >
                {publishSuccess ? (
                  <LoadingState 
                    isLoading={false}
                    isSuccess={true}
                    successText="Published! Redirecting..."
                    size="sm"
                  />
                ) : isPublishing ? (
                  <LoadingState 
                    isLoading={true}
                    loadingText="Publishing..."
                    size="sm"
                  />
                ) : (
                  'Publish Article'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}