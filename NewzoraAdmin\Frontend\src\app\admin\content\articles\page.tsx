'use client';

import React, { useState, useEffect } from 'react';
import { 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  EyeOff,
  Calendar,
  User,
  Tag,
  MoreHorizontal
} from 'lucide-react';

interface Article {
  id: string;
  title: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  category: string;
  tags: string[];
  status: 'published' | 'draft' | 'pending' | 'archived';
  publishedAt?: string;
  updatedAt: string;
  views: number;
  likes: number;
  comments: number;
  featured: boolean;
}

const ArticleManagementPage: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [selectedArticles, setSelectedArticles] = useState<string[]>([]);

  useEffect(() => {
    fetchArticles();
  }, []);

  const fetchArticles = async () => {
    try {
      // Mock data - in real app, this would fetch from API
      const mockArticles: Article[] = [
        {
          id: '1',
          title: 'Getting Started with React 18',
          author: { id: '1', name: 'John Smith' },
          category: 'Technology',
          tags: ['React', 'JavaScript', 'Frontend'],
          status: 'published',
          publishedAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          views: 1234,
          likes: 89,
          comments: 23,
          featured: true
        },
        {
          id: '2',
          title: 'Advanced TypeScript Patterns',
          author: { id: '2', name: 'Sarah Johnson' },
          category: 'Technology',
          tags: ['TypeScript', 'Programming'],
          status: 'published',
          publishedAt: '2024-01-14T14:30:00Z',
          updatedAt: '2024-01-14T14:30:00Z',
          views: 987,
          likes: 67,
          comments: 15,
          featured: false
        },
        {
          id: '3',
          title: 'Building Scalable APIs',
          author: { id: '3', name: 'Mike Brown' },
          category: 'Backend',
          tags: ['API', 'Node.js', 'Architecture'],
          status: 'draft',
          updatedAt: '2024-01-13T09:15:00Z',
          views: 0,
          likes: 0,
          comments: 0,
          featured: false
        },
        {
          id: '4',
          title: 'UI/UX Design Principles',
          author: { id: '4', name: 'Emily Davis' },
          category: 'Design',
          tags: ['UI', 'UX', 'Design'],
          status: 'pending',
          updatedAt: '2024-01-12T16:45:00Z',
          views: 0,
          likes: 0,
          comments: 0,
          featured: false
        }
      ];
      
      setArticles(mockArticles);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch articles:', error);
      setLoading(false);
    }
  };

  const handleStatusChange = async (articleId: string, newStatus: Article['status']) => {
    try {
      setArticles(prev => prev.map(article => 
        article.id === articleId ? { ...article, status: newStatus } : article
      ));
      // In real app, this would call API
      console.log(`Article ${articleId} status changed to ${newStatus}`);
    } catch (error) {
      console.error('Failed to update article status:', error);
    }
  };

  const handleToggleFeatured = async (articleId: string) => {
    try {
      setArticles(prev => prev.map(article => 
        article.id === articleId ? { ...article, featured: !article.featured } : article
      ));
      // In real app, this would call API
      console.log(`Article ${articleId} featured status toggled`);
    } catch (error) {
      console.error('Failed to toggle featured status:', error);
    }
  };

  const handleDeleteArticle = async (articleId: string) => {
    if (confirm('Are you sure you want to delete this article? This action cannot be undone.')) {
      try {
        setArticles(prev => prev.filter(article => article.id !== articleId));
        // In real app, this would call API
        console.log(`Article ${articleId} deleted`);
      } catch (error) {
        console.error('Failed to delete article:', error);
      }
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedArticles.length === 0) return;
    
    try {
      switch (action) {
        case 'publish':
          setArticles(prev => prev.map(article => 
            selectedArticles.includes(article.id) ? { ...article, status: 'published' } : article
          ));
          break;
        case 'unpublish':
          setArticles(prev => prev.map(article => 
            selectedArticles.includes(article.id) ? { ...article, status: 'draft' } : article
          ));
          break;
        case 'delete':
          if (confirm(`Are you sure you want to delete ${selectedArticles.length} articles?`)) {
            setArticles(prev => prev.filter(article => !selectedArticles.includes(article.id)));
          }
          break;
      }
      setSelectedArticles([]);
    } catch (error) {
      console.error('Failed to perform bulk action:', error);
    }
  };

  const filteredArticles = articles.filter(article => {
    const matchesSearch = article.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         article.author.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || article.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || article.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusBadge = (status: Article['status']) => {
    const styles = {
      published: 'bg-green-100 text-green-800',
      draft: 'bg-gray-100 text-gray-800',
      pending: 'bg-yellow-100 text-yellow-800',
      archived: 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles[status]}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Article Management</h1>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Article Management</h1>
          <p className="text-gray-600 mt-1">Manage and moderate all articles on the platform</p>
        </div>
        <button className="btn-primary flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          New Article
        </button>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search articles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="form-input pl-10"
            />
          </div>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">All Status</option>
            <option value="published">Published</option>
            <option value="draft">Draft</option>
            <option value="pending">Pending</option>
            <option value="archived">Archived</option>
          </select>
          
          <select
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            className="form-select"
          >
            <option value="all">All Categories</option>
            <option value="Technology">Technology</option>
            <option value="Backend">Backend</option>
            <option value="Design">Design</option>
            <option value="Business">Business</option>
          </select>
          
          {selectedArticles.length > 0 && (
            <div className="flex space-x-2">
              <select
                onChange={(e) => {
                  if (e.target.value) {
                    handleBulkAction(e.target.value);
                    e.target.value = '';
                  }
                }}
                className="form-select"
              >
                <option value="">Bulk Actions</option>
                <option value="publish">Publish</option>
                <option value="unpublish">Unpublish</option>
                <option value="delete">Delete</option>
              </select>
            </div>
          )}
        </div>
      </div>

      {/* Articles Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={selectedArticles.length === filteredArticles.length && filteredArticles.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedArticles(filteredArticles.map(article => article.id));
                      } else {
                        setSelectedArticles([]);
                      }
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Article
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Stats
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredArticles.map((article) => (
                <tr key={article.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedArticles.includes(article.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedArticles(prev => [...prev, article.id]);
                        } else {
                          setSelectedArticles(prev => prev.filter(id => id !== article.id));
                        }
                      }}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div>
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">{article.title}</div>
                          {article.featured && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                              Featured
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-500">
                          <span className="mr-2">{article.category}</span>
                          {article.tags.map((tag, index) => (
                            <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-800 mr-1">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{article.author.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(article.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="space-y-1">
                      <div>{article.views.toLocaleString()} views</div>
                      <div>{article.likes} likes • {article.comments} comments</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="space-y-1">
                      {article.publishedAt && (
                        <div>Published: {formatDate(article.publishedAt)}</div>
                      )}
                      <div>Updated: {formatDate(article.updatedAt)}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => handleToggleFeatured(article.id)}
                        className="text-yellow-600 hover:text-yellow-900"
                        title={article.featured ? 'Remove from featured' : 'Add to featured'}
                      >
                        {article.featured ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                      <button className="text-blue-600 hover:text-blue-900">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteArticle(article.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                      <div className="relative">
                        <button className="text-gray-400 hover:text-gray-600">
                          <MoreHorizontal className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredArticles.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No articles found matching your criteria</div>
          </div>
        )}
      </div>

      {/* Pagination */}
      <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow">
        <div className="flex-1 flex justify-between sm:hidden">
          <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Previous
          </button>
          <button className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
            Next
          </button>
        </div>
        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">1</span> to <span className="font-medium">{filteredArticles.length}</span> of{' '}
              <span className="font-medium">{filteredArticles.length}</span> results
            </p>
          </div>
          <div>
            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                Previous
              </button>
              <button className="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                1
              </button>
              <button className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                Next
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ArticleManagementPage;
