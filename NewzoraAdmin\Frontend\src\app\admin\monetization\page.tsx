'use client';

import React, { useState, useEffect } from 'react';
import { DollarSign, TrendingUp, Users, Eye, CheckCircle, XCircle, Clock, AlertTriangle, CreditCard, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';
import StatsCard from '@/components/admin/dashboard/StatsCard';
import SafeDatePicker, { DateRange } from '@/components/admin/common/SafeDatePicker';
import { dashboardService } from '@/services/dashboardService';
import { filterByDateRange } from '@/utils/dateFilters';

const MonetizationPage: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);
  const [selectedWithdrawals, setSelectedWithdrawals] = useState<string[]>([]);
  const [withdrawalFilter, setWithdrawalFilter] = useState('all');
  const [stats, setStats] = useState({
    totalRevenue: 125600,
    totalWithdrawals: 45200,
    pendingWithdrawals: 8900,
    completedWithdrawals: 36300,
    adImpressions: 245000,
    averageCTR: 3.2,
    premiumUsers: 1240
  });

  // 收入趋势数据
  const [revenueTrendData, setRevenueTrendData] = useState([
    { date: '2024-01-01', revenue: 8500, withdrawals: 2100, netRevenue: 6400 },
    { date: '2024-01-02', revenue: 9200, withdrawals: 2300, netRevenue: 6900 },
    { date: '2024-01-03', revenue: 7800, withdrawals: 1900, netRevenue: 5900 },
    { date: '2024-01-04', revenue: 10100, withdrawals: 2500, netRevenue: 7600 },
    { date: '2024-01-05', revenue: 11200, withdrawals: 2800, netRevenue: 8400 },
    { date: '2024-01-06', revenue: 9800, withdrawals: 2400, netRevenue: 7400 },
    { date: '2024-01-07', revenue: 12500, withdrawals: 3100, netRevenue: 9400 }
  ]);

  // Generate comprehensive withdrawal records for testing
  const generateWithdrawalRecords = () => {
    const records = [];
    const users = [
      { username: 'john_doe', email: '<EMAIL>', userId: 'user123' },
      { username: 'jane_smith', email: '<EMAIL>', userId: 'user456' },
      { username: 'mike_wilson', email: '<EMAIL>', userId: 'user789' },
      { username: 'sarah_jones', email: '<EMAIL>', userId: 'user321' },
      { username: 'david_brown', email: '<EMAIL>', userId: 'user654' },
      { username: 'lisa_garcia', email: '<EMAIL>', userId: 'user987' },
      { username: 'tom_wilson', email: '<EMAIL>', userId: 'user246' },
      { username: 'emma_davis', email: '<EMAIL>', userId: 'user135' }
    ];
    const methods = ['PayPal', 'Bank Transfer', 'Cryptocurrency', 'Stripe'];
    const statuses = ['pending', 'approved', 'completed', 'rejected', 'processing'];

    // Add some predefined records first
    const predefinedRecords = [
      {
        id: 'WD001',
        userId: 'user123',
        username: 'john_doe',
        email: '<EMAIL>',
        amount: 500.00,
        method: 'PayPal',
        accountInfo: '<EMAIL>',
        status: 'pending',
        requestDate: '2024-01-15T10:30:00Z',
        processedDate: null,
        notes: '',
        adminNotes: '',
        user: 'John Doe',
        date: '2024-01-15',
        createdAt: '2024-01-15T10:30:00Z'
      },
      {
        id: 'WD002',
        userId: 'user456',
        username: 'jane_smith',
        email: '<EMAIL>',
        amount: 750.00,
        method: 'Bank Transfer',
        accountInfo: 'Bank: Chase, Account: ****1234',
        status: 'approved',
        requestDate: '2024-01-14T14:20:00Z',
        processedDate: '2024-01-15T09:15:00Z',
        notes: 'Urgent withdrawal request',
        adminNotes: 'Verified account details',
        user: 'Jane Smith',
        date: '2024-01-14',
        createdAt: '2024-01-14T14:20:00Z'
      }
    ];

    records.push(...predefinedRecords);

    // Generate additional random records
    for (let i = 3; i <= 50; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 365));
      const user = users[Math.floor(Math.random() * users.length)];
      const method = methods[Math.floor(Math.random() * methods.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      records.push({
        id: `WD${i.toString().padStart(3, '0')}`,
        userId: user.userId,
        username: user.username,
        email: user.email,
        amount: Math.floor(Math.random() * 1000) + 100,
        method: method,
        accountInfo: method === 'PayPal' ? `${user.username}@paypal.com` :
                    method === 'Bank Transfer' ? `Bank: Chase, Account: ****${Math.floor(Math.random() * 9999)}` :
                    method === 'Cryptocurrency' ? `BTC: 1${Math.random().toString(36).substring(2, 15)}` :
                    `Stripe: ****${Math.floor(Math.random() * 9999)}`,
        status: status,
        requestDate: date.toISOString(),
        processedDate: status === 'completed' || status === 'rejected' ?
                      new Date(date.getTime() + Math.random() * ********).toISOString() : null,
        notes: Math.random() > 0.7 ? 'Regular withdrawal request' : '',
        adminNotes: status === 'completed' ? 'Processed successfully' :
                   status === 'rejected' ? 'Insufficient verification' : '',
        user: user.username.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
        date: date.toISOString().split('T')[0],
        createdAt: date.toISOString()
      });
    }

    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  };
  
  // Ad revenue data
  const [adRevenueData, setAdRevenueData] = useState([
    { name: 'Mon', revenue: 1200, impressions: 24000, ctr: 2.5 },
    { name: 'Tue', revenue: 1800, impressions: 32000, ctr: 3.1 },
    { name: 'Wed', revenue: 1500, impressions: 28000, ctr: 2.8 },
    { name: 'Thu', revenue: 2100, impressions: 35000, ctr: 3.5 },
    { name: 'Fri', revenue: 2400, impressions: 42000, ctr: 3.8 },
    { name: 'Sat', revenue: 1900, impressions: 31000, ctr: 3.2 },
    { name: 'Sun', revenue: 1600, impressions: 29000, ctr: 2.9 },
  ]);
  
  // Ad placement data
  const adPlacements = [
    { id: '1', name: 'Homepage Banner', type: 'Banner Ad', status: 'Active', revenue: 3200, impressions: 64000, ctr: 3.2 },
    { id: '2', name: 'Article Inline', type: 'Content Ad', status: 'Active', revenue: 2800, impressions: 56000, ctr: 2.8 },
    { id: '3', name: 'Sidebar', type: 'Display Ad', status: 'Active', revenue: 1500, impressions: 30000, ctr: 2.5 },
    { id: '4', name: 'Popup Ad', type: 'Interstitial', status: 'Paused', revenue: 800, impressions: 12000, ctr: 4.2 },
  ];
  
  // 提现记录数据状态
  const [withdrawalRecords, setWithdrawalRecords] = useState(() => generateWithdrawalRecords());
  const [allWithdrawalRecords, setAllWithdrawalRecords] = useState(() => generateWithdrawalRecords());

  useEffect(() => {
    // 初始化数据
    const allRecords = generateWithdrawalRecords();
    setAllWithdrawalRecords(allRecords);
    setWithdrawalRecords(allRecords.slice(0, 10)); // 显示前10条

    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  const handleDateRangeChange = (dateRange: DateRange) => {
    setCurrentDateRange(dateRange);

    // 根据日期范围过滤提现记录
    if (allWithdrawalRecords.length > 0) {
      const filteredRecords = filterByDateRange(allWithdrawalRecords, dateRange, {
        dateField: 'createdAt'
      });
      setWithdrawalRecords(filteredRecords.slice(0, 10)); // 显示前10条
    }

    // 根据日期范围计算统计数据
    const daysDiff = Math.ceil(
      (dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    const multiplier = Math.max(0.1, daysDiff / 30); // 基于30天的比例

    setStats({
      totalRevenue: Math.floor(12560 * multiplier),
      adImpressions: Math.floor(245000 * multiplier),
      averageCTR: 3.2 + (Math.random() - 0.5) * 0.5, // 小幅波动
      premiumUsers: Math.floor(1240 * multiplier)
    });

    // 更新图表数据 - 根据天数范围决定数据粒度
    if (daysDiff <= 7) {
      // 7天内显示每日数据
      setAdRevenueData([
        { name: 'Mon', revenue: Math.floor(1200 * multiplier), impressions: Math.floor(24000 * multiplier), ctr: 2.5 },
        { name: 'Tue', revenue: Math.floor(1800 * multiplier), impressions: Math.floor(32000 * multiplier), ctr: 3.1 },
        { name: 'Wed', revenue: Math.floor(1500 * multiplier), impressions: Math.floor(28000 * multiplier), ctr: 2.8 },
        { name: 'Thu', revenue: Math.floor(2100 * multiplier), impressions: Math.floor(35000 * multiplier), ctr: 3.5 },
        { name: 'Fri', revenue: Math.floor(2400 * multiplier), impressions: Math.floor(42000 * multiplier), ctr: 3.8 },
        { name: 'Sat', revenue: Math.floor(1900 * multiplier), impressions: Math.floor(31000 * multiplier), ctr: 3.2 },
        { name: 'Sun', revenue: Math.floor(1600 * multiplier), impressions: Math.floor(29000 * multiplier), ctr: 2.9 },
      ]);

      // 更新收入趋势数据
      setRevenueTrendData([
        { date: dateRange.startDate.toISOString().split('T')[0], revenue: Math.floor(8500 * multiplier), withdrawals: Math.floor(2100 * multiplier), netRevenue: Math.floor(6400 * multiplier) },
        { date: new Date(dateRange.startDate.getTime() + ********).toISOString().split('T')[0], revenue: Math.floor(9200 * multiplier), withdrawals: Math.floor(2300 * multiplier), netRevenue: Math.floor(6900 * multiplier) },
        { date: new Date(dateRange.startDate.getTime() + 172800000).toISOString().split('T')[0], revenue: Math.floor(7800 * multiplier), withdrawals: Math.floor(1900 * multiplier), netRevenue: Math.floor(5900 * multiplier) },
        { date: new Date(dateRange.startDate.getTime() + 259200000).toISOString().split('T')[0], revenue: Math.floor(10100 * multiplier), withdrawals: Math.floor(2500 * multiplier), netRevenue: Math.floor(7600 * multiplier) },
        { date: new Date(dateRange.startDate.getTime() + 345600000).toISOString().split('T')[0], revenue: Math.floor(11200 * multiplier), withdrawals: Math.floor(2800 * multiplier), netRevenue: Math.floor(8400 * multiplier) },
        { date: new Date(dateRange.startDate.getTime() + 432000000).toISOString().split('T')[0], revenue: Math.floor(9800 * multiplier), withdrawals: Math.floor(2400 * multiplier), netRevenue: Math.floor(7400 * multiplier) },
        { date: dateRange.endDate.toISOString().split('T')[0], revenue: Math.floor(12500 * multiplier), withdrawals: Math.floor(3100 * multiplier), netRevenue: Math.floor(9400 * multiplier) }
      ]);
    } else if (daysDiff <= 30) {
      // 30天内显示每周数据
      setAdRevenueData([
        { name: 'Week 1', revenue: Math.floor(8500 * multiplier), impressions: Math.floor(180000 * multiplier), ctr: 3.2 },
        { name: 'Week 2', revenue: Math.floor(9200 * multiplier), impressions: Math.floor(195000 * multiplier), ctr: 3.5 },
        { name: 'Week 3', revenue: Math.floor(7800 * multiplier), impressions: Math.floor(165000 * multiplier), ctr: 3.1 },
        { name: 'Week 4', revenue: Math.floor(10100 * multiplier), impressions: Math.floor(210000 * multiplier), ctr: 3.8 },
      ]);

      // 更新收入趋势数据 - 每周数据
      setRevenueTrendData([
        { date: 'Week 1', revenue: Math.floor(35000 * multiplier), withdrawals: Math.floor(8500 * multiplier), netRevenue: Math.floor(26500 * multiplier) },
        { date: 'Week 2', revenue: Math.floor(38000 * multiplier), withdrawals: Math.floor(9200 * multiplier), netRevenue: Math.floor(28800 * multiplier) },
        { date: 'Week 3', revenue: Math.floor(32000 * multiplier), withdrawals: Math.floor(7800 * multiplier), netRevenue: Math.floor(24200 * multiplier) },
        { date: 'Week 4', revenue: Math.floor(42000 * multiplier), withdrawals: Math.floor(10100 * multiplier), netRevenue: Math.floor(31900 * multiplier) }
      ]);
    } else {
      // 超过30天显示每月数据
      setAdRevenueData([
        { name: 'Month 1', revenue: Math.floor(32000 * multiplier), impressions: Math.floor(650000 * multiplier), ctr: 3.1 },
        { name: 'Month 2', revenue: Math.floor(38000 * multiplier), impressions: Math.floor(720000 * multiplier), ctr: 3.4 },
        { name: 'Month 3', revenue: Math.floor(42000 * multiplier), impressions: Math.floor(810000 * multiplier), ctr: 3.7 },
      ]);

      // 更新收入趋势数据 - 每月数据
      setRevenueTrendData([
        { date: 'Month 1', revenue: Math.floor(150000 * multiplier), withdrawals: Math.floor(35000 * multiplier), netRevenue: Math.floor(115000 * multiplier) },
        { date: 'Month 2', revenue: Math.floor(180000 * multiplier), withdrawals: Math.floor(42000 * multiplier), netRevenue: Math.floor(138000 * multiplier) },
        { date: 'Month 3', revenue: Math.floor(210000 * multiplier), withdrawals: Math.floor(48000 * multiplier), netRevenue: Math.floor(162000 * multiplier) }
      ]);
    }
  };

  const handleApproveWithdrawal = async (id: string) => {
    try {
      // Update withdrawal status to approved
      setWithdrawalRecords(prev => prev.map(record =>
        record.id === id ? {
          ...record,
          status: 'approved',
          processedDate: new Date().toISOString(),
          adminNotes: 'Approved by admin'
        } : record
      ));

      // Update stats
      const record = withdrawalRecords.find(r => r.id === id);
      if (record) {
        setStats(prev => ({
          ...prev,
          pendingWithdrawals: prev.pendingWithdrawals - record.amount
        }));
      }

      alert('Withdrawal approved successfully!');
    } catch (error) {
      console.error('Failed to approve withdrawal:', error);
      alert('Failed to approve withdrawal');
    }
  };

  const handleCompleteWithdrawal = async (id: string) => {
    const transactionId = prompt('Please enter transaction ID or confirmation number:');
    if (!transactionId) return;

    try {
      // Update withdrawal status to completed
      setWithdrawalRecords(prev => prev.map(record =>
        record.id === id ? {
          ...record,
          status: 'completed',
          processedDate: new Date().toISOString(),
          adminNotes: `Completed - Transaction ID: ${transactionId}`
        } : record
      ));

      // Update stats
      const record = withdrawalRecords.find(r => r.id === id);
      if (record) {
        setStats(prev => ({
          ...prev,
          completedWithdrawals: prev.completedWithdrawals + record.amount,
          pendingWithdrawals: prev.pendingWithdrawals - record.amount
        }));
      }

      alert('Withdrawal completed successfully!');
    } catch (error) {
      console.error('Failed to complete withdrawal:', error);
      alert('Failed to complete withdrawal');
    }
  };

  const handleRejectWithdrawal = async (id: string) => {
    const reason = prompt('Please enter rejection reason:');
    if (!reason) return;

    try {
      // Update withdrawal status to rejected
      setWithdrawalRecords(prev => prev.map(record =>
        record.id === id ? {
          ...record,
          status: 'rejected',
          processedDate: new Date().toISOString(),
          adminNotes: `Rejected - Reason: ${reason}`
        } : record
      ));

      // Update stats
      const record = withdrawalRecords.find(r => r.id === id);
      if (record) {
        setStats(prev => ({
          ...prev,
          pendingWithdrawals: prev.pendingWithdrawals - record.amount
        }));
      }

      alert('Withdrawal rejected successfully!');
    } catch (error) {
      console.error('Failed to reject withdrawal:', error);
      alert('Failed to reject withdrawal');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold text-gray-900">Revenue Management</h1>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              💰 Revenue Management
            </h1>
            <p className="text-lg text-gray-600">
              Comprehensive revenue tracking, withdrawal management, and advertising analytics
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            <SafeDatePicker
              value={currentDateRange}
              onChange={handleDateRangeChange}
              placeholder="Select date range"
              className="w-full sm:w-64"
            />
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-3xl font-bold text-green-600">${stats.totalRevenue.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600 font-medium">+12%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Withdrawals</p>
              <p className="text-3xl font-bold text-yellow-600">${stats.pendingWithdrawals.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowDownRight className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-sm text-yellow-600 font-medium">-5%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Withdrawals</p>
              <p className="text-3xl font-bold text-blue-600">${stats.completedWithdrawals.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-sm text-blue-600 font-medium">+8%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Premium Users</p>
              <p className="text-3xl font-bold text-purple-600">{stats.premiumUsers.toLocaleString()}</p>
              <div className="flex items-center mt-2">
                <ArrowUpRight className="h-4 w-4 text-purple-500 mr-1" />
                <span className="text-sm text-purple-600 font-medium">+15%</span>
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Ad Placement Management */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                <Eye className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Ad Placement Management</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {adPlacements.map((placement) => (
                <div key={placement.id} className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-semibold text-gray-900">{placement.name}</h4>
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                          placement.status === 'Active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {placement.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Type</p>
                          <p className="font-medium">{placement.type}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Revenue</p>
                          <p className="font-medium text-green-600">${placement.revenue.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Impressions</p>
                          <p className="font-medium">{placement.impressions.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">CTR</p>
                          <p className="font-medium">{placement.ctr}%</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <button className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                        Edit
                      </button>
                      <button className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium">
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">1</span> to <span className="font-medium">4</span> of <span className="font-medium">4</span> results
            </div>
            <div className="flex space-x-2">
              <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
                Previous
              </button>
              <button className="px-3 py-1 border border-gray-300 rounded text-sm text-gray-700 bg-white hover:bg-gray-50">
                Next
              </button>
            </div>
          </div>
        </div>

        {/* Withdrawal Management */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                <CreditCard className="h-4 w-4 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">Withdrawal Management</h3>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {withdrawalRecords.slice(0, 5).map((record) => (
                <div key={record.id} className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-semibold text-gray-900">{record.user || record.username}</h4>
                        <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                          record.status === 'completed' || record.status === 'Completed'
                            ? 'bg-green-100 text-green-800'
                            : record.status === 'processing' || record.status === 'Processing'
                              ? 'bg-yellow-100 text-yellow-800'
                              : record.status === 'pending'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-red-100 text-red-800'
                        }`}>
                          {record.status}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Amount</p>
                          <p className="font-medium text-green-600">${record.amount}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Method</p>
                          <p className="font-medium">{record.method}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Date</p>
                          <p className="font-medium">{record.date}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Email</p>
                          <p className="font-medium text-xs">{record.email}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      {(record.status === 'processing' || record.status === 'Processing' || record.status === 'pending') && (
                        <>
                          <button
                            onClick={() => handleApproveWithdrawal(record.id)}
                            className="px-3 py-1 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm font-medium"
                          >
                            Approve
                          </button>
                          <button
                            onClick={() => handleRejectWithdrawal(record.id)}
                            className="px-3 py-1 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors text-sm font-medium"
                          >
                            Reject
                          </button>
                        </>
                      )}
                      {(record.status === 'completed' || record.status === 'Completed') && (
                        <button className="px-3 py-1 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                          Details
                        </button>
                      )}
                        <button className="px-3 py-1 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium">
                          Details
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="bg-gray-50 px-6 py-3 flex items-center justify-between border-t border-gray-200 rounded-b-2xl">
            <div className="text-sm text-gray-700">
              Showing <span className="font-medium">1</span> to <span className="font-medium">5</span> of <span className="font-medium">{withdrawalRecords.length}</span> results
            </div>
            <div className="flex space-x-2">
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                Previous
              </button>
              <button className="px-4 py-2 border border-gray-300 rounded-lg text-sm text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                Next
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue Trend Chart */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8 mt-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-4">
              <TrendingUp className="h-5 w-5 text-white" />
            </div>
            <div>
              <h3 className="text-2xl font-bold text-gray-900">Revenue Trend Analysis</h3>
              <p className="text-gray-600">Track revenue performance over time</p>
            </div>
          </div>
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full mr-2"></div>
              <span className="text-gray-700 font-medium">Total Revenue</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-red-600 rounded-full mr-2"></div>
              <span className="text-gray-700 font-medium">Withdrawals</span>
            </div>
            <div className="flex items-center">
              <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-green-600 rounded-full mr-2"></div>
              <span className="text-gray-700 font-medium">Net Revenue</span>
            </div>
          </div>
        </div>

        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={revenueTrendData}>
              <defs>
                <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#3b82f6" stopOpacity={0.1}/>
                </linearGradient>
                <linearGradient id="colorWithdrawals" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#ef4444" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#ef4444" stopOpacity={0.1}/>
                </linearGradient>
                <linearGradient id="colorNetRevenue" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#10b981" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#10b981" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickFormatter={(value) => {
                  if (value.includes('-')) {
                    return new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                  }
                  return value;
                }}
              />
              <YAxis
                tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`}
              />
              <Tooltip
                formatter={(value: number, name: string) => [
                  `$${value.toLocaleString()}`,
                  name === 'revenue' ? 'Total Revenue' :
                  name === 'withdrawals' ? 'Withdrawals' : 'Net Revenue'
                ]}
                labelFormatter={(label) => {
                  if (typeof label === 'string' && label.includes('-')) {
                    return new Date(label).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    });
                  }
                  return label;
                }}
              />
              <Area
                type="monotone"
                dataKey="revenue"
                stroke="#3b82f6"
                fillOpacity={1}
                fill="url(#colorRevenue)"
                strokeWidth={2}
              />
              <Area
                type="monotone"
                dataKey="withdrawals"
                stroke="#ef4444"
                fillOpacity={1}
                fill="url(#colorWithdrawals)"
                strokeWidth={2}
              />
              <Area
                type="monotone"
                dataKey="netRevenue"
                stroke="#10b981"
                fillOpacity={1}
                fill="url(#colorNetRevenue)"
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Summary Stats */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-900">Total Revenue</p>
                <p className="text-lg font-semibold text-blue-900">
                  ${revenueTrendData.reduce((sum, item) => sum + item.revenue, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-red-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                <DollarSign className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-red-900">Total Withdrawals</p>
                <p className="text-lg font-semibold text-red-900">
                  ${revenueTrendData.reduce((sum, item) => sum + item.withdrawals, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <TrendingUp className="w-4 h-4 text-white" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-900">Net Revenue</p>
                <p className="text-lg font-semibold text-green-900">
                  ${revenueTrendData.reduce((sum, item) => sum + item.netRevenue, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      </div>
    </div>
  );
};

export default MonetizationPage;

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedWithdrawals(withdrawalRecords.map(r => r.id));
                      } else {
                        setSelectedWithdrawals([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {withdrawalRecords
                .filter(record => withdrawalFilter === 'all' || record.status === withdrawalFilter)
                .map((record) => (
                <tr key={record.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedWithdrawals.includes(record.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedWithdrawals(prev => [...prev, record.id]);
                        } else {
                          setSelectedWithdrawals(prev => prev.filter(id => id !== record.id));
                        }
                      }}
                      className="rounded border-gray-300"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{record.username}</div>
                        <div className="text-sm text-gray-500">{record.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">¥{record.amount.toLocaleString()}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{record.method}</div>
                    <div className="text-sm text-gray-500 truncate max-w-32">{record.accountInfo}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      record.status === 'completed' ? 'bg-green-100 text-green-800' :
                      record.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                      record.status === 'processing' ? 'bg-yellow-100 text-yellow-800' :
                      record.status === 'pending' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(record.requestDate).toLocaleDateString('en-US')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      {record.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApproveWithdrawal(record.id)}
                            className="text-green-600 hover:text-green-900 flex items-center"
                          >
                            <CheckCircle className="w-4 h-4 mr-1" />
                            Approve
                          </button>
                          <button
                            onClick={() => handleRejectWithdrawal(record.id)}
                            className="text-red-600 hover:text-red-900 flex items-center"
                          >
                            <XCircle className="w-4 h-4 mr-1" />
                            Reject
                          </button>
                        </>
                      )}
                      {record.status === 'approved' && (
                        <button
                          onClick={() => handleCompleteWithdrawal(record.id)}
                          className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                          <CreditCard className="w-4 h-4 mr-1" />
                          Complete
                        </button>
                      )}
                      {record.status === 'processing' && (
                        <button
                          onClick={() => handleCompleteWithdrawal(record.id)}
                          className="text-blue-600 hover:text-blue-900 flex items-center"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Mark Complete
                        </button>
                      )}
                      {(record.status === 'completed' || record.status === 'rejected') && (
                        <span className="text-gray-400 text-xs">
                          {record.processedDate && new Date(record.processedDate).toLocaleDateString('en-US')}
                        </span>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {withdrawalRecords.filter(record => withdrawalFilter === 'all' || record.status === withdrawalFilter).length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No withdrawal records found for the selected filter.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default MonetizationPage;