# OneNews Nginx 反向代理部署指南

## 概述

本文档详细说明了如何为 OneNews 项目配置和部署 Nginx 反向代理服务器。Nginx 将作为前端 Web 服务器，处理静态文件服务、负载均衡、SSL 终端和安全防护。

## 🏗️ 架构设计

```
Internet → Nginx (Port 80/443) → Frontend (Port 3000)
                                → Backend API (Port 5000)
                                → WebSocket (Socket.IO)
```

## 📁 配置文件结构

```
nginx/
├── nginx.conf              # 主配置文件
├── conf.d/
│   ├── default.conf        # HTTP 虚拟主机配置
│   └── ssl.conf           # HTTPS 虚拟主机配置
└── ssl/
    ├── README.md          # SSL 证书配置说明
    ├── fullchain.pem      # SSL 证书链（生产环境）
    └── privkey.pem        # SSL 私钥（生产环境）
```

## 🚀 快速部署

### 1. 使用 Docker Compose 部署

```bash
# 启动完整服务栈（包含 Nginx）
docker-compose up -d

# 仅启动 Nginx 服务
docker-compose up -d nginx
```

### 2. 独立部署 Nginx

```bash
# 拉取 Nginx 镜像
docker pull nginx:alpine

# 运行 Nginx 容器
docker run -d \
  --name onenews-nginx \
  -p 80:80 \
  -p 443:443 \
  -v $(pwd)/nginx/nginx.conf:/etc/nginx/nginx.conf:ro \
  -v $(pwd)/nginx/conf.d:/etc/nginx/conf.d:ro \
  -v $(pwd)/nginx/ssl:/etc/nginx/ssl:ro \
  --network onenews_default \
  nginx:alpine
```

## ⚙️ 配置详解

### 主配置文件 (nginx.conf)

- **工作进程**: 自动检测 CPU 核心数
- **连接数**: 每个工作进程 1024 个连接
- **Gzip 压缩**: 启用，压缩级别 6
- **上游服务器**: 配置后端和前端负载均衡
- **安全设置**: 隐藏服务器版本信息

### HTTP 配置 (default.conf)

- **端口**: 监听 80 端口
- **健康检查**: `/health` 端点
- **API 代理**: `/api/*` 路由到后端服务
- **WebSocket**: `/socket.io/*` 支持实时通信
- **静态文件**: 优化缓存策略
- **安全头**: 完整的安全头配置

### HTTPS 配置 (ssl.conf)

- **端口**: 监听 443 端口
- **SSL 协议**: TLS 1.2 和 1.3
- **HSTS**: 强制 HTTPS 访问
- **OCSP**: 在线证书状态检查
- **安全密码套件**: 现代化加密算法

## 🔒 SSL/TLS 配置

### 开发环境 - 自签名证书

```bash
# 生成自签名证书
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/privkey.pem \
    -out nginx/ssl/fullchain.pem \
    -subj "/C=CN/ST=Beijing/L=Beijing/O=OneNews/CN=localhost"
```

### 生产环境 - Let's Encrypt

```bash
# 安装 Certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 自动续期
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### 生产环境 - 手动证书

1. 将证书文件放置在 `nginx/ssl/` 目录
2. 确保文件权限正确：
   ```bash
   chmod 600 nginx/ssl/privkey.pem
   chmod 644 nginx/ssl/fullchain.pem
   ```

## 🔧 性能优化

### 缓存策略

- **静态资源**: 1年缓存
- **API 响应**: 不缓存
- **图片/媒体**: 30天缓存
- **字体文件**: 1年缓存

### 压缩配置

- **Gzip**: 启用，支持多种文件类型
- **压缩级别**: 6（平衡压缩率和 CPU 使用）
- **最小文件大小**: 1KB

### 连接优化

- **Keep-alive**: 65秒超时
- **上游连接池**: 32个持久连接
- **缓冲区**: 优化大小设置

## 🛡️ 安全配置

### 安全头

```nginx
X-Frame-Options: SAMEORIGIN
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff
Referrer-Policy: no-referrer-when-downgrade
Content-Security-Policy: default-src 'self' ...
Strict-Transport-Security: max-age=31536000; includeSubDomains
```

### 访问控制

- **限流**: 每秒 10 个请求，突发 20 个
- **连接限制**: 每 IP 10 个并发连接
- **敏感文件**: 禁止访问隐藏文件

### DDoS 防护

- **连接限制**: 基于 IP 地址
- **请求频率**: 限制请求速率
- **缓冲区**: 防止大文件攻击

## 📊 监控和日志

### 访问日志

```nginx
log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for" '
                'rt=$request_time uct="$upstream_connect_time" '
                'uht="$upstream_header_time" urt="$upstream_response_time"';
```

### 健康检查

- **端点**: `/health`
- **响应**: HTTP 200 "healthy"
- **监控**: 可用于负载均衡器健康检查

## 🧪 测试和验证

### 配置测试

```bash
# PowerShell (Windows)
powershell -ExecutionPolicy Bypass -File scripts/nginx-test.ps1

# Bash (Linux/Mac)
bash scripts/test-nginx.sh
```

### 功能测试

```bash
# 测试 HTTP 访问
curl -I http://localhost/

# 测试 API 代理
curl -I http://localhost/api/health

# 测试 WebSocket
curl -I http://localhost/socket.io/

# 测试 HTTPS（如果配置）
curl -I https://localhost/
```

## 🔄 负载均衡

### 算法配置

- **least_conn**: 最少连接数算法
- **健康检查**: 3次失败后标记不可用
- **故障转移**: 30秒超时

### 扩展配置

```nginx
upstream backend {
    least_conn;
    server backend1:5000 max_fails=3 fail_timeout=30s;
    server backend2:5000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}
```

## 🚨 故障排除

### 常见问题

1. **502 Bad Gateway**
   - 检查上游服务器是否运行
   - 验证网络连接
   - 查看 Nginx 错误日志

2. **SSL 证书错误**
   - 验证证书文件路径
   - 检查证书有效期
   - 确认域名匹配

3. **静态文件 404**
   - 检查文件路径配置
   - 验证文件权限
   - 确认代理设置

### 日志查看

```bash
# Docker 容器日志
docker logs onenews-nginx

# 传统安装日志
tail -f /var/log/nginx/error.log
tail -f /var/log/nginx/access.log
```

## 📈 性能调优

### 系统级优化

```bash
# 增加文件描述符限制
ulimit -n 65535

# 优化内核参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_tw_buckets = 6000' >> /etc/sysctl.conf
```

### Nginx 优化

```nginx
worker_processes auto;
worker_connections 1024;
worker_rlimit_nofile 65535;

# 启用 sendfile
sendfile on;
tcp_nopush on;
tcp_nodelay on;
```

## 🔄 更新和维护

### 配置更新

```bash
# 测试配置
nginx -t

# 重新加载配置
nginx -s reload

# Docker 环境
docker exec onenews-nginx nginx -s reload
```

### 证书续期

```bash
# Let's Encrypt 自动续期
certbot renew --dry-run

# 手动续期后重启
docker restart onenews-nginx
```

## 📞 支持

如果遇到问题，请：

1. 查看错误日志
2. 运行配置测试脚本
3. 检查网络连接
4. 验证服务状态

---

**注意**: 在生产环境中部署前，请确保：
- 所有证书文件正确配置
- 防火墙规则已设置
- 监控系统已就位
- 备份策略已实施
