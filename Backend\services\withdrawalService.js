const crypto = require('crypto');
const WithdrawalRequest = require('../models/WithdrawalRequest');
const UserBalance = require('../models/UserBalance');

// 全球税率配置
const TAX_RATES = {
  'US': 0.24, // 美国24%
  'CN': 0.20, // 中国20%
  'GB': 0.20, // 英国20%
  'DE': 0.26, // 德国26%
  'JP': 0.20, // 日本20%
  'KR': 0.22, // 韩国22%
  'SG': 0.17, // 新加坡17%
  'HK': 0.17, // 香港17%
  'DEFAULT': 0.15, // 默认15%
};

// 手续费配置
const FEE_RATES = {
  'bank_card': 0.025, // 银行卡2.5%
  'paypal': 0.035, // PayPal 3.5%
  'crypto': 0.015, // 数字货币1.5%
};

class WithdrawalService {
  // 计算税费
  static calculateTaxAndFees(amount, country, method) {
    const taxRate = TAX_RATES[country] || TAX_RATES.DEFAULT;
    const feeRate = FEE_RATES[method] || 0.03;
    
    const taxAmount = amount * taxRate;
    const feeAmount = amount * feeRate;
    const netAmount = amount - taxAmount - feeAmount;
    
    return {
      taxRate,
      feeRate,
      taxAmount: Math.round(taxAmount * 100) / 100,
      feeAmount: Math.round(feeAmount * 100) / 100,
      netAmount: Math.round(netAmount * 100) / 100,
    };
  }

  // 获取下一个周三日期
  static getNextWednesday() {
    const now = new Date();
    const dayOfWeek = now.getDay();
    const daysUntilWednesday = (3 - dayOfWeek + 7) % 7 || 7;
    const nextWednesday = new Date(now);
    nextWednesday.setDate(now.getDate() + daysUntilWednesday);
    nextWednesday.setHours(10, 0, 0, 0); // 上午10点处理
    return nextWednesday;
  }

  // 生成验证令牌
  static generateVerificationToken() {
    return crypto.randomBytes(32).toString('hex');
  }

  // 创建提现请求
  static async createWithdrawalRequest(userId, requestData) {
    const { amount, withdrawalMethod, withdrawalDetails, country } = requestData;
    
    // 检查余额
    const userBalance = await UserBalance.findOne({ where: { userId } });
    if (!userBalance || userBalance.availableBalance < amount) {
      throw new Error('余额不足');
    }

    if (amount < 100) {
      throw new Error('最低提现金额为100美元');
    }

    // 计算税费
    const calculations = this.calculateTaxAndFees(amount, country, withdrawalMethod);
    
    // 生成验证令牌
    const verificationToken = this.generateVerificationToken();
    const verificationExpires = new Date(Date.now() + 30 * 60 * 1000); // 30分钟过期

    // 创建提现请求
    const withdrawalRequest = await WithdrawalRequest.create({
      userId,
      amount,
      withdrawalMethod,
      withdrawalDetails,
      country,
      ...calculations,
      verificationToken,
      verificationExpires,
      scheduledPaymentDate: this.getNextWednesday(),
    });

    return withdrawalRequest;
  }

  // 验证提现请求
  static async verifyWithdrawal(token, verificationCode) {
    const request = await WithdrawalRequest.findOne({
      where: {
        verificationToken: token,
        status: 'pending',
      },
    });

    if (!request) {
      throw new Error('无效的验证令牌');
    }

    if (new Date() > request.verificationExpires) {
      throw new Error('验证令牌已过期');
    }

    // 简化验证：检查验证码（实际应该发送短信/邮件）
    if (verificationCode !== '123456') {
      throw new Error('验证码错误');
    }

    // 更新状态
    await request.update({
      status: 'verified',
      verificationToken: null,
      verificationExpires: null,
    });

    // 冻结用户余额
    const userBalance = await UserBalance.findOne({ where: { userId: request.userId } });
    await userBalance.update({
      availableBalance: userBalance.availableBalance - request.amount,
      pendingBalance: userBalance.pendingBalance + request.amount,
    });

    return request;
  }
}

module.exports = WithdrawalService;