const express = require('express');
const router = express.Router();
const { Notification, User } = require('../models');
const { authenticateToken } = require('../middleware/auth');

// 获取用户通知列表
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 20, type, isRead } = req.query;
    
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
    };
    
    if (type) options.type = type;
    if (isRead !== undefined) options.isRead = isRead === 'true';

    const result = await Notification.getUserNotifications(req.user.userId, options);
    
    // 获取通知的详细信息
    const notifications = await Promise.all(
      result.rows.map(async (notification) => {
        const notificationData = notification.toJSON();
        
        // 获取关联的用户和文章信息
        if (notification.fromUserId) {
          const fromUser = await User.findByPk(notification.fromUserId, {
            attributes: ['id', 'username', 'display_name', 'avatar_url'],
          });
          notificationData.fromUser = fromUser;
        }
        
        if (notification.articleId) {
          const article = await Article.findByPk(notification.articleId, {
            attributes: ['id', 'title', 'image'],
          });
          notificationData.article = article;
        }
        
        return notificationData;
      })
    );

    res.json({
      notifications,
      total: result.count,
      page: parseInt(page),
      totalPages: Math.ceil(result.count / parseInt(limit)),
    });
  } catch (error) {
    console.error('获取通知失败:', error);
    res.status(500).json({ message: error.message });
  }
});

// 获取未读通知数量
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const count = await Notification.getUnreadCount(req.user.userId);
    res.json({ count });
  } catch (error) {
    console.error('获取未读数量失败:', error);
    res.status(500).json({ message: error.message });
  }
});

// 标记通知为已读
router.put('/:id/read', authenticateToken, async (req, res) => {
  try {
    const notification = await Notification.findOne({
      where: {
        id: req.params.id,
        userId: req.user.userId,
      },
    });

    if (!notification) {
      return res.status(404).json({ message: '通知不存在' });
    }

    if (!notification.isRead) {
      await notification.markAsRead();
    }

    res.json({ message: '已标记为已读' });
  } catch (error) {
    console.error('标记已读失败:', error);
    res.status(500).json({ message: error.message });
  }
});

// 标记所有通知为已读
router.put('/read-all', authenticateToken, async (req, res) => {
  try {
    await Notification.markAllAsRead(req.user.userId);
    res.json({ message: '所有通知已标记为已读' });
  } catch (error) {
    console.error('标记所有已读失败:', error);
    res.status(500).json({ message: error.message });
  }
});

// 删除通知
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const notification = await Notification.findOne({
      where: {
        id: req.params.id,
        userId: req.user.userId,
      },
    });

    if (!notification) {
      return res.status(404).json({ message: '通知不存在' });
    }

    await notification.destroy();
    res.json({ message: '通知已删除' });
  } catch (error) {
    console.error('删除通知失败:', error);
    res.status(500).json({ message: error.message });
  }
});

// 创建测试通知（开发环境）
router.post('/test', authenticateToken, async (req, res) => {
  if (process.env.NODE_ENV !== 'development') {
    return res.status(403).json({ message: '仅开发环境可用' });
  }

  try {
    const notification = await Notification.create({
      userId: req.user.userId,
      type: 'system',
      title: '测试通知',
      content: '这是一个测试通知，用于验证通知系统功能。',
      data: { test: true },
      actionUrl: '/notifications',
    });

    res.status(201).json(notification);
  } catch (error) {
    console.error('创建测试通知失败:', error);
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;