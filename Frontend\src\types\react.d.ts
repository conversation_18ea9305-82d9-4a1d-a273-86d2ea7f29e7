// React type extension file
import 'react';

declare module 'react' {
  // Ensure all React types are available
  export interface HTMLAttributes<T> {
    className?: string;
  }
  
  export interface StyleHTMLAttributes<T> extends HTMLAttributes<T> {
    jsx?: boolean;
  }
}

// Global type definitions
declare global {
  namespace JSX {
    interface IntrinsicElements {
      style: React.DetailedHTMLProps<React.StyleHTMLAttributes<HTMLStyleElement> & { jsx?: boolean }, HTMLStyleElement>;
    }
  }
}
