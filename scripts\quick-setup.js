#!/usr/bin/env node

/**
 * Newzora 快速设置脚本
 * 一键完成数据库配置和项目初始化
 */

const { execSync } = require('child_process');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function executeCommand(command, description, cwd = process.cwd()) {
  try {
    log(`🔄 ${description}...`, 'cyan');
    execSync(command, { 
      encoding: 'utf8', 
      stdio: 'inherit',
      cwd: cwd
    });
    log(`✅ ${description} 完成`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} 失败: ${error.message}`, 'red');
    return false;
  }
}

async function quickSetup() {
  log('\n🚀 Newzora 快速设置开始', 'bright');
  log('这将设置数据库、安装依赖并启动开发服务器\n', 'cyan');

  const rootDir = path.join(__dirname, '..');
  
  try {
    // 1. 验证配置
    log('=== 步骤 1: 验证配置 ===', 'yellow');
    if (!executeCommand('node scripts/verify-database-config.js', '验证数据库配置', rootDir)) {
      throw new Error('配置验证失败');
    }

    // 2. 安装依赖
    log('\n=== 步骤 2: 安装依赖 ===', 'yellow');
    
    // 安装根目录依赖
    executeCommand('npm install', '安装根目录依赖', rootDir);
    
    // 安装主站后端依赖
    executeCommand('npm install', '安装主站后端依赖', path.join(rootDir, 'Backend'));
    
    // 安装主站前端依赖
    executeCommand('npm install', '安装主站前端依赖', path.join(rootDir, 'Frontend'));
    
    // 安装管理后台后端依赖
    executeCommand('npm install', '安装管理后台后端依赖', path.join(rootDir, 'NewzoraAdmin/Backend'));
    
    // 安装管理后台前端依赖
    executeCommand('npm install', '安装管理后台前端依赖', path.join(rootDir, 'NewzoraAdmin/Frontend'));

    // 3. 设置数据库
    log('\n=== 步骤 3: 设置数据库 ===', 'yellow');
    if (!executeCommand('node scripts/database-manager.js setup', '设置数据库', rootDir)) {
      log('⚠️  数据库设置失败，请手动检查 PostgreSQL 服务是否运行', 'yellow');
      log('您可以稍后运行:', 'cyan');
      log('  - 主站数据库: node scripts/web-database-manager.js setup', 'cyan');
      log('  - 后台数据库: node NewzoraAdmin/scripts/database-manager.js setup', 'cyan');
    }

    // 4. 构建项目
    log('\n=== 步骤 4: 构建项目 ===', 'yellow');
    executeCommand('npm run build --if-present', '构建主站前端', path.join(rootDir, 'Frontend'));

    // 5. 显示完成信息
    log('\n🎉 快速设置完成！', 'green');
    log('\n📋 接下来的步骤:', 'cyan');
    log('1. 确保 PostgreSQL 服务正在运行', 'yellow');
    log('2. 如果数据库设置失败，运行: node scripts/database-manager.js setup', 'yellow');
    log('3. 启动开发服务器:', 'yellow');
    log('   - 主站前台: cd Frontend && npm run dev', 'cyan');
    log('   - 主站后端: cd Backend && npm run dev', 'cyan');
    log('   - 管理后台前端: cd NewzoraAdmin/Frontend && npm run dev', 'cyan');
    log('   - 管理后台后端: cd NewzoraAdmin/Backend && npm run dev', 'cyan');
    log('\n🌐 访问地址:', 'cyan');
    log('   - 主站前台: http://localhost:3000', 'blue');
    log('   - 管理后台: http://localhost:3001', 'blue');
    log('   - 主站API: http://localhost:5000', 'blue');
    log('   - 管理后台API: http://localhost:5001', 'blue');
    
    log('\n📊 数据库信息:', 'cyan');
    log('   - 主站数据库: PostgreSQL-newzora_web (端口: 5432)', 'blue');
    log('   - 管理数据库: PostgreSQL-newzora_admin (端口: 5433)', 'blue');

  } catch (error) {
    log(`\n❌ 快速设置失败: ${error.message}`, 'red');
    log('\n🔧 故障排除建议:', 'cyan');
    log('1. 确保 Node.js 和 npm 已正确安装', 'yellow');
    log('2. 确保 PostgreSQL 服务正在运行', 'yellow');
    log('3. 检查网络连接和防火墙设置', 'yellow');
    log('4. 查看详细错误信息并手动执行失败的步骤', 'yellow');
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  quickSetup();
}

module.exports = { quickSetup };