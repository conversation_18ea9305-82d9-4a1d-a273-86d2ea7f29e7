require('dotenv').config();
const { testEmailConfig, sendTestEmail } = require('../services/emailService');

async function testEmail() {
  console.log('🔧 开始测试邮件配置...\n');

  try {
    // 1. 测试SMTP连接
    console.log('1. 测试SMTP连接...');
    const isConfigValid = await testEmailConfig();
    
    if (!isConfigValid) {
      console.error('❌ SMTP配置验证失败');
      process.exit(1);
    }

    console.log('✅ SMTP配置验证成功\n');

    // 2. 发送测试邮件
    console.log('2. 发送测试邮件...');
    const testEmail = process.env.EMAIL_USER; // 发送给自己
    
    if (!testEmail) {
      console.error('❌ 未设置EMAIL_USER环境变量');
      process.exit(1);
    }

    const result = await sendTestEmail(testEmail);
    
    if (result.success) {
      console.log('✅ 测试邮件发送成功');
      console.log(`📧 邮件ID: ${result.messageId}`);
      console.log(`📮 邮件服务: ${result.provider}`);
      console.log(`📬 收件人: ${testEmail}`);
    } else {
      console.error('❌ 测试邮件发送失败');
    }

  } catch (error) {
    console.error('❌ 邮件测试失败:', error.message);
    process.exit(1);
  }

  console.log('\n🎉 邮件配置测试完成！');
}

// 运行测试
testEmail();