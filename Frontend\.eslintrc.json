{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off", "@next/next/no-page-custom-font": "off"}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "env": {"browser": true, "es2020": true, "node": true}}