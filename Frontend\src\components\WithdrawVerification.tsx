'use client';

import { useState } from 'react';
import { 
  Shield, 
  CheckCircle, 
  User, 
  MapPin, 
  FileText, 
  Phone, 
  Mail, 
  Video,
  Camera,
  Smartphone,
  Clock,
  AlertTriangle,
  Info,
  Star,
  Lock
} from 'lucide-react';

interface VerificationStep {
  id: string;
  title: string;
  description: string;
  icon: any;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  required: boolean;
  estimatedTime: string;
  requirements: string[];
}

interface WithdrawVerificationProps {
  verificationSteps: VerificationStep[];
  onStepAction: (stepId: string) => void;
  activeStep?: string | null;
}

export default function WithdrawVerification({ 
  verificationSteps, 
  onStepAction, 
  activeStep 
}: WithdrawVerificationProps) {
  const [expandedStep, setExpandedStep] = useState<string | null>(null);

  const getOverallProgress = () => {
    const completed = verificationSteps.filter(step => step.status === 'completed').length;
    return Math.round((completed / verificationSteps.length) * 100);
  };

  const isFullyVerified = () => {
    return verificationSteps.every(step => step.status === 'completed');
  };

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6">
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
          <Shield className="h-8 w-8 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">Identity Verification</h3>
        <div className="w-full bg-gray-200 rounded-full h-3 mb-2 overflow-hidden">
          <div 
            className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500 shadow-sm"
            style={{ width: `${getOverallProgress()}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600 font-medium">{getOverallProgress()}% Complete</p>
      </div>

      {/* Verification Steps */}
      <div className="space-y-4">
        {verificationSteps.map((step, index) => {
          const Icon = step.icon;
          const isExpanded = expandedStep === step.id;
          
          return (
            <div 
              key={step.id} 
              className={`border rounded-xl transition-all duration-300 ${
                step.status === 'completed' 
                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200' 
                  : step.status === 'in_progress' 
                    ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200' 
                    : step.status === 'failed'
                      ? 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
                      : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
              }`}
            >
              <div 
                className="p-4 cursor-pointer"
                onClick={() => setExpandedStep(isExpanded ? null : step.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      step.status === 'completed' 
                        ? 'bg-green-500' 
                        : step.status === 'in_progress' 
                          ? 'bg-blue-500' 
                          : step.status === 'failed'
                            ? 'bg-red-500'
                            : 'bg-gray-400'
                    }`}>
                      {step.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-white" />
                      ) : step.status === 'in_progress' ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      ) : step.status === 'failed' ? (
                        <AlertTriangle className="h-5 w-5 text-white" />
                      ) : (
                        <Icon className="h-5 w-5 text-white" />
                      )}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900">{step.title}</h4>
                        {step.required && (
                          <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                            Required
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600">{step.description}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {step.status === 'completed' && (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        <span className="text-xs font-medium">Verified</span>
                      </div>
                    )}
                    
                    <div className="text-xs text-gray-500 flex items-center">
                      <Clock className="h-3 w-3 mr-1" />
                      {step.estimatedTime}
                    </div>
                  </div>
                </div>
              </div>

              {/* Expanded Content */}
              {isExpanded && (
                <div className="px-4 pb-4 border-t border-gray-200">
                  <div className="mt-4 space-y-3">
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 mb-2">Requirements:</h5>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {step.requirements.map((req, idx) => (
                          <li key={idx} className="flex items-center">
                            <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-2"></div>
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    {step.status !== 'completed' && (
                      <button
                        onClick={() => onStepAction(step.id)}
                        disabled={activeStep === step.id}
                        className={`w-full mt-3 py-2 px-4 rounded-lg text-sm font-medium transition-all ${
                          step.status === 'in_progress' || activeStep === step.id
                            ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                            : 'bg-blue-600 text-white hover:bg-blue-700'
                        }`}
                      >
                        {step.status === 'in_progress' || activeStep === step.id 
                          ? 'Under Review...' 
                          : step.status === 'failed'
                            ? 'Retry Verification'
                            : 'Start Verification'
                        }
                      </button>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Status Summary */}
      <div className="mt-6">
        {isFullyVerified() ? (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-semibold text-green-800">Verification Complete!</p>
                <p className="text-xs text-green-600">You can now withdraw funds safely</p>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center mr-3">
                <Lock className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="text-sm font-semibold text-yellow-800">Verification Required</p>
                <p className="text-xs text-yellow-600">
                  Complete {verificationSteps.filter(s => s.status !== 'completed').length} more steps to enable withdrawals
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Help Section */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Need Help?</p>
            <p className="text-xs">
              Our verification process ensures the security of your funds. 
              Contact support if you need assistance with any step.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
