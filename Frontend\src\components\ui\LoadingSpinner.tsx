'use client';

import React from 'react';
import { cn } from '@/lib/utils';

export interface LoadingSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'dots' | 'pulse' | 'bars' | 'ring' | 'gradient';
  color?: 'primary' | 'secondary' | 'accent' | 'white';
  className?: string;
}

export function LoadingSpinner({
  size = 'md',
  variant = 'default',
  color = 'primary',
  className,
}: LoadingSpinnerProps) {
  // 尺寸配置
  const sizeConfig = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
  };

  // 颜色配置
  const colorConfig = {
    primary: 'text-primary border-primary',
    secondary: 'text-secondary border-secondary',
    accent: 'text-accent border-accent',
    white: 'text-white border-white',
  };

  const baseClasses = cn(
    'inline-block animate-spin',
    sizeConfig[size],
    colorConfig[color],
    className
  );

  // 默认旋转加载器
  if (variant === 'default') {
    return (
      <div className={baseClasses}>
        <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </div>
    );
  }

  // 点状加载器
  if (variant === 'dots') {
    return (
      <div className={cn('flex space-x-1', className)}>
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            className={cn(
              'rounded-full animate-pulse',
              size === 'xs'
                ? 'w-1 h-1'
                : size === 'sm'
                  ? 'w-1.5 h-1.5'
                  : size === 'md'
                    ? 'w-2 h-2'
                    : size === 'lg'
                      ? 'w-3 h-3'
                      : 'w-4 h-4',
              colorConfig[color].split(' ')[0]
            )}
            style={{
              animationDelay: `${i * 0.2}s`,
              animationDuration: '1.4s',
            }}
          />
        ))}
      </div>
    );
  }

  // 脉冲加载器
  if (variant === 'pulse') {
    return (
      <div
        className={cn(
          'rounded-full animate-pulse',
          sizeConfig[size],
          colorConfig[color].split(' ')[0],
          className
        )}
      />
    );
  }

  // 条状加载器
  if (variant === 'bars') {
    return (
      <div className={cn('flex items-end space-x-1', className)}>
        {[0, 1, 2, 3].map((i) => (
          <div
            key={i}
            className={cn(
              'animate-bounce',
              size === 'xs'
                ? 'w-0.5 h-2'
                : size === 'sm'
                  ? 'w-0.5 h-3'
                  : size === 'md'
                    ? 'w-1 h-4'
                    : size === 'lg'
                      ? 'w-1 h-6'
                      : 'w-1.5 h-8',
              colorConfig[color].split(' ')[0]
            )}
            style={{
              animationDelay: `${i * 0.1}s`,
              animationDuration: '1s',
            }}
          />
        ))}
      </div>
    );
  }

  // 环形加载器
  if (variant === 'ring') {
    return (
      <div
        className={cn(
          'border-4 border-t-transparent rounded-full animate-spin',
          sizeConfig[size],
          colorConfig[color].split(' ')[1],
          className
        )}
      />
    );
  }

  // 渐变加载器
  if (variant === 'gradient') {
    return (
      <div className={cn('relative overflow-hidden rounded-full', sizeConfig[size], className)}>
        <div className="absolute inset-0 bg-gradient-to-r from-primary via-secondary to-accent animate-spin" />
        <div className="absolute inset-1 bg-surface rounded-full" />
      </div>
    );
  }

  return null;
}

// 全屏加载组件
export function FullScreenLoader({
  message = 'Loading...',
  variant = 'default',
  size = 'lg',
}: {
  message?: string;
  variant?: LoadingSpinnerProps['variant'];
  size?: LoadingSpinnerProps['size'];
}) {
  return (
    <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="text-center">
        <LoadingSpinner variant={variant} size={size} />
        <p className="mt-4 text-text-secondary font-medium">{message}</p>
      </div>
    </div>
  );
}

// 骨架屏组件
export function Skeleton({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn('animate-pulse rounded-md bg-neutral-200 dark:bg-neutral-700', className)}
      {...props}
    />
  );
}

// 骨架屏文本
export function SkeletonText({ lines = 3, className }: { lines?: number; className?: string }) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton key={i} className={cn('h-4', i === lines - 1 ? 'w-3/4' : 'w-full')} />
      ))}
    </div>
  );
}
